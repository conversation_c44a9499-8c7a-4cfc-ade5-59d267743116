<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.FactoryQualificationMapper">
    
    <resultMap type="FactoryQualification" id="FactoryQualificationResult">
        <result property="id"    column="id"    />
        <result property="factoryId"    column="factory_id"    />
        <result property="qualificationName"    column="qualification_name"    />
        <result property="attachment"    column="attachment"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectFactoryQualificationVo">
        select id, factory_id, qualification_name, attachment, create_time, update_time from factory_qualification
    </sql>

    <select id="selectFactoryQualificationList" parameterType="FactoryQualification" resultMap="FactoryQualificationResult">
        <include refid="selectFactoryQualificationVo"/>
        <where>  
            <if test="factoryId != null "> and factory_id = #{factoryId}</if>
            <if test="qualificationName != null  and qualificationName != ''"> and qualification_name like concat('%', #{qualificationName}, '%')</if>
            <if test="attachment != null  and attachment != ''"> and attachment = #{attachment}</if>
        </where>
    </select>
    
    <select id="selectFactoryQualificationById" parameterType="Long" resultMap="FactoryQualificationResult">
        <include refid="selectFactoryQualificationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFactoryQualification" parameterType="FactoryQualification" useGeneratedKeys="true" keyProperty="id">
        insert into factory_qualification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="factoryId != null">factory_id,</if>
            <if test="qualificationName != null and qualificationName != ''">qualification_name,</if>
            <if test="attachment != null">attachment,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="factoryId != null">#{factoryId},</if>
            <if test="qualificationName != null and qualificationName != ''">#{qualificationName},</if>
            <if test="attachment != null">#{attachment},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateFactoryQualification" parameterType="FactoryQualification">
        update factory_qualification
        <trim prefix="SET" suffixOverrides=",">
            <if test="factoryId != null">factory_id = #{factoryId},</if>
            <if test="qualificationName != null and qualificationName != ''">qualification_name = #{qualificationName},</if>
            <if test="attachment != null">attachment = #{attachment},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFactoryQualificationById" parameterType="Long">
        delete from factory_qualification where id = #{id}
    </delete>

    <delete id="deleteFactoryQualificationByIds" parameterType="String">
        delete from factory_qualification where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>