# Tomcat
server:
  port: 9211

# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-portalconsole
  profiles:
    # 环境配置
#    active: dev
    active: prod
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: 8fd725fc-adb3-4a7e-bb5b-7dc4e5e9a5d1
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: 8fd725fc-adb3-4a7e-bb5b-7dc4e5e9a5d1
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}


ali:
  sign: 柠檬豆平台
  templatePass: SMS_232910346
  templateFail: SMS_232910346
