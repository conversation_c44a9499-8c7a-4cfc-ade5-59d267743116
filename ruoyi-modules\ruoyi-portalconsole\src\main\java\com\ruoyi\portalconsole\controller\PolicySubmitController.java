package com.ruoyi.portalconsole.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.common.core.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.PolicySubmit;
import com.ruoyi.portalconsole.service.IPolicySubmitService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 政策申报Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/PolicySubmit")
public class PolicySubmitController extends BaseController
{
    @Autowired
    private IPolicySubmitService policySubmitService;

    /**
     * 查询政策申报列表
     */
    @RequiresPermissions("portalconsole:PolicySubmit:list")
    @GetMapping("/list")
    public TableDataInfo list(PolicySubmit policySubmit)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<PolicySubmit> list = policySubmitService.selectPolicySubmitList(policySubmit);
        return getDataTable(list);
    }

    /**
     * 导出政策申报列表
     */
    @RequiresPermissions("portalconsole:PolicySubmit:export")
    @Log(title = "政策申报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PolicySubmit policySubmit)
    {
        List<PolicySubmit> list = policySubmitService.selectPolicySubmitList(policySubmit);
        ExcelUtil<PolicySubmit> util = new ExcelUtil<PolicySubmit>(PolicySubmit.class);
        util.exportExcel(response, list, "政策申报数据");
    }

    /**
     * 获取政策申报详细信息
     */
    @RequiresPermissions("portalconsole:PolicySubmit:query")
    @GetMapping(value = "/{policySubmitId}")
    public AjaxResult getInfo(@PathVariable("policySubmitId") Long policySubmitId)
    {
        return success(policySubmitService.selectPolicySubmitByPolicySubmitId(policySubmitId));
    }

    /**
     * 新增政策申报
     */
    @RequiresPermissions("portalconsole:PolicySubmit:add")
    @Log(title = "政策申报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PolicySubmit policySubmit)
    {
        return toAjax(policySubmitService.insertPolicySubmit(policySubmit));
    }

    /**
     * 新增政策申报
     */
    @Log(title = "政策申报", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    public AjaxResult addByImport(@RequestBody PolicySubmit policySubmit)
    {
        return toAjax(policySubmitService.insertPolicySubmit(policySubmit));
    }

    /**
     * 修改政策申报
     */
    @RequiresPermissions("portalconsole:PolicySubmit:edit")
    @Log(title = "政策申报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PolicySubmit policySubmit)
    {
        return toAjax(policySubmitService.updatePolicySubmit(policySubmit));
    }

    /**
     * 审核政策申报
     */
    @RequiresPermissions("portalconsole:PolicySubmit:audit")
    @Log(title = "政策申报", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody PolicySubmit policySubmit)
    {
        return toAjax(policySubmitService.auditPolicySubmit(policySubmit));
    }

    /**
     * 删除政策申报
     */
    @RequiresPermissions("portalconsole:PolicySubmit:remove")
    @Log(title = "政策申报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{policySubmitIds}")
    public AjaxResult remove(@PathVariable Long[] policySubmitIds)
    {
        return toAjax(policySubmitService.deletePolicySubmitByPolicySubmitIds(policySubmitIds));
    }
}
