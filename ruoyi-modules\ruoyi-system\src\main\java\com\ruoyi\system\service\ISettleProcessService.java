package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SettleProcess;

/**
 * 入驻申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface ISettleProcessService 
{
    /**
     * 查询入驻申请
     * 
     * @param id 入驻申请主键
     * @return 入驻申请
     */
    public SettleProcess selectSettleProcessById(Long id);

    /**
     * 查询入驻申请列表
     * 
     * @param settleProcess 入驻申请
     * @return 入驻申请集合
     */
    public List<SettleProcess> selectSettleProcessList(SettleProcess settleProcess);

    /**
     * 新增入驻申请
     * 
     * @param settleProcess 入驻申请
     * @return 结果
     */
    public int insertSettleProcess(SettleProcess settleProcess);

    /**
     * 修改入驻申请
     * 
     * @param settleProcess 入驻申请
     * @return 结果
     */
    public int updateSettleProcess(SettleProcess settleProcess);

    /**
     * 批量删除入驻申请
     * 
     * @param ids 需要删除的入驻申请主键集合
     * @return 结果
     */
    public int deleteSettleProcessByIds(Long[] ids);

    /**
     * 删除入驻申请信息
     * 
     * @param id 入驻申请主键
     * @return 结果
     */
    public int deleteSettleProcessById(Long id);
}
