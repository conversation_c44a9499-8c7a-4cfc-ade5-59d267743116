package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 制造订单对象 manufacture_order
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
public class ManufactureOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    private Long id;

    /** 需求截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "需求截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deadline;

    /** 订单状态（0 未接单/ 1进行中/ 2已完成） */
    @Excel(name = "订单状态", readConverterExp = "0=,未=接单/,1=进行中/,2=已完成")
    private String status;

    /** 订单类型（生产订单/工序外协/入驻工厂） */
    @Excel(name = "订单类型", readConverterExp = "生=产订单/工序外协/入驻工厂")
    private String orderType;

    /** 托单价格 */
    @Excel(name = "托单价格")
    private BigDecimal price;

    /** 需求企业 */
    @Excel(name = "需求企业")
    private String demandCompany;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 交货地址 */
    @Excel(name = "交货地址")
    private String deliveryAddress;

    /** 文件要求 */
    @Excel(name = "文件要求")
    private String fileRequirement;

    /** 开户行 */
    @Excel(name = "开户行")
    private String bankName;

    /** 付款账号 */
    @Excel(name = "付款账号")
    private String paymentAccount;

    /** 审核状态（0待审核 1已通过 2未通过） */
    @Excel(name = "审核状态", readConverterExp = "0=待审核,1=已通过,2=未通过")
    private String auditStatus;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setDeadline(Date deadline)
    {
        this.deadline = deadline;
    }

    public Date getDeadline()
    {
        return deadline;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setOrderType(String orderType)
    {
        this.orderType = orderType;
    }

    public String getOrderType()
    {
        return orderType;
    }
    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }

    public BigDecimal getPrice()
    {
        return price;
    }
    public void setDemandCompany(String demandCompany)
    {
        this.demandCompany = demandCompany;
    }

    public String getDemandCompany()
    {
        return demandCompany;
    }
    
    public void setContactPerson(String contactPerson)
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson()
    {
        return contactPerson;
    }
    
    public void setContactPhone(String contactPhone)
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone()
    {
        return contactPhone;
    }
    public void setDeliveryAddress(String deliveryAddress)
    {
        this.deliveryAddress = deliveryAddress;
    }

    public String getDeliveryAddress()
    {
        return deliveryAddress;
    }
    public void setFileRequirement(String fileRequirement)
    {
        this.fileRequirement = fileRequirement;
    }

    public String getFileRequirement()
    {
        return fileRequirement;
    }
    public void setBankName(String bankName)
    {
        this.bankName = bankName;
    }

    public String getBankName()
    {
        return bankName;
    }
    public void setPaymentAccount(String paymentAccount)
    {
        this.paymentAccount = paymentAccount;
    }

    public String getPaymentAccount()
    {
        return paymentAccount;
    }

    public String getAuditStatus()
    {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus)
    {
        this.auditStatus = auditStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("deadline", getDeadline())
                .append("status", getStatus())
                .append("orderType", getOrderType())
                .append("price", getPrice())
                .append("demandCompany", getDemandCompany())
                .append("contactPerson", getContactPerson())
                .append("contactPhone", getContactPhone())
                .append("deliveryAddress", getDeliveryAddress())
                .append("fileRequirement", getFileRequirement())
                .append("bankName", getBankName())
                .append("paymentAccount", getPaymentAccount())
                .append("auditStatus", getAuditStatus())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
