package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.FactoryPerformanceMapper;
import com.ruoyi.system.domain.FactoryPerformance;
import com.ruoyi.system.service.IFactoryPerformanceService;

/**
 * 工厂业绩情况Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@Service
public class FactoryPerformanceServiceImpl implements IFactoryPerformanceService 
{
    @Autowired
    private FactoryPerformanceMapper factoryPerformanceMapper;

    /**
     * 查询工厂业绩情况
     * 
     * @param id 工厂业绩情况主键
     * @return 工厂业绩情况
     */
    @Override
    public FactoryPerformance selectFactoryPerformanceById(Long id)
    {
        return factoryPerformanceMapper.selectFactoryPerformanceById(id);
    }

    /**
     * 查询工厂业绩情况列表
     * 
     * @param factoryPerformance 工厂业绩情况
     * @return 工厂业绩情况
     */
    @Override
    public List<FactoryPerformance> selectFactoryPerformanceList(FactoryPerformance factoryPerformance)
    {
        return factoryPerformanceMapper.selectFactoryPerformanceList(factoryPerformance);
    }

    /**
     * 新增工厂业绩情况
     * 
     * @param factoryPerformance 工厂业绩情况
     * @return 结果
     */
    @Override
    public int insertFactoryPerformance(FactoryPerformance factoryPerformance)
    {
        factoryPerformance.setCreateTime(DateUtils.getNowDate());
        return factoryPerformanceMapper.insertFactoryPerformance(factoryPerformance);
    }

    /**
     * 修改工厂业绩情况
     * 
     * @param factoryPerformance 工厂业绩情况
     * @return 结果
     */
    @Override
    public int updateFactoryPerformance(FactoryPerformance factoryPerformance)
    {
        factoryPerformance.setUpdateTime(DateUtils.getNowDate());
        return factoryPerformanceMapper.updateFactoryPerformance(factoryPerformance);
    }

    /**
     * 批量删除工厂业绩情况
     * 
     * @param ids 需要删除的工厂业绩情况主键
     * @return 结果
     */
    @Override
    public int deleteFactoryPerformanceByIds(Long[] ids)
    {
        return factoryPerformanceMapper.deleteFactoryPerformanceByIds(ids);
    }

    /**
     * 删除工厂业绩情况信息
     * 
     * @param id 工厂业绩情况主键
     * @return 结果
     */
    @Override
    public int deleteFactoryPerformanceById(Long id)
    {
        return factoryPerformanceMapper.deleteFactoryPerformanceById(id);
    }
}
