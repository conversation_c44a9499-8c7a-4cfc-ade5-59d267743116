package com.ruoyi.portalconsole.mapper;

import java.util.List;
import com.ruoyi.portalconsole.domain.Recommendation;

/**
 * 建议及反馈Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-06
 */
public interface RecommendationMapper 
{
    /**
     * 查询建议及反馈
     * 
     * @param id 建议及反馈主键
     * @return 建议及反馈
     */
    public Recommendation selectRecommendationById(Long id);

    /**
     * 查询建议及反馈列表
     * 
     * @param recommendation 建议及反馈
     * @return 建议及反馈集合
     */
    public List<Recommendation> selectRecommendationList(Recommendation recommendation);

    /**
     * 新增建议及反馈
     * 
     * @param recommendation 建议及反馈
     * @return 结果
     */
    public int insertRecommendation(Recommendation recommendation);

    /**
     * 修改建议及反馈
     * 
     * @param recommendation 建议及反馈
     * @return 结果
     */
    public int updateRecommendation(Recommendation recommendation);

    /**
     * 删除建议及反馈
     * 
     * @param id 建议及反馈主键
     * @return 结果
     */
    public int deleteRecommendationById(Long id);

    /**
     * 批量删除建议及反馈
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecommendationByIds(Long[] ids);
}
