<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.SysFileInfoMapper">
    
    <resultMap type="SysFileInfo" id="SysFileInfoResult">
        <result property="fileId"    column="file_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileFullPath"    column="file_full_path"    />
        <result property="fileExtension"    column="file_extension"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysFileInfoVo">
        select file_id, file_name, file_path, file_full_path, file_extension, del_flag, create_by, create_time, update_by, update_time, remark from sys_file_info
    </sql>

    <select id="selectSysFileInfoList" parameterType="SysFileInfo" resultMap="SysFileInfoResult">
        <include refid="selectSysFileInfoVo"/>
        <where>  
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="fileExtension != null "> and file_extension = #{fileExtension}</if>
        </where>
    </select>
    
    <select id="selectSysFileInfoByFileId" parameterType="Long" resultMap="SysFileInfoResult">
        <include refid="selectSysFileInfoVo"/>
        where file_id = #{fileId}
    </select>
        
    <insert id="insertSysFileInfo" parameterType="SysFileInfo" useGeneratedKeys="true" keyProperty="fileId">
        insert into sys_file_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null">file_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="fileFullPath != null">file_full_path,</if>
            <if test="fileExtension != null">file_extension,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="fileFullPath != null">#{fileFullPath},</if>
            <if test="fileExtension != null">#{fileExtension},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysFileInfo" parameterType="SysFileInfo">
        update sys_file_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="fileFullPath != null">file_full_path = #{fileFullPath},</if>
            <if test="fileExtension != null">file_extension = #{fileExtension},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where file_id = #{fileId}
    </update>

    <delete id="deleteSysFileInfoByFileId" parameterType="Long">
        delete from sys_file_info where file_id = #{fileId}
    </delete>

    <delete id="deleteSysFileInfoByFileIds" parameterType="String">
        delete from sys_file_info where file_id in 
        <foreach item="fileId" collection="array" open="(" separator="," close=")">
            #{fileId}
        </foreach>
    </delete>
</mapper>