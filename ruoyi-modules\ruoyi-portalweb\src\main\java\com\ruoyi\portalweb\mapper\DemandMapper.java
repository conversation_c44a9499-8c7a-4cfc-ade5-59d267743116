package com.ruoyi.portalweb.mapper;


import com.ruoyi.portalweb.api.domain.Demand;
import com.ruoyi.portalweb.vo.DemandVO;

import java.util.List;

/**
 * 服务需求(NEW)Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface DemandMapper 
{
    /**
     * 查询服务需求(NEW)
     * 
     * @param id 服务需求(NEW)主键
     * @return 服务需求(NEW)
     */
    public DemandVO selectDemandById(Long id);

    /**
     * 查询服务需求(NEW)列表
     * 
     * @param demand 服务需求(NEW)
     * @return 服务需求(NEW)集合
     */
    public List<DemandVO> selectDemandList(DemandVO demand);

    /**
     * 新增服务需求(NEW)
     * 
     * @param demand 服务需求(NEW)
     * @return 结果
     */
    public int insertDemand(Demand demand);

    /**
     * 修改服务需求(NEW)
     * 
     * @param demand 服务需求(NEW)
     * @return 结果
     */
    public int updateDemand(Demand demand);

    /**
     * 增加阅读量统计
     * @param id
     * @return
     */
    public int addDemandViewCount(Long id);

    /**
     * 删除服务需求(NEW)
     * 
     * @param id 服务需求(NEW)主键
     * @return 结果
     */
    public int deleteDemandById(Long id);

    /**
     * 批量删除服务需求(NEW)
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDemandByIds(Long[] ids);

    public  List<DemandVO> selectDemandListByMemberIds(List<Long> memberIds);

    public List<DemandVO> selectDemandListByDemandIds(List<Long> demandIds);
}
