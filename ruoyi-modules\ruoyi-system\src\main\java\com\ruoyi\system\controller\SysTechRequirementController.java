package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysTechRequirement;
import com.ruoyi.system.service.ISysTechRequirementService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 技术需求Controller
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/SysTechRequirement")
public class SysTechRequirementController extends BaseController
{
    @Autowired
    private ISysTechRequirementService sysTechRequirementService;

    /**
     * 查询技术需求列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SysTechRequirement sysTechRequirement)
    {
        startPage();
        List<SysTechRequirement> list = sysTechRequirementService.selectSysTechRequirementList(sysTechRequirement);
        return getDataTable(list);
    }

    /**
     * 导出技术需求列表
     */
    @RequiresPermissions("system:SysTechRequirement:export")
    @Log(title = "技术需求", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysTechRequirement sysTechRequirement)
    {
        List<SysTechRequirement> list = sysTechRequirementService.selectSysTechRequirementList(sysTechRequirement);
        ExcelUtil<SysTechRequirement> util = new ExcelUtil<SysTechRequirement>(SysTechRequirement.class);
        util.exportExcel(response, list, "技术需求数据");
    }

    /**
     * 获取技术需求详细信息
     */
    @RequiresPermissions("system:SysTechRequirement:query")
    @GetMapping(value = "/{requirementId}")
    public AjaxResult getInfo(@PathVariable("requirementId") Long requirementId)
    {
        return success(sysTechRequirementService.selectSysTechRequirementByRequirementId(requirementId));
    }

    /**
     * 新增技术需求
     */
    @RequiresPermissions("system:SysTechRequirement:add")
    @Log(title = "技术需求", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysTechRequirement sysTechRequirement)
    {
        return toAjax(sysTechRequirementService.insertSysTechRequirement(sysTechRequirement));
    }

    /**
     * 修改技术需求
     */
    @RequiresPermissions("system:SysTechRequirement:edit")
    @Log(title = "技术需求", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysTechRequirement sysTechRequirement)
    {
        return toAjax(sysTechRequirementService.updateSysTechRequirement(sysTechRequirement));
    }

    /**
     * 删除技术需求
     */
    @RequiresPermissions("system:SysTechRequirement:remove")
    @Log(title = "技术需求", businessType = BusinessType.DELETE)
	@DeleteMapping("/{requirementIds}")
    public AjaxResult remove(@PathVariable Long[] requirementIds)
    {
        return toAjax(sysTechRequirementService.deleteSysTechRequirementByRequirementIds(requirementIds));
    }
}
