<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.PolicySubmitMapper">

    <resultMap type="PolicySubmit" id="PolicySubmitResult">
        <result property="policySubmitId" column="policy_submit_id"/>
        <result property="policySubmitUnit" column="policy_submit_unit"/>
        <result property="policySubmitType" column="policy_submit_type"/>
        <result property="policySubmitTitle" column="policy_submit_title"/>
        <result property="policySubmitEndDate" column="policy_submit_end_date"/>
        <result property="policySubmitReward" column="policy_submit_reward"/>
        <result property="policySubmitImg" column="policy_submit_img"/>
        <result property="policySubmitContent" column="policy_submit_content"/>
        <result property="policySubmitStatus" column="policy_submit_status"/>
        <result property="policyLabel" column="policy_label"/>
        <result property="companyLabel" column="company_label"/>
        <result property="recommend" column="recommend"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberId" column="member_id"/>
    </resultMap>

    <sql id="selectPolicySubmitVo">
        select policy_submit_id, policy_submit_unit, policy_submit_type, policy_submit_title, policy_submit_end_date,
        policy_submit_reward, policy_submit_img, policy_submit_content, policy_submit_status, policy_label,member_id,
        company_label, recommend, del_flag, create_by, create_time, update_by, update_time, remark from policy_submit
    </sql>

    <select id="selectPolicySubmitList" parameterType="PolicySubmit" resultMap="PolicySubmitResult">
        <include refid="selectPolicySubmitVo"/>
        <where>
            <if test="policySubmitUnit != null  and policySubmitUnit != ''">and policy_submit_unit =
                #{policySubmitUnit}
            </if>
            <if test="policySubmitType != null  and policySubmitType != ''">and policy_submit_type =
                #{policySubmitType}
            </if>
            <if test="policySubmitTitle != null  and policySubmitTitle != ''">and policy_submit_title =
                #{policySubmitTitle}
            </if>
            <if test="policySubmitEndDate != null ">and policy_submit_end_date = #{policySubmitEndDate}</if>
            <if test="policySubmitReward != null  and policySubmitReward != ''">and policy_submit_reward =
                #{policySubmitReward}
            </if>
            <if test="policySubmitImg != null  and policySubmitImg != ''">and policy_submit_img = #{policySubmitImg}
            </if>
            <if test="policySubmitContent != null  and policySubmitContent != ''">and policy_submit_content =
                #{policySubmitContent}
            </if>
            <if test="policySubmitStatus != null  and policySubmitStatus != ''">and policy_submit_status =
                #{policySubmitStatus}
            </if>
            <if test="recommend != null ">and recommend = #{recommend}</if>
        </where>
    </select>

    <select id="selectPolicySubmitByPolicySubmitId" parameterType="Long" resultMap="PolicySubmitResult">
        <include refid="selectPolicySubmitVo"/>
        where policy_submit_id = #{policySubmitId}
    </select>

    <insert id="insertPolicySubmit" parameterType="PolicySubmit" useGeneratedKeys="true" keyProperty="policySubmitId">
        insert into policy_submit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="policySubmitUnit != null">policy_submit_unit,</if>
            <if test="policySubmitType != null">policy_submit_type,</if>
            <if test="policySubmitTitle != null">policy_submit_title,</if>
            <if test="policySubmitEndDate != null">policy_submit_end_date,</if>
            <if test="policySubmitReward != null">policy_submit_reward,</if>
            <if test="policySubmitImg != null">policy_submit_img,</if>
            <if test="policySubmitContent != null">policy_submit_content,</if>
            <if test="policySubmitStatus != null">policy_submit_status,</if>
            <if test="policyLabel != null">policy_label,</if>
            <if test="companyLabel != null">company_label,</if>
            <if test="recommend != null">recommend,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="policySubmitUnit != null">#{policySubmitUnit},</if>
            <if test="policySubmitType != null">#{policySubmitType},</if>
            <if test="policySubmitTitle != null">#{policySubmitTitle},</if>
            <if test="policySubmitEndDate != null">#{policySubmitEndDate},</if>
            <if test="policySubmitReward != null">#{policySubmitReward},</if>
            <if test="policySubmitImg != null">#{policySubmitImg},</if>
            <if test="policySubmitContent != null">#{policySubmitContent},</if>
            <if test="policySubmitStatus != null">#{policySubmitStatus},</if>
            <if test="policyLabel != null">#{policyLabel},</if>
            <if test="companyLabel != null">#{companyLabel},</if>
            <if test="recommend != null">#{recommend},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updatePolicySubmit" parameterType="PolicySubmit">
        update policy_submit
        <trim prefix="SET" suffixOverrides=",">
            <if test="policySubmitUnit != null">policy_submit_unit = #{policySubmitUnit},</if>
            <if test="policySubmitType != null">policy_submit_type = #{policySubmitType},</if>
            <if test="policySubmitTitle != null">policy_submit_title = #{policySubmitTitle},</if>
            <if test="policySubmitEndDate != null">policy_submit_end_date = #{policySubmitEndDate},</if>
            <if test="policySubmitReward != null">policy_submit_reward = #{policySubmitReward},</if>
            <if test="policySubmitImg != null">policy_submit_img = #{policySubmitImg},</if>
            <if test="policySubmitContent != null">policy_submit_content = #{policySubmitContent},</if>
            <if test="policySubmitStatus != null">policy_submit_status = #{policySubmitStatus},</if>
            <if test="policyLabel != null">policy_label = #{policyLabel},</if>
            <if test="companyLabel != null">company_label = #{companyLabel},</if>
            <if test="recommend != null">recommend = #{recommend},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where policy_submit_id = #{policySubmitId}
    </update>

    <delete id="deletePolicySubmitByPolicySubmitId" parameterType="Long">
        delete from policy_submit where policy_submit_id = #{policySubmitId}
    </delete>

    <delete id="deletePolicySubmitByPolicySubmitIds" parameterType="String">
        delete from policy_submit where policy_submit_id in
        <foreach item="policySubmitId" collection="array" open="(" separator="," close=")">
            #{policySubmitId}
        </foreach>
    </delete>
</mapper>