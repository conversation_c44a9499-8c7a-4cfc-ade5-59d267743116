package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.JobInfo;
import com.ruoyi.system.service.IJobInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 用工信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@RestController
@RequestMapping("/jobInfo")
public class JobInfoController extends BaseController
{
    @Autowired
    private IJobInfoService jobInfoService;

    /**
     * 查询用工信息列表
     */
//    @RequiresPermissions("system:jobInfo:list")
    @GetMapping("/list")
    public TableDataInfo list(JobInfo jobInfo)
    {
        startPage();
        List<JobInfo> list = jobInfoService.selectJobInfoList(jobInfo);
        return getDataTable(list);
    }
    @GetMapping("user/list")
    public TableDataInfo list2(JobInfo jobInfo)
    {
        startPage();
//        jobInfo.setCreateBy(SecurityUtils.getLoginMember().getMemberphone());
        List<JobInfo> list = jobInfoService.selectJobInfoList(jobInfo);
        return getDataTable(list);
    }


    /**
     * 导出用工信息列表
     */

    @Log(title = "用工信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JobInfo jobInfo)
    {
        List<JobInfo> list = jobInfoService.selectJobInfoList(jobInfo);
        ExcelUtil<JobInfo> util = new ExcelUtil<JobInfo>(JobInfo.class);
        util.exportExcel(response, list, "用工信息数据");
    }

    /**
     * 获取用工信息详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(jobInfoService.selectJobInfoById(id));
    }

    /**
     * 新增用工信息
     */

    @Log(title = "用工信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JobInfo jobInfo)
    {
        return toAjax(jobInfoService.insertJobInfo(jobInfo));
    }

    /**
     * 修改用工信息
     */

    @Log(title = "用工信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JobInfo jobInfo)
    {
        return toAjax(jobInfoService.updateJobInfo(jobInfo));
    }

    /**
     * 删除用工信息
     */

    @Log(title = "用工信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(jobInfoService.deleteJobInfoByIds(ids));
    }
}
