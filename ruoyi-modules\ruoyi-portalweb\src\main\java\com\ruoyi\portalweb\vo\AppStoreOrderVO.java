package com.ruoyi.portalweb.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.portalweb.api.domain.AppStoreOrder;

public class AppStoreOrderVO extends AppStoreOrder {
    private static final long serialVersionUID = 1L;

    private String saleCompanyName;

    private String buyCompanyName;

    @JsonIgnore
    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSaleCompanyName() {
        return saleCompanyName;
    }

    public void setSaleCompanyName(String saleCompanyName) {
        this.saleCompanyName = saleCompanyName;
    }

    public String getBuyCompanyName() {return buyCompanyName;}

    public void setBuyCompanyName(String buyCompanyName) {this.buyCompanyName = buyCompanyName;}
}
