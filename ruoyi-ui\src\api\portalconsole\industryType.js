import request from '@/utils/request'

// 查询行业类别列表
export function listIndustryType(query) {
  return request({
    url: '/portalconsole/industryType/list',
    method: 'get',
    params: query
  })
}

// 查询行业类别详细
export function getIndustryType(industryTypeId) {
  return request({
    url: '/portalconsole/industryType/' + industryTypeId,
    method: 'get'
  })
}

// 新增行业类别
export function addIndustryType(data) {
  return request({
    url: '/portalconsole/industryType',
    method: 'post',
    data: data
  })
}

// 修改行业类别
export function updateIndustryType(data) {
  return request({
    url: '/portalconsole/industryType',
    method: 'put',
    data: data
  })
}

// 删除行业类别
export function delIndustryType(industryTypeId) {
  return request({
    url: '/portalconsole/industryType/' + industryTypeId,
    method: 'delete'
  })
}
