<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="update">
        <keep-alive>
          <update :form='form' v-if='activeName == "update" && form'></update>
        </keep-alive>
      </el-tab-pane>
      <el-tab-pane label="需求评级" name="grade">
        <keep-alive>
          <grade :id='id' v-if='activeName == "grade" && id'></grade>
        </keep-alive>
      </el-tab-pane>
      <el-tab-pane label="需求跟进" name="follow">
        <keep-alive>
          <demand-follow :form="form" v-if='activeName == "follow" && form'/>
        </keep-alive>
      </el-tab-pane>
      <el-tab-pane label="需求平台" name="platform">
        <keep-alive>
          <platform :form='form' v-if='activeName == "platform" && form'></platform>
        </keep-alive>
      </el-tab-pane>
      <el-tab-pane label="聊天室记录" name="chatroom">
        <keep-alive>
          <chatroom :form='form' v-if='activeName == "chatroom" && form'></chatroom>
        </keep-alive>
      </el-tab-pane>
        <el-tab-pane label="关键词" name="keyword">
                <keywords :module="module" :productId="id" v-if='activeName == "keyword" && form'/>
                <div
                    style="
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    "
                >
                    <el-button type="primary" @click="affirmSync"
                        >确认同步</el-button
                    >
                </div>
        </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
// import { productkeyword } from "@/api/system/keyword";
//   import {
//     getRequire
//   } from "@/api/sso/service";
  import update from './update';
  import grade from './grade';
  import platform from './platform';
  import chatroom from './chatroom';
  import DemandFollow from "./follow";
// import keywords from "@/views/components/keywords/index";
  export default {
    components: {
      update,
      grade,
      platform,
      chatroom,
      DemandFollow,
    //   keywords
    },
    data() {
      return {
        id: '',
        activeName: 'update',
        form: null,
        module:null
      }
    },
    created() {
    //   this.id = this.$route.query.id;
    //   getRequire(this.id).then(response => {
    //     this.form = response.data;
    //     this.module = this.form.platform=='uuc'?'demand':'require'
    //   });
    },
    methods: {
      handleClick(tab, event) {
        this.activeName=tab.name
      },
        /* 确认同步 */
        affirmSync() {
            // productkeyword({
            //   productId:this.id,
            //   module:this.module
            // }).then((response) => {
            //      this.msgSuccess("同步成功");
            // }).catch((err) => {
            //      this.msgError("同步失败");

            // });
        },
    }
  }
</script>

<style>
  .d-container {
    padding: 0 10px 10px 10px;
  }
</style>
