import request from '@/utils/request'

// 查询应用商店订单列表
export function listAppStoreOrder(query) {
  return request({
    url: '/portalconsole/AppStoreOrder/list',
    method: 'get',
    params: query
  })
}

// 查询应用商店订单详细
export function getAppStoreOrder(appStoreOrderId) {
  return request({
    url: '/portalconsole/AppStoreOrder/' + appStoreOrderId,
    method: 'get'
  })
}

// 新增应用商店订单
export function addAppStoreOrder(data) {
  return request({
    url: '/portalconsole/AppStoreOrder',
    method: 'post',
    data: data
  })
}

// 修改应用商店订单
export function updateAppStoreOrder(data) {
  return request({
    url: '/portalconsole/AppStoreOrder',
    method: 'put',
    data: data
  })
}

// 删除应用商店订单
export function delAppStoreOrder(appStoreOrderId) {
  return request({
    url: '/portalconsole/AppStoreOrder/' + appStoreOrderId,
    method: 'delete'
  })
}
