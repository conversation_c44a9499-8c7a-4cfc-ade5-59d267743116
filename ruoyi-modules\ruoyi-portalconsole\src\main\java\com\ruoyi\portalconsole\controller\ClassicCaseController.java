package com.ruoyi.portalconsole.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.ClassicCase;
import com.ruoyi.portalconsole.service.IClassicCaseService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 典型案例Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/ClassicCase")
public class ClassicCaseController extends BaseController
{
    @Autowired
    private IClassicCaseService classicCaseService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询典型案例列表
     */
    @RequiresPermissions("portalconsole:ClassicCase:list")
    @GetMapping("/list")
    public TableDataInfo list(ClassicCase classicCase)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<ClassicCase> list = classicCaseService.selectClassicCaseList(classicCase);
        return getDataTable(list);
    }

    /**
     * 导出典型案例列表
     */
    @RequiresPermissions("portalconsole:ClassicCase:export")
    @Log(title = "典型案例", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ClassicCase classicCase)
    {
        List<ClassicCase> list = classicCaseService.selectClassicCaseList(classicCase);
        ExcelUtil<ClassicCase> util = new ExcelUtil<ClassicCase>(ClassicCase.class);
        util.exportExcel(response, list, "典型案例数据");
    }

    /**
     * 获取典型案例详细信息
     */
    @RequiresPermissions("portalconsole:ClassicCase:query")
    @GetMapping(value = "/{classicCaseId}")
    public AjaxResult getInfo(@PathVariable("classicCaseId") Long classicCaseId)
    {
        return success(classicCaseService.selectClassicCaseByClassicCaseId(classicCaseId));
    }

    /**
     * 新增典型案例
     */
    @RequiresPermissions("portalconsole:ClassicCase:add")
    @Log(title = "典型案例", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ClassicCase classicCase)
    {
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        classicCase.setUpdateBy(userNickName.getData());
        classicCase.setCreateBy(userNickName.getData());
        return toAjax(classicCaseService.insertClassicCase(classicCase));
    }

    /**
     * 新增典型案例
     */
    @Log(title = "典型案例", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    public AjaxResult addByImport(@RequestBody ClassicCase classicCase)
    {
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        classicCase.setUpdateBy(userNickName.getData());
        classicCase.setCreateBy(userNickName.getData());
        return toAjax(classicCaseService.insertClassicCase(classicCase));
    }

    /**
     * 修改典型案例
     */
    @RequiresPermissions("portalconsole:ClassicCase:edit")
    @Log(title = "典型案例", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ClassicCase classicCase)
    {
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        classicCase.setUpdateBy(userNickName.getData());
        return toAjax(classicCaseService.updateClassicCase(classicCase));
    }

    /**
     * 删除典型案例
     */
    @RequiresPermissions("portalconsole:ClassicCase:remove")
    @Log(title = "典型案例", businessType = BusinessType.DELETE)
	@DeleteMapping("/{classicCaseIds}")
    public AjaxResult remove(@PathVariable Long[] classicCaseIds)
    {
        return toAjax(classicCaseService.deleteClassicCaseByClassicCaseIds(classicCaseIds));
    }
}
