<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserFriendHistoryMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.SysUserFriendHistory">
    <!--@mbg.generated-->
    <!--@Table sys_user_friend_history-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="appy_user_name" jdbcType="VARCHAR" property="appyUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="apply_state" jdbcType="INTEGER" property="applyState" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_name, appy_user_name, create_time, apply_state, update_time, remark
  </sql>

  <select id="getApplyFriendList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sys_user_friend_history
    where user_name = #{userName}
    order by create_time desc
  </select>

  <select id="getDetail" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sys_user_friend_history
    where
        id = #{id}
    and user_name = #{userName}
  </select>

  <update id="updateState" parameterType="com.ruoyi.system.domain.SysUserFriendHistory">
    update sys_user_friend_history
    set
    apply_state = #{applyState,jdbcType=BOOLEAN},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
    and user_name = #{userName}
  </update>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.system.domain.SysUserFriendHistory" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_user_friend_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userName != null">
        user_name,
      </if>
      <if test="appyUserName != null">
        appy_user_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="applyState != null">
        apply_state,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="appyUserName != null">
        #{appyUserName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyState != null">
        #{applyState,jdbcType=BOOLEAN},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="reloadApply" parameterType="com.ruoyi.system.domain.SysUserFriendHistory">
    update sys_user_friend_history
    set
    create_time = #{createTime,jdbcType=BOOLEAN}
    where   user_name = #{userName}
    and appy_user_name = #{appyUserName}
    and apply_state = 0
  </update>

</mapper>