package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SettledFactory;
import com.ruoyi.system.domain.dto.SettledFactoryDetailDTO;
import com.ruoyi.system.domain.dto.SettledFactoryAddDTO;

/**
 * 入驻工厂Service接口
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface ISettledFactoryService
{
    /**
     * 查询入驻工厂
     *
     * @param id 入驻工厂主键
     * @return 入驻工厂
     */
    public SettledFactory selectSettledFactoryById(Long id);

    /**
     * 查询入驻工厂详情（包含关联数据）
     *
     * @param id 入驻工厂主键
     * @return 入驻工厂详情
     */
    public SettledFactoryDetailDTO selectSettledFactoryDetailById(Long id);

    /**
     * 查询入驻工厂列表
     *
     * @param settledFactory 入驻工厂
     * @return 入驻工厂集合
     */
    public List<SettledFactory> selectSettledFactoryList(SettledFactory settledFactory);

    /**
     * 根据产品ID查询关联的工厂列表
     *
     * @param productId 产品ID
     * @return 入驻工厂集合
     */
    public List<SettledFactory> selectSettledFactoryListByProductId(Long productId);

    /**
     * 新增入驻工厂（包含关联数据）
     *
     * @param factoryAdd 入驻工厂及关联数据
     * @return 结果
     */
    public int insertSettledFactoryWithRelated(SettledFactoryAddDTO factoryAdd);

    /**
     * 新增入驻工厂
     *
     * @param settledFactory 入驻工厂
     * @return 结果
     */
    public int insertSettledFactory(SettledFactory settledFactory);

    /**
     * 修改入驻工厂
     *
     * @param settledFactory 入驻工厂
     * @return 结果
     */
    public int updateSettledFactory(SettledFactory settledFactory);

    /**
     * 批量删除入驻工厂
     *
     * @param ids 需要删除的入驻工厂主键集合
     * @return 结果
     */
    public int deleteSettledFactoryByIds(Long[] ids);

    /**
     * 删除入驻工厂信息
     *
     * @param id 入驻工厂主键
     * @return 结果
     */
    public int deleteSettledFactoryById(Long id);
}
