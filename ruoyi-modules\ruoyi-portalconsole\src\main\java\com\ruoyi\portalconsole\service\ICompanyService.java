package com.ruoyi.portalconsole.service;

import java.util.List;
import com.ruoyi.portalconsole.domain.Company;
import com.ruoyi.portalconsole.domain.vo.CompanyVO;
import com.ruoyi.portalconsole.domain.vo.MemberTelephoneVO;

/**
 * 企业信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface ICompanyService 
{
    /**
     * 查询企业信息
     * 
     * @param companyId 企业信息主键
     * @return 企业信息
     */
    public Company selectCompanyByCompanyId(Long companyId);

    /**
     * 查询企业信息列表
     * 
     * @param company 企业信息
     * @return 企业信息集合
     */
    public List<Company> selectCompanyList(Company company);

    /**
     * 新增企业信息
     * 
     * @param company 企业信息
     * @return 结果
     */
    public int insertCompany(Company company);

    /**
     * 修改企业信息
     * 
     * @param company 企业信息
     * @return 结果
     */
    public int updateCompany(Company company);

    /**
     * 审核企业信息
     *
     * @param company 企业信息
     * @return 结果
     */
    public int auditCompany(Company company);

    /**
     * 审核企业信息
     *
     * @param company 企业信息
     * @return 结果
     */
    public int auditCompanyBatch(CompanyVO company);

    /**
     * 批量删除企业信息
     * 
     * @param companyIds 需要删除的企业信息主键集合
     * @return 结果
     */
    public int deleteCompanyByCompanyIds(Long[] companyIds);

    /**
     * 删除企业信息信息
     * 
     * @param companyId 企业信息主键
     * @return 结果
     */
    public int deleteCompanyByCompanyId(Long companyId);


}
