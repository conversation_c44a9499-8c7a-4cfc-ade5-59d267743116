package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.CompositeMaterial;

/**
 * 复材展厅Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface ICompositeMaterialService 
{
    /**
     * 查询复材展厅
     * 
     * @param id 复材展厅主键
     * @return 复材展厅
     */
    public CompositeMaterial selectCompositeMaterialById(Long id);

    /**
     * 查询复材展厅列表
     *
     * @param compositeMaterial 复材展厅
     * @return 复材展厅集合
     */
    public List<CompositeMaterial> selectCompositeMaterialList(CompositeMaterial compositeMaterial);

    /**
     * 根据产品ID查询关联的展厅列表
     *
     * @param productId 产品ID
     * @return 复材展厅集合
     */
    public List<CompositeMaterial> selectCompositeMaterialListByProductId(Long productId);

    /**
     * 新增复材展厅
     * 
     * @param compositeMaterial 复材展厅
     * @return 结果
     */
    public int insertCompositeMaterial(CompositeMaterial compositeMaterial);

    /**
     * 修改复材展厅
     * 
     * @param compositeMaterial 复材展厅
     * @return 结果
     */
    public int updateCompositeMaterial(CompositeMaterial compositeMaterial);

    /**
     * 批量删除复材展厅
     * 
     * @param ids 需要删除的复材展厅主键集合
     * @return 结果
     */
    public int deleteCompositeMaterialByIds(Long[] ids);

    /**
     * 删除复材展厅信息
     * 
     * @param id 复材展厅主键
     * @return 结果
     */
    public int deleteCompositeMaterialById(Long id);
}
