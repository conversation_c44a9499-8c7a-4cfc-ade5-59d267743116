package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.FactoryQualification;
import com.ruoyi.system.service.IFactoryQualificationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 工厂资质证件Controller
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@RestController
@RequestMapping("/qualification")
public class FactoryQualificationController extends BaseController
{
    @Autowired
    private IFactoryQualificationService factoryQualificationService;

    /**
     * 查询工厂资质证件列表
     */
    @RequiresPermissions("system:qualification:list")
    @GetMapping("/list")
    public TableDataInfo list(FactoryQualification factoryQualification)
    {
        startPage();
        List<FactoryQualification> list = factoryQualificationService.selectFactoryQualificationList(factoryQualification);
        return getDataTable(list);
    }

    /**
     * 导出工厂资质证件列表
     */
    @RequiresPermissions("system:qualification:export")
    @Log(title = "工厂资质证件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FactoryQualification factoryQualification)
    {
        List<FactoryQualification> list = factoryQualificationService.selectFactoryQualificationList(factoryQualification);
        ExcelUtil<FactoryQualification> util = new ExcelUtil<FactoryQualification>(FactoryQualification.class);
        util.exportExcel(response, list, "工厂资质证件数据");
    }

    /**
     * 获取工厂资质证件详细信息
     */
    @RequiresPermissions("system:qualification:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(factoryQualificationService.selectFactoryQualificationById(id));
    }

    /**
     * 新增工厂资质证件
     */
    @RequiresPermissions("system:qualification:add")
    @Log(title = "工厂资质证件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FactoryQualification factoryQualification)
    {
        return toAjax(factoryQualificationService.insertFactoryQualification(factoryQualification));
    }

    /**
     * 修改工厂资质证件
     */
    @RequiresPermissions("system:qualification:edit")
    @Log(title = "工厂资质证件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FactoryQualification factoryQualification)
    {
        return toAjax(factoryQualificationService.updateFactoryQualification(factoryQualification));
    }

    /**
     * 删除工厂资质证件
     */
    @RequiresPermissions("system:qualification:remove")
    @Log(title = "工厂资质证件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(factoryQualificationService.deleteFactoryQualificationByIds(ids));
    }
}
