package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.TalentInfoApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.TalentInfoMapper;
import com.ruoyi.system.service.ITalentInfoService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.domain.TalentInfo;

/**
 * 人才信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Service
public class TalentInfoServiceImpl implements ITalentInfoService
{
    @Autowired
    private TalentInfoMapper talentInfoMapper;

    /**
     * 查询人才信息
     *
     * @param id 人才信息主键
     * @return 人才信息
     */
    @Override
    public TalentInfo selectTalentInfoById(Long id)
    {
        return talentInfoMapper.selectTalentInfoById(id);
    }

    /**
     * 根据用户ID查询人才信息
     *
     * @param userId 用户ID
     * @return 人才信息
     */
    @Override
    public TalentInfoApi selectTalentInfoByUserId(Long userId)
    {
        return talentInfoMapper.selectTalentInfoByUserId(userId);
    }

    /**
     * 查询人才信息列表
     *
     * @param talentInfo 人才信息
     * @return 人才信息
     */
    @Override
    public List<TalentInfo> selectTalentInfoList(TalentInfo talentInfo)
    {
        return talentInfoMapper.selectTalentInfoList(talentInfo);
    }

    /**
     * 新增人才信息
     *
     * @param talentInfo 人才信息
     * @return 结果
     */
    @Override
    public int insertTalentInfo(TalentInfo talentInfo)
    {
        talentInfo.setCreateTime(DateUtils.getNowDate());
        // 设置默认入驻状态为待审核
        talentInfo.setSettledStatus("0");
        // 设置当前登录用户ID
        talentInfo.setUserId(SecurityUtils.getUserId());
        return talentInfoMapper.insertTalentInfo(talentInfo);
    }

    /**
     * 修改人才信息
     *
     * @param talentInfo 人才信息
     * @return 结果
     */
    @Override
    public int updateTalentInfo(TalentInfo talentInfo)
    {
        talentInfo.setUpdateTime(DateUtils.getNowDate());
        return talentInfoMapper.updateTalentInfo(talentInfo);
    }

    /**
     * 批量删除人才信息
     *
     * @param ids 需要删除的人才信息主键
     * @return 结果
     */
    @Override
    public int deleteTalentInfoByIds(Long[] ids)
    {
        return talentInfoMapper.deleteTalentInfoByIds(ids);
    }

    /**
     * 删除人才信息信息
     *
     * @param id 人才信息主键
     * @return 结果
     */
    @Override
    public int deleteTalentInfoById(Long id)
    {
        return talentInfoMapper.deleteTalentInfoById(id);
    }
}
