package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CompositeMaterialMapper;
import com.ruoyi.system.domain.CompositeMaterial;
import com.ruoyi.system.service.ICompositeMaterialService;

/**
 * 复材展厅Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class CompositeMaterialServiceImpl implements ICompositeMaterialService 
{
    @Autowired
    private CompositeMaterialMapper compositeMaterialMapper;

    /**
     * 查询复材展厅
     * 
     * @param id 复材展厅主键
     * @return 复材展厅
     */
    @Override
    public CompositeMaterial selectCompositeMaterialById(Long id)
    {
        return compositeMaterialMapper.selectCompositeMaterialById(id);
    }

    /**
     * 查询复材展厅列表
     *
     * @param compositeMaterial 复材展厅
     * @return 复材展厅
     */
    @Override
    public List<CompositeMaterial> selectCompositeMaterialList(CompositeMaterial compositeMaterial)
    {
        return compositeMaterialMapper.selectCompositeMaterialList(compositeMaterial);
    }

    /**
     * 根据产品ID查询关联的展厅列表
     *
     * @param productId 产品ID
     * @return 复材展厅集合
     */
    @Override
    public List<CompositeMaterial> selectCompositeMaterialListByProductId(Long productId)
    {
        return compositeMaterialMapper.selectCompositeMaterialListByProductId(productId);
    }

    /**
     * 新增复材展厅
     * 
     * @param compositeMaterial 复材展厅
     * @return 结果
     */
    @Override
    public int insertCompositeMaterial(CompositeMaterial compositeMaterial)
    {
        compositeMaterial.setCreateTime(DateUtils.getNowDate());
        return compositeMaterialMapper.insertCompositeMaterial(compositeMaterial);
    }

    /**
     * 修改复材展厅
     * 
     * @param compositeMaterial 复材展厅
     * @return 结果
     */
    @Override
    public int updateCompositeMaterial(CompositeMaterial compositeMaterial)
    {
        compositeMaterial.setUpdateTime(DateUtils.getNowDate());
        return compositeMaterialMapper.updateCompositeMaterial(compositeMaterial);
    }

    /**
     * 批量删除复材展厅
     * 
     * @param ids 需要删除的复材展厅主键
     * @return 结果
     */
    @Override
    public int deleteCompositeMaterialByIds(Long[] ids)
    {
        return compositeMaterialMapper.deleteCompositeMaterialByIds(ids);
    }

    /**
     * 删除复材展厅信息
     * 
     * @param id 复材展厅主键
     * @return 结果
     */
    @Override
    public int deleteCompositeMaterialById(Long id)
    {
        return compositeMaterialMapper.deleteCompositeMaterialById(id);
    }
}
