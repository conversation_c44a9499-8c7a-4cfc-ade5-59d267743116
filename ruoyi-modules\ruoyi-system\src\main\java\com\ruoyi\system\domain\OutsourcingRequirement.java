package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 工序外协需求对象 outsourcing_requirement
 * 
 * <AUTHOR>
 * @date 2025-03-08
 */
public class OutsourcingRequirement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 需求ID */
    private Long id;

    /** 外协工序名称 */
    @Excel(name = "外协工序名称")
    private String processName;

    /** 加工数量 */
    @Excel(name = "加工数量")
    private Long processingQuantity;

    /** 外协加工内容 */
    @Excel(name = "外协加工内容")
    private String outsourcingContent;

    /** 要求完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "要求完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date requiredCompletionTime;

    /** 工程号 */
    @Excel(name = "工程号")
    private String projectNumber;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setProcessName(String processName) 
    {
        this.processName = processName;
    }

    public String getProcessName() 
    {
        return processName;
    }
    public void setProcessingQuantity(Long processingQuantity) 
    {
        this.processingQuantity = processingQuantity;
    }

    public Long getProcessingQuantity() 
    {
        return processingQuantity;
    }
    public void setOutsourcingContent(String outsourcingContent) 
    {
        this.outsourcingContent = outsourcingContent;
    }

    public String getOutsourcingContent() 
    {
        return outsourcingContent;
    }
    public void setRequiredCompletionTime(Date requiredCompletionTime) 
    {
        this.requiredCompletionTime = requiredCompletionTime;
    }

    public Date getRequiredCompletionTime() 
    {
        return requiredCompletionTime;
    }
    public void setProjectNumber(String projectNumber) 
    {
        this.projectNumber = projectNumber;
    }

    public String getProjectNumber() 
    {
        return projectNumber;
    }
    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("processName", getProcessName())
            .append("processingQuantity", getProcessingQuantity())
            .append("outsourcingContent", getOutsourcingContent())
            .append("requiredCompletionTime", getRequiredCompletionTime())
            .append("projectNumber", getProjectNumber())
            .append("remarks", getRemarks())
            .append("companyName", getCompanyName())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
