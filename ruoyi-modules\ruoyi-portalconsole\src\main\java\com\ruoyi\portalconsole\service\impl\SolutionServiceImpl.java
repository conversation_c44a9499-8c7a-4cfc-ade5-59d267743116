package com.ruoyi.portalconsole.service.impl;

import java.util.List;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalconsole.domain.SolutionAdvantage;
import com.ruoyi.portalconsole.domain.SolutionCase;
import com.ruoyi.portalconsole.domain.SolutionPain;
import com.ruoyi.portalconsole.domain.vo.SolutionVO;
import com.ruoyi.portalconsole.mapper.SolutionAdvantageMapper;
import com.ruoyi.portalconsole.mapper.SolutionCaseMapper;
import com.ruoyi.portalconsole.mapper.SolutionPainMapper;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.SolutionMapper;
import com.ruoyi.portalconsole.domain.Solution;
import com.ruoyi.portalconsole.service.ISolutionService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 解决方案Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class SolutionServiceImpl implements ISolutionService 
{
    @Autowired
    private SolutionMapper solutionMapper;

    @Autowired
    private SolutionPainMapper solutionPainMapper;

    @Autowired
    private SolutionAdvantageMapper solutionAdvantageMapper;

    @Autowired
    private SolutionCaseMapper solutionCaseMapper;

    @Autowired
    private RemoteUserService remoteUserService;


    /**
     * 查询解决方案
     * 
     * @param solutionId 解决方案主键
     * @return 解决方案
     */
    @Override
    public SolutionVO selectSolutionBySolutionId(Long solutionId)
    {
        return solutionMapper.selectSolutionBySolutionId(solutionId);
    }

    @Override
    public SolutionVO selectSolutionDetailBySolutionId(Long solutionId) {
        SolutionVO solutionVO = selectSolutionBySolutionId(solutionId);

        List<SolutionPain> solutionPains = solutionPainMapper.selectSolutionPainList(new SolutionPain(solutionId));
        List<SolutionAdvantage> solutionAdvantages = solutionAdvantageMapper.selectSolutionAdvantageList(new SolutionAdvantage(solutionId));
        List<SolutionCase> solutionCases = solutionCaseMapper.selectSolutionCaseList(new SolutionCase(solutionId));
        solutionVO.setList(solutionAdvantages,solutionPains,solutionCases);

        return solutionVO;
    }

    /**
     * 查询解决方案列表
     * 
     * @param solution 解决方案
     * @return 解决方案
     */
    @Override
    public List<SolutionVO> selectSolutionList(Solution solution)
    {
        return solutionMapper.selectSolutionList(solution);
    }

    /**
     * 新增解决方案
     * 
     * @param solutionVO 解决方案
     * @return 结果
     */
    @Override
    @Transactional
    public int insertSolution(SolutionVO solutionVO)
    {
        Solution solution = new Solution();
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        BeanUtils.copyProperties(solutionVO, solution);
        solution.setUpdateBy(userNickName.getData());
        solution.setCreateBy(userNickName.getData());

        // 插入新的数据
        int i = solutionMapper.insertSolution(solution);
        solutionVO.setSolutionId(solution.getSolutionId());
        addSolutionChildren(true, userNickName.getData(), solutionVO);
        return i;
    }

    /**
     * 修改解决方案
     * 
     * @param solution 解决方案
     * @return 结果
     */
    @Override
    public int updateSolution(Solution solution)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        solution.setUpdateBy(userNickName.getData());
        solution.setUpdateTime(DateUtils.getNowDate());
        return solutionMapper.updateSolution(solution);
    }

    @Override
    @Transactional
    public int updateSolutionDetail(SolutionVO solutionVO) {
        Solution solution = new Solution();
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        BeanUtils.copyProperties(solutionVO, solution);
        solution.setUpdateBy(userNickName.getData());
        addSolutionChildren(false,userNickName.getData(),solutionVO);

        // 更新主表
        return solutionMapper.updateSolution(solution);
    }

    /**
     * 批量删除解决方案
     * 
     * @param solutionIds 需要删除的解决方案主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteSolutionBySolutionIds(Long[] solutionIds)
    {

        solutionAdvantageMapper.deleteSolutionAdvantageBySolutionIds(solutionIds);
        solutionPainMapper.deleteSolutionPainBySolutionIds(solutionIds);
        solutionCaseMapper.deleteSolutionCaseBySolutionIds(solutionIds);

        return solutionMapper.deleteSolutionBySolutionIds(solutionIds);

    }

    /**
     * 删除解决方案信息
     * 
     * @param solutionId 解决方案主键
     * @return 结果
     */
    @Override
    public int deleteSolutionBySolutionId(Long solutionId)
    {
        return solutionMapper.deleteSolutionBySolutionId(solutionId);
    }


    // 处理解决方案子表数据
    private void addSolutionChildren(Boolean isAdd,String nickName, SolutionVO solutionVO){
        // 删除子表数据
        if (!isAdd){
            solutionAdvantageMapper.deleteSolutionAdvantageBySolutionId(solutionVO.getSolutionId());
            solutionPainMapper.deleteSolutionPainBySolutionId(solutionVO.getSolutionId());
            solutionCaseMapper.deleteSolutionCaseBySolutionId(solutionVO.getSolutionId());
        }

        // 插入子表数据
        if (solutionVO.getSolutionAdvantageList() !=null && !solutionVO.getSolutionAdvantageList().isEmpty()){
            solutionVO.getSolutionAdvantageList().forEach((solutionAdvantage) -> {
                solutionAdvantage.setUpdateBy(nickName);
                solutionAdvantage.setCreateBy(nickName);
                solutionAdvantage.setSolutionId(solutionVO.getSolutionId());
            });
            solutionAdvantageMapper.insertSolutionAdvantageList(solutionVO.getSolutionAdvantageList());
        }

        if(solutionVO.getSolutionCaseList()!= null && !solutionVO.getSolutionCaseList().isEmpty()){
            solutionVO.getSolutionCaseList().forEach((solutionCase) -> {
                solutionCase.setUpdateBy(nickName);
                solutionCase.setCreateBy(nickName);
                solutionCase.setSolutionId(solutionVO.getSolutionId());
            });
            solutionCaseMapper.insertSolutionCaseList(solutionVO.getSolutionCaseList());
        }

        if (solutionVO.getSolutionPainList()!= null && !solutionVO.getSolutionPainList().isEmpty()){
            solutionVO.getSolutionPainList().forEach((solutionPain) -> {
                solutionPain.setUpdateBy(nickName);
                solutionPain.setCreateBy(nickName);
                solutionPain.setSolutionId(solutionVO.getSolutionId());
            });
            solutionPainMapper.insertSolutionPainList(solutionVO.getSolutionPainList());
        }

    }


}
