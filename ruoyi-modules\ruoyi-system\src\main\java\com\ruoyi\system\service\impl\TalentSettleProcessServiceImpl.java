package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.TalentSettleProcessMapper;
import com.ruoyi.system.domain.TalentSettleProcess;
import com.ruoyi.system.service.ITalentSettleProcessService;

/**
 * 人才入驻流程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class TalentSettleProcessServiceImpl implements ITalentSettleProcessService 
{
    @Autowired
    private TalentSettleProcessMapper talentSettleProcessMapper;

    /**
     * 查询人才入驻流程
     * 
     * @param id 人才入驻流程主键
     * @return 人才入驻流程
     */
    @Override
    public TalentSettleProcess selectTalentSettleProcessById(Long id)
    {
        return talentSettleProcessMapper.selectTalentSettleProcessById(id);
    }

    /**
     * 查询人才入驻流程列表
     * 
     * @param talentSettleProcess 人才入驻流程
     * @return 人才入驻流程
     */
    @Override
    public List<TalentSettleProcess> selectTalentSettleProcessList(TalentSettleProcess talentSettleProcess)
    {
        return talentSettleProcessMapper.selectTalentSettleProcessList(talentSettleProcess);
    }

    /**
     * 新增人才入驻流程
     * 
     * @param talentSettleProcess 人才入驻流程
     * @return 结果
     */
    @Override
    public int insertTalentSettleProcess(TalentSettleProcess talentSettleProcess)
    {
        talentSettleProcess.setCreateTime(DateUtils.getNowDate());
        return talentSettleProcessMapper.insertTalentSettleProcess(talentSettleProcess);
    }

    /**
     * 修改人才入驻流程
     * 
     * @param talentSettleProcess 人才入驻流程
     * @return 结果
     */
    @Override
    public int updateTalentSettleProcess(TalentSettleProcess talentSettleProcess)
    {
        talentSettleProcess.setUpdateTime(DateUtils.getNowDate());
        return talentSettleProcessMapper.updateTalentSettleProcess(talentSettleProcess);
    }

    /**
     * 批量删除人才入驻流程
     * 
     * @param ids 需要删除的人才入驻流程主键
     * @return 结果
     */
    @Override
    public int deleteTalentSettleProcessByIds(Long[] ids)
    {
        return talentSettleProcessMapper.deleteTalentSettleProcessByIds(ids);
    }

    /**
     * 删除人才入驻流程信息
     * 
     * @param id 人才入驻流程主键
     * @return 结果
     */
    @Override
    public int deleteTalentSettleProcessById(Long id)
    {
        return talentSettleProcessMapper.deleteTalentSettleProcessById(id);
    }
}
