<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.ExpertDatabaseMapper">

    <resultMap type="com.ruoyi.portalweb.vo.ExpertDatabaseVO" id="ExpertDatabaseResult">
        <result property="expertDatabaseId" column="expert_database_id"/>
        <result property="expertDatabaseName" column="expert_database_name"/>
        <result property="expertDatabaseExpert" column="expert_database_expert"/>
        <result property="expertDatabaseWorkunit" column="expert_database_workunit"/>
        <result property="expertDatabasePost" column="expert_database_post"/>
        <result property="expertDatabaseContact" column="expert_database_contact"/>
        <result property="expertDatabaseEmail" column="expert_database_email"/>
        <result property="expertDatabasePhone" column="expert_database_phone"/>
        <result property="expertDatabaseWechat" column="expert_database_wechat"/>
        <result property="expertDatabaseDirection" column="expert_database_direction"/>
        <result property="expertDatabaseAchievement" column="expert_database_achievement"/>
        <result property="expertDatabaseTag" column="expert_database_tag"/>
        <result property="expertDatabaseTechnology" column="expert_database_technology"/>
        <result property="expertDatabaseIntroduction" column="expert_database_introduction"/>
        <result property="expertDatabaseImghead" column="expert_database_imghead"/>
        <result property="expertDatabaseIntroduce" column="expert_database_introduce"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectExpertDatabaseVo">
        select expert_database_id, expert_database_name, expert_database_expert, expert_database_workunit,
        expert_database_post, expert_database_contact, expert_database_email, expert_database_phone,
        expert_database_wechat, expert_database_direction, expert_database_achievement, expert_database_tag,
        expert_database_technology, expert_database_introduction, expert_database_imghead, expert_database_introduce,
        del_flag, create_by, create_time, update_by, update_time, remark from expert_database
    </sql>

    <select id="selectExpertDatabaseList" parameterType="ExpertDatabaseVO" resultMap="ExpertDatabaseResult">
        <include refid="selectExpertDatabaseVo"/>
        <where>
            <if test="expertDatabaseName != null  and expertDatabaseName != ''">and expert_database_name like
                concat('%', #{expertDatabaseName}, '%')
            </if>
            <if test="expertDatabaseExpert != null ">and expert_database_expert = #{expertDatabaseExpert}</if>
            <if test="expertDatabaseWorkunit != null  and expertDatabaseWorkunit != ''">and expert_database_workunit =
                #{expertDatabaseWorkunit}
            </if>
            <if test="expertDatabasePost != null  and expertDatabasePost != ''">and expert_database_post =
                #{expertDatabasePost}
            </if>
            <if test="expertDatabaseContact != null  and expertDatabaseContact != ''">and expert_database_contact =
                #{expertDatabaseContact}
            </if>
            <if test="expertDatabaseEmail != null  and expertDatabaseEmail != ''">and expert_database_email =
                #{expertDatabaseEmail}
            </if>
            <if test="expertDatabasePhone != null  and expertDatabasePhone != ''">and expert_database_phone =
                #{expertDatabasePhone}
            </if>
            <if test="expertDatabaseWechat != null  and expertDatabaseWechat != ''">and expert_database_wechat =
                #{expertDatabaseWechat}
            </if>
            <if test="expertDatabaseDirection != null  and expertDatabaseDirection != ''">and expert_database_direction
                like concat('%',#{expertDatabaseDirection},'%')
            </if>
            <if test="expertDatabaseAchievement != null  and expertDatabaseAchievement != ''">and
                expert_database_achievement = #{expertDatabaseAchievement}
            </if>
            <if test="expertDatabaseTag != null  and expertDatabaseTag != ''">and expert_database_tag =
                #{expertDatabaseTag}
            </if>
            <if test="expertDatabaseTechnology != null  and expertDatabaseTechnology != ''">and
                expert_database_technology = #{expertDatabaseTechnology}
            </if>
            <if test="expertDatabaseIntroduction != null  and expertDatabaseIntroduction != ''">and
                expert_database_introduction = #{expertDatabaseIntroduction}
            </if>
            <if test="expertDatabaseImghead != null  and expertDatabaseImghead != ''">and expert_database_imghead =
                #{expertDatabaseImghead}
            </if>
            <if test="expertDatabaseIntroduce != null  and expertDatabaseIntroduce != ''">and expert_database_introduce
                = #{expertDatabaseIntroduce}
            </if>
            <if test="keyword != null  and keyword != ''">and ( expert_database_name like
                concat('%', #{keyword}, '%') or expert_database_direction like concat('%',#{keyword},'%') )
            </if>
        </where>
    </select>

    <select id="selectExpertDatabaseByExpertDatabaseId" parameterType="Long" resultMap="ExpertDatabaseResult">
        <include refid="selectExpertDatabaseVo"/>
        where expert_database_id = #{expertDatabaseId}
    </select>

    <insert id="insertExpertDatabase" parameterType="ExpertDatabase" useGeneratedKeys="true"
            keyProperty="expertDatabaseId">
        insert into expert_database
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="expertDatabaseName != null">expert_database_name,</if>
            <if test="expertDatabaseExpert != null">expert_database_expert,</if>
            <if test="expertDatabaseWorkunit != null">expert_database_workunit,</if>
            <if test="expertDatabasePost != null">expert_database_post,</if>
            <if test="expertDatabaseContact != null">expert_database_contact,</if>
            <if test="expertDatabaseEmail != null">expert_database_email,</if>
            <if test="expertDatabasePhone != null">expert_database_phone,</if>
            <if test="expertDatabaseWechat != null">expert_database_wechat,</if>
            <if test="expertDatabaseDirection != null">expert_database_direction,</if>
            <if test="expertDatabaseAchievement != null">expert_database_achievement,</if>
            <if test="expertDatabaseTag != null">expert_database_tag,</if>
            <if test="expertDatabaseTechnology != null">expert_database_technology,</if>
            <if test="expertDatabaseIntroduction != null">expert_database_introduction,</if>
            <if test="expertDatabaseImghead != null">expert_database_imghead,</if>
            <if test="expertDatabaseIntroduce != null">expert_database_introduce,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="expertDatabaseName != null">#{expertDatabaseName},</if>
            <if test="expertDatabaseExpert != null">#{expertDatabaseExpert},</if>
            <if test="expertDatabaseWorkunit != null">#{expertDatabaseWorkunit},</if>
            <if test="expertDatabasePost != null">#{expertDatabasePost},</if>
            <if test="expertDatabaseContact != null">#{expertDatabaseContact},</if>
            <if test="expertDatabaseEmail != null">#{expertDatabaseEmail},</if>
            <if test="expertDatabasePhone != null">#{expertDatabasePhone},</if>
            <if test="expertDatabaseWechat != null">#{expertDatabaseWechat},</if>
            <if test="expertDatabaseDirection != null">#{expertDatabaseDirection},</if>
            <if test="expertDatabaseAchievement != null">#{expertDatabaseAchievement},</if>
            <if test="expertDatabaseTag != null">#{expertDatabaseTag},</if>
            <if test="expertDatabaseTechnology != null">#{expertDatabaseTechnology},</if>
            <if test="expertDatabaseIntroduction != null">#{expertDatabaseIntroduction},</if>
            <if test="expertDatabaseImghead != null">#{expertDatabaseImghead},</if>
            <if test="expertDatabaseIntroduce != null">#{expertDatabaseIntroduce},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateExpertDatabase" parameterType="ExpertDatabase">
        update expert_database
        <trim prefix="SET" suffixOverrides=",">
            <if test="expertDatabaseName != null">expert_database_name = #{expertDatabaseName},</if>
            <if test="expertDatabaseExpert != null">expert_database_expert = #{expertDatabaseExpert},</if>
            <if test="expertDatabaseWorkunit != null">expert_database_workunit = #{expertDatabaseWorkunit},</if>
            <if test="expertDatabasePost != null">expert_database_post = #{expertDatabasePost},</if>
            <if test="expertDatabaseContact != null">expert_database_contact = #{expertDatabaseContact},</if>
            <if test="expertDatabaseEmail != null">expert_database_email = #{expertDatabaseEmail},</if>
            <if test="expertDatabasePhone != null">expert_database_phone = #{expertDatabasePhone},</if>
            <if test="expertDatabaseWechat != null">expert_database_wechat = #{expertDatabaseWechat},</if>
            <if test="expertDatabaseDirection != null">expert_database_direction = #{expertDatabaseDirection},</if>
            <if test="expertDatabaseAchievement != null">expert_database_achievement = #{expertDatabaseAchievement},
            </if>
            <if test="expertDatabaseTag != null">expert_database_tag = #{expertDatabaseTag},</if>
            <if test="expertDatabaseTechnology != null">expert_database_technology = #{expertDatabaseTechnology},</if>
            <if test="expertDatabaseIntroduction != null">expert_database_introduction =
                #{expertDatabaseIntroduction},
            </if>
            <if test="expertDatabaseImghead != null">expert_database_imghead = #{expertDatabaseImghead},</if>
            <if test="expertDatabaseIntroduce != null">expert_database_introduce = #{expertDatabaseIntroduce},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where expert_database_id = #{expertDatabaseId}
    </update>

    <delete id="deleteExpertDatabaseByExpertDatabaseId" parameterType="Long">
        delete from expert_database where expert_database_id = #{expertDatabaseId}
    </delete>

    <delete id="deleteExpertDatabaseByExpertDatabaseIds" parameterType="String">
        delete from expert_database where expert_database_id in
        <foreach item="expertDatabaseId" collection="array" open="(" separator="," close=")">
            #{expertDatabaseId}
        </foreach>
    </delete>
</mapper>