package com.ruoyi.portalweb.service;


import com.ruoyi.portalweb.api.domain.Demand;
import com.ruoyi.portalweb.vo.DemandVO;

import java.util.List;

/**
 * 服务需求(NEW)Service接口
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface IDemandService {
    /**
     * 查询服务需求(NEW)
     *
     * @param id 服务需求(NEW)主键
     * @return 服务需求(NEW)
     */
    public DemandVO selectDemandById(Long id);
    public DemandVO detailDesk(Long id);

    /**
     * 查询服务需求(NEW)列表
     *
     * @param demand 服务需求(NEW)
     * @return 服务需求(NEW)集合
     */
    public List<DemandVO> selectDemandList(DemandVO demand);
    public List<DemandVO> selectDemandListByDemandIds(List<Long> demandIds);
    public List<DemandVO> listDeskCompanyRelated(Long companyRelatedId);

    /**
     * 新增服务需求(NEW)
     *
     * @param demand 服务需求(NEW)
     * @return 结果
     */
    public int insertDemand(DemandVO demand);

    /**
     * 修改服务需求(NEW)
     *
     * @param demand 服务需求(NEW)
     * @return 结果
     */
    public int updateDemand(DemandVO demand);

    /**
     * 批量删除服务需求(NEW)
     *
     * @param ids 需要删除的服务需求(NEW)主键集合
     * @return 结果
     */
    public int deleteDemandByIds(Long[] ids);

    /**
     * 删除服务需求(NEW)信息
     *
     * @param id 服务需求(NEW)主键
     * @return 结果
     */
    public int deleteDemandById(Long id);
}
