package com.ruoyi.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 车间信息对象 workshop_info
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public class WorkshopInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 车间ID */
    private Long id;

    /** 车间名称 */
    @Excel(name = "车间名称")
    private String name;

    /** 所属单位 */
    @Excel(name = "所属单位")
    private String company;

    /** 车间地址 */
    @Excel(name = "车间地址")
    private String address;

    /** 车间面积 */
    @Excel(name = "车间面积")
    private String area;

    /** 参考价格（元/天） */
    @Excel(name = "参考价格", readConverterExp = "元=/天")
    private BigDecimal price;

    /** 车间概况 */
    @Excel(name = "车间概况")
    private String description;

    /** 设备资源 */
    @Excel(name = "设备资源")
    private String resources;

    /** 生产能力 */
    @Excel(name = "生产能力")
    private String capability;

    /** 注意事项 */
    @Excel(name = "注意事项")
    private String notes;

    /** 车间图片 */
    @Excel(name = "车间图片")
    private String images;

//    审核状态 0.未审核 1审核通过，2，审核未通过
    private Integer checkStatus;

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    /** 车间类型（生产车间/中试车间） */
    @Excel(name = "车间类型", readConverterExp = "生=产车间/中试车间")
    private String type;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setCompany(String company) 
    {
        this.company = company;
    }

    public String getCompany() 
    {
        return company;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setArea(String area) 
    {
        this.area = area;
    }

    public String getArea() 
    {
        return area;
    }
    public void setPrice(BigDecimal price) 
    {
        this.price = price;
    }

    public BigDecimal getPrice() 
    {
        return price;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setResources(String resources) 
    {
        this.resources = resources;
    }

    public String getResources() 
    {
        return resources;
    }
    public void setCapability(String capability) 
    {
        this.capability = capability;
    }

    public String getCapability() 
    {
        return capability;
    }
    public void setNotes(String notes) 
    {
        this.notes = notes;
    }

    public String getNotes() 
    {
        return notes;
    }
    public void setImages(String images) 
    {
        this.images = images;
    }

    public String getImages() 
    {
        return images;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("company", getCompany())
            .append("address", getAddress())
            .append("area", getArea())
            .append("price", getPrice())
            .append("description", getDescription())
            .append("resources", getResources())
            .append("capability", getCapability())
            .append("notes", getNotes())
            .append("images", getImages())
            .append("type", getType())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
