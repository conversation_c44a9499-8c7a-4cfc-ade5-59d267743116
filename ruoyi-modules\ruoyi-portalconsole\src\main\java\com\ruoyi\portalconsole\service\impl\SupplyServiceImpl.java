package com.ruoyi.portalconsole.service.impl;

import java.util.List;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalconsole.domain.Demand;
import com.ruoyi.portalconsole.domain.Message;
import com.ruoyi.portalconsole.enums.MessageStatus;
import com.ruoyi.portalconsole.enums.Title;
import com.ruoyi.portalconsole.service.IMessageService;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.portalconsole.domain.Supply;
import com.ruoyi.portalconsole.domain.vo.SupplyVO;
import com.ruoyi.portalconsole.mapper.SupplyMapper;
import com.ruoyi.portalconsole.service.ISupplyService;

/**
 * 服务供给Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class SupplyServiceImpl implements ISupplyService 
{
    @Autowired
    private SupplyMapper supplyMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private IMessageService messageService;

    /**
     * 查询服务供给
     * 
     * @param id 服务供给主键
     * @return 服务供给
     */
    @Override
    public SupplyVO selectSupplyById(Long id)
    {
        return supplyMapper.selectSupplyById(id);
    }

    /**
     * 查询服务供给列表
     * 
     * @param supply 服务供给
     * @return 服务供给
     */
    @Override
    public List<SupplyVO> selectSupplyList(Supply supply)
    {
        return supplyMapper.selectSupplyList(supply);
    }

    /**
     * 新增服务供给
     * 
     * @param supply 服务供给
     * @return 结果
     */
    @Override
    public int insertSupply(Supply supply)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        supply.setCreateBy(userNickName.getData());
        supply.setUpdateBy(userNickName.getData());
        supply.setPublisher(userNickName.getData());
        return supplyMapper.insertSupply(supply);
    }

    /**
     * 修改服务供给
     * 
     * @param supply 服务供给
     * @return 结果
     */
    @Override
    public int updateSupply(Supply supply)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        supply.setUpdateBy(userNickName.getData());
        return supplyMapper.updateSupply(supply);
    }

    /**
     * 审核服务供给
     *
     * @param supply 服务供给
     * @return 结果
     */
    @Override
    public int auditSupply(Supply supply)
    {
        // 查询供给信息
        SupplyVO supplyVO = selectSupplyById(supply.getId());

        // 修改审核状态
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        Supply audit = new Supply();
        audit.setUpdateBy(userNickName.getData());
        audit.setAuditStatus(supply.getAuditStatus());
        audit.setId(supply.getId());
        int i = updateSupply(audit);

        // 通知用户消息
        Message message = new Message().initData(Title.SUPPLY.getName(),"您提交的"+ Title.SUPPLY.getName() +"已审核", MessageStatus.UNREAD.getCode(),supplyVO.getMemberId());
        messageService.insertMessageByAsync(message);
        return i;
    }

    /**
     * 批量删除服务供给
     * 
     * @param ids 需要删除的服务供给主键
     * @return 结果
     */
    @Override
    public int deleteSupplyByIds(Long[] ids)
    {
        return supplyMapper.deleteSupplyByIds(ids);
    }

    /**
     * 删除服务供给信息
     * 
     * @param id 服务供给主键
     * @return 结果
     */
    @Override
    public int deleteSupplyById(Long id)
    {
        return supplyMapper.deleteSupplyById(id);
    }
}
