package com.ruoyi.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.web.domain.AjaxResult;
//import com.ruoyi.im.api.RemoteImUserService;
import com.ruoyi.system.api.domain.Member;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ISysUserChatService;
import com.ruoyi.system.utils.SystemConstants;
import com.ruoyi.system.utils.WeiXinUtils;
import com.ruoyi.system.utils.RongTool;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


@Service
@RefreshScope
public class SysUserChatServiceImpl implements ISysUserChatService {
    private static final Logger log = LoggerFactory.getLogger(SysUserChatServiceImpl.class);

    @Autowired
    private MemberMapper memberMapper;

    @Autowired
    private SysUserFriendMapper sysUserFriendMapper;

    @Autowired
    private UserChatGroupMapper userChatGroupMapper;

    @Autowired
    private UserChatMessageMapper userChatMessageMapper;

    @Autowired
    private UserRongYunStateMapper userRongYunStateMapper;
    @Autowired
    private UserChatGroupMemberMapper userChatGroupMemberMapper;
//    @Autowired
//    private RemoteImUserService remoteImUserService;

    /**
     * 微信的appid和秘钥
     */
    @Value("${wx.appId}")
    public String appId;
    @Value("${wx.appSecret}")
    private String appSecret;
    @Value("${wx.templateId}")
    private String templateId;
    @Value("${wx.miniappId}")
    private String miniappId;
    @Value("${wx.pagepath}")
    private String pagepath;

    @Autowired
    private RongTool rongTool;

    /**
     * 获取用户融云token
     *
     * @param userName
     * @return
     */
    @Override
    public AjaxResult getRongToken(String userName) {
        try {
            //TODO 第一步查询是否获取过融云token
            //TODO 不存在融云token的去掉融云接口生成融云token
//            SysUser sysUser = userMapper.selectUserDetailUserName(userName);
            Member sysUser = memberMapper.selectMemberByMemberPhone(userName);
            if(ObjectUtil.isEmpty(sysUser)){
                //TODO 用户不存在
                return  AjaxResult.error("用户不存在");
            }
            if(ObjectUtil.isNotEmpty(sysUser.getRongYunToken())){
                AjaxResult ajaxResult = AjaxResult.success();
                ajaxResult.put("rongYunToken", sysUser.getRongYunToken());
                ajaxResult.put("userName", sysUser.getMemberPhone());
                return ajaxResult;
            }

            if(ObjectUtil.isEmpty(sysUser.getNickname())){
                sysUser.setNickname(sysUser.getMemberPhone());
            }

            String token = rongTool.getToken(userName, sysUser.getNickname(), sysUser.getAvatar());
            if (ObjectUtil.isEmpty(token)) {
                return  AjaxResult.error("从融云服务器获取融云token失败");
            }
            sysUser.setRongYunToken(token);
            memberMapper.updateMemberRongYunTokenByMemberPhone(sysUser);
            AjaxResult ajaxResult = AjaxResult.success();
            ajaxResult.put("rongYunToken", token);
            ajaxResult.put("userName", sysUser.getMemberPhone());
            //remoteImUserService.syncUser(userName,nickName,sysUser.getAvatar(),sysUser.getRongYunToken());
            return ajaxResult;
        } catch (Exception e) {
            log.error("获取用户融云token失败{}", e);
            return AjaxResult.error("获取用户融云token失败！");
        }
    }

    /**
     * 根据用户信息获取用户详情
     * @param userName 登录用户名称
     * @param searchUserName 查询的用户名称
     * @return
     */
    @Override
    public AjaxResult getUserDetal(String userName, String searchUserName) {
        /**
         * 查询信息
         */
        Member sysUser = memberMapper.selectMemberByMemberPhone(searchUserName);
        if(ObjectUtil.isEmpty(searchUserName)){
            return AjaxResult.error("用户不存在");
        }
        if(ObjectUtil.isEmpty(sysUser.getAvatar())){
            sysUser.setAvatar(SystemConstants.DEFAULT_LOGO);
        }
        sysUser.setRongYunToken(null);
        Integer friendFlag = 0;
        String remark ="";
        Integer boundWxFlag =0;
        /**
         * 判断是否是好友
         */
        if(ObjectUtil.isNotEmpty(userName) && !userName.equals(searchUserName)){
            //TODO 判断是否是好友
            SysUserFriend sysUserFriend = sysUserFriendMapper.selectFriendByFriendName(userName, searchUserName);
            if(ObjectUtil.isNotEmpty(sysUserFriend)){
                remark = sysUserFriend.getRemark();
                //TODO 是好友
                friendFlag = 1;
            }
        }
        //TODO 判断是否已经绑定微信
        if(searchUserName.equals(userName)){
            UserRongYunState userRongYunState = userRongYunStateMapper.selectByuserName(searchUserName);
            if(ObjectUtil.isNotEmpty(userRongYunState) && ObjectUtil.isNotEmpty(userRongYunState.getOpenId())){
                boundWxFlag = 1;
            }
        }
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put("sysUser",sysUser);
        ajaxResult.put("remark",remark);
        ajaxResult.put("friendFlag",friendFlag);
        ajaxResult.put("boundWxFlag",boundWxFlag);
        return ajaxResult;
    }

    /**
     *
     * @param userChatMessage
     *         targetId :接收方手机号或群聊id
     *         content：消息内容（内容、多媒体二选一，多媒体只有一个）
     *         mediaUrl：多媒体链接（内容、多媒体二选一，图片只有一个）
     *         contentType 消息类型 0 文字消息 1图片消息 2视频 3音频 4 文件
     *         conversationType 所属会话类型 单聊	1 群聊	3 聊天室	4 系统	6
     *         fileName 文件名
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult sendMessage(String userName, UserChatMessage userChatMessage) {
        if(userChatMessage.getSendable()){
            //TODO 评论内容存表
            Date now = new Date();
            userChatMessage.setSendUserName(userName);
            userChatMessage.setCreateTime(now);
            userChatMessage.setCreateTimestamp(now.getTime());
            userChatMessage.setDeleteFlag(1);
            userChatMessage.setUserName(userName);
            userChatMessage.setConversationId(userChatMessage.getTargetId());
            userChatMessageMapper.insertSelective(userChatMessage);
            //TODO 发送人头像昵称
            Member sysUser = memberMapper.selectMemberByMemberPhone(userName);
            userChatMessage.setConversationPic(SystemConstants.DEFAULT_LOGO);
            userChatMessage.setConversationName("未知");
            if(ObjectUtil.isNotEmpty(sysUser)){
                if(ObjectUtil.isNotEmpty(sysUser.getNickname())){
                    userChatMessage.setConversationName(sysUser.getNickname());
                }
                if(ObjectUtil.isNotEmpty(sysUser.getAvatar())){
                    userChatMessage.setConversationPic(sysUser.getAvatar());
                }
            }
            if(userChatMessage.getConversationType() == 1){
                //TODO 接收人头像昵称
                sysUser = memberMapper.selectMemberByMemberPhone(userChatMessage.getTargetId());
                if(ObjectUtil.isEmpty(sysUser.getRongYunToken())){
                    String nickName = sysUser.getNickname();
                    if(ObjectUtil.isEmpty(nickName)){
                        nickName = sysUser.getMemberPhone();
                    }
                    String token = rongTool.getToken(userName, nickName, sysUser.getAvatar());
                    if (ObjectUtil.isEmpty(token)) {
                        return  AjaxResult.error("从融云服务器获取融云token失败");
                    }
                    sysUser.setRongYunToken(token);
                    memberMapper.updateMemberRongYunTokenByMemberPhone(sysUser);
                }
                userChatMessage.setConversationPicTwo(SystemConstants.DEFAULT_LOGO);
                userChatMessage.setConversationNameTwo("未知");
                if(ObjectUtil.isNotEmpty(sysUser)){
                    userChatMessage.setConversationNameTwo(sysUser.getNickname());
                    if(ObjectUtil.isNotEmpty(sysUser.getAvatar())){
                        userChatMessage.setConversationPicTwo(sysUser.getAvatar());
                    }
                }
                //TODO 单聊，发送方接收方各存一份,方便查询评论列表使用
                userChatMessage.setUserName(userChatMessage.getTargetId());
                userChatMessage.setConversationId(userName);
                userChatMessage.setOriginalId(userChatMessage.getId());
                userChatMessageMapper.insertSelective(userChatMessage);

                //TODO 发送到融云
                int code = rongTool.sendPrivateMessage(userChatMessage);
                if (code != 200) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return AjaxResult.error("用户通过融云发送信息失败");
                }
            }else {

                //TODO 群聊的查询群图片和名称
                UserChatGroup userChatGroup = userChatGroupMapper.selectById(Long.valueOf(userChatMessage.getTargetId()));
                if(ObjectUtil.isEmpty(userChatGroup)){
                    return AjaxResult.error("您已被移除群聊");
                }
                //TODO 判断用户是否在群聊里面
                UserChatGroupMember userChatGroupMember = userChatGroupMemberMapper.selectByGroupIdAndUserName(Long.valueOf(userChatMessage.getTargetId()),userChatMessage.getSendUserName());
                if (ObjectUtil.isEmpty(userChatGroupMember)) {
                    return AjaxResult.error("您已被移除群聊");
                }
                userChatMessage.setConversationPicTwo(SystemConstants.DEFAULT_LOGO);
                userChatMessage.setConversationNameTwo("群聊名称未设置");
                if(ObjectUtil.isNotEmpty(userChatGroup)){
                    userChatMessage.setConversationPicTwo(userChatGroup.getAvatar());
                    userChatMessage.setConversationNameTwo(userChatGroup.getGroupName());
                }
                //TODO 发送到融云
                int code = rongTool.sendGroupMessage(userChatMessage);
                if (code != 200) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return AjaxResult.error("用户通过融云发送信息失败");
                }

            }
        }
        //TODO 消息通知 如果不是自己发送的开启消息通知
        this.messageNotice(userChatMessage);
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put("userChatMessage",userChatMessage);
        return ajaxResult;
    }

    /**
     * 同步用户状态
     * @param jsonArray
     * @return
     */
    @Override
    public Integer reciveUserRongYunState(JSONArray jsonArray) {
        for(int i =0;i<jsonArray.size();i++){
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            //TODO 查询有没有 有更新没有插入
            UserRongYunState userRongYunState = userRongYunStateMapper.selectByuserName(jsonObject.getString("userid"));

            if(ObjectUtil.isNotEmpty(userRongYunState)){

                userRongYunState.setTimestamp(jsonObject.getLong("time"));
                userRongYunState.setClientip(jsonObject.getString("clientIp"));

                if(jsonObject.getInteger("status") != 0){
                    if(userRongYunState.getOnlineNum()<= 0){
                        continue;
                    }
                    //TODO 用户多端在线数减1
                    userRongYunState.setOnlineNum(-1);
                }else {
                    //TODO 用户多端在线数+1
                    userRongYunState.setOnlineNum(1);
                }
                userRongYunStateMapper.updateOnlineNum(userRongYunState);
            }else {
                userRongYunState = new UserRongYunState();
                if(jsonObject.getInteger("status") != 0){
                    userRongYunState.setOnlineNum(0);
                }else {
                    userRongYunState.setOnlineNum(1);
                }
                userRongYunState.setTimestamp(jsonObject.getLong("time"));
                userRongYunState.setClientip(jsonObject.getString("clientIp"));
                userRongYunState.setUserName(jsonObject.getString("userid"));
                userRongYunStateMapper.insertSelective(userRongYunState);
            }
        }
        return 200;
    }

    @Override
    public AjaxResult initializeRongToken(Integer page, Integer rows) {

        if(ObjectUtil.isEmpty(page) && ObjectUtil.isEmpty(rows)){
            page = 1;
            rows = 50;
            loop:while (true){
                PageHelper.startPage(page,rows,false);
                List<Member>  sysUserList = memberMapper.selectMemberListWithoutRongYunToken();
                if(ObjectUtil.isEmpty(sysUserList)){
                   return AjaxResult.error("已经循环完了，跑了"+page+"页数据");
                }
                for(Member sysUser:sysUserList){
                    if(ObjectUtil.isEmpty(sysUser.getRongYunToken())){
                        String nickName = sysUser.getNickname();
                        if(ObjectUtil.isEmpty(nickName)){
                            nickName = sysUser.getMemberPhone();
                        }
                        String token = rongTool.getToken(sysUser.getMemberPhone(), nickName, sysUser.getAvatar());
                        if (ObjectUtil.isEmpty(token)) {
                            log.error("获取融云token失败");
                            AjaxResult ajaxResult= AjaxResult.error("获取融云token失败");
                            ajaxResult.put("sysUser",sysUser);
                            return ajaxResult;
                        }
                        sysUser.setRongYunToken(token);
                        memberMapper.updateMemberRongYunTokenByMemberPhone(sysUser);
                    }
                }
                System.out.println(page);
                page++;

            }

        }else {
            PageHelper.startPage(page,rows,false);
            List<Member>  sysUserList = memberMapper.selectMemberListWithoutRongYunToken();
            if(ObjectUtil.isEmpty(sysUserList)){
                return AjaxResult.error("无用户数据了");
            }
            for(Member sysUser:sysUserList){
                if(ObjectUtil.isEmpty(sysUser.getRongYunToken())){
                    String nickName = sysUser.getNickname();
                    if(ObjectUtil.isEmpty(nickName)){
                        nickName = sysUser.getMemberPhone();
                    }
                    String token = rongTool.getToken(sysUser.getMemberPhone(), nickName, sysUser.getAvatar());
                    if (ObjectUtil.isEmpty(token)) {
                        log.error("获取融云token失败");
                        AjaxResult ajaxResult= AjaxResult.error("获取融云token失败");
                        ajaxResult.put("sysUser",sysUser);
                        return ajaxResult;
                    }
                    sysUser.setRongYunToken(token);
                    memberMapper.updateMemberRongYunTokenByMemberPhone(sysUser);
                }
            }
            return AjaxResult.success();
        }

    }
    /**
     * 用户绑定微信公众号，发送离线消息
     * @param userName 用户手机号
     * @param code 微信返回的code码
     * @return
     */
    @Override
    public AjaxResult boundWeiXin(String userName, String code) {
        JSONObject jsonObject = WeiXinUtils.getOpenid(code,appId,appSecret);
        if(ObjectUtil.isEmpty(jsonObject)){
            return AjaxResult.error("获取用户对应微信公众号openId失败");
        }
        if(jsonObject.containsKey("errmsg")){
            return AjaxResult.error("获取用户openId失败，微信返回："+jsonObject);
        }
        //TODO 保存信息
        String openId = jsonObject.getString("openid");
        //TODO 查询是否有数据
        UserRongYunState userRongYunState = userRongYunStateMapper.selectByuserName(userName);
        if(ObjectUtil.isEmpty(userRongYunState)){
            userRongYunState.setUserName(userName);
            userRongYunState.setOnlineNum(1);
            userRongYunState.setTimestamp(System.currentTimeMillis());
            userRongYunState.setClientip("");
            userRongYunState.setOpenId(openId);
            userRongYunState.setWxUpdateTime(new Date());
            userRongYunStateMapper.insertSelective(userRongYunState);
        }else {
            userRongYunState.setOpenId(openId);
            userRongYunState.setWxUpdateTime(new Date());
            userRongYunStateMapper.updateByPrimaryKeySelective(userRongYunState);
        }
//        JSONObject imUser = remoteImUserService.getUserByUserId(userName).getData();
//        if(imUser!=null && StringUtils.isBlank(imUser.getString("openid"))){
//            imUser.put("openid",openId);
//            remoteImUserService.updateOpenid(imUser);
//        }
        return AjaxResult.success("绑定成功，请重新打开个人信息页面！");
    }

    @Override
    public AjaxResult unboundWeiXin(String userName) {
        UserRongYunState userRongYunState = userRongYunStateMapper.selectByuserName(userName);
        if(ObjectUtil.isEmpty(userRongYunState)){
            return AjaxResult.error("未找到绑定信息");
        }
        userRongYunState.setOpenId("");
        userRongYunState.setTimestamp(System.currentTimeMillis());
        userRongYunStateMapper.updateByPrimaryKey(userRongYunState);
        return AjaxResult.success();
    }

    /**
     * 发送离线消息到用户的微信上
     */
    private void messageNotice(UserChatMessage userChatMessage) {
        try {
            ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
            singleThreadExecutor.execute(new Runnable() {
                @Override
                public void run() {
                    //TODO 1 获取微信的accesssToken
                    String resultToken = HttpUtil.get("http://search.ningmengdou.com/Wechat/WechatApi/getAccessToken");
                    if(ObjectUtil.isEmpty(resultToken)){
                        log.error("用户交互离线消息通知获取微信accessToken失败");
                        return;
                    }
                   JSONObject tokenJson =  JSON.parseObject(resultToken);
                    if(!"000000".equals(tokenJson.getString("status")) || !tokenJson.containsKey("access_token")){
                        log.error("用户交互离线消息通知调用php项目获取accessToken失败");
                        return;
                    }
                    String accessToken = tokenJson.getString("access_token");
                    if(ObjectUtil.isEmpty(accessToken)){
                        log.error("用户交互离线消息通知调用php项目获取accessToken失败");
                        return;
                    }
                    if(userChatMessage.getConversationType() == 1){
                        //TODO 单聊
                        UserRongYunState userRongYunState = userRongYunStateMapper.selectByuserName(userChatMessage.getTargetId());
                        JSONObject imUser = null;
//                        JSONObject imUser = remoteImUserService.getUserByUserId(userChatMessage.getTargetId()).getData();
                        if((ObjectUtil.isNotEmpty(userRongYunState) && userRongYunState.getOnlineNum() == 0
                                && ObjectUtil.isNotEmpty(userRongYunState.getOpenId())) ||
                                (ObjectUtil.isNotEmpty(imUser) && imUser.getInteger("online") == 0
                                        && ObjectUtil.isNotEmpty(imUser.getString("openid")))){
                            String openid = userRongYunState!=null && StringUtils.isNotBlank(userRongYunState.getOpenId())?
                                    userRongYunState.getOpenId():imUser.getString("openid");
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            //TODO 发送微信模板
                            String url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token="+accessToken;
                            //TODO 组装模板数据
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("touser",openid);
                            jsonObject.put("template_id",templateId);
                            if(StringUtils.isNotBlank(pagepath)){
                                JSONObject miniprogram = new JSONObject();
                                JSONObject item = new JSONObject();
                                item.put("type","");
                                item.put("senderUserId",userChatMessage.getTargetId());
                                item.put("targetId",userChatMessage.getSendUserName());
                                item.put("name",userChatMessage.getConversationNameTwo());
                                miniprogram.put("appid",miniappId);
                                miniprogram.put("pagepath",pagepath+"?item="+item.toJSONString());
                                jsonObject.put("miniprogram",miniprogram);
                            }
                            JSONObject data = new JSONObject();
                            JSONObject first = new JSONObject();
                            first.put("value","尊敬的客户：\r\n您好！您收到一条聊天未读信息。");
                            first.put("color","#666666");
                            data.put("first",first);
                            JSONObject keyword1 = new JSONObject();
                            keyword1.put("value",userChatMessage.getConversationName());
                            keyword1.put("color","#173177");
                            data.put("keyword1",keyword1);
                            JSONObject keyword2 = new JSONObject();
                            keyword2.put("value",userChatMessage.getCreateTime()!=null?simpleDateFormat.format(userChatMessage.getCreateTime()):simpleDateFormat.format(new Date()));
                            keyword2.put("color","#173177");
                            data.put("keyword2",keyword2);
                            JSONObject keyword3 = new JSONObject();
//                            消息类型 0 文字消息 1图片消息 2视频 3音频 4 文件
                            if(userChatMessage.getContentType() == 0){
                                keyword3.put("value",userChatMessage.getContent());
                            }else if(userChatMessage.getContentType() == 1){
                                keyword3.put("value","[图片消息]");
                            }else if(userChatMessage.getContentType() == 2){
                                keyword3.put("value","[视频消息]");
                            }else if(userChatMessage.getContentType() == 3){
                                keyword3.put("value","[音频消息]");
                            }else if(userChatMessage.getContentType() == 4){
                                keyword3.put("value","[文件消息]");
                            }else {
                                keyword3.put("value","[其他消息]");
                            }
                            keyword3.put("color","#173177");
                            data.put("keyword3",keyword3);
                            JSONObject remark = new JSONObject();
                            remark.put("value","更多内容请登录柠檬豆官网工作台查看！");
                            remark.put("color","#173177");
                            data.put("remark",remark);
                            jsonObject.put("data",data);
                            String result =  HttpUtil.post(url,jsonObject.toJSONString());
                            if(ObjectUtil.isEmpty(result)){
                                log.error("微信发送交互信息到模板失败============================");
                                return;
                            }
                            JSONObject resultJson = JSONObject.parseObject(result);
                            if(!"ok".equals(resultJson.getString("errmsg"))){
                                log.error("微信发送交互信息到模板失败,返回信息============================"+result);
                            }
                        }
                    }else {
                        //TODO 群聊



                    }
                }
            });
        }catch (Exception e){
            log.error("交互信息发送到微信模板失败{}",e);
        }

    }


}
