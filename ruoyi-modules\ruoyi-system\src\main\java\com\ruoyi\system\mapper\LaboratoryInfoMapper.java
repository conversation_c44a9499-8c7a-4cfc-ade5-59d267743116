package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.LaboratoryInfo;

/**
 * 实验室信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface LaboratoryInfoMapper 
{
    /**
     * 查询实验室信息
     * 
     * @param id 实验室信息主键
     * @return 实验室信息
     */
    public LaboratoryInfo selectLaboratoryInfoById(Long id);

    /**
     * 查询实验室信息列表
     * 
     * @param laboratoryInfo 实验室信息
     * @return 实验室信息集合
     */
    public List<LaboratoryInfo> selectLaboratoryInfoList(LaboratoryInfo laboratoryInfo);

    /**
     * 新增实验室信息
     * 
     * @param laboratoryInfo 实验室信息
     * @return 结果
     */
    public int insertLaboratoryInfo(LaboratoryInfo laboratoryInfo);

    /**
     * 修改实验室信息
     * 
     * @param laboratoryInfo 实验室信息
     * @return 结果
     */
    public int updateLaboratoryInfo(LaboratoryInfo laboratoryInfo);

    /**
     * 删除实验室信息
     * 
     * @param id 实验室信息主键
     * @return 结果
     */
    public int deleteLaboratoryInfoById(Long id);

    /**
     * 批量删除实验室信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLaboratoryInfoByIds(Long[] ids);
}
