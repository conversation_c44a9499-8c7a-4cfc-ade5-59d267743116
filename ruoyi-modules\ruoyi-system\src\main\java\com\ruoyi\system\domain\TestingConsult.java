package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 检测咨询对象 testing_consult
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public class TestingConsult extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 检测项目ID */
    @Excel(name = "检测项目ID")
    private Long testingItemId;

    /** 检测项目名称 */
    @Excel(name = "检测项目名称")
    private String testingItemName;

    /** 咨询人姓名 */
    @Excel(name = "咨询人姓名")
    private String consultName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 咨询内容 */
    @Excel(name = "咨询内容")
    private String consultContent;

    /** 回复内容 */
    @Excel(name = "回复内容")
    private String replyContent;

    /** 咨询状态（0待回复 1已回复） */
    @Excel(name = "咨询状态", readConverterExp = "0=待回复,1=已回复")
    private String status;

    /** 回复时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "回复时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date replyTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTestingItemId(Long testingItemId) 
    {
        this.testingItemId = testingItemId;
    }

    public Long getTestingItemId() 
    {
        return testingItemId;
    }
    public void setTestingItemName(String testingItemName) 
    {
        this.testingItemName = testingItemName;
    }

    public String getTestingItemName() 
    {
        return testingItemName;
    }
    public void setConsultName(String consultName) 
    {
        this.consultName = consultName;
    }

    public String getConsultName() 
    {
        return consultName;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setConsultContent(String consultContent) 
    {
        this.consultContent = consultContent;
    }

    public String getConsultContent() 
    {
        return consultContent;
    }
    public void setReplyContent(String replyContent) 
    {
        this.replyContent = replyContent;
    }

    public String getReplyContent() 
    {
        return replyContent;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setReplyTime(Date replyTime) 
    {
        this.replyTime = replyTime;
    }

    public Date getReplyTime() 
    {
        return replyTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("testingItemId", getTestingItemId())
            .append("testingItemName", getTestingItemName())
            .append("consultName", getConsultName())
            .append("contactPhone", getContactPhone())
            .append("consultContent", getConsultContent())
            .append("replyContent", getReplyContent())
            .append("status", getStatus())
            .append("replyTime", getReplyTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
