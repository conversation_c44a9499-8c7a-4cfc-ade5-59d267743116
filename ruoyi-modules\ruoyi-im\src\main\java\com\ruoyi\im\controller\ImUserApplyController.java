package com.ruoyi.im.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.im.api.domain.ImUser;
import com.ruoyi.im.api.domain.ImUserApply;
import com.ruoyi.im.api.domain.ImUserFriend;
import com.ruoyi.im.api.dto.ImUserApplyVo;
import com.ruoyi.im.service.ImUserApplyService;
import com.ruoyi.im.api.util.SqlUtils;
import com.ruoyi.im.service.ImUserFriendService;
import com.ruoyi.im.service.ImUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@RestController
@RequestMapping("/im/userapply")
public class ImUserApplyController {

    @Resource
    private ImUserApplyService imUserApplyService;

    @Resource
    private ImUserFriendService imUserFriendService;

    @Resource
    private ImUserService imUserService;
    /***
     * ImUserApply分页条件搜索实现
     * @param imUserApply
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/search/{page}/{size}" )
    public TableDataInfo findPage(@RequestBody(required = false) ImUserApply imUserApply, @PathVariable("page")  int page, @PathVariable("size")  int size, @RequestParam(value = "fields",required = false) String fields){
        Page<ImUserApply> pageSearch = new Page<>(page,size);
        QueryWrapper<ImUserApply> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        imUserApplyService.page(pageSearch,wrapper);
        TableDataInfo tableDataInfo = new TableDataInfo(pageSearch.getRecords(), (int) pageSearch.getTotal());
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setMsg("success");
        return tableDataInfo;
    }

    /***
     * 多条件搜索数据
     * @param imUserApply
     * @return
     */
    @PostMapping(value = "/search" )
    public R<List<ImUserApply>> findList(@RequestBody(required = false)  ImUserApply imUserApply, @RequestParam(value = "fields",required = false) String fields){
        QueryWrapper<ImUserApply> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        return R.ok(imUserApplyService.list(wrapper)) ;
    }

    /***
     * 修改ImUserApply数据
     * @param imUserApply
     * @return
     */
    @PostMapping(value="/{id}")
    public R<Boolean> update(@RequestBody  ImUserApply imUserApply,@PathVariable("id") Long id){
        if(imUserApplyService.updateById(imUserApply)){
            return R.ok(true) ;
        }
        return R.ok(false) ;
    }

    /***
     * 新增ImUserApply数据  申请好友
     * @param imUserApply
     * @return
     */
    /***
     * 新增ImUserApply数据  申请好友
     * @param imUserApply
     * @return
     */
    @PostMapping(value="/apply")
    public R<Long> add(@RequestBody ImUserApply imUserApply){
        if (!Objects.isNull(imUserApply)) {
            ImUserApply apply = imUserApplyService.getOne(new QueryWrapper<ImUserApply>().eq("receiverId", imUserApply.getReceiverId()).eq("senderId",imUserApply.getSenderId()));
            //校验是否之前申请过，如未有则可以申请
            if (!Objects.isNull(apply)) {
                //校验是否是待处理的
                if (apply.getStatus()==0) {
                    return R.ok(null) ;
                }
                if (apply.getStatus() == 2) {
                    if(imUserApplyService.save(imUserApply)){
                        return R.ok(imUserApply.getId()) ;
                    }
                }
            }else{
                if(imUserApplyService.save(imUserApply)){
                    return R.ok(imUserApply.getId()) ;
                }
            }
        }
        return R.fail(400,"申请失败") ;
    }


    /***
     * 同意或者拒绝，同意操作需要添加好友记录
     * @param imUserApply
     * @return
     */
    @PostMapping(value="/op")
    @Transactional
    public R<Boolean> updateStatus(@RequestBody  ImUserApply imUserApply){
        if (!Objects.isNull(imUserApply)) {
            try {
                if (imUserApplyService.updateById(imUserApply)) {
                    if (imUserApply.getStatus() == 1) {
                        ImUserFriend imUserFriend = new ImUserFriend();
                        imUserFriend.setFriendId(imUserApply.getReceiverId()); //好友
                        imUserFriend.setUserId(imUserApply.getSenderId()); //用户自己
                        imUserFriendService.save(imUserFriend);
                    }
                    return R.ok(true);
                }
            }catch (Exception e){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        }
        return R.fail(400,"缺少必要的请求参数") ;
    }
    /***
     * 根据ID查询ImUserApply数据
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public R<ImUserApply> findById(@PathVariable("id") Long id){
        return R.ok(imUserApplyService.getById(id)) ;
    }

    /***
     * 批量状态
     * @param opid
     * @return
     */
    @PostMapping("/batch/delete")
    public R<Boolean> batchDelete(@RequestParam("opid") String opid){
        if(imUserApplyService.removeByIds(SqlUtils.str2LongList(opid,","))){
            return R.ok(true);
        }
        return R.ok(false);
    }


    /***
     * 查看用户所有的好友申请
     * @param imUserApply
     * @return
     */
    @PostMapping(value = "/list" )
    public R<List<ImUserApplyVo>> userApplyList(@RequestBody ImUserApply imUserApply){
        if (!Objects.isNull(imUserApply)) {

            List<ImUserApply> receiverId = imUserApplyService.list(new QueryWrapper<ImUserApply>().eq("receiverId", imUserApply.getReceiverId()));
            //封装接口返回的数据
            ArrayList<ImUserApplyVo> list = new ArrayList<>();

            if (receiverId.size()!=0) {
                for (ImUserApply userApply : receiverId) {
                    ImUser user = imUserService.getOne(new QueryWrapper<ImUser>().eq("userId", userApply.getSenderId()));
                    //封装数据
                    ImUserApplyVo imUserApplyVo = new ImUserApplyVo();
                    imUserApplyVo.setSenderId(userApply.getSenderId());
                    imUserApplyVo.setStatus(userApply.getStatus());
                    imUserApplyVo.setRemark(userApply.getRemark());
                    imUserApplyVo.setReceiverId(userApply.getReceiverId());
                    imUserApplyVo.setId(userApply.getId());
                    imUserApplyVo.setUser(user);
                    //封装返回数据
                    list.add(imUserApplyVo);
                }
                return R.ok(list) ;
            }
        }
        return R.ok(null);
    }


    /**
     * 数量
     * @param imUserApply
     * @return
     */
    @PostMapping(value = "/number" )
    public R<Integer> userApplyNumber(@RequestBody ImUserApply imUserApply){
        return R.ok(imUserApplyService.count(new QueryWrapper<ImUserApply>().eq("receiverId", imUserApply.getReceiverId()).eq("status",0)));
    }
}
