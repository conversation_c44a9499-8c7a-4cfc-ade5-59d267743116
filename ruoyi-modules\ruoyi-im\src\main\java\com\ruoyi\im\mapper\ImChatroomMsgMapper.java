package com.ruoyi.im.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.im.api.domain.ImChatroomMsg;
import com.ruoyi.im.mapper.provider.ImChatroomMsgMapperProvider;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

public interface ImChatroomMsgMapper extends BaseMapper<ImChatroomMsg>
{

    @SelectProvider(type = ImChatroomMsgMapperProvider.class,method = "findLatestMsg")
    List<ImChatroomMsg> findLatestMsg(@Param("ids") List<String> ids);

    @Select("select distinct toUserId from im_chatroom_msg where fromUserId=#{telphone}")
    List<String> findSent(@Param("telphone") String telphone);
}
