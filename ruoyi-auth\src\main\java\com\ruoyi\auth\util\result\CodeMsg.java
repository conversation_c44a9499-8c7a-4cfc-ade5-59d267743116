package com.ruoyi.auth.util.result;

public class CodeMsg {

    private int code;
    private String msg;

    //通用的错误码
    public static CodeMsg SUCCESS = new CodeMsg(200, "操作成功");
    public static CodeMsg SERVER_ERROR = new CodeMsg(500100, "网络开小差了~~");
    public static CodeMsg BIND_ERROR = new CodeMsg(500101, "%s");
    public static CodeMsg REQUEST_ILLEGAL = new CodeMsg(500102, "无效的数据请求");
    public static CodeMsg ACCESS_LIMIT_REACHED = new CodeMsg(500103, "访问太频繁");
    public static CodeMsg CODE_LIMIT_ERROR = new CodeMsg(500104, "短信发送过于频繁");
    public static CodeMsg REQUEST_ERROR = new CodeMsg(500105, "请求信息不存在");

    //管理员模块 5002XX
    public static CodeMsg ADMIN_NOT_EXISTS = new CodeMsg(500201, "管理员尚未添加");
    public static CodeMsg ADMIN_PASS_ERROR = new CodeMsg(500202, "管理员密码有误");
    public static CodeMsg ADMIN_NOT_LOGIN = new CodeMsg(500203, "管理员尚未登录");
    public static CodeMsg ADMIN_NOT_ACCESS = new CodeMsg(500204, "管理员没有权限");
    public static CodeMsg ADMIN_USERNAME_EXIST = new CodeMsg(500205, "管理员已经存在");
    public static CodeMsg ADMIN_ADD_ERROR = new CodeMsg(500206, "添加失败");
    public static CodeMsg ADMIN_EDIT_ERROR = new CodeMsg(500207, "编辑失败");
    public static CodeMsg ADMIN_DEL_ERROR = new CodeMsg(500208, "删除失败");
    public static CodeMsg ADMIN_OP_ERROR = new CodeMsg(500209, "操作失败");
    public static CodeMsg ADMIN_FORBIDDEN = new CodeMsg(500210, "管理员已被禁用");

    //用户模块 5003XX
    public static CodeMsg USER_NOT_LOGIN = new CodeMsg(500300, "用户尚未登录");
    public static CodeMsg USER_NOT_REG = new CodeMsg(500301, "用户尚未注册");
    public static CodeMsg USER_OP_ERROR = new CodeMsg(500302, "用户操作失败");
    public static CodeMsg USER_PHONE_ERROR = new CodeMsg(500303, "用户手机号为空");


    private CodeMsg() {
    }

    private CodeMsg(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public CodeMsg fillArgs(Object... args) {
        int code = this.code;
        String message = String.format(this.msg, args);
        return new CodeMsg(code, message);
    }

    @Override
    public String toString() {
        return "CodeMsg [code=" + code + ", msg=" + msg + "]";
    }


}
