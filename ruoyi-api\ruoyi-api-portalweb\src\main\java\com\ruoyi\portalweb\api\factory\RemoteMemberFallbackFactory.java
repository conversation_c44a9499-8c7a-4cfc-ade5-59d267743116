package com.ruoyi.portalweb.api.factory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.portalweb.api.RemoteMemberService;
import com.ruoyi.portalweb.api.domain.Member;
import com.ruoyi.portalweb.api.model.LoginMember;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteMemberFallbackFactory implements FallbackFactory<RemoteMemberService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteMemberFallbackFactory.class);

    @Override
    public RemoteMemberService create(Throwable throwable)
    {
        log.error("会员服务调用失败:{}", throwable.getMessage());
        return new RemoteMemberService()
        {
            @Override
            public R<LoginMember> getMemberInfo(String member, String source)
            {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }
            
            @Override
            public R<Boolean> registerMemberInfo(Member member, String source)
            {
                return R.fail("注册用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> checkUserSmsCode(String username, String smsCode) {
                return R.fail("检查验证码失败:" + throwable.getMessage());
            }
            @Override
            public R<LoginMember> getMemberSms(String username, String smsCode) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }
            @Override
            public R<Boolean> updateMemberPassword(Member member, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }
        };
    }
}
