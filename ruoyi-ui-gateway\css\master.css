@charset "utf-8";

/* CSS Document */

@charset "utf-8";
body,
ul,
ol,
li,
p,
h1,
h2,
h3,
h4,
h5,
h6,
form,
fieldset,
table,
td,
img,
div {
    margin: 0;
    padding: 0;
    border: 0;
}

body {
    color: #333;
    font-size: 14px;
    font-family: "Microsoft YaHei";
    background-color: #fff;
    margin: 0 auto;
    line-height: 30px;
}

body img {
    max-width: 100%;
}

ul,
ol {
    list-style-type: none;
}

select,
input,
img,
select {
    vertical-align: middle;
}

input {
    font-size: 12px;
}

a {
    text-decoration: none;
    color: #333;
}

a:hover {
    color: #3E86F9;
    text-decoration: none;
}

.clear {
    clear: both;
}

@font-face {
    font-family: "iconfont";
    /* Project id 2705019 */
    src: url('fonts/iconfont.woff2?t=1627436994441') format('woff2'), url('fonts/iconfont.woff?t=1627436994441') format('woff'), url('fonts/iconfont.ttf?t=1627436994441') format('truetype');
}

.iconfont {
    font-family: "iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-shoucang:before {
    content: "\e613";
}

.icon-shoucang1:before {
    content: "\e6c8";
}

.icon-youxiang:before {
    content: "\e63f";
}
.el-icon-phone:before {
    content: "\e795";
}
.icon-dianhua:before {
    content: "\e612";
}

.icon-duihao:before {
    content: "\e684";
}

.top_bg {
    height: 100px;
    width: 100%;
    min-width: 1200px;
}

.logo {
    width: 200px;
    position: absolute;
    top: 20%;
    left: 25px;
    padding: 60px 40px;
    border-radius: 6px;
}

.cur {
    color: #de791b !important;
}

.person {
    position: absolute;
    right: 50px;
    top: 40px;
}

.banner {
    padding: 0px;
    min-height: 400px;
    margin-top: 0px;
    margin-right: auto;
    margin-bottom: 0px;
    margin-left: auto;
    position: relative;
    min-width: 1200px;
    background-position: center center;
}

.banner2 {
    background-repeat: no-repeat;
    background-position: center center;
    padding: 0px;
    height: 400px;
    margin-top: 0px;
    margin-right: auto;
    margin-bottom: 0px;
    margin-left: auto;
    position: relative;
    min-width: 1200px;
    width: 100%;
}

.kechuang_bg {
    background-repeat: no-repeat;
    background-position: center center;
    padding: 0px;
    height: 278px;
    margin-top: 40px;
    margin-right: auto;
    margin-bottom: 0px;
    margin-left: auto;
    background-image: url(../images/kechuang_bg.png);
    min-width: 1200px;
    width: 100%;
}

.kechuang {
    width: 1200px;
    margin: 0 auto;
}

.search2 {
    display: flex;
    flex-flow: nowrap;
    justify-content: center;
    width: 100%;
}

.search {
    background-image: url(../images/search.png);
    background-repeat: no-repeat;
    background-position: center center;
    padding: 0px;
    height: 60px;
    width: 880px;
    margin-top: 0px;
    margin-right: auto;
    margin-bottom: 0px;
    margin-left: auto;
    position: absolute;
    left: 0;
    right: 0;
    z-index: 10;
    bottom: 130px;
}

.search_txt {
    width: 700px;
    height: 50px;
    left: 30px;
    line-height: 50px;
    top: 5px;
    border: none;
    position: absolute;
    font-size: 14px;
    color: #666;
    outline: none;
}

.search_but {
    background-color: transparent;
    width: 108px;
    height: 60px;
    right: 0;
    position: absolute;
    border: none;
    outline: none;
}

.kanban {
    width: 1224px;
    position: relative;
    z-index: 10;
    margin-top: -55px;
    margin-right: auto;
    margin-bottom: 0;
    margin-left: auto;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
}

.kanban_c {
    background-color: #fff;
    width: 11%;
    border-radius: 5px;
    box-shadow: 1px 3px 5px #eee;
    font-size: 14px;
    padding-top: 2%;
    padding-right: 2%;
    padding-bottom: 2%;
    padding-left: 10%;
    height: 70px;
    line-height: 35px;
    position: relative;
}

.kanban_c a {
    font-size: 16px;
}

.kanban_c a strong {
    font-size: 18px;
}

.kanban_pic {
    position: absolute;
    left: 10%;
}

.huodong {
    width: 1398px;
    margin-top: 0;
    margin-right: auto;
    margin-bottom: 0;
    margin-left: auto;
    padding-top: 30px;
    padding-bottom: 30px;
}

.zhuce {
    width: 100%;
    height: 100%;
    background-image: url(../images/zhucel.png);
    background-repeat: no-repeat;
    background-position: center top;
    position: relative;
    background-size: cover;
}

.title {
    font-size: 24px;
    line-height: 35px;
    margin-top: 25px;
    color: #333;
    font-weight: bold;
    width: 100%;
}

.title span {
    float: right;
}

.title span a {
    font-size: 16px;
    color: #de791b;
    font-weight: normal;
}

ul.shuju_list {
    margin: 0px;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    padding-top: 30px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
    width: 100%;
}

ul.shuju_list li {
    width: 18%;
    text-align: center;
    line-height: 35px;
    padding-top: 10px;
    padding-bottom: 10px;
    font-size: 16px;
}

.shuju1 {
    background-image: linear-gradient(to bottom, #fff, #9fe8e2);
}

.shuju1 strong {
    color: #1cb5a7;
    font-size: 24px;
}

.shuju1 i {
    color: #1cb5a7;
    font-style: normal;
}

.shuju2 {
    background-image: linear-gradient(to bottom, #fff, #fbcdc0);
}

.shuju2 strong {
    color: #f06343;
    font-size: 24px;
}

.shuju2 i {
    color: #f06343;
    font-style: normal;
}

.shuju3 {
    background-image: linear-gradient(to bottom, #fff, #acd1fa);
}

.shuju3 strong {
    color: #216ec4;
    font-size: 24px;
}

.shuju3 i {
    color: #216ec4;
    font-style: normal;
}

.shuju4 {
    background-image: linear-gradient(to bottom, #fff, #fbcbb4);
}

.shuju4 strong {
    color: #b54e1c;
    font-size: 24px;
}

.shuju4 i {
    color: #b54e1c;
    font-style: normal;
}

.shuju5 {
    background-image: linear-gradient(to bottom, #fff, #f6d39a);
}

.shuju5 strong {
    color: #da8d11;
    font-size: 24px;
}

.shuju5 i {
    color: #da8d11;
    font-style: normal;
}

ul.chengguo_index_list {
    margin: 0px;
    padding: 0px;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    width: 100%;
}

ul.chengguo_index_list li {
    width: 23%;
    border-radius: 0 0 5px 5px;
    background-color: #fff;
    line-height: 30px;
    margin-top: 15px;
    margin-right: 0px;
    margin-bottom: 15px;
    margin-left: 0px;
    box-shadow: 3px 3px 5px #eee;
    padding-top: 0px;
    padding-right: 0px;
    padding-bottom: 10px;
    padding-left: 0px;
}

.chengguo_leixing {
    font-size: 20px;
    line-height: 70px;
    color: #fff;
    background-repeat: repeat;
    background-position: left center;
    margin: 0px;
    height: 70px;
    width: 90%;
    padding-top: 0px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 10%;
}

.chengguo_leixing1 {
    background-image: url(../images/leixing1.png);
}

.chengguo_leixing2 {
    background-image: url(../images/leixing2.png);
}

.chengguo_leixing3 {
    background-image: url(../images/leixing3.png);
}

.chengguo_leixing4 {
    background-image: url(../images/leixing4.png);
}

.chengguo_leixing5 {
    background-image: url(../images/leixing5.png);
}

.chengguo_leixing6 {
    background-image: url(../images/leixing6.png);
}

.chengguo_leixing7 {
    background-image: url(../images/leixing7.png);
}

.chengguo_leixing8 {
    background-image: url(../images/leixing8.png);
}

.chengguo_title {
    font-size: 16px;
    color: #333333;
    height: 60px;
    overflow: hidden;
    padding-top: 5%;
    padding-right: 8%;
    padding-bottom: 5%;
    padding-left: 8%;
    font-weight: bold;
}

.lijijiaru {
    font-size: 16px;
    color: #333333;
    padding-top: 0%;
    padding-right: 8%;
    padding-bottom: 0%;
    padding-left: 8%;
}

.lijijiaru a {
    color: #de791b;
}

ul.zhibo_list {
    margin: 0px;
    padding: 0px;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    width: 100%;
}

ul.zhibo_list li {
    width: 27%;
    border-radius: 5px;
    background-color: #fff;
    line-height: 30px;
    margin-top: 15px;
    margin-right: 0px;
    margin-bottom: 15px;
    margin-left: 0px;
    box-shadow: 3px 3px 5px #eee;
    padding-top: 20px;
    padding-right: 2%;
    padding-bottom: 20px;
    padding-left: 2%;
}

ul.zhibo_list li a {
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    color: #666;
}

.zhibo_pic {
    width: 25%;
    margin-top: 18px;
}

.zhibo_pic img {
    border-radius: 50%;
    height: 81px;
    width: 81px;
}

.zhibo_txt {
    width: 68%;
}

.zhibo_title {
    line-height: 30px;
    font-size: 18px;
    height: 60px;
    overflow: hidden;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

ul.xuqiu_index_list {
    margin: 0px;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    width: 100%;
    padding-top: 30px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
}

ul.xuqiu_index_list li {
    width: 29.333%;
    background-color: #fff;
    line-height: 30px;
    margin-top: -1px;
    margin-right: -1px;
    margin-bottom: 0px;
    margin-left: -1px;
    padding-top: 40px;
    padding-right: 2%;
    padding-bottom: 20px;
    padding-left: 2%;
    border: 1px solid #eee;
    position: relative;
}

ul.xuqiu_index_list li a {
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    color: #666;
}

.xuqiu_pic {
    width: 25%;
    margin-top: 0px;
}

.xuqiu_txt {
    width: 68%;
}

.xuqiu_title {
    line-height: 30px;
    font-size: 18px;
    height: 30px;
    overflow: hidden;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

.xuqiu_title:hover {
    color: #2c8ed9
}

.xuqiu_c {
    line-height: 30px;
    font-size: 16px;
    height: 60px;
    overflow: hidden;
    font-weight: normal;
    margin-bottom: 10px;
    color: #666;
}

.zuixin {
    position: absolute;
    right: 0;
    top: 0;
    text-align: center;
    color: #fff;
    background-color: #2c8ed9;
    padding-top: 0px;
    padding-right: 20px;
    padding-bottom: 0px;
    padding-left: 20px;
}

ul.huodong_index_list {
    margin: 0px;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    width: 100%;
    padding-top: 30px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
}

ul.huodong_index_list li {
    background-image: url(../images/huodong1.png);
    background-repeat: repeat-x;
    background-position: left center;
    padding: 0px;
    height: 280px;
    position: relative;
    background-size: cover;
    margin-top: 10px;
    margin-right: 0px;
    margin-bottom: 10px;
    margin-left: 0px;
}

.huodong_a {
    width: 58%
}

.huodong_b {
    width: 40%
}

ul.huodong_index_list li a {
    position: absolute;
    bottom: 30px;
    left: 30px;
    color: #fff;
    font-size: 16px;
    line-height: 30px;
    width: 90%;
}

ul.huodong_index_list li a strong {
    font-size: 20px;
}

.kechuang_index {
    margin: 0px;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    width: 100%;
    padding-top: 30px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
}

.kechuang_index img {
    width: 100%;
}

.kechuang_index_l {
    width: 54%;
}

.kechuang_index_r {
    width: 44%;
}

.kechuang_c {
    line-height: 35px;
}

.kechuang_c_txt {
    font-size: 20px;
    color: #333;
    font-weight: bold;
    line-height: 30px;
    height: 30px;
    padding-top: 0px;
    padding-right: 15px;
    padding-bottom: 0px;
    padding-left: 15px;
    overflow: hidden;
    margin-top: 10px;
    margin-right: 0px;
    margin-bottom: 0px;
    margin-left: 0px;
}

.kechuang_c_txt2 {
    padding-top: 0px;
    padding-right: 15px;
    padding-bottom: 0px;
    padding-left: 15px;
    font-size: 16px;
    font-weight: normal;
    color: #666;
    margin-bottom: 10px;
}

.kechuang_index_list {
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
}

.kechuang_index_list li {
    width: 48%;
}

ul.news_index_list {
    margin: 0px;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    width: 100%;
    padding-top: 30px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
}

ul.news_index_list li {
    width: 30%;
}

.news_title {
    line-height: 25px;
    padding-top: 0px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 20px;
    border-top-width: 0px;
    border-right-width: 0px;
    border-bottom-width: 0px;
    border-left-width: 4px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: solid;
    border-left-style: solid;
    border-top-color: #de791b;
    border-right-color: #de791b;
    border-bottom-color: #de791b;
    border-left-color: #de791b;
    font-size: 18px;
    font-weight: bold;
}

.news_title a {
    padding-left: 20px;
    color: #de791b;
    font-size: 16px;
    font-weight: normal;
}

ul.news_list {
    width: 100%;
    padding-top: 15px;
    padding-right: 0px;
    padding-bottom: 15px;
    padding-left: 0px;
}

ul.news_list li {
    width: 100%;
    line-height: 35px;
}

ul.news_list li img {
    padding-bottom: 10px;
}

ul.news_list li a {
    font-size: 16px;
}

.team {
    text-align: center;
    line-height: 30px;
    margin-top: 0px;
    margin-right: auto;
    margin-bottom: 0px;
    margin-left: auto;
    padding-top: 30px;
}

.team_pic img {
    border-radius: 50%;
    width: 120px;
}

.team a {
    font-size: 16px;
}

.teadm_c {
    text-align: left;
    color: #666;
}

.team_name {
    padding-top: 10px;
    font-weight: bold;
}

.team_zhiwei {
    padding-bottom: 10px;
    font-weight: bold;
}

.bottom_bg {
    background-repeat: no-repeat;
    background-position: center center;
    padding: 0px;
    height: 369px;
    margin-top: 0px;
    margin-right: auto;
    margin-bottom: 0px;
    margin-left: auto;
    background-image: url(../images/bottom_bg.png);
    min-width: 1200px;
    width: 100%;
}

.bottom {
    width: 1200px;
    font-size: 14px;
    color: #fff;
    margin-top: 0;
    margin-right: auto;
    margin-bottom: 0;
    margin-left: auto;
    padding-top: 70px;
}

.bottom a {
    font-size: 14px;
    color: #fff;
}

.bottom_top {
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
}

.bottom1 {
    width: 16%
}

.bottom2 {
    width: 20%;
    font-size: 16px;
}

.bottom3 {
    width: 30%;
    font-size: 18px;
}

.bottom3 a {
    font-size: 18px;
}

.bottom3 a:hover {
    color: #428AFA;
}

.bottom4 {
    width: 10%;
    text-align: right;
    position: relative;
}

.youlian {
    text-align: center;
    padding-top: 20px;
}

.youlian a {
    font-size: 14px;
}

.youlian a:hover {
    color: #428AFA;
}

.banquan {
    text-align: center;
    padding-top: 20px;
}

.pagesel {
    color: #fff !important;
    background-color: #428AFA !important; border:none!important;
}

.main_t {
    text-align: center;
    line-height: 30px;
    font-size: 26px;
    color: #000;

    margin-top: 50px;
    margin-bottom: 30px;
    width: 100%;
}

.main_t i {
    display: block;
    line-height: 50px;
    font-weight: normal;
    font-size: 16px;
    font-style: normal;
    color: #666
}

.main_t span {
    float: right;
}

.main_t span a {
    font-size: 14px;
}

.main_t2 {
    text-align: left;
    line-height: 30px;
    font-size: 18px;
    color: #000;
    font-weight: bold;
    margin-top: 20px;
    margin-bottom: 20px;
    width: 100%;
}

.main_t2 i {
    display: block;
    line-height: 50px;
    font-weight: normal;
    font-size: 16px;
    font-style: normal;
    color: #666
}

.main_t2 span {
    float: right;
}

.main_t2 span a {
    font-size: 14px;
    color: #5283db;
    font-weight: normal;
}

.about_l {
    width: 40%;
    float: left;
}

.about_t {
    padding-top: 20px;
    padding-bottom: 20px;
}

.about_t strong {
    font-size: 24px;
    font-weight: normal;
}

.about_r {
    width: 50%;
    float: right;
}

ul.about_list {
    margin: 0px;
    padding-top: 20px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
}

ul.about_list li {
    margin: 0px;
    padding-top: 10px;
    padding-right: 0px;
    padding-bottom: 10px;
    padding-left: 0px;
    position: relative;
    padding-left: 60px;
}

.about_pic {
    position: absolute;
    left: 0;
    top: 10px;
    width: 50px;
}

ul.about_list li p strong {
    color: #033333;
    font-size: 20px;
}

.more,
.more:hover {
    color: #fff;
    border-radius: 5px;
    background-color: #de791b;
    height: 50px;
    width: 150px;
    display: block;
    text-align: center;
    line-height: 50px;
}

.more2,
.more2:hover {
    color: #fff;
    border-radius: 35px;
    background-color: #de791b;
    height: 60px;
    width: 180px;
    display: block;
    text-align: center;
    line-height: 60px;
    padding: 0px;
    margin-top: 0px;
    margin-right: auto;
    margin-bottom: 0px;
    margin-left: auto;
    font-size: 18px;
    background-image: linear-gradient(to right, #de791b, #f6b476);
    cursor: pointer;
}

.more3,
.more3:hover {
    color: #fff;
    border-radius: 5px;
    background-color: #de791b;
    height: 50px;
    width: 150px;
    text-align: center;
    line-height: 50px;
}

.duijie_l {
    width: 46%;
    float: left;
    padding-top: 20px;
    padding-bottom: 20px;
}

.duijie_r {
    width: 46%;
    float: right;
    padding-top: 20px;
    padding-bottom: 20px;
}

.duijie_t1 {
    border-top-width: 0px;
    border-right-width: 0px;
    border-bottom-width: 0px;
    border-left-width: 2px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: solid;
    border-left-style: solid;
    border-top-color: #de791b;
    border-right-color: #de791b;
    border-bottom-color: #de791b;
    border-left-color: #de791b;
    padding-left: 10px;
    font-size: 16px;
    font-weight: bold;
}

.duijie_t1 span {
    float: right;
}

.duijie_t1 a {
    font-size: 16px;
    color: #de791b;
}

.duijie_t2 {
    border-top-width: 0px;
    border-right-width: 0px;
    border-bottom-width: 0px;
    border-left-width: 2px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: solid;
    border-left-style: solid;
    border-top-color: #1281c7;
    border-right-color: #1281c7;
    border-bottom-color: #1281c7;
    border-left-color: #1281c7;
    padding-left: 10px;
    font-size: 16px;
    font-weight: bold;
}

.duijie_t2 span {
    float: right;
}

.duijie_t2 a {
    font-size: 16px;
    color: #1281c7;
}

ul.duijie_list {
    padding: 0px;
    margin-top: 50px;
    margin-right: 0px;
    margin-bottom: 0px;
    margin-left: 0px;
}

ul.duijie_list2 {
    padding: 0px;
    margin-top: 50px;
    margin-right: 0px;
    margin-bottom: 0px;
    margin-left: 0px;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
}

ul.duijie_list li {
    padding: 0px;
    margin-top: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
    margin-left: 0px;
    line-height: 40px;
    border-top-width: 0px;
    border-right-width: 0px;
    border-bottom-width: 1px;
    border-left-width: 0px;
    border-top-style: dashed;
    border-right-style: dashed;
    border-bottom-style: dashed;
    border-left-style: dashed;
    border-top-color: #eee;
    border-right-color: #eee;
    border-bottom-color: #eee;
    border-left-color: #eee;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
}

ul.duijie_list2 li {
    padding: 0px;
    margin-top: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
    margin-left: 0px;
    line-height: 40px;
    border-top-width: 0px;
    border-right-width: 0px;
    border-bottom-width: 1px;
    border-left-width: 0px;
    border-top-style: dashed;
    border-right-style: dashed;
    border-bottom-style: dashed;
    border-left-style: dashed;
    border-top-color: #eee;
    border-right-color: #eee;
    border-bottom-color: #eee;
    border-left-color: #eee;
    width: 50%;
}

.duijie1,
.duijie5 {
    width: 60%;
    line-height: 40px;
    height: 40px;
    overflow: hidden;
}

.duijie2 {
    width: 20%;
}

.duijie3 {
    width: 20%;
    text-align: right;
}

.duijie3 a {
    color: #de791b;
}

.duijie4 {
    width: 20%;
    text-align: right;
}

.duijie4 a {
    color: #1281c7;
}

.duijie5 a:hover {
    color: #1281c7;
}

.case_tab {
    width: 8%;
    float: left;
    text-align: center;
    font-size: 18px;
    line-height: 50px;
}

.case_tab a {
    font-size: 18px;
    display: block;
    font-weight: bold;
}

.selected3 {
    color: #de791b;
    border-top-width: 0px;
    border-right-width: 0px;
    border-bottom-width: 2px;
    border-left-width: 0px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: solid;
    border-left-style: solid;
    border-top-color: #de791b;
    border-right-color: #de791b;
    border-bottom-color: #de791b;
    border-left-color: #de791b;
}

.case_c {
    width: 85%;
    float: right;
}

.case_title {
    font-size: 24px
}

.case_c_c {
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    padding-top: 15px;
}

.case_neirong {
    width: 48%;
}

ul.case_neirong li {
    padding-top: 15px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 15px;
    background-image: url(../images/line.png);
    background-repeat: no-repeat;
    background-position: left 20px;
}

ul.case_neirong li strong {
    font-size: 18px;
    color: #333333;
}

.case_pic {
    width: 48%;
}

ul.jiqun_list {
    padding: 0px;
    margin-top: 50px;
    margin-right: 0px;
    margin-bottom: 20px;
    margin-left: 0px;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
}

ul.jiqun_list li {
    width: 30%;
    padding-top: 15px;
    padding-bottom: 15px;
}

.news_index_left {
    width: 40%;
    float: left;
}

.news_index_left_title {
    font-size: 24px;
    line-height: 50px;
    padding-bottom: 10px;
}

.news_index_left_title a {
    font-size: 14px;
    color: #de791b;
}

.news_index_left_c img {
    width: 100%;
    padding-bottom: 20px;
}

.news_index_left_c a {
    font-size: 18px;
    line-height: 40px;
}

.news_index_right {
    width: 55%;
    float: right;
}

.news_introduction {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

.news_index_title {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

ul.news_index_list0 {
    margin: 0px;
    width: 100%;
    padding-top: 40px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
}

ul.news_index_list0 li {
    margin: 0px;
    padding-top: 25px;
    padding-right: 0px;
    padding-bottom: 25px;
    padding-left: 0px;
    border-top-width: 0px;
    border-right-width: 0px;
    border-bottom-width: 1px;
    border-left-width: 0px;
    border-top-style: dotted;
    border-right-style: dotted;
    border-bottom-style: dotted;
    border-left-style: dotted;
    border-top-color: #ccc;
    border-right-color: #ccc;
    border-bottom-color: #ccc;
    border-left-color: #ccc;
    font-size: 16px;
    color: #666;
    position: relative;
    padding-left: 100px;
}

ul.news_index_list0 li a {
    font-size: 18px;
    color: #333;
    font-weight: bold;
}

ul.news_index_list0 li a:hover {
    color: #de791b;
}

.date {
    position: absolute;
    left: 0;
    top: 25px;
    width: 90px;
    text-align: center;
    font-size: 16px;
    color: #666;
}

.date strong {
    font-size: 24px;
    color: #333;
}


/*新导航*/


/* 本案例CSS*/

header {
    position: fixed;
    z-index: 9999;
    width: 100%;
    border-bottom: 0px solid #de791b;
    box-sizing: border-box;
}
.son{
    width: 100%;
    height: 36px;
    background-color: #E58331;
    background:repeating-linear-gradient(to right,#f3b078,#ee730f);
    padding: 0 2%;
    box-sizing: border-box;
    color: #fff;

}
.sonBannerall{
    height: 582px;
}
.sonBanner{
    width: 96%;
    position: absolute;

    top: 36px;
    left: 2%;
    background-image: url('../images/sonBanner.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 0 10%;
    box-sizing: border-box;
    z-index: 10000;
}
.black{
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.3;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
}
.sonList{
    width: 18%;
    height: 100px;
    float: left;
    margin-right: 3.5%;
    margin-left: 3.5%;
    font-size: 30px;
    margin-top: 80px;
    cursor: pointer;
}
.wrap {
    width: 1100px;
    margin: 0 auto;
}

#nav {
    display: flex;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

ul.nav {
    display: inline-flex;
    flex: 1;
    padding-left: 50px;
}

li.nav-item {
    flex: 1;
}

li.nav-item>a {
    display: block;
    text-align: center;
    line-height: 100px;
    font-size: 16px;
    position: relative;
}

li.nav-item>a::before {
    display: none;
    content: '';
    position: absolute;
    left: 50%;
    bottom: 0;
    position: absolute;
}


/*li.nav-item>a:0:before{ display: none; content: ''; position: absolute; left: 50%; bottom: 0; transform: translateX(-50%); border-width: 0 10px 10px; border-style: solid; border-color: transparent transparent #de791b; position: absolute;}*/

li.nav-item:hover>a,
li.nav-item.active>a {
    color: #de791b;
    transition: all 1s ease;
}

li.nav-item:hover>a::before,
li.nav-item.active>a::before {
    display: block;
}


/* 二级菜单 */

.subMenu {
    display: none;
    position: absolute;
    top: 80px;
    left: 50%;
    right: 0;
    width: 1200px;
    background-color: #fff;
    margin-left: -600px;
}

.subMenu>ul {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: center;
}

.subMenu>ul li {
    margin-top: 0px;
    margin-right: 10px;
    margin-bottom: 0px;
    margin-left: 10px;
}

.subMenu>ul>li>a {
    display: block;
    text-align: left;
    line-height: 50px;
    color: #de791b;
    margin-right: 10px;
    margin-left: 10px;
    padding-top: 0;
    padding-right: 0px;
    padding-bottom: 0;
    padding-left: 0px;
}

.subMenu>ul>li>a:hover {
    color: #7AB2FF;
    font-weight: bold;
}

.sanji {
    text-align: left;
    border-top-width: 1px;
    border-right-width: 0px;
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: solid;
    border-left-style: solid;
    border-top-color: #ccc;
    border-right-color: #ccc;
    border-bottom-color: #ccc;
    border-left-color: #ccc;
    margin-top: 0px;
    margin-right: 0px;
    margin-bottom: 20px;
    margin-left: 0px;
    padding-bottom: 15px;
    padding-right: 0px;
    padding-left: 0px;
    padding-top: 10px;
}

.sanji li {
    padding: 0;
    margin: 0px;
    line-height: 40px;
}

.sanji a {
    color: #333;
    display: block;
}
ul.newsanji{

	padding-top: 10px;
	padding-bottom: 30px;width: 90%;
	padding-left: 5%;
	padding-right: 5%;
}
ul.newsanji li{
	width: 100%;
	padding-left: 0%;
	padding-right: 0%;
	color: #333;
	font-size: 16px;
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 1px;
	border-left-width: 0px;
	border-top-color: #eee;
	border-right-color: #eee;
	border-bottom-color: #eee;
	border-left-color: #eee;
	line-height: 30px;
	border-top-style: solid;
	border-right-style: solid;
	border-bottom-style: solid;
	border-left-style: solid;
}

ul.newsanji li a{ display:inline-block; padding-left:20px; padding-right:20px; font-size:14px; font-weight:normal; width:250px}
.jjfa {
    position: absolute;
    left: 50%;
    height: 142px;
    width: 228px;
    color: #333;
    background-size: cover;
    padding-top: 50px;
    padding-right: 45px;
    padding-left: 45px;
    line-height: 25px;
}

.jjfa strong {
    font-size: 16px;
}

.jjfa:hover {
    background-image: url(../images/fangan_bg.png);
    background-repeat: no-repeat;
    background-position: center center;
    color: #fff;
}

.jjfa1 {
    margin-left: -334px;
    top: 45px;
}

.jjfa2 {
    margin-left: 21px;
    top: 45px;
}

.jjfa3 {
    margin-left: -514px;
    top: 205px;
}

.jjfa4 {
    margin-left: -160px;
    top: 205px;
}

.jjfa5 {
    margin-left: 190px;
    top: 205px;
}

.jjfa6 {
    margin-left: -689px;
    top: 375px;
}

.jjfa7 {
    margin-left: -337px;
    top: 375px;
}

.jjfa8 {
    margin-left: 20px;
    top: 375px;
}

.jjfa9 {
    margin-left: 372px;
    top: 375px;
}


/*提示*/

.tishi_bg1,
.tishi_bg2,
.tishi_bg3,
.tishi_bg4,
.tishi_bg5 {
    position: fixed;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10;
    top: 0;
    left: 0;
}

.tishi {
    width: 820px;
    position: relative;
    margin-top: 10%;
    margin-right: auto;
    margin-bottom: 0;
    margin-left: auto;
    background-color: #fff;
    padding-top: 50px;
    padding-right: 80px;
    padding-bottom: 50px;
    padding-left: 80px;
    border-radius: 5px;
}

.tishi_t {
    text-align: center;
    padding-bottom: 15px;
}

.tishi_t strong {
    font-size: 24px;
}

.tishi_c {
    padding-top: 20px;
}

.text11 {
    width: 47%;
    line-height: 48px;
    margin-top: 0px;
    margin-right: auto;
    margin-bottom: 0px;
    margin-left: auto;
    border: 1px solid #ccc;
    outline: none;
    padding-top: 0px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 3%;
    border-radius: 5px;
}

.wancheng,
.wancheng:hover {
    width: 50%;
    color: #fff;
    text-align: center;
    background-color: #de791b;
    border-radius: 5px;
    display: block;
    line-height: 50px;
    padding: 0px;
    margin-top: 30px;
    margin-right: auto;
    margin-bottom: 0px;
    margin-left: auto;
}

.close {
    position: absolute;
    right: 20px;
    top: 15px;
    color: #de791b
}

.next {
    margin-top: 20px;
    margin-right: 0px;
    margin-left: auto;
    display: block;
}

ul.qiye_list {
    margin: 0px;
    padding: 0px;
}

ul.qiye_list li {
    margin: 0px;
    padding: 15px;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    background-color: #fdf9f5;
    border-top-width: 0px;
    border-right-width: 0px;
    border-bottom-width: 2px;
    border-left-width: 0px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: solid;
    border-left-style: solid;
    border-top-color: #eee;
    border-right-color: #eee;
    border-bottom-color: #eee;
    border-left-color: #eee;
}

.qiye1 {
    width: 180px;
}

.qiye2 {
    width: 300px;
}

.qiye3 {
    background-color: #fdeedf;
    line-height: 30px;
    height: 30px;
    padding-top: 0px;
    padding-right: 15px;
    padding-bottom: 0px;
    padding-left: 15px;
}

.qiye4 {
    background-color: #dceafc;
    line-height: 30px;
    height: 30px;
    padding-top: 0px;
    padding-right: 15px;
    padding-bottom: 0px;
    padding-left: 15px;
}

.qiye5 {
    padding-top: 0px;
    padding-right: 0px;
    padding-bottom: 15px;
    padding-left: 0px;
    border: 1px solid #eee;
}

.qiye5 a {
    color: #e87c14;
}

.shenfen {
    padding-top: 0px;
    padding-right: 0px;
    padding-bottom: 10px;
    padding-left: 0px;
    border-bottom-width: 1px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: solid;
    border-left-style: solid;
    border-top-color: #eee;
    border-right-color: #eee;
    border-bottom-color: #eee;
    border-left-color: #eee;
}

ul.shenfen_list {
    margin: 0px;
    padding: 0px;
    padding-right: 20px;
    overflow-x: hidden;
    overflow-y: scroll;
    height: 300px;
    float: left;
    width: 100px;
}

ul.shenfen_list li {
    margin: 0px;
    line-height: 40px;
    text-align: center;
    padding-top: 0px;
    padding-right: 0px;
    padding-bottom: 1px;
    padding-left: 0px;
}

ul.shenfen_list li a {
    font-size: 14px;
    font-weight: bold;
    display: block;
}

.selected8,
ul.shenfen_list li a:hover {
    color: #fff;
    background-color: #e87c14;
}

ul.erji {
    width: 650px;
    float: right;
    margin: 0px;
    padding: 0px;
}

ul.erji li {
    float: none;
}

ul.erji li a {
    font-weight: bold;
}

ul.erji li ul {
    padding-top: 10px;
    padding-bottom: 10px;
}

ul.erji li ul li {
    float: left;
}

ul.erji li ul li a {
    font-weight: normal;
    padding-top: 0px;
    padding-right: 10px;
    padding-bottom: 0px;
    padding-left: 10px;
}

.other {
    font-size: 14px;
    padding-top: 10px;
}

.other span {
    float: right;
}


/*客服*/

.fl {
    float: left;
}

.fr {
    float: right;
}

.tl {
    text-align: left;
}

.tr {
    text-align: right;
}

.tc {
    text-align: center;
}

.color-white {
    color: white!important;
}

.red {
    color: #fc8080!important;
}

.fz12 {
    font-size: 12px;
}

.fz14 {
    font-size: 14px;
}

.wfs {
    width: 100%;
    position: relative;
}

.p_r {
    position: relative;
}


/*悬浮链接*/

.suspension {
    position: fixed;
    z-index: 55;
    right: 0;
    bottom: 85px;
    width: 70px;
    height: 240px;
}

.suspension-box {
    position: relative;
    float: right;
}

.suspension .a {
    display: block;
    width: 44px;
    height: 44px;
    background-color: #353535;
    margin-bottom: 4px;
    cursor: pointer;
    outline: none;
}

.suspension .a.active,
.suspension .a:hover {
    background: #063c78;
}

.suspension .a .i {
    float: left;
    width: 44px;
    height: 44px;
    background-image: url(../images/side_icon.png);
    background-repeat: no-repeat;
}


/* .suspension .a-service .i{background-position:0 0;} */

.suspension .a-service .i {
    width: 20px;
    height: 20px;
    margin-top: 12px;
    margin-left: 12px;
    background-image: url(../images/suspension-bg.png);
    background-repeat: no-repeat;
    background-position: 0 0;
}

.suspension .a-service-fabu .i {
    width: 20px;
    height: 20px;
    margin-top: 12px;
    margin-left: 12px;
    background-image: url(../images/fabu.png);
    background-repeat: no-repeat;
    background-size: contain;
}

.suspension .a-service-phone .i {
    width: 20px;
    height: 20px;
    margin-top: 12px;
    margin-left: 12px;
    background-image: url(../images/suspension-bg.png);
    background-repeat: no-repeat;
    background-position: -27px 0;
}

.suspension .a-qrcode .i {
    background-position: -44px 0;
}

.suspension .a-cart .i {
    background-position: -88px 0;
}

.suspension .a-top .i {
    background-position: -132px 0;
}

.suspension .a-top {
    background: #D2D3D6;
    display: none;
}

.suspension .a-top:hover {
    background: #c0c1c3;
}

.suspension .d {
    display: none;
    width: 223px;
    background: #fff;
    position: absolute;
    right: 67px;
    min-height: 90px;
    border: 1px solid #E0E1E5;
    border-radius: 3px;
    box-shadow: 0px 2px 5px 0px rgba(161, 163, 175, 0.11);
}

.suspension .d .arrow {
    position: absolute;
    width: 8px;
    height: 12px;
    background: url(../images/side_bg_arrow.png) no-repeat;
    right: -8px;
    top: 31px;
}

.suspension .d-service {
    top: 0;
}

.suspension .d-service-fabu {
    top: 34px;
}

.suspension .d-service-wechat {
    top: 54px;
}

.suspension .d-qrcode {
    top: 78px;
}

.suspension .d .inner-box {
    padding: 8px 22px 12px;
}

.suspension .d-service-item {
    border-bottom: 0px solid #eee;
    padding: 14px 0;
}

.suspension .d-service .d-service-item {
    border-bottom: none;
}

.suspension .d-service-item .circle {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    overflow: hidden;
    background: #fff;
    display: block;
    float: left;
}

.suspension .d-service-item .i-qq {
    width: 44px;
    height: 44px;
    background: url(../images/side_con_icon03.png) no-repeat center 15px;
    display: block;
    transition: all .2s;
    border-radius: 50%;
    overflow: hidden;
}

.suspension .d-service-item:hover .i-qq {
    background-position: center 3px;
}

.suspension .d-service-item .i-fabu {
    width: 44px;
    height: 44px;
    background: url(../images/fabu2.png) no-repeat center 15px;
    display: block;
    transition: all .2s;
    border-radius: 50%;
    overflow: hidden;
    background-size: 20px;
}

.suspension .d-service-item:hover .i-fabu {
    background-position: center center;
}

.suspension .d-service-item .i-tel {
    width: 44px;
    height: 44px;
    background: url(../images/side_con_icon02.png) no-repeat center center;
    display: block;
}

.suspension .d-service-item h3 {
    float: left;
    width: 112px;
    line-height: 44px;
    font-size: 15px;
    margin-left: 12px;
}

.suspension .d-service-item .text {
    float: left;
    width: 112px;
    line-height: 22px;
    font-size: 15px;
    margin-left: 12px;
}

.suspension .d-service-item .text .number2 {
    font-family: Arial, "Microsoft Yahei", "HanHei SC", PingHei, "PingFang SC", "Helvetica Neue", Helvetica, Arial, "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
}

.suspension .d-service-intro {
    padding-top: 10px;
}

.suspension .d-service-intro p {
    float: left;
    line-height: 27px;
    font-size: 12px;
    width: 50%;
    white-space: nowrap;
    color: #888;
}

.suspension .d-service-intro i {
    background: url(../images/side_con_icon01.png) no-repeat center center;
    height: 27px;
    width: 14px;
    margin-right: 5px;
    vertical-align: top;
    display: inline-block;
}

.suspension .d-qrcode {
    text-align: center;
}

.suspension .d-qrcode .inner-box {
    padding: 20px 0;
}

.suspension .d-qrcode p {
    font-size: 16px;
    color: #93959c;
}

.jrgzt,
.jrgzt:hover {
    line-height: 30px;
    color: #fff;
    background-color: #de7a1c;
    padding-top: 5px;
    padding-right: 10px;
    padding-bottom: 5px;
    padding-left: 10px;
}

.ljjr,
.ljjr:hover {
    color: #fff;
    background-color: #0039a0;
    padding-top: 5px;
    padding-right: 15px;
    padding-bottom: 5px;
    padding-left: 15px;
    border-radius: 5px;
    position: absolute;
    top: 20px;
    left: 50%;
    margin-left: 550px;
}
