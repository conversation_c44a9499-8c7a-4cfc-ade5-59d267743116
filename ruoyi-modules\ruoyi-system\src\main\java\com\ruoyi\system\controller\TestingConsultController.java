package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.TestingConsult;
import com.ruoyi.system.service.ITestingConsultService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 检测咨询Controller
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@RestController
@RequestMapping("/testingConsult")
public class TestingConsultController extends BaseController
{
    @Autowired
    private ITestingConsultService testingConsultService;

    /**
     * 查询检测咨询列表
     */

    @GetMapping("/list")
    public TableDataInfo list(TestingConsult testingConsult)
    {
        startPage();
        List<TestingConsult> list = testingConsultService.selectTestingConsultList(testingConsult);
        return getDataTable(list);
    }

    /**
     * 导出检测咨询列表
     */

    @Log(title = "检测咨询", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TestingConsult testingConsult)
    {
        List<TestingConsult> list = testingConsultService.selectTestingConsultList(testingConsult);
        ExcelUtil<TestingConsult> util = new ExcelUtil<TestingConsult>(TestingConsult.class);
        util.exportExcel(response, list, "检测咨询数据");
    }

    /**
     * 获取检测咨询详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(testingConsultService.selectTestingConsultById(id));
    }

    /**
     * 新增检测咨询
     */

    @Log(title = "检测咨询", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TestingConsult testingConsult)
    {
        return toAjax(testingConsultService.insertTestingConsult(testingConsult));
    }

    /**
     * 修改检测咨询
     */

    @Log(title = "检测咨询", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TestingConsult testingConsult)
    {
        return toAjax(testingConsultService.updateTestingConsult(testingConsult));
    }

    /**
     * 删除检测咨询
     */

    @Log(title = "检测咨询", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(testingConsultService.deleteTestingConsultByIds(ids));
    }
}
