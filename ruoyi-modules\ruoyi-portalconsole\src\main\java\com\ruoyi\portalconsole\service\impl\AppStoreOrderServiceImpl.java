package com.ruoyi.portalconsole.service.impl;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalconsole.domain.AppStoreOrder;
import com.ruoyi.portalconsole.domain.vo.AppStoreOrderVO;
import com.ruoyi.portalconsole.mapper.AppStoreMapper;
import com.ruoyi.portalconsole.mapper.AppStoreOrderMapper;
import com.ruoyi.portalconsole.service.IAppStoreOrderService;
import com.ruoyi.portalconsole.utils.NumberGeneraterUtil;
import com.ruoyi.portalconsole.domain.vo.AppStoreVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 应用商店订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class AppStoreOrderServiceImpl implements IAppStoreOrderService {
    @Autowired
    private AppStoreOrderMapper appStoreOrderMapper;
    @Autowired
    private AppStoreMapper appStoreMapper;

    /**
     * 查询应用商店订单
     *
     * @param appStoreOrderId 应用商店订单主键
     * @return 应用商店订单
     */
    @Override
    public AppStoreOrderVO selectAppStoreOrderByAppStoreOrderId(Long appStoreOrderId) {
        return appStoreOrderMapper.selectAppStoreOrderByAppStoreOrderId(appStoreOrderId);
    }

    /**
     * 查询应用商店订单列表
     *
     * @param appStoreOrder 应用商店订单
     * @return 应用商店订单
     */
    @Override
    public List<AppStoreOrderVO> selectAppStoreOrderList(AppStoreOrder appStoreOrder) {
        return appStoreOrderMapper.selectAppStoreOrderList(appStoreOrder);
    }

    /**
     * 新增应用商店订单
     *
     * @param appStoreOrder 应用商店订单
     * @return 结果
     */
    @Override
    public int insertAppStoreOrder(AppStoreOrder appStoreOrder) {
        if (Objects.isNull(appStoreOrder.getAppStoreId())) {
            throw new ServiceException("应用id不能为空");
        }
        //校验应用
        AppStoreVO appStoreVO = appStoreMapper.selectAppStoreByAppStoreId(appStoreOrder.getAppStoreId());
        if (appStoreVO == null) {
            throw new ServiceException("未找到应用");
        }
        appStoreOrder.setSaleMemberId(appStoreVO.getCompanyId());
        appStoreOrder.setBuyMemberId(SecurityUtils.getUserId());
        appStoreOrder.setOrderTime(DateUtils.getNowDate());
        appStoreOrder.setAppStorePrice(appStoreVO.getAppStorePrice());
        appStoreOrder.setAppStoreOrderNo(NumberGeneraterUtil.getInstance().generateTimeStampCode("AS"));
        appStoreOrder.setCreateTime(DateUtils.getNowDate());
        return appStoreOrderMapper.insertAppStoreOrder(appStoreOrder);
    }

    /**
     * 修改应用商店订单
     *
     * @param appStoreOrder 应用商店订单
     * @return 结果
     */
    @Override
    public int updateAppStoreOrder(AppStoreOrder appStoreOrder) {
        appStoreOrder.setUpdateTime(DateUtils.getNowDate());
        return appStoreOrderMapper.updateAppStoreOrder(appStoreOrder);
    }

    /**
     * 批量删除应用商店订单
     *
     * @param appStoreOrderIds 需要删除的应用商店订单主键
     * @return 结果
     */
    @Override
    public int deleteAppStoreOrderByAppStoreOrderIds(Long[] appStoreOrderIds) {
        return appStoreOrderMapper.deleteAppStoreOrderByAppStoreOrderIds(appStoreOrderIds);
    }

    /**
     * 删除应用商店订单信息
     *
     * @param appStoreOrderId 应用商店订单主键
     * @return 结果
     */
    @Override
    public int deleteAppStoreOrderByAppStoreOrderId(Long appStoreOrderId) {
        return appStoreOrderMapper.deleteAppStoreOrderByAppStoreOrderId(appStoreOrderId);
    }
}
