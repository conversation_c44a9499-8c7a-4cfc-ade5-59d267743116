package com.ruoyi.portalconsole.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalconsole.domain.Message;
import com.ruoyi.portalconsole.domain.vo.MemberTelephoneVO;
import com.ruoyi.portalconsole.enums.MessageStatus;
import com.ruoyi.portalconsole.enums.Title;
import com.ruoyi.portalconsole.service.IMemberService;
import com.ruoyi.portalconsole.service.IMessageService;
import com.ruoyi.system.api.RemoteUserService;
import org.bouncycastle.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.PolicySubmitMapper;
import com.ruoyi.portalconsole.domain.PolicySubmit;
import com.ruoyi.portalconsole.service.IPolicySubmitService;

/**
 * 政策申报Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class PolicySubmitServiceImpl implements IPolicySubmitService 
{
    @Autowired
    private PolicySubmitMapper policySubmitMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private IMessageService messageService;

    @Autowired
    private IMemberService memberService;

    /**
     * 查询政策申报
     * 
     * @param policySubmitId 政策申报主键
     * @return 政策申报
     */
    @Override
    public PolicySubmit selectPolicySubmitByPolicySubmitId(Long policySubmitId)
    {
        return policySubmitMapper.selectPolicySubmitByPolicySubmitId(policySubmitId);
    }

    /**
     * 查询政策申报列表
     * 
     * @param policySubmit 政策申报
     * @return 政策申报
     */
    @Override
    public List<PolicySubmit> selectPolicySubmitList(PolicySubmit policySubmit)
    {
        return policySubmitMapper.selectPolicySubmitList(policySubmit);
    }

    /**
     * 新增政策申报
     * 
     * @param policySubmit 政策申报
     * @return 结果
     */
    @Override
    public int insertPolicySubmit(PolicySubmit policySubmit)
    {
        String[] labels = Strings.split(policySubmit.getPolicyLabel(), ',');
        List<MemberTelephoneVO> memberTelephoneVOS = memberService.selectMemberIdList(labels);
        List<String> phones = new ArrayList<String>();
        for (MemberTelephoneVO memberTelephoneVO : memberTelephoneVOS){
           phones.add(memberTelephoneVO.getMemberPhone());
        }

        // 发送短信息
        messageService.sendPhoneMessage(phones);

        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        policySubmit.setUpdateBy(userNickName.getData());
        policySubmit.setCreateBy(userNickName.getData());
        policySubmit.setCreateTime(DateUtils.getNowDate());
        return policySubmitMapper.insertPolicySubmit(policySubmit);
    }

    /**
     * 修改政策申报
     * 
     * @param policySubmit 政策申报
     * @return 结果
     */
    @Override
    public int updatePolicySubmit(PolicySubmit policySubmit)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        policySubmit.setUpdateBy(userNickName.getData());
        policySubmit.setUpdateTime(DateUtils.getNowDate());
        return policySubmitMapper.updatePolicySubmit(policySubmit);
    }

    /**
     * 审核政策申报
     *
     * @param policySubmit 政策申报
     * @return 结果
     */
    @Override
    public int auditPolicySubmit(PolicySubmit policySubmit)
    {
        // 查询政策申报信息
        PolicySubmit policySubmit1 = selectPolicySubmitByPolicySubmitId(policySubmit.getPolicySubmitId());

        // 修改审核状态
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        PolicySubmit audit = new PolicySubmit();
        audit.setUpdateBy(userNickName.getData());
        audit.setPolicySubmitStatus(policySubmit.getPolicySubmitStatus());
        audit.setPolicySubmitId(policySubmit.getPolicySubmitId());
        int i = updatePolicySubmit(audit);

        // 通知用户消息
        Message message = new Message().initData(Title.POLICY.getName(),"您提交的"+ Title.POLICY.getName() +"已审核", MessageStatus.UNREAD.getCode(),policySubmit1.getMemberId());
        messageService.insertMessageByAsync(message);
        policySubmit.setUpdateTime(DateUtils.getNowDate());
        return i;
    }

    /**
     * 批量删除政策申报
     * 
     * @param policySubmitIds 需要删除的政策申报主键
     * @return 结果
     */
    @Override
    public int deletePolicySubmitByPolicySubmitIds(Long[] policySubmitIds)
    {
        return policySubmitMapper.deletePolicySubmitByPolicySubmitIds(policySubmitIds);
    }

    /**
     * 删除政策申报信息
     * 
     * @param policySubmitId 政策申报主键
     * @return 结果
     */
    @Override
    public int deletePolicySubmitByPolicySubmitId(Long policySubmitId)
    {
        return policySubmitMapper.deletePolicySubmitByPolicySubmitId(policySubmitId);
    }
}
