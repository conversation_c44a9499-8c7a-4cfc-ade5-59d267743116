package com.ruoyi.portalweb.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.SolutionPain;
import com.ruoyi.portalweb.service.ISolutionPainService;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 解决方案行业痛点Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/SolutionPain")
public class SolutionPainController extends BaseController
{
    @Autowired
    private ISolutionPainService solutionPainService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询解决方案行业痛点列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SolutionPain solutionPain)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        PageUtils.getLocalPage().setPageSize(10000);
        List<SolutionPain> list = solutionPainService.selectSolutionPainList(solutionPain);
        return getDataTable(list);
    }

    /**
     * 导出解决方案行业痛点列表
     */
    @Log(title = "解决方案行业痛点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SolutionPain solutionPain)
    {
        List<SolutionPain> list = solutionPainService.selectSolutionPainList(solutionPain);
        ExcelUtil<SolutionPain> util = new ExcelUtil<SolutionPain>(SolutionPain.class);
        util.exportExcel(response, list, "解决方案行业痛点数据");
    }

    /**
     * 获取解决方案行业痛点详细信息
     */
    @GetMapping(value = "/{solutionPainId}")
    public AjaxResult getInfo(@PathVariable("solutionPainId") Long solutionPainId)
    {
        return success(solutionPainService.selectSolutionPainBySolutionPainId(solutionPainId));
    }

    /**
     * 新增解决方案行业痛点
     */
    @Log(title = "解决方案行业痛点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SolutionPain solutionPain)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        solutionPain.setUpdateBy(userNickName.getData());
        solutionPain.setCreateBy(userNickName.getData());
        return toAjax(solutionPainService.insertSolutionPain(solutionPain));
    }

    /**
     * 修改解决方案行业痛点
     */
    @Log(title = "解决方案行业痛点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SolutionPain solutionPain)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        solutionPain.setUpdateBy(userNickName.getData());
        return toAjax(solutionPainService.updateSolutionPain(solutionPain));
    }

    /**
     * 删除解决方案行业痛点
     */
    @Log(title = "解决方案行业痛点", businessType = BusinessType.DELETE)
	@DeleteMapping("/{solutionPainIds}")
    public AjaxResult remove(@PathVariable Long[] solutionPainIds)
    {
        return toAjax(solutionPainService.deleteSolutionPainBySolutionPainIds(solutionPainIds));
    }
}
