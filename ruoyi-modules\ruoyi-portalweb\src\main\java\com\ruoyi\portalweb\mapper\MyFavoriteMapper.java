package com.ruoyi.portalweb.mapper;

import java.util.List;
import com.ruoyi.portalweb.api.domain.MyFavorite;
import com.ruoyi.portalweb.vo.MyFavoriteVO;
import org.apache.ibatis.annotations.Param;

/**
 * 我的收藏Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-25
 */
public interface MyFavoriteMapper 
{
    /**
     * 查询我的收藏
     * 
     * @param myFavoriteId 我的收藏主键
     * @return 我的收藏
     */
    public MyFavoriteVO selectMyFavoriteByMyFavoriteId(Long myFavoriteId);

    /**
     * 查询我的收藏列表
     * 
     * @param myFavorite 我的收藏
     * @return 我的收藏集合
     */
    public List<MyFavoriteVO> selectMyFavoriteList(MyFavorite myFavorite);

    /**
     * 新增我的收藏
     * 
     * @param myFavorite 我的收藏
     * @return 结果
     */
    public int insertMyFavorite(MyFavorite myFavorite);

    /**
     * 修改我的收藏
     * 
     * @param myFavorite 我的收藏
     * @return 结果
     */
    public int updateMyFavorite(MyFavorite myFavorite);

    /**
     * 删除我的收藏
     * 
     * @param myFavoriteId 我的收藏主键
     * @return 结果
     */
    public int deleteMyFavoriteByMyFavoriteId(Long myFavoriteId);

    /**
     * 批量删除我的收藏
     * 
     * @param myFavoriteIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMyFavoriteByMyFavoriteIds(Long[] myFavoriteIds);

    public MyFavoriteVO selectMyFavoriteByIssueId(MyFavorite myFavorite);

    Integer updateMyFavoriteStatus( @Param("issueId")Long issueId, @Param("favoriteType")String type, @Param("status")String status);
}
