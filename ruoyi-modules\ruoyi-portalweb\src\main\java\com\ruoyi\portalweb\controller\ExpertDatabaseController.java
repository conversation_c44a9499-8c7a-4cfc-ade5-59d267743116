package com.ruoyi.portalweb.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.ExpertDatabase;
import com.ruoyi.portalweb.service.IExpertDatabaseService;
import com.ruoyi.portalweb.vo.ExpertDatabaseVO;
import com.ruoyi.system.api.RemoteUserService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 专家库Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/ExpertDatabase")
public class ExpertDatabaseController extends BaseController
{
    @Autowired
    private IExpertDatabaseService expertDatabaseService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询专家库列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ExpertDatabaseVO expertDatabase)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<ExpertDatabase> list = expertDatabaseService.selectExpertDatabaseList(expertDatabase);
        return getDataTable(list);
    }

    /**
     * 查询专家库列表
     */
    @GetMapping("/listDesk")
    public TableDataInfo listDesk(ExpertDatabaseVO expertDatabase)
    {
        startPage();
        PageUtils.setOrderBy("create_time DESC");
        List<ExpertDatabase> list = expertDatabaseService.selectExpertDatabaseList(expertDatabase);
        return getDataTable(list);
    }

    /**
     * 导出专家库列表
     */
    @Log(title = "专家库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExpertDatabaseVO expertDatabase)
    {
        List<ExpertDatabase> list = expertDatabaseService.selectExpertDatabaseList(expertDatabase);
        ExcelUtil<ExpertDatabase> util = new ExcelUtil<ExpertDatabase>(ExpertDatabase.class);
        util.exportExcel(response, list, "专家库数据");
    }

//    /**
//     * 获取专家库详细信息
//     */
//    @GetMapping(value = "/{expertDatabaseId}")
//    public AjaxResult getInfo(@PathVariable("expertDatabaseId") Long expertDatabaseId)
//    {
//        return success(expertDatabaseService.selectExpertDatabaseByExpertDatabaseId(expertDatabaseId));
//    }

    /**
     * 获取专家库详细信息
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取专家库详细信息", notes = "传入")
    public AjaxResult detail(ExpertDatabase expertDatabase) {
        return success(expertDatabaseService.selectExpertDatabaseByExpertDatabaseId(expertDatabase.getExpertDatabaseId()));
    }

    /**
     * 获取服务需求(NEW)详细信息
     */
    @GetMapping("/detailDesk")
    @ApiOperation(value = "获取专家库详细信息", notes = "传入")
    public AjaxResult detailDesk(ExpertDatabase expertDatabase) {
        return success(expertDatabaseService.selectExpertDatabaseByExpertDatabaseId(expertDatabase.getExpertDatabaseId()));
    }

    /**
     * 新增专家库
     */
    @Log(title = "专家库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExpertDatabase expertDatabase)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        expertDatabase.setUpdateBy(userNickName.getData());
        expertDatabase.setCreateBy(userNickName.getData());
        return toAjax(expertDatabaseService.insertExpertDatabase(expertDatabase));
    }

    /**
     * 修改专家库
     */
    @Log(title = "专家库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExpertDatabase expertDatabase)
    {
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        expertDatabase.setUpdateBy(userNickName.getData());
        return toAjax(expertDatabaseService.updateExpertDatabase(expertDatabase));
    }

    /**
     * 删除专家库
     */
    @Log(title = "专家库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{expertDatabaseIds}")
    public AjaxResult remove(@PathVariable Long[] expertDatabaseIds)
    {
        return toAjax(expertDatabaseService.deleteExpertDatabaseByExpertDatabaseIds(expertDatabaseIds));
    }
}
