package com.ruoyi.auth.util.result;

import java.io.Serializable;
import java.util.List;

public class TableResult<T> implements Serializable {

	private static final long serialVersionUID = 1L;
	private Integer code = 0;
	private String msg = "";
	private List<T> data;
	private long count;

	public TableResult(){

	}

	public TableResult(List<T> data, long count) {
		this.data = data;
		this.count = count;
		this.code = 0;
		this.msg = "";
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public List<T> getData() {
		return data;
	}

	public void setData(List<T> data) {
		this.data = data;
	}

	public long getCount() {
		return count;
	}

	public void setCount(long count) {
		this.count = count;
	}
}
