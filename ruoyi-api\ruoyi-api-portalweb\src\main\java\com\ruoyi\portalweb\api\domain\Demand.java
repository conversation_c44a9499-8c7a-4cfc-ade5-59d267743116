package com.ruoyi.portalweb.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 服务需求(NEW)对象 demand
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public class Demand extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @ApiModelProperty(value = "主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /** 公司名称 */
    @Excel(name = "公司名称")
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contact;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /** 需求标题 */
    @Excel(name = "需求标题")
    @ApiModelProperty(value = "需求标题")
    private String title;

    /** 需求类型 */
    @Excel(name = "需求类型")
    @ApiModelProperty(value = "需求标题")
    private String type;

    /** 需求概述 */
    @Excel(name = "需求概述")
    @ApiModelProperty(value = "需求概述")
    private String description;

    /** 应用领域 */
    @Excel(name = "应用领域")
    @ApiModelProperty(value = "应用领域")
    private String applicationArea;

    /** 产品照片 */
    @Excel(name = "产品照片")
    @ApiModelProperty(value = "产品照片")
    private String imageUrl;

//    /** 招标类型 */
//    @Excel(name = "招标类型")
//    @ApiModelProperty(value = "招标类型")
//    private String bidType;
//
//    /** 定向企业 */
//    @Excel(name = "定向企业")
//    @ApiModelProperty(value = "定向企业")
//    private String targetCompany;
//
//    /** 招标开始时间 */
//    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "招标开始时间", width = 30, dateFormat = "yyyy-MM-dd")
//    @ApiModelProperty(value = "招标开始时间")
//    private Date bidStartTime;
//
//    /** 招标结束时间 */
//    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "招标结束时间", width = 30, dateFormat = "yyyy-MM-dd")
//    @ApiModelProperty(value = "招标结束时间")
//    private Date bidEndTime;
//
//    /** 截止时间 */
//    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "截止时间", width = 30, dateFormat = "yyyy-MM-dd")
//    @ApiModelProperty(value = "截止时间")
//    private Date bidDeadline;
//
//    /** 附件 */
//    @Excel(name = "附件")
//    @ApiModelProperty(value = "附件")
//    private String attachment;
//
//    /** 发布人 */
//    @Excel(name = "发布人")
//    @ApiModelProperty(value = "发布人")
//    private String publisher;

    /** 审核状态 */
    @Excel(name = "审核状态")
    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    /** 展示限制 */
    @Excel(name = "展示限制")
    @ApiModelProperty(value = "展示限制")
    private String visible;

    /** 推荐状态 */
    @Excel(name = "推荐状态")
    @ApiModelProperty(value = "推荐状态")
    private Long recommend;

    /** 处理状态 */
    @Excel(name = "处理状态")
    @ApiModelProperty(value = "处理状态")
    private String processStatus;

    /** 显示状态: 0:上架 1:下架 */
    @Excel(name = "显示状态: 0:上架 1:下架")
    @ApiModelProperty(value = "显示状态: 0:上架 1:下架")
    private Integer onShow;

//    /** 意向企业 */
//    @Excel(name = "意向企业")
//    @ApiModelProperty(value = "意向企业")
//    private String prospectiveCorp;
//
//    /** 自动发布: 0:否 1:是 */
//    @Excel(name = "自动发布: 0:否 1:是")
//    @ApiModelProperty(value = "自动发布: 0:否 1:是")
//    private Integer autoPublish;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 会员id */
    @Excel(name = "会员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long memberId;

    /** 查看次数 */
    @Excel(name = "查看次数")
    @ApiModelProperty(value = "查看次数")
    private Long viewCount;

    public Long getViewCount() {return viewCount;}

    public void setViewCount(Long viewCount) {this.viewCount = viewCount;}

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    public void setContact(String contact) 
    {
        this.contact = contact;
    }

    public String getContact() 
    {
        return contact;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setApplicationArea(String applicationArea) 
    {
        this.applicationArea = applicationArea;
    }

    public String getApplicationArea() 
    {
        return applicationArea;
    }
    public void setImageUrl(String imageUrl) 
    {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl() 
    {
        return imageUrl;
    }
//    public void setBidType(String bidType)
//    {
//        this.bidType = bidType;
//    }
//
//    public String getBidType()
//    {
//        return bidType;
//    }
//    public void setTargetCompany(String targetCompany)
//    {
//        this.targetCompany = targetCompany;
//    }
//
//    public String getTargetCompany()
//    {
//        return targetCompany;
//    }
//    public void setBidStartTime(Date bidStartTime)
//    {
//        this.bidStartTime = bidStartTime;
//    }
//
//    public Date getBidStartTime()
//    {
//        return bidStartTime;
//    }
//    public void setBidEndTime(Date bidEndTime)
//    {
//        this.bidEndTime = bidEndTime;
//    }
//
//    public Date getBidEndTime()
//    {
//        return bidEndTime;
//    }
//    public void setBidDeadline(Date bidDeadline)
//    {
//        this.bidDeadline = bidDeadline;
//    }
//
//    public Date getBidDeadline()
//    {
//        return bidDeadline;
//    }
//    public void setattachment(String attachment)
//    {
//        this.attachment = attachment;
//    }
//
//    public String getattachment()
//    {
//        return attachment;
//    }
//    public void setPublisher(String publisher)
//    {
//        this.publisher = publisher;
//    }
//
//    public String getPublisher()
//    {
//        return publisher;
//    }
    public void setAuditStatus(String auditStatus)
    {
        this.auditStatus = auditStatus;
    }

    public String getAuditStatus()
    {
        return auditStatus;
    }
    public void setVisible(String visible) 
    {
        this.visible = visible;
    }

    public String getVisible() 
    {
        return visible;
    }
    public void setRecommend(Long recommend) 
    {
        this.recommend = recommend;
    }

    public Long getRecommend()
    {
        return recommend;
    }
    public void setProcessStatus(String processStatus)
    {
        this.processStatus = processStatus;
    }

    public String getProcessStatus()
    {
        return processStatus;
    }
    public void setOnShow(Integer onShow)
    {
        this.onShow = onShow;
    }

    public Integer getOnShow()
    {
        return onShow;
    }
//    public void setProspectiveCorp(String prospectiveCorp)
//    {
//        this.prospectiveCorp = prospectiveCorp;
//    }
//
//    public String getProspectiveCorp()
//    {
//        return prospectiveCorp;
//    }
//    public void setAutoPublish(Integer autoPublish)
//    {
//        this.autoPublish = autoPublish;
//    }
//
//    public Integer getAutoPublish()
//    {
//        return autoPublish;
//    }
    public void setCreateBy(String createBy) 
    {
        this.createBy = createBy;
    }

    public String getCreateBy() 
    {
        return createBy;
    }
    public void setCreateTime(Date createTime) 
    {
        this.createTime = createTime;
    }

    public Date getCreateTime() 
    {
        return createTime;
    }
    public void setUpdateBy(String updateBy) 
    {
        this.updateBy = updateBy;
    }

    public String getUpdateBy() 
    {
        return updateBy;
    }
    public void setUpdateTime(Date updateTime) 
    {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() 
    {
        return updateTime;
    }

    public void setMemberId(Long memberId)
    {
        this.memberId = memberId;
    }

    public Long getMemberId()
    {
        return memberId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("companyName", getCompanyName())
            .append("contact", getContact())
            .append("phone", getPhone())
            .append("title", getTitle())
            .append("type", getType())
            .append("description", getDescription())
            .append("applicationArea", getApplicationArea())
            .append("imageUrl", getImageUrl())
//            .append("bidType", getBidType())
//            .append("targetCompany", getTargetCompany())
//            .append("bidStartTime", getBidStartTime())
//            .append("bidEndTime", getBidEndTime())
//            .append("bidDeadline", getBidDeadline())
//            .append("attachment", getattachment())
//            .append("publisher", getPublisher())
            .append("auditStatus", getAuditStatus())
            .append("visible", getVisible())
            .append("recommend", getRecommend())
            .append("processStatus", getProcessStatus())
            .append("onShow", getOnShow())
//            .append("prospectiveCorp", getProspectiveCorp())
//            .append("autoPublish", getAutoPublish())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("memberId", getMemberId())
            .append("viewCount", getViewCount())
            .toString();
    }
}
