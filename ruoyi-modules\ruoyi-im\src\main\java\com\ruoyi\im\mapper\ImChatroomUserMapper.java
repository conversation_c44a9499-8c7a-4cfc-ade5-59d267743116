package com.ruoyi.im.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.im.api.domain.ImChatroomUser;
import com.ruoyi.im.api.dto.ChatroomPojo;
import com.ruoyi.im.mapper.provider.ImChatroomMsgMapperProvider;
import com.ruoyi.im.mapper.provider.ImChatroomUserMapperProvider;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

public interface ImChatroomUserMapper extends BaseMapper<ImChatroomUser>
{

    @SelectProvider(type = ImChatroomUserMapperProvider.class,method = "findUser")
    List<ChatroomPojo> findUser(@Param("ids") String ids);
}
