package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.domain.LaboratoryInfo;
import com.ruoyi.system.domain.LabTestingRelation;
import com.ruoyi.system.domain.TestingItem;
import com.ruoyi.system.domain.dto.TestingItemDTO;
import com.ruoyi.system.domain.dto.TestingItemWithLabsDTO;
import com.ruoyi.system.mapper.LabTestingRelationMapper;
import com.ruoyi.system.service.ILabTestingRelationService;
import com.ruoyi.system.service.ITestingItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.mapper.TestingItemMapper;

/**
 * 检测项目Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Service
public class TestingItemServiceImpl implements ITestingItemService
{
    @Autowired
    private TestingItemMapper testingItemMapper;
    
    @Autowired
    private ILabTestingRelationService labTestingRelationService;

    /**
     * 查询检测项目
     *
     * @param id 检测项目主键
     * @return 检测项目
     */
    @Override
    public TestingItem selectTestingItemById(Long id)
    {
        return testingItemMapper.selectTestingItemById(id);
    }

    /**
     * 查询检测项目列表
     *
     * @param testingItem 检测项目
     * @return 检测项目
     */
    @Override
    public List<TestingItem> selectTestingItemList(TestingItem testingItem)
    {
        return testingItemMapper.selectTestingItemList(testingItem);
    }

    /**
     * 新增检测项目
     *
     * @param testingItem 检测项目
     * @return 结果
     */
    @Override
    public int insertTestingItem(TestingItem testingItem)
    {
        testingItem.setCreateTime(DateUtils.getNowDate());
        return testingItemMapper.insertTestingItem(testingItem);
    }
    
    /**
     * 新增检测项目并关联实验室
     *
     * @param testingItemDTO 检测项目数据传输对象
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTestingItemWithLabs(TestingItemDTO testingItemDTO)
    {
        // 插入检测项目
        TestingItem testingItem = testingItemDTO.getTestingItem();
        testingItem.setCreateTime(DateUtils.getNowDate());
        int rows = testingItemMapper.insertTestingItem(testingItem);
        
        // 获取新插入的检测项目ID
        Long testingItemId = testingItem.getId();
        
        // 处理实验室关联
        insertLabTestingRelations(testingItemDTO, testingItemId);
        
        return rows;
    }

    /**
     * 修改检测项目
     *
     * @param testingItem 检测项目
     * @return 结果
     */
    @Override
    public int updateTestingItem(TestingItem testingItem)
    {
        testingItem.setUpdateTime(DateUtils.getNowDate());
        return testingItemMapper.updateTestingItem(testingItem);
    }
    
    /**
     * 修改检测项目并更新关联实验室
     *
     * @param testingItemDTO 检测项目数据传输对象
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTestingItemWithLabs(TestingItemDTO testingItemDTO)
    {
        // 更新检测项目
        TestingItem testingItem = testingItemDTO.getTestingItem();
        testingItem.setUpdateTime(DateUtils.getNowDate());
        int rows = testingItemMapper.updateTestingItem(testingItem);
        
        // 获取检测项目ID
        Long testingItemId = testingItem.getId();
        
        // 删除原有关联
        LabTestingRelation labTestingRelation = new LabTestingRelation();
        labTestingRelation.setTestingId(testingItemId);
        List<LabTestingRelation> oldRelations = labTestingRelationService.selectLabTestingRelationList(labTestingRelation);
        for (LabTestingRelation relation : oldRelations) {
            labTestingRelationService.deleteLabTestingRelationById(relation.getId());
        }
        
        // 处理新的实验室关联
        insertLabTestingRelations(testingItemDTO, testingItemId);
        
        return rows;
    }

    /**
     * 批量删除检测项目
     *
     * @param ids 需要删除的检测项目主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTestingItemByIds(Long[] ids)
    {
        // 删除关联的实验室关系
        for (Long id : ids) {
            deleteLabTestingRelationsByTestingId(id);
        }
        
        // 删除检测项目
        return testingItemMapper.deleteTestingItemByIds(ids);
    }

    /**
     * 删除检测项目信息
     *
     * @param id 检测项目主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTestingItemById(Long id)
    {
        // 删除关联的实验室关系
        deleteLabTestingRelationsByTestingId(id);
        
        // 删除检测项目
        return testingItemMapper.deleteTestingItemById(id);
    }
    
    /**
     * 查询检测项目详细信息及关联实验室
     *
     * @param id 检测项目主键
     * @return 检测项目及关联实验室信息
     */
    @Override
    public TestingItemWithLabsDTO selectTestingItemWithLabsById(Long id)
    {
        // 查询检测项目基本信息
        TestingItem testingItem = testingItemMapper.selectTestingItemById(id);
        if (testingItem == null) {
            return null;
        }
        
        // 查询关联的实验室信息
        List<LaboratoryInfo> labs = testingItemMapper.selectLabsByTestingId(id);
        
        // 查询关联关系
        List<LabTestingRelation> relations = testingItemMapper.selectRelationsByTestingId(id);
        
        // 组装DTO
        TestingItemWithLabsDTO dto = new TestingItemWithLabsDTO();
        dto.setTestingItem(testingItem);
        dto.setLabs(labs);
        dto.setLabTestingRelations(relations);
        
        return dto;
    }
    
    /**
     * 查询检测项目列表及关联实验室
     *
     * @param testingItem 检测项目
     * @return 检测项目及关联实验室信息集合
     */
    @Override
    public List<TestingItemWithLabsDTO> selectTestingItemWithLabsList(TestingItem testingItem)
    {
        // 查询检测项目列表
        List<TestingItem> testingItems = testingItemMapper.selectTestingItemList(testingItem);
        if (testingItems == null || testingItems.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 组装结果集
        List<TestingItemWithLabsDTO> result = new ArrayList<>();
        for (TestingItem item : testingItems) {
            TestingItemWithLabsDTO dto = selectTestingItemWithLabsById(item.getId());
            if (dto != null) {
                result.add(dto);
            }
        }
        
        return result;
    }
    
    /**
     * 插入实验室与检测项目的关联关系
     * 
     * @param testingItemDTO 检测项目数据传输对象
     * @param testingItemId 检测项目ID
     */
    private void insertLabTestingRelations(TestingItemDTO testingItemDTO, Long testingItemId) {
        List<LabTestingRelation> relations = testingItemDTO.getLabTestingRelations();
        if (relations != null && !relations.isEmpty()) {
            for (LabTestingRelation relation : relations) {
                relation.setTestingId(testingItemId);
                relation.setCreateTime(DateUtils.getNowDate());
                labTestingRelationService.insertLabTestingRelation(relation);
            }
        } else if (testingItemDTO.getLabIds() != null && !testingItemDTO.getLabIds().isEmpty()) {
            for (Long labId : testingItemDTO.getLabIds()) {
                LabTestingRelation relation = new LabTestingRelation();
                relation.setLabId(labId);
                relation.setTestingId(testingItemId);
                relation.setCreateTime(DateUtils.getNowDate());
                labTestingRelationService.insertLabTestingRelation(relation);
            }
        }
    }
    
    /**
     * 根据检测项目ID删除关联关系
     * 
     * @param testingItemId 检测项目ID
     */
    private void deleteLabTestingRelationsByTestingId(Long testingItemId) {
        LabTestingRelation labTestingRelation = new LabTestingRelation();
        labTestingRelation.setTestingId(testingItemId);
        List<LabTestingRelation> relations = labTestingRelationService.selectLabTestingRelationList(labTestingRelation);
        for (LabTestingRelation relation : relations) {
            labTestingRelationService.deleteLabTestingRelationById(relation.getId());
        }
    }
    
    /**
     * 查询检测项目列表并左联查检测实验室
     *
     * @param labType 实验室类型
     * @return 检测项目及关联实验室信息集合
     */
    @Override
    public List<TestingItemWithLabsDTO> selectTestingItemLeftJoinLabs(String labType)
    {
        return testingItemMapper.selectTestingItemLeftJoinLabs(labType);
    }
}
