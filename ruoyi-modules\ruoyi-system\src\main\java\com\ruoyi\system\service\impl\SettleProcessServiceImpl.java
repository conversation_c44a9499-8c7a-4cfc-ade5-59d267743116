package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SettleProcessMapper;
import com.ruoyi.system.domain.SettleProcess;
import com.ruoyi.system.service.ISettleProcessService;

/**
 * 入驻申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class SettleProcessServiceImpl implements ISettleProcessService 
{
    @Autowired
    private SettleProcessMapper settleProcessMapper;

    /**
     * 查询入驻申请
     * 
     * @param id 入驻申请主键
     * @return 入驻申请
     */
    @Override
    public SettleProcess selectSettleProcessById(Long id)
    {
        return settleProcessMapper.selectSettleProcessById(id);
    }

    /**
     * 查询入驻申请列表
     * 
     * @param settleProcess 入驻申请
     * @return 入驻申请
     */
    @Override
    public List<SettleProcess> selectSettleProcessList(SettleProcess settleProcess)
    {
        return settleProcessMapper.selectSettleProcessList(settleProcess);
    }

    /**
     * 新增入驻申请
     * 
     * @param settleProcess 入驻申请
     * @return 结果
     */
    @Override
    public int insertSettleProcess(SettleProcess settleProcess)
    {
        settleProcess.setCreateTime(DateUtils.getNowDate());
        return settleProcessMapper.insertSettleProcess(settleProcess);
    }

    /**
     * 修改入驻申请
     * 
     * @param settleProcess 入驻申请
     * @return 结果
     */
    @Override
    public int updateSettleProcess(SettleProcess settleProcess)
    {
        settleProcess.setUpdateTime(DateUtils.getNowDate());
        return settleProcessMapper.updateSettleProcess(settleProcess);
    }

    /**
     * 批量删除入驻申请
     * 
     * @param ids 需要删除的入驻申请主键
     * @return 结果
     */
    @Override
    public int deleteSettleProcessByIds(Long[] ids)
    {
        return settleProcessMapper.deleteSettleProcessByIds(ids);
    }

    /**
     * 删除入驻申请信息
     * 
     * @param id 入驻申请主键
     * @return 结果
     */
    @Override
    public int deleteSettleProcessById(Long id)
    {
        return settleProcessMapper.deleteSettleProcessById(id);
    }
}
