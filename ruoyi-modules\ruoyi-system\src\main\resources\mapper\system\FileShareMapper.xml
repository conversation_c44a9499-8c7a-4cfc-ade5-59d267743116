<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.FileShareMapper">
    
    <resultMap type="FileShare" id="FileShareResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileSize"    column="file_size"    />
        <result property="category"    column="category"    />
        <result property="subCategory"    column="sub_category"    />
        <result property="description"    column="description"    />
        <result property="downloadCount"    column="download_count"    />
        <result property="viewCount"    column="view_count"    />
        <result property="contacts"    column="contacts"    />
        <result property="phone"    column="phone"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectFileShareVo">
        select id, title, file_name, file_type, file_path, file_size, category, sub_category, description, download_count, view_count, contacts, phone, status, del_flag, create_by, create_time, update_by, update_time, remark from file_share
    </sql>

    <select id="selectFileShareList" parameterType="FileShare" resultMap="FileShareResult">
        <include refid="selectFileShareVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="subCategory != null  and subCategory != ''"> and sub_category = #{subCategory}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="downloadCount != null "> and download_count = #{downloadCount}</if>
            <if test="viewCount != null "> and view_count = #{viewCount}</if>
            <if test="contacts != null  and contacts != ''"> and contacts = #{contacts}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectFileShareById" parameterType="Long" resultMap="FileShareResult">
        <include refid="selectFileShareVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFileShare" parameterType="FileShare" useGeneratedKeys="true" keyProperty="id">
        insert into file_share
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="filePath != null">file_path,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="category != null">category,</if>
            <if test="subCategory != null">sub_category,</if>
            <if test="description != null">description,</if>
            <if test="downloadCount != null">download_count,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="contacts != null">contacts,</if>
            <if test="phone != null">phone,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="category != null">#{category},</if>
            <if test="subCategory != null">#{subCategory},</if>
            <if test="description != null">#{description},</if>
            <if test="downloadCount != null">#{downloadCount},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="contacts != null">#{contacts},</if>
            <if test="phone != null">#{phone},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateFileShare" parameterType="FileShare">
        update file_share
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="category != null">category = #{category},</if>
            <if test="subCategory != null">sub_category = #{subCategory},</if>
            <if test="description != null">description = #{description},</if>
            <if test="downloadCount != null">download_count = #{downloadCount},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="contacts != null">contacts = #{contacts},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFileShareById" parameterType="Long">
        delete from file_share where id = #{id}
    </delete>

    <delete id="deleteFileShareByIds" parameterType="String">
        delete from file_share where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>