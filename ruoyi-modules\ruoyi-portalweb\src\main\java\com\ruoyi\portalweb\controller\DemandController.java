package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.portalweb.api.domain.Demand;
import com.ruoyi.portalweb.service.IDemandService;
import com.ruoyi.portalweb.vo.DemandVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 服务需求(NEW)Controller
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/demand")
@Api(value = "3.服务需求", tags = "3.服务需求")
public class DemandController extends BaseController {
    @Autowired
    private IDemandService demandService;

    /**
     * 查询服务需求(NEW)列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询服务需求(NEW)列表", notes = "传入")
    public TableDataInfo list(DemandVO demand) {
        startPage();
        List<DemandVO> list = demandService.selectDemandList(demand);
        return getDataTable(list);
    }

    /**
     * 查询服务需求(NEW)列表
     */
    @GetMapping("/listDesk")
    @ApiOperation(value = "查询服务需求(NEW)列表", notes = "传入")
    public TableDataInfo listDesk(DemandVO demand) {
        startPage();
        PageUtils.setOrderBy("create_time DESC");
        List<DemandVO> list = demandService.selectDemandList(demand);
        return getDataTable(list);
    }

    /**
     * 查询服务需求(NEW)列表
     */
    @GetMapping("/listDesk/CompanyRelated")
    @ApiOperation(value = "查询服务需求(NEW)列表", notes = "传入")
    public TableDataInfo listDeskCompanyRelated(@RequestParam(value ="companyRelatedId") Long companyRelatedId) {
        List<DemandVO> list = demandService.listDeskCompanyRelated(companyRelatedId);
        return getDataTable(list);
    }


    /**
     * 导出服务需求(NEW)列表
     */
    @Log(title = "服务需求(NEW)", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出服务需求(NEW)列表", notes = "传入")
    public void export(HttpServletResponse response, DemandVO demand) {
        List<DemandVO> list = demandService.selectDemandList(demand);
        ExcelUtil<DemandVO> util = new ExcelUtil<DemandVO>(DemandVO.class);
        util.exportExcel(response, list, "服务需求(NEW)数据");
    }

//    /**
//     * 获取服务需求(NEW)详细信息
//     */
//    @GetMapping(value = "/{id}")
//    @ApiOperation(value = "获取服务需求(NEW)详细信息", notes = "传入")
//    public AjaxResult getInfo(@ApiParam(value = "主键", required = true) @PathVariable("id") Long id) {
//        return success(demandService.selectDemandById(id));
//    }
    
    /**
     * 获取服务需求(NEW)详细信息
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取服务需求(NEW)详细信息", notes = "传入")
    public AjaxResult detail(Demand demand) {
        return success(demandService.selectDemandById(demand.getId()));
    }
    
    /**
     * 获取服务需求(NEW)详细信息
     */
    @GetMapping("/detailDesk")
    @ApiOperation(value = "获取服务需求(NEW)详细信息", notes = "传入")
    public AjaxResult detailDesk(Demand demand) {
        return success(demandService.detailDesk(demand.getId()));
    }

    /**
     * 新增服务需求(NEW)
     */
    @Log(title = "服务需求(NEW)", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增服务需求(NEW)", notes = "传入")
    public AjaxResult add(@RequestBody DemandVO demand) {
        return toAjax(demandService.insertDemand(demand));
    }

    /**
     * 修改服务需求(NEW)
     */
    @Log(title = "服务需求(NEW)", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改服务需求(NEW)", notes = "传入")
    public AjaxResult edit(@RequestBody DemandVO demand) {
        return toAjax(demandService.updateDemand(demand));
    }

    /**
     * 删除服务需求(NEW)
     */
    @Log(title = "服务需求(NEW)", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除服务需求(NEW)", notes = "传入")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(demandService.deleteDemandByIds(ids));
    }
}
