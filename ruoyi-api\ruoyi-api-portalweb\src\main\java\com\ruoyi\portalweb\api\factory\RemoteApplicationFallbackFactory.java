package com.ruoyi.portalweb.api.factory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.portalweb.api.RemoteApplicationService;
import com.ruoyi.portalweb.api.domain.Application;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteApplicationFallbackFactory implements FallbackFactory<RemoteApplicationService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteApplicationFallbackFactory.class);

    @Override
    public RemoteApplicationService create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteApplicationService()
        {
            @Override
            public R<Application> selectApplicationByAppId(String appid)
            {
                return R.fail("获取app失败:" + throwable.getMessage());
            }

        };
    }
}
