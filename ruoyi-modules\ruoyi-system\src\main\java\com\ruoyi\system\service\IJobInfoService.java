package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.JobInfo;

/**
 * 用工信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface IJobInfoService 
{
    /**
     * 查询用工信息
     * 
     * @param id 用工信息主键
     * @return 用工信息
     */
    public JobInfo selectJobInfoById(Long id);

    /**
     * 查询用工信息列表
     * 
     * @param jobInfo 用工信息
     * @return 用工信息集合
     */
    public List<JobInfo> selectJobInfoList(JobInfo jobInfo);

    /**
     * 新增用工信息
     * 
     * @param jobInfo 用工信息
     * @return 结果
     */
    public int insertJobInfo(JobInfo jobInfo);

    /**
     * 修改用工信息
     * 
     * @param jobInfo 用工信息
     * @return 结果
     */
    public int updateJobInfo(JobInfo jobInfo);

    /**
     * 批量删除用工信息
     * 
     * @param ids 需要删除的用工信息主键集合
     * @return 结果
     */
    public int deleteJobInfoByIds(Long[] ids);

    /**
     * 删除用工信息信息
     * 
     * @param id 用工信息主键
     * @return 结果
     */
    public int deleteJobInfoById(Long id);
}
