import request from '@/utils/request'

// 查询建议及反馈列表
export function listRecommendation(query) {
  return request({
    url: '/portalconsole/recommendation/list',
    method: 'get',
    params: query
  })
}

// 查询建议及反馈详细
export function getRecommendation(id) {
  return request({
    url: '/portalconsole/recommendation/' + id,
    method: 'get'
  })
}

// 新增建议及反馈
export function addRecommendation(data) {
  return request({
    url: '/portalconsole/recommendation',
    method: 'post',
    data: data
  })
}

// 修改建议及反馈
export function updateRecommendation(data) {
  return request({
    url: '/portalconsole/recommendation',
    method: 'put',
    data: data
  })
}

// 删除建议及反馈
export function delRecommendation(id) {
  return request({
    url: '/portalconsole/recommendation/' + id,
    method: 'delete'
  })
}
