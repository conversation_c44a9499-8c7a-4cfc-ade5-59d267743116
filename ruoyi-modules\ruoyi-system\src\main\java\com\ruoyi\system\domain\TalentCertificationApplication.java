package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 衡水市职业技能鉴定中心申请对象 talent_certification_application
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
public class TalentCertificationApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 学历 */
    @Excel(name = "学历")
    private String education;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contact;

    /** 鉴定工种1 */
    @Excel(name = "鉴定工种1")
    private String certificationType1;

    /** 鉴定技能等级 */
    @Excel(name = "鉴定技能等级")
    private String skillLevel;

    /** 鉴定工种2 */
    @Excel(name = "鉴定工种2")
    private String certificationType2;

    /** 状态：0-待审核，1-已通过，2-已拒绝 */
    @Excel(name = "状态：0-待审核，1-已通过，2-已拒绝")
    private Integer status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setEducation(String education) 
    {
        this.education = education;
    }

    public String getEducation() 
    {
        return education;
    }
    public void setContact(String contact) 
    {
        this.contact = contact;
    }

    public String getContact() 
    {
        return contact;
    }
    public void setCertificationType1(String certificationType1) 
    {
        this.certificationType1 = certificationType1;
    }

    public String getCertificationType1() 
    {
        return certificationType1;
    }
    public void setSkillLevel(String skillLevel) 
    {
        this.skillLevel = skillLevel;
    }

    public String getSkillLevel() 
    {
        return skillLevel;
    }
    public void setCertificationType2(String certificationType2) 
    {
        this.certificationType2 = certificationType2;
    }

    public String getCertificationType2() 
    {
        return certificationType2;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("idCard", getIdCard())
            .append("education", getEducation())
            .append("contact", getContact())
            .append("certificationType1", getCertificationType1())
            .append("skillLevel", getSkillLevel())
            .append("certificationType2", getCertificationType2())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("status", getStatus())
            .append("remark", getRemark())
            .toString();
    }
}
