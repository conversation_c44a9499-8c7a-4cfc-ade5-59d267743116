-- 检测需求表
CREATE TABLE `testing_requirement` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '需求ID',
  `testing_content` varchar(255) DEFAULT NULL COMMENT '检测内容',
  `testing_requirements` text COMMENT '检测要求',
  `basic_requirements` text COMMENT '基础要求',
  `image_url` varchar(500) DEFAULT NULL COMMENT '场景图片URL',
  `company_name` varchar(255) DEFAULT NULL COMMENT '公司名称',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='检测需求表';

-- 插入测试数据
INSERT INTO `testing_requirement` (`id`, `testing_content`, `testing_requirements`, `basic_requirements`, `image_url`, `company_name`, `contact_person`, `contact_phone`, `status`, `create_time`, `update_time`) VALUES
(1, '复合材料强度测试', '测试复合材料在不同温度和压力下的强度变化', '需要测试-20°C到120°C温度范围内的材料强度变化', '/profile/upload/2025/03/08/material_test_20250308.jpg', '恒润复合材料有限公司', '张工', '13812345678', '0', NOW(), NOW()),
(2, '碳纤维拉伸测试', '测试碳纤维复合材料的拉伸强度和弹性模量', '按照ASTM D3039标准进行测试', '/profile/upload/2025/03/08/carbon_fiber_20250308.jpg', '先进复合材料科技有限公司', '李工', '13987654321', '0', NOW(), NOW()),
(3, '环氧树脂固化度分析', '分析环氧树脂在不同固化条件下的固化度和性能', '需要DSC和FTIR分析', NULL, '高分子材料研究所', '王研究员', '13567891234', '0', NOW(), NOW());
