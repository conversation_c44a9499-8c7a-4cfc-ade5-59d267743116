package com.ruoyi.portalweb.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.CompanyRelated;
import com.ruoyi.portalweb.api.domain.Demand;
import com.ruoyi.portalweb.api.domain.Supply;
import com.ruoyi.portalweb.api.enums.MyFavoriteStatus;
import com.ruoyi.portalweb.api.enums.MyFavoriteType;
import com.ruoyi.portalweb.service.ICompanyRelatedService;
import com.ruoyi.portalweb.service.IDemandService;
import com.ruoyi.portalweb.service.ISupplyService;
import com.ruoyi.portalweb.vo.DemandVO;
import com.ruoyi.portalweb.vo.MyFavoriteVO;
import com.ruoyi.portalweb.vo.SupplyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.ruoyi.portalweb.mapper.MyFavoriteMapper;
import com.ruoyi.portalweb.api.domain.MyFavorite;
import com.ruoyi.portalweb.service.IMyFavoriteService;

/**
 * 我的收藏Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-25
 */
@Service
public class MyFavoriteServiceImpl implements IMyFavoriteService 
{
    @Autowired
    private MyFavoriteMapper myFavoriteMapper;

    @Autowired
    private IDemandService demandService;

    @Autowired
    private ISupplyService supplyService;

    @Autowired
    private ICompanyRelatedService companyRelatedService;


    /**
     * 查询我的收藏
     * 
     * @param myFavoriteId 我的收藏主键
     * @return 我的收藏
     */
    @Override
    public MyFavorite selectMyFavoriteByMyFavoriteId(Long myFavoriteId)
    {
        return myFavoriteMapper.selectMyFavoriteByMyFavoriteId(myFavoriteId);
    }

    /**
     * 查询我的收藏
     *
     * @param type 我的收藏类型
     * @param issueId 我的收藏数据主键id
     * @return 我的收藏
     */
    @Override
    public MyFavorite selectMyFavoriteByIssueId(String type, Long issueId) {
        MyFavorite myFavorite = new MyFavorite();
        myFavorite.setIssueId(issueId);
        myFavorite.setFavoriteType(type);
        myFavorite.setMemberId(SecurityUtils.getUserId());
        return myFavoriteMapper.selectMyFavoriteByIssueId(myFavorite);
    }

    /**
     * 查询我的收藏列表
     * 
     * @param myFavorite 我的收藏
     * @return 我的收藏
     */
    @Override
    public List<MyFavoriteVO> selectMyFavoriteList(MyFavorite myFavorite)
    {
        myFavorite.setMemberId(SecurityUtils.getUserId());
        List<MyFavoriteVO> myFavorites = myFavoriteMapper.selectMyFavoriteList(myFavorite);
        if (!myFavorites.isEmpty() && Objects.equals(myFavorite.getFavoriteType(), MyFavoriteType.DEMAND.getType())){
            List<Long> demandIds = new ArrayList<>();
            for (MyFavoriteVO mf : myFavorites){
                demandIds.add(mf.getIssueId());
            }

            List<DemandVO> demands = demandService.selectDemandListByDemandIds(demandIds);
            for (MyFavoriteVO favoriteVO : myFavorites){
                for (Demand demand : demands){
                    if (Objects.equals(demand.getId(), favoriteVO.getIssueId())){
                        favoriteVO.setProcessStatus(demand.getProcessStatus());
                        break;
                    }
                }
            }
        }

        if (!myFavorites.isEmpty() && Objects.equals(myFavorite.getFavoriteType(), MyFavoriteType.SUPPLY.getType())){
            List<Long> supplyIds = new ArrayList<>();
            for (MyFavoriteVO mf : myFavorites){
                supplyIds.add(mf.getIssueId());
            }
            List<Supply> supplys = supplyService.selectSupplyListBySupplyIds(supplyIds);
            for (MyFavoriteVO favoriteVO : myFavorites){
                for (Supply supply : supplys){
                    if (Objects.equals(supply.getId(), favoriteVO.getIssueId())){
                        favoriteVO.setProcess(supply.getProcess());
                        break;
                    }
                }
            }
        }


        return myFavorites;
    }

    /**
     * 新增我的收藏
     * 
     * @param myFavorite 我的收藏
     * @return 结果
     */
    @Override
    public int insertMyFavorite(MyFavorite myFavorite)
    {

        myFavorite.setMemberId(SecurityUtils.getUserId());
        switch (myFavorite.getFavoriteType()) {
            case "demand":
                // 查找需求
                DemandVO demandVO = demandService.selectDemandById(myFavorite.getIssueId());
                myFavorite.setMainContent(demandVO.getTitle());
                myFavorite.setCompanyName(demandVO.getCompanyName());
                break;
            case "supply":
                // 查找supply
                SupplyVO supplyVO = supplyService.selectSupplyById(myFavorite.getIssueId());
                myFavorite.setMainContent(supplyVO.getTitle());
                myFavorite.setCompanyName(supplyVO.getOrganization());
                break;
            case "companyRelated":
                   // 查找关联企业
                CompanyRelated companyRelated = companyRelatedService.selectCompanyRelatedByCompanyRelatedId(myFavorite.getIssueId());
                myFavorite.setMainContent(companyRelated.getIntrduction());
                myFavorite.setCompanyName(companyRelated.getCompanyName());
                break;
            default:
                throw new ServiceException("unknown favorite type");
        }


        MyFavorite existedMyFavorite = this.selectMyFavoriteByIssueId(myFavorite.getFavoriteType(), myFavorite.getIssueId());
        if (existedMyFavorite!= null){return 1;}
        myFavorite.setStatus(MyFavoriteStatus.VALID.getValue());
        myFavorite.setCreateTime(DateUtils.getNowDate());
        return myFavoriteMapper.insertMyFavorite(myFavorite);
    }

    /**
     * 修改我的收藏
     * 
     * @param myFavorite 我的收藏
     * @return 结果
     */
    @Override
    public int updateMyFavorite(MyFavorite myFavorite)
    {
        myFavorite.setUpdateTime(DateUtils.getNowDate());
        return myFavoriteMapper.updateMyFavorite(myFavorite);
    }

    /**
     * 修改我的收藏
     *
     * @param issueId 我的收藏的数据ID
     * @param type 收藏的数据类型
     * @param status 我的收藏
     * @return 结果
     */
    @Async
    public CompletableFuture<Integer> updateMyFavoriteStatus(Long issueId, String type, String status)
    {
        return CompletableFuture.completedFuture(myFavoriteMapper.updateMyFavoriteStatus(issueId,type,status));
    }

    /**
     * 批量删除我的收藏
     * 
     * @param myFavoriteIds 需要删除的我的收藏主键
     * @return 结果
     */
    @Override
    public int deleteMyFavoriteByMyFavoriteIds(Long[] myFavoriteIds)
    {
        return myFavoriteMapper.deleteMyFavoriteByMyFavoriteIds(myFavoriteIds);
    }

    /**
     * 删除我的收藏信息
     * 
     * @param myFavoriteId 我的收藏主键
     * @return 结果
     */
    @Override
    public int deleteMyFavoriteByMyFavoriteId(Long myFavoriteId)
    {
        return myFavoriteMapper.deleteMyFavoriteByMyFavoriteId(myFavoriteId);
    }
}
