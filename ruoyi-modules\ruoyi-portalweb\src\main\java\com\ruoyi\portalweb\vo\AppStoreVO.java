package com.ruoyi.portalweb.vo;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.portalweb.api.domain.AppStore;
import io.swagger.annotations.ApiModelProperty;

/**
 * 应用商店对象
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public class AppStoreVO extends AppStore {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "完整url")
    private String fileFullPath;
    
    @ApiModelProperty(value = "应用类型")
    private String appStoreTypeName;
    @ApiModelProperty(value = "交付方式")
    private String deliveryMethodName;

    @ApiModelProperty(value = "查询方式,我的需求queryType='my'")
    private String queryType;

    @JsonIgnore
    private String keyword;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    public String getAppStoreTypeName() {
        return appStoreTypeName;
    }
    public void setAppStoreTypeName(String appStoreTypeName) {
        this.appStoreTypeName = appStoreTypeName;
    }
    
    public String getDeliveryMethodName() {
        return deliveryMethodName;
    }
    public void setDeliveryMethodName(String deliveryMethodName) {
        this.deliveryMethodName = deliveryMethodName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileFullPath() {
        return fileFullPath;
    }

    public void setFileFullPath(String fileFullPath) {
        this.fileFullPath = fileFullPath;
    }

}
