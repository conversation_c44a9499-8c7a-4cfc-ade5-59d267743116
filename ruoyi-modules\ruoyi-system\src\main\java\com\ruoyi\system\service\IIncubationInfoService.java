package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.IncubationInfo;

/**
 * 创业孵化信息Service接口
 *
 * <AUTHOR>
 * @date 2025-03-11
 */
public interface IIncubationInfoService
{
    /**
     * 查询创业孵化信息
     *
     * @param id 创业孵化信息主键
     * @return 创业孵化信息
     */
    public IncubationInfo selectIncubationInfoById(Long id);

    /**
     * 查询创业孵化信息列表
     *
     * @param incubationInfo 创业孵化信息
     * @return 创业孵化信息集合
     */
    public List<IncubationInfo> selectIncubationInfoList(IncubationInfo incubationInfo);

    /**
     * 新增创业孵化信息
     *
     * @param incubationInfo 创业孵化信息
     * @return 结果
     */
    public int insertIncubationInfo(IncubationInfo incubationInfo);

    /**
     * 修改创业孵化信息
     *
     * @param incubationInfo 创业孵化信息
     * @return 结果
     */
    public int updateIncubationInfo(IncubationInfo incubationInfo);

    /**
     * 批量删除创业孵化信息
     *
     * @param ids 需要删除的创业孵化信息主键集合
     * @return 结果
     */
    public int deleteIncubationInfoByIds(Long[] ids);

    /**
     * 删除创业孵化信息信息
     *
     * @param id 创业孵化信息主键
     * @return 结果
     */
    public int deleteIncubationInfoById(Long id);
}
