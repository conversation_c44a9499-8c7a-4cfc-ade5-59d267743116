package com.ruoyi.portalconsole.domain.vo;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.portalconsole.domain.AppStore;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import java.util.List;

/**
 * 应用商店对象
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public class AppStoreVO extends AppStore {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "应用Ids")
    private List<Long> appStoreIds;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "完整url")
    private String fileFullPath;

    @ApiModelProperty(value = "应用类型")
    private String appStoreTypeName;

    @ApiModelProperty(value = "交付方式")
    private String deliveryMethodName;

    public List<Long> getAppStoreIds() {return appStoreIds;}

    public void setAppStoreIds(List<Long> appStoreIds) {this.appStoreIds = appStoreIds;}

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileFullPath() {
        return fileFullPath;
    }

    public void setFileFullPath(String fileFullPath) {
        this.fileFullPath = fileFullPath;
    }

    public String getAppStoreTypeName() {
        return appStoreTypeName;
    }

    public void setAppStoreTypeName(String appStoreTypeName) {
        this.appStoreTypeName = appStoreTypeName;
    }

    public String getDeliveryMethodName() {
        return deliveryMethodName;
    }

    public void setDeliveryMethodName(String deliveryMethodName) {
        this.deliveryMethodName = deliveryMethodName;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this).append("appStoreIds", appStoreIds).append("companyName", companyName).append("filePath", filePath).append("fileFullPath", fileFullPath).append("appStoreTypeName", appStoreTypeName).append("deliveryMethodName", deliveryMethodName).toString();
    }
}
