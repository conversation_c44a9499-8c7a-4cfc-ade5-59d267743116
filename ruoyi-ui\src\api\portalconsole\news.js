import request from '@/utils/request'

// 查询新闻中心列表
export function listNews(query) {
  return request({
    url: '/portalconsole/news/list',
    method: 'get',
    params: query
  })
}

// 查询新闻中心详细
export function getNews(newsId) {
  return request({
    url: '/portalconsole/news/' + newsId,
    method: 'get'
  })
}

// 新增新闻中心
export function addNews(data) {
  return request({
    url: '/portalconsole/news',
    method: 'post',
    data: data
  })
}

// 修改新闻中心
export function updateNews(data) {
  return request({
    url: '/portalconsole/news',
    method: 'put',
    data: data
  })
}

// 删除新闻中心
export function delNews(newsId) {
  return request({
    url: '/portalconsole/news/' + newsId,
    method: 'delete'
  })
}
