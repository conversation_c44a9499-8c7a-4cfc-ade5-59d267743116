package com.ruoyi.portalconsole.mapper;

import java.util.List;
import com.ruoyi.portalconsole.domain.ExpertDatabase;

/**
 * 专家库Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface ExpertDatabaseMapper 
{
    /**
     * 查询专家库
     * 
     * @param expertDatabaseId 专家库主键
     * @return 专家库
     */
    public ExpertDatabase selectExpertDatabaseByExpertDatabaseId(Long expertDatabaseId);

    /**
     * 查询专家库列表
     * 
     * @param expertDatabase 专家库
     * @return 专家库集合
     */
    public List<ExpertDatabase> selectExpertDatabaseList(ExpertDatabase expertDatabase);

    /**
     * 新增专家库
     * 
     * @param expertDatabase 专家库
     * @return 结果
     */
    public int insertExpertDatabase(ExpertDatabase expertDatabase);

    /**
     * 修改专家库
     * 
     * @param expertDatabase 专家库
     * @return 结果
     */
    public int updateExpertDatabase(ExpertDatabase expertDatabase);

    /**
     * 删除专家库
     * 
     * @param expertDatabaseId 专家库主键
     * @return 结果
     */
    public int deleteExpertDatabaseByExpertDatabaseId(Long expertDatabaseId);

    /**
     * 批量删除专家库
     * 
     * @param expertDatabaseIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExpertDatabaseByExpertDatabaseIds(Long[] expertDatabaseIds);
}
