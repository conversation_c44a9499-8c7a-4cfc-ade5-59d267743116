<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.CompositeMaterialMapper">

    <resultMap type="CompositeMaterial" id="CompositeMaterialResult">
        <result property="id"           column="id"            />
        <result property="materialName" column="material_name" />
        <result property="imageUrl"     column="image_url"     />
        <result property="imageList"    column="image_list"    />
        <result property="description"  column="description"   />
        <result property="productType"  column="product_type"  />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"       column="status"        />
        <result property="delFlag"      column="del_flag"      />
        <result property="createBy"     column="create_by"     />
        <result property="createTime"   column="create_time"   />
        <result property="updateBy"     column="update_by"     />
        <result property="updateTime"   column="update_time"   />
        <result property="remark"       column="remark"        />
    </resultMap>

    <sql id="selectCompositeMaterialVo">
        select id, material_name, image_url, image_list, description, product_type, sort_order, status, del_flag, create_by, create_time, update_by, update_time, remark
        from composite_material
    </sql>

    <select id="selectCompositeMaterialList" parameterType="CompositeMaterial" resultMap="CompositeMaterialResult">
        <include refid="selectCompositeMaterialVo"/>
        <where>
            <if test="materialName != null  and materialName != ''"> and material_name like concat('%', #{materialName}, '%')</if>
            <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            and del_flag = '0'
        </where>
        order by sort_order asc
    </select>

    <select id="selectCompositeMaterialById" parameterType="Long" resultMap="CompositeMaterialResult">
        <include refid="selectCompositeMaterialVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <insert id="insertCompositeMaterial" parameterType="CompositeMaterial" useGeneratedKeys="true" keyProperty="id">
        insert into composite_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialName != null">material_name,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="imageList != null">image_list,</if>
            <if test="description != null">description,</if>
            <if test="productType != null">product_type,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialName != null">#{materialName},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="imageList != null">#{imageList},</if>
            <if test="description != null">#{description},</if>
            <if test="productType != null">#{productType},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCompositeMaterial" parameterType="CompositeMaterial">
        update composite_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="imageList != null">image_list = #{imageList},</if>
            <if test="description != null">description = #{description},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCompositeMaterialById" parameterType="Long">
        update composite_material set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteCompositeMaterialByIds" parameterType="String">
        update composite_material set del_flag = '2' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
