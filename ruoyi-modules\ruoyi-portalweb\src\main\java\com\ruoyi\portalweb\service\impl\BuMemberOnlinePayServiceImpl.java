package com.ruoyi.portalweb.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalweb.mapper.BuMemberOnlinePayMapper;
import com.ruoyi.portalweb.api.domain.BuMemberOnlinePay;
import com.ruoyi.portalweb.service.IBuMemberOnlinePayService;

/**
 * 商城线上支付Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@Service
public class BuMemberOnlinePayServiceImpl implements IBuMemberOnlinePayService 
{
    @Autowired
    private BuMemberOnlinePayMapper buMemberOnlinePayMapper;

    /**
     * 查询商城线上支付
     * 
     * @param id 商城线上支付主键
     * @return 商城线上支付
     */
    @Override
    public BuMemberOnlinePay selectBuMemberOnlinePayById(Long id)
    {
        return buMemberOnlinePayMapper.selectBuMemberOnlinePayById(id);
    }

    /**
     * 查询商城线上支付列表
     * 
     * @param buMemberOnlinePay 商城线上支付
     * @return 商城线上支付
     */
    @Override
    public List<BuMemberOnlinePay> selectBuMemberOnlinePayList(BuMemberOnlinePay buMemberOnlinePay)
    {
        return buMemberOnlinePayMapper.selectBuMemberOnlinePayList(buMemberOnlinePay);
    }

    /**
     * 新增商城线上支付
     * 
     * @param buMemberOnlinePay 商城线上支付
     * @return 结果
     */
    @Override
    public int insertBuMemberOnlinePay(BuMemberOnlinePay buMemberOnlinePay)
    {
        buMemberOnlinePay.setCreateTime(DateUtils.getNowDate());
        return buMemberOnlinePayMapper.insertBuMemberOnlinePay(buMemberOnlinePay);
    }

    /**
     * 修改商城线上支付
     * 
     * @param buMemberOnlinePay 商城线上支付
     * @return 结果
     */
    @Override
    public int updateBuMemberOnlinePay(BuMemberOnlinePay buMemberOnlinePay)
    {
        buMemberOnlinePay.setUpdateTime(DateUtils.getNowDate());
        return buMemberOnlinePayMapper.updateBuMemberOnlinePay(buMemberOnlinePay);
    }

    /**
     * 批量删除商城线上支付
     * 
     * @param ids 需要删除的商城线上支付主键
     * @return 结果
     */
    @Override
    public int deleteBuMemberOnlinePayByIds(Long[] ids)
    {
        return buMemberOnlinePayMapper.deleteBuMemberOnlinePayByIds(ids);
    }

    /**
     * 删除商城线上支付信息
     * 
     * @param id 商城线上支付主键
     * @return 结果
     */
    @Override
    public int deleteBuMemberOnlinePayById(Long id)
    {
        return buMemberOnlinePayMapper.deleteBuMemberOnlinePayById(id);
    }

    @Override
    public BuMemberOnlinePay selectBuMemberOnlinePayByAppOrderNo(String appStoreOrderNo) {
        return buMemberOnlinePayMapper.selectBuMemberOnlinePayByAppOrderNo(appStoreOrderNo);
    }
}
