package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.FactoryPersonnelMapper;
import com.ruoyi.system.domain.FactoryPersonnel;
import com.ruoyi.system.service.IFactoryPersonnelService;

/**
 * 工厂人员能力Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@Service
public class FactoryPersonnelServiceImpl implements IFactoryPersonnelService 
{
    @Autowired
    private FactoryPersonnelMapper factoryPersonnelMapper;

    /**
     * 查询工厂人员能力
     * 
     * @param id 工厂人员能力主键
     * @return 工厂人员能力
     */
    @Override
    public FactoryPersonnel selectFactoryPersonnelById(Long id)
    {
        return factoryPersonnelMapper.selectFactoryPersonnelById(id);
    }

    /**
     * 查询工厂人员能力列表
     * 
     * @param factoryPersonnel 工厂人员能力
     * @return 工厂人员能力
     */
    @Override
    public List<FactoryPersonnel> selectFactoryPersonnelList(FactoryPersonnel factoryPersonnel)
    {
        return factoryPersonnelMapper.selectFactoryPersonnelList(factoryPersonnel);
    }

    /**
     * 新增工厂人员能力
     * 
     * @param factoryPersonnel 工厂人员能力
     * @return 结果
     */
    @Override
    public int insertFactoryPersonnel(FactoryPersonnel factoryPersonnel)
    {
        factoryPersonnel.setCreateTime(DateUtils.getNowDate());
        return factoryPersonnelMapper.insertFactoryPersonnel(factoryPersonnel);
    }

    /**
     * 修改工厂人员能力
     * 
     * @param factoryPersonnel 工厂人员能力
     * @return 结果
     */
    @Override
    public int updateFactoryPersonnel(FactoryPersonnel factoryPersonnel)
    {
        factoryPersonnel.setUpdateTime(DateUtils.getNowDate());
        return factoryPersonnelMapper.updateFactoryPersonnel(factoryPersonnel);
    }

    /**
     * 批量删除工厂人员能力
     * 
     * @param ids 需要删除的工厂人员能力主键
     * @return 结果
     */
    @Override
    public int deleteFactoryPersonnelByIds(Long[] ids)
    {
        return factoryPersonnelMapper.deleteFactoryPersonnelByIds(ids);
    }

    /**
     * 删除工厂人员能力信息
     * 
     * @param id 工厂人员能力主键
     * @return 结果
     */
    @Override
    public int deleteFactoryPersonnelById(Long id)
    {
        return factoryPersonnelMapper.deleteFactoryPersonnelById(id);
    }
}
