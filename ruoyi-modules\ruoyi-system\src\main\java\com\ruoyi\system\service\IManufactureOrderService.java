package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.ManufactureOrder;
import com.ruoyi.system.domain.dto.OrderWithMaterialsDTO;

/**
 * 制造订单Service接口
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface IManufactureOrderService
{
    /**
     * 查询制造订单
     *
     * @param id 制造订单主键
     * @return 制造订单
     */
    public ManufactureOrder selectManufactureOrderById(Long id);

    /**
     * 查询制造订单列表
     *
     * @param manufactureOrder 制造订单
     * @return 制造订单集合
     */
    public List<ManufactureOrder> selectManufactureOrderList(ManufactureOrder manufactureOrder);

    /**
     * 新增制造订单
     *
     * @param manufactureOrder 制造订单
     * @return 结果
     */
    public int insertManufactureOrder(ManufactureOrder manufactureOrder);
    
    /**
     * 新增制造订单（包含物料信息）
     *
     * @param orderWithMaterials 制造订单及物料信息
     * @return 结果
     */
    public int insertOrderWithMaterials(OrderWithMaterialsDTO orderWithMaterials);

    /**
     * 修改制造订单
     *
     * @param manufactureOrder 制造订单
     * @return 结果
     */
    public int updateManufactureOrder(ManufactureOrder manufactureOrder);
    
    /**
     * 修改制造订单（包含物料信息）
     *
     * @param orderWithMaterials 制造订单及物料信息
     * @return 结果
     */
    public int updateOrderWithMaterials(OrderWithMaterialsDTO orderWithMaterials);

    /**
     * 批量删除制造订单
     *
     * @param ids 需要删除的制造订单主键集合
     * @return 结果
     */
    public int deleteManufactureOrderByIds(Long[] ids);

    /**
     * 删除制造订单信息
     *
     * @param id 制造订单主键
     * @return 结果
     */
    public int deleteManufactureOrderById(Long id);
    
    /**
     * 查询制造订单及其关联的物料信息
     *
     * @param id 制造订单主键
     * @return 制造订单及物料信息
     */
    public OrderWithMaterialsDTO selectOrderWithMaterialsById(Long id);
}
