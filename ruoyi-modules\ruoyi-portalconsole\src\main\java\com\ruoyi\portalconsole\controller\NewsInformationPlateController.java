package com.ruoyi.portalconsole.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.NewsInformationPlate;
import com.ruoyi.portalconsole.service.INewsInformationPlateService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 咨询板块Controller
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
@RestController
@RequestMapping("/NewsInformationPlate")
public class NewsInformationPlateController extends BaseController
{
    @Autowired
    private INewsInformationPlateService newsInformationPlateService;

    /**
     * 查询咨询板块列表
     */
    @RequiresPermissions("portalconsole:NewsInformationPlate:list")
    @GetMapping("/list")
    public TableDataInfo list(NewsInformationPlate newsInformationPlate)
    {
        startPage();
        List<NewsInformationPlate> list = newsInformationPlateService.selectNewsInformationPlateList(newsInformationPlate);
        return getDataTable(list);
    }

    /**
     * 导出咨询板块列表
     */
    @RequiresPermissions("portalconsole:NewsInformationPlate:export")
    @Log(title = "咨询板块", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NewsInformationPlate newsInformationPlate)
    {
        List<NewsInformationPlate> list = newsInformationPlateService.selectNewsInformationPlateList(newsInformationPlate);
        ExcelUtil<NewsInformationPlate> util = new ExcelUtil<NewsInformationPlate>(NewsInformationPlate.class);
        util.exportExcel(response, list, "咨询板块数据");
    }

    /**
     * 获取咨询板块详细信息
     */
    @RequiresPermissions("portalconsole:NewsInformationPlate:query")
    @GetMapping(value = "/{newsInformationPlateId}")
    public AjaxResult getInfo(@PathVariable("newsInformationPlateId") Long newsInformationPlateId)
    {
        return success(newsInformationPlateService.selectNewsInformationPlateByNewsInformationPlateId(newsInformationPlateId));
    }

    /**
     * 新增咨询板块
     */
    @RequiresPermissions("portalconsole:NewsInformationPlate:add")
    @Log(title = "咨询板块", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NewsInformationPlate newsInformationPlate)
    {
        return toAjax(newsInformationPlateService.insertNewsInformationPlate(newsInformationPlate));
    }

    /**
     * 修改咨询板块
     */
    @RequiresPermissions("portalconsole:NewsInformationPlate:edit")
    @Log(title = "咨询板块", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NewsInformationPlate newsInformationPlate)
    {
        return toAjax(newsInformationPlateService.updateNewsInformationPlate(newsInformationPlate));
    }

    /**
     * 删除咨询板块
     */
    @RequiresPermissions("portalconsole:NewsInformationPlate:remove")
    @Log(title = "咨询板块", businessType = BusinessType.DELETE)
	@DeleteMapping("/{newsInformationPlateIds}")
    public AjaxResult remove(@PathVariable Long[] newsInformationPlateIds)
    {
        return toAjax(newsInformationPlateService.deleteNewsInformationPlateByNewsInformationPlateIds(newsInformationPlateIds));
    }
}
