package com.ruoyi.portalweb.vo;

import com.ruoyi.portalweb.api.domain.IntentionApply;
import io.swagger.annotations.ApiModelProperty;

/**
 * 意向申请
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public class IntentionApplyVO extends IntentionApply {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "查询方式,我的供给queryType='my'")
    private String queryType;

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }


    public String getIntentionTypeName() {
        return intentionTypeName;
    }

    public void setIntentionTypeName(String intentionTypeName) {
        this.intentionTypeName = intentionTypeName;
    }

    @ApiModelProperty(value = "意向类型名称")
    private String intentionTypeName;

    public String getResourceTitle() {
        return resourceTitle;
    }

    public void setResourceTitle(String resourceTitle) {
        this.resourceTitle = resourceTitle;
    }

    @ApiModelProperty(value = "对应供需标题")
    private String resourceTitle;

}
