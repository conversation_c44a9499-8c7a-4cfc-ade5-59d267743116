package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.ClassicCaseMapper;
import com.ruoyi.portalconsole.domain.ClassicCase;
import com.ruoyi.portalconsole.service.IClassicCaseService;

/**
 * 典型案例Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class ClassicCaseServiceImpl implements IClassicCaseService 
{
    @Autowired
    private ClassicCaseMapper classicCaseMapper;

    /**
     * 查询典型案例
     * 
     * @param classicCaseId 典型案例主键
     * @return 典型案例
     */
    @Override
    public ClassicCase selectClassicCaseByClassicCaseId(Long classicCaseId)
    {
        return classicCaseMapper.selectClassicCaseByClassicCaseId(classicCaseId);
    }

    /**
     * 查询典型案例列表
     * 
     * @param classicCase 典型案例
     * @return 典型案例
     */
    @Override
    public List<ClassicCase> selectClassicCaseList(ClassicCase classicCase)
    {
        return classicCaseMapper.selectClassicCaseList(classicCase);
    }

    /**
     * 新增典型案例
     * 
     * @param classicCase 典型案例
     * @return 结果
     */
    @Override
    public int insertClassicCase(ClassicCase classicCase)
    {
        classicCase.setCreateTime(DateUtils.getNowDate());
        return classicCaseMapper.insertClassicCase(classicCase);
    }

    /**
     * 修改典型案例
     * 
     * @param classicCase 典型案例
     * @return 结果
     */
    @Override
    public int updateClassicCase(ClassicCase classicCase)
    {
        classicCase.setUpdateTime(DateUtils.getNowDate());
        return classicCaseMapper.updateClassicCase(classicCase);
    }

    /**
     * 批量删除典型案例
     * 
     * @param classicCaseIds 需要删除的典型案例主键
     * @return 结果
     */
    @Override
    public int deleteClassicCaseByClassicCaseIds(Long[] classicCaseIds)
    {
        return classicCaseMapper.deleteClassicCaseByClassicCaseIds(classicCaseIds);
    }

    /**
     * 删除典型案例信息
     * 
     * @param classicCaseId 典型案例主键
     * @return 结果
     */
    @Override
    public int deleteClassicCaseByClassicCaseId(Long classicCaseId)
    {
        return classicCaseMapper.deleteClassicCaseByClassicCaseId(classicCaseId);
    }
}
