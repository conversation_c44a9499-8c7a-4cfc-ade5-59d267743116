package com.ruoyi.portalconsole.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 应用商店订单对象 app_store_order
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
public class AppStoreOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 应用商店订单ID
     */
    private Long appStoreOrderId;

    /**
     * 订单编号
     */
    @Excel(name = "订单编号")
    private String appStoreOrderNo;

    /**
     * 销售公司ID
     */
    @Excel(name = "销售公司ID")
    private Long saleCompanyId;

    /**
     * 购买公司ID
     */
    @Excel(name = "购买公司ID")
    private Long buyCompanyId;

    /**
     * 下单时间
     */
    @Excel(name = "下单时间")
    private Date orderTime;

    /**
     * 价格
     */
    @Excel(name = "价格")
    private BigDecimal appStorePrice;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 应用id
     */
    private Long appStoreId;

    /**
     * 销售用户ID
     */
    private Long saleMemberId;

    /**
     * 购买用户ID
     */
    private Long buyMemberId;
    /**
     *  应用名称
     */
    @Excel(name = "应用名称")
    private String appStoreName;

    /**
     *  订单状态
     */
    @Excel(name = "订单状态")
    private String orderStatus;

    @Excel(name = "应用地址(交付方式-SaaS使用时填写)")
    private String erweima;
    @Excel(name = "下载服务(交付方式-下载服务时填写)")
    private String downloadUrl;
    @Excel(name = "发票状态")
    private String invoiceStatus;
    @Excel(name = "发票图片")
    private String invoiceImageUrl;

    /** 服务器出口ip */
    @Excel(name = "服务器出口ip")
    private String ip;

    /** 应用web地址 */
    @Excel(name = "应用web地址")
    private String appWebUrl;

    /** 应用web地址(体验) */
    @Excel(name = "应用web地址(体验)")
    private String appWebTrialUrl;

    /** 健康检查服务地址 */
    @Excel(name = "健康检查服务地址")
    private String healthInspectionUrl;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contact;

    /** 测试token */
    @Excel(name = "测试token")
    private String testToken;

    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(String invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    public String getInvoiceImageUrl() {
        return invoiceImageUrl;
    }

    public void setInvoiceImageUrl(String invoiceImageUrl) {
        this.invoiceImageUrl = invoiceImageUrl;
    }

    public String getAppStoreName() {
        return appStoreName;
    }

    public void setAppStoreName(String appStoreName) {
        this.appStoreName = appStoreName;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getErweima() {
        return erweima;
    }

    public void setErweima(String erweima) {
        this.erweima = erweima;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Long getSaleMemberId() {
        return saleMemberId;
    }

    public void setSaleMemberId(Long saleMemberId) {
        this.saleMemberId = saleMemberId;
    }

    public Long getBuyMemberId() {
        return buyMemberId;
    }

    public void setBuyMemberId(Long buyMemberId) {
        this.buyMemberId = buyMemberId;
    }

    public Long getAppStoreId() {
        return appStoreId;
    }

    public void setAppStoreId(Long appStoreId) {
        this.appStoreId = appStoreId;
    }


    public void setAppStoreOrderId(Long appStoreOrderId) {
        this.appStoreOrderId = appStoreOrderId;
    }

    public Long getAppStoreOrderId() {
        return appStoreOrderId;
    }

    public void setAppStoreOrderNo(String appStoreOrderNo) {
        this.appStoreOrderNo = appStoreOrderNo;
    }

    public String getAppStoreOrderNo() {
        return appStoreOrderNo;
    }

    public void setSaleCompanyId(Long saleCompanyId) {
        this.saleCompanyId = saleCompanyId;
    }

    public Long getSaleCompanyId() {
        return saleCompanyId;
    }

    public void setBuyCompanyId(Long buyCompanyId) {
        this.buyCompanyId = buyCompanyId;
    }

    public Long getBuyCompanyId() {
        return buyCompanyId;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setAppStorePrice(BigDecimal appStorePrice) {
        this.appStorePrice = appStorePrice;
    }

    public BigDecimal getAppStorePrice() {
        return appStorePrice;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setIp(String ip)
    {
        this.ip = ip;
    }

    public String getIp()
    {
        return ip;
    }
    public void setAppWebUrl(String appWebUrl)
    {
        this.appWebUrl = appWebUrl;
    }

    public String getAppWebUrl()
    {
        return appWebUrl;
    }
    public void setAppWebTrialUrl(String appWebTrialUrl)
    {
        this.appWebTrialUrl = appWebTrialUrl;
    }

    public String getAppWebTrialUrl()
    {
        return appWebTrialUrl;
    }
    public void setHealthInspectionUrl(String healthInspectionUrl)
    {
        this.healthInspectionUrl = healthInspectionUrl;
    }

    public String getHealthInspectionUrl()
    {
        return healthInspectionUrl;
    }
    public void setContact(String contact)
    {
        this.contact = contact;
    }

    public String getContact()
    {
        return contact;
    }
    public void setTestToken(String testToken)
    {
        this.testToken = testToken;
    }

    public String getTestToken()
    {
        return testToken;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("appStoreOrderId", getAppStoreOrderId())
                .append("appStoreOrderNo", getAppStoreOrderNo())
                .append("saleCompanyId", getSaleCompanyId())
                .append("buyCompanyId", getBuyCompanyId())
                .append("appStoreName", getAppStoreName())
                .append("orderStatus", getOrderStatus())
                .append("erweima", getErweima())
                .append("downloadUrl", getDownloadUrl())
                .append("orderTime", getOrderTime())
                .append("appStorePrice", getAppStorePrice())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
