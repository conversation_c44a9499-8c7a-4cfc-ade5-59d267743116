package com.ruoyi.portalweb.scm.service;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.portalweb.api.domain.AppStoreOrder;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public interface IWxPayService {

    Map<String, Object> nativePay(AppStoreOrder appStoreOrder) throws Exception;

    AjaxResult jsapiPay(AppStoreOrder appStoreOrder, HttpServletRequest request);
//
//    Map<String, Object> nativePayModel(AppStoreOrder appStoreOrder) throws Exception;
}
