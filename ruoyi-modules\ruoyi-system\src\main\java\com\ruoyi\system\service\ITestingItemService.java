package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.TestingItem;
import com.ruoyi.system.domain.dto.TestingItemDTO;
import com.ruoyi.system.domain.dto.TestingItemWithLabsDTO;

/**
 * 检测项目Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface ITestingItemService 
{
    /**
     * 查询检测项目
     * 
     * @param id 检测项目主键
     * @return 检测项目
     */
    public TestingItem selectTestingItemById(Long id);

    /**
     * 查询检测项目列表
     * 
     * @param testingItem 检测项目
     * @return 检测项目集合
     */
    public List<TestingItem> selectTestingItemList(TestingItem testingItem);

    /**
     * 新增检测项目
     * 
     * @param testingItem 检测项目
     * @return 结果
     */
    public int insertTestingItem(TestingItem testingItem);

    /**
     * 新增检测项目并关联实验室
     * 
     * @param testingItemDTO 检测项目数据传输对象
     * @return 结果
     */
    public int insertTestingItemWithLabs(TestingItemDTO testingItemDTO);

    /**
     * 修改检测项目
     * 
     * @param testingItem 检测项目
     * @return 结果
     */
    public int updateTestingItem(TestingItem testingItem);

    /**
     * 修改检测项目并更新关联实验室
     * 
     * @param testingItemDTO 检测项目数据传输对象
     * @return 结果
     */
    public int updateTestingItemWithLabs(TestingItemDTO testingItemDTO);

    /**
     * 批量删除检测项目
     * 
     * @param ids 需要删除的检测项目主键集合
     * @return 结果
     */
    public int deleteTestingItemByIds(Long[] ids);

    /**
     * 删除检测项目信息
     * 
     * @param id 检测项目主键
     * @return 结果
     */
    public int deleteTestingItemById(Long id);

    /**
     * 查询检测项目详细信息及关联实验室
     * 
     * @param id 检测项目主键
     * @return 检测项目及关联实验室信息
     */
    public TestingItemWithLabsDTO selectTestingItemWithLabsById(Long id);
    
    /**
     * 查询检测项目列表及关联实验室
     * 
     * @param testingItem 检测项目
     * @return 检测项目及关联实验室信息集合
     */
    public List<TestingItemWithLabsDTO> selectTestingItemWithLabsList(TestingItem testingItem);
    
    /**
     * 查询检测项目列表并左联查检测实验室
     * 
     * @param labType 实验室类型
     * @return 检测项目及关联实验室信息集合
     */
    public List<TestingItemWithLabsDTO> selectTestingItemLeftJoinLabs(String labType);
}
