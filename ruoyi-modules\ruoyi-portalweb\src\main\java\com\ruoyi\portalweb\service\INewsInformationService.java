package com.ruoyi.portalweb.service;


import com.ruoyi.portalweb.api.domain.NewsInformation;
import com.ruoyi.portalweb.vo.NewsInformationVO;

import java.util.List;

/**
 * 动态资讯Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface INewsInformationService 
{
    /**
     * 查询动态资讯
     * 
     * @param newsInformationId 动态资讯主键
     * @return 动态资讯
     */
    public NewsInformationVO selectNewsInformationByNewsInformationId(Long newsInformationId);

    /**
     * 查询动态资讯列表
     * 
     * @param newsInformation 动态资讯
     * @return 动态资讯集合
     */
    public List<NewsInformationVO> selectNewsInformationList(NewsInformationVO newsInformation);

    /**
     * 新增动态资讯
     * 
     * @param newsInformation 动态资讯
     * @return 结果
     */
    public int insertNewsInformation(NewsInformation newsInformation);

    /**
     * 修改动态资讯
     * 
     * @param newsInformation 动态资讯
     * @return 结果
     */
    public int updateNewsInformation(NewsInformation newsInformation);

    /**
     * 批量删除动态资讯
     * 
     * @param newsInformationIds 需要删除的动态资讯主键集合
     * @return 结果
     */
    public int deleteNewsInformationByNewsInformationIds(Long[] newsInformationIds);

    /**
     * 删除动态资讯信息
     * 
     * @param newsInformationId 动态资讯主键
     * @return 结果
     */
    public int deleteNewsInformationByNewsInformationId(Long newsInformationId);
}
