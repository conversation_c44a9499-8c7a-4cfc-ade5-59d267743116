package com.ruoyi.portalweb.mapper;


import com.ruoyi.portalweb.api.domain.IntentionApply;
import com.ruoyi.portalweb.vo.IntentionApplyVO;

import java.util.List;

/**
 * 意向申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
public interface IntentionApplyMapper 
{
    /**
     * 查询意向申请
     * 
     * @param id 意向申请主键
     * @return 意向申请
     */
    public IntentionApplyVO selectIntentionApplyById(Long id);

    /**
     * 查询意向申请列表
     * 
     * @param intentionApply 意向申请
     * @return 意向申请集合
     */
    public List<IntentionApplyVO> selectIntentionApplyList(IntentionApplyVO intentionApply);

    /**
     * 新增意向申请
     * 
     * @param intentionApply 意向申请
     * @return 结果
     */
    public int insertIntentionApply(IntentionApply intentionApply);

    /**
     * 修改意向申请
     * 
     * @param intentionApply 意向申请
     * @return 结果
     */
    public int updateIntentionApply(IntentionApply intentionApply);

    /**
     * 删除意向申请
     * 
     * @param id 意向申请主键
     * @return 结果
     */
    public int deleteIntentionApplyById(Long id);

    /**
     * 批量删除意向申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIntentionApplyByIds(Long[] ids);
}
