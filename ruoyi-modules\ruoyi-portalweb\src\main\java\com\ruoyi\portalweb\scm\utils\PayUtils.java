package com.ruoyi.portalweb.scm.utils;

import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.LinkedList;
import java.util.List;


public class PayUtils {

    protected static Logger logger = LoggerFactory.getLogger(PayUtils.class);



    /**
     * 参数转换为XML
     *
     * @param params
     * @return
     */
    public static String toXml(List<NameValuePair> params) {
        StringBuilder sb = new StringBuilder();
        sb.append("<xml>");
        for (int i = 0; i < params.size(); i++) {
            sb.append("<" + params.get(i).getName() + ">");

            sb.append(params.get(i).getValue());
            sb.append("</" + params.get(i).getName() + ">");
        }
        sb.append("</xml>");

        //logger.debug("xml:{}", sb.toString());
        /**
         * 需要注意的 获取到的字符串要编码
         */
        try {
            return new String(sb.toString().getBytes(), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 接受微信回调结果
     *
     * @param request
     * @return
     * @throws IOException
     */
    public static String reciverWx(HttpServletRequest request) throws IOException {
        InputStream inputStream;
        StringBuffer sb = new StringBuffer();
        inputStream = request.getInputStream();
        String s;
        BufferedReader in = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
        while ((s = in.readLine()) != null) {
            sb.append(s);
        }
        in.close();
        inputStream.close();
        return sb.toString();
    }

    /**
     *
     * @param prepayid
     * @param nonceStr
     * @return
     */
    public static String genAppParams(String prepayid,String nonceStr,String appSecret,String orderId) {
        List<NameValuePair> signParams = new LinkedList<NameValuePair>();
      //  signParams.add(new BasicNameValuePair("appId", "wxe4602a0a1a3c4775")); // 云端研发小程序应用ID
       // signParams.add(new BasicNameValuePair("appId", "wxe411837960491937")); // 随机字符串
       // signParams.add(new BasicNameValuePair("appId", "wx0d647b53d9ee9557")); // 国家海洋小程序应用ID
        signParams.add(new BasicNameValuePair("appId","wx7087db0843201a88"));//云研工作室小程序应用ID
        signParams.add(new BasicNameValuePair("nonceStr", nonceStr)); // 随机字符串
        signParams.add(new BasicNameValuePair("package", "prepay_id="+prepayid)); // 扩展字段 暂填写固定值Sign=WXPay
        signParams.add(new BasicNameValuePair("signType", "MD5"));
        signParams.add(new BasicNameValuePair("timeStamp", String.valueOf(genTimeStamp()))); // 时间戳
        String paySign = genPackageSign(signParams,appSecret);
        signParams.add(new BasicNameValuePair("paySign", paySign));
        signParams.add(new BasicNameValuePair("orderId", orderId)); // 业务订单ID
        String xmlstring = toXml(signParams);
        return xmlstring;
    }

    /**
     * 获取时间戳
     *
     * @return
     */
    public static long genTimeStamp() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 生成签名
     */
    public static String genPackageSign(List<NameValuePair> params,String appSecret) {
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < params.size(); i++) {
            sb.append(params.get(i).getName());
            sb.append('=');
            sb.append(params.get(i).getValue());
            sb.append('&');
        }
        sb.append("key=");
        sb.append(appSecret);
        String packageSign = MD5Utils.getMessageDigest(sb.toString().getBytes()).toUpperCase();
        //logger.debug("订单签名:{}", packageSign);
        return packageSign;
    }




}
