package com.ruoyi.portalweb.vo;

import com.ruoyi.portalweb.api.domain.Message;
import io.swagger.annotations.ApiModelProperty;

/**
 * 消息
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public class MessageVO extends Message
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "查询方式,我的供给queryType='my'")
    private String queryType;
	public String getQueryType() {
		return queryType;
	}
	public void setQueryType(String queryType) {
		this.queryType = queryType;
	}

	@ApiModelProperty(value = "消息状态(已读/未读)")
    private String messageStatusName;
	public String getMessageStatusName() {
		return messageStatusName;
	}
	public void setQMessageStatusName(String messageStatusName) {
		this.messageStatusName = messageStatusName;
	}

   
}
