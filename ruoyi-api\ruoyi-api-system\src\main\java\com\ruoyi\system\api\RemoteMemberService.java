package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.Member;
import com.ruoyi.system.api.factory.RemoteMemberFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 会员服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteSystemMemberService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteMemberFallbackFactory.class)
public interface RemoteMemberService {

    /**
     * 通过手机号查询会员信息
     *
     * @param memberPhone 手机号
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/member/info/{memberPhone}")
    public R<Member> getMemberInfo(@PathVariable("memberPhone") String memberPhone, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 验证会员密码
     *
     * @param memberPhone 手机号
     * @param password 密码
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/member/validate/{memberPhone}/{password}")
    public R<Member> validateMemberPassword(@PathVariable("memberPhone") String memberPhone, 
                                          @PathVariable("password") String password,
                                          @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
