package com.ruoyi.im.api;

import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.im.api.domain.ImUserApply;
import com.ruoyi.im.api.dto.ImUserApplyVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.api
 * @ClassName: RemoteImUserapplyService
 * @Author: ${maguojun}
 * @Description: ${description}
 * @Date: 2022/3/16 13:55
 * @Version: 1.0
 */
@FeignClient(contextId = "remoteImUserapplyService", value = ServiceNameConstants.IM_SERVICE)
public interface RemoteImUserapplyService {


    /***
     * 查看用户所有的好友申请列表
     * @param imUserApply
     * @return
     */
    @PostMapping(value = "/im/userapply/list" )
    R<List<ImUserApplyVo>> userApplyList(@RequestBody ImUserApply imUserApply);


    /***
     * 新增ImUserApply数据  申请好友
     * @param imUserApply
     * @return
     */
    @PostMapping(value="/im/userapply/apply")
    R<Long> add(@RequestBody ImUserApply imUserApply);

    /***
     * 同意或者拒绝，同意操作需要添加好友记录
     * @param imUserApply
     * @return
     */
    @PostMapping(value="/im/userapply/op")
    R<Boolean> updateStatus(@RequestBody  ImUserApply imUserApply);

    /**
     * 数量
     * @param imUserApply
     * @return
     */
    @PostMapping(value = "/im/userapply/number" )
    R<Integer> userApplyNumber(@RequestBody ImUserApply imUserApply);
}
