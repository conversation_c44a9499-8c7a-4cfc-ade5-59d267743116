package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.SolutionAdvantageMapper;
import com.ruoyi.portalconsole.domain.SolutionAdvantage;
import com.ruoyi.portalconsole.service.ISolutionAdvantageService;

/**
 * 解决方案方案优势Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class SolutionAdvantageServiceImpl implements ISolutionAdvantageService 
{
    @Autowired
    private SolutionAdvantageMapper solutionAdvantageMapper;

    /**
     * 查询解决方案方案优势
     * 
     * @param solutionAdvantageId 解决方案方案优势主键
     * @return 解决方案方案优势
     */
    @Override
    public SolutionAdvantage selectSolutionAdvantageBySolutionAdvantageId(Long solutionAdvantageId)
    {
        return solutionAdvantageMapper.selectSolutionAdvantageBySolutionAdvantageId(solutionAdvantageId);
    }

    /**
     * 查询解决方案方案优势列表
     * 
     * @param solutionAdvantage 解决方案方案优势
     * @return 解决方案方案优势
     */
    @Override
    public List<SolutionAdvantage> selectSolutionAdvantageList(SolutionAdvantage solutionAdvantage)
    {
        return solutionAdvantageMapper.selectSolutionAdvantageList(solutionAdvantage);
    }

    /**
     * 新增解决方案方案优势
     * 
     * @param solutionAdvantage 解决方案方案优势
     * @return 结果
     */
    @Override
    public int insertSolutionAdvantage(SolutionAdvantage solutionAdvantage)
    {
        solutionAdvantage.setCreateTime(DateUtils.getNowDate());
        return solutionAdvantageMapper.insertSolutionAdvantage(solutionAdvantage);
    }

    /**
     * 修改解决方案方案优势
     * 
     * @param solutionAdvantage 解决方案方案优势
     * @return 结果
     */
    @Override
    public int updateSolutionAdvantage(SolutionAdvantage solutionAdvantage)
    {
        solutionAdvantage.setUpdateTime(DateUtils.getNowDate());
        return solutionAdvantageMapper.updateSolutionAdvantage(solutionAdvantage);
    }

    /**
     * 批量删除解决方案方案优势
     * 
     * @param solutionAdvantageIds 需要删除的解决方案方案优势主键
     * @return 结果
     */
    @Override
    public int deleteSolutionAdvantageBySolutionAdvantageIds(Long[] solutionAdvantageIds)
    {
        return solutionAdvantageMapper.deleteSolutionAdvantageBySolutionAdvantageIds(solutionAdvantageIds);
    }

    /**
     * 删除解决方案方案优势信息
     * 
     * @param solutionAdvantageId 解决方案方案优势主键
     * @return 结果
     */
    @Override
    public int deleteSolutionAdvantageBySolutionAdvantageId(Long solutionAdvantageId)
    {
        return solutionAdvantageMapper.deleteSolutionAdvantageBySolutionAdvantageId(solutionAdvantageId);
    }
}
