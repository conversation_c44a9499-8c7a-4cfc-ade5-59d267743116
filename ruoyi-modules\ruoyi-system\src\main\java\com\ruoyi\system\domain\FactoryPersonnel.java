package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 工厂人员能力对象 factory_personnel
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public class FactoryPersonnel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 工厂ID */
    @Excel(name = "工厂ID")
    private Long factoryId;

    /** 技术人员姓名 */
    @Excel(name = "技术人员姓名")
    private String technicianName;

    /** 专业技术工种 */
    @Excel(name = "专业技术工种")
    private String technicalType;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setFactoryId(Long factoryId) 
    {
        this.factoryId = factoryId;
    }

    public Long getFactoryId() 
    {
        return factoryId;
    }
    public void setTechnicianName(String technicianName) 
    {
        this.technicianName = technicianName;
    }

    public String getTechnicianName() 
    {
        return technicianName;
    }
    public void setTechnicalType(String technicalType) 
    {
        this.technicalType = technicalType;
    }

    public String getTechnicalType() 
    {
        return technicalType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("factoryId", getFactoryId())
            .append("technicianName", getTechnicianName())
            .append("technicalType", getTechnicalType())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
