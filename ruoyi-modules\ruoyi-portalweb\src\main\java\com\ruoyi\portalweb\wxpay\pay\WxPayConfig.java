package com.ruoyi.portalweb.wxpay.pay;

/**
 * 微信支付配置类
 * 
 * <AUTHOR>
 * 
 */
public abstract class WxPayConfig {

	/**
	 * 预支付接口
	 */
	public static final String PREPAY_URL = String.format("https://api.mch.weixin.qq.com/pay/unifiedorder");

	/**
	 * 查询订单接口
	 */
	public static final String ORDER_QUERY_URL = "https://api.mch.weixin.qq.com/pay/orderquery";

	/**
	 * 退款接口
	 */
	public static final String REFUND_URL = "https://api.mch.weixin.qq.com/secapi/pay/refund";
	
	 /**
     * HTTP(S) 连接超时时间，单位毫秒
     *
     * @return
     */
    public static int getHttpConnectTimeoutMs() {
        return 6*1000;
    }

    /**
     * HTTP(S) 读数据超时时间，单位毫秒
     *
     * @return
     */
    public static int getHttpReadTimeoutMs() {
        return 8*1000;
    }

}
