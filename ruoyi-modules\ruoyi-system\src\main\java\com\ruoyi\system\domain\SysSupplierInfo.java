package com.ruoyi.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 供方信息对象 sys_supplier_info
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public class SysSupplierInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 供方ID */
    private Long supplierId;

    /** 关联的技术需求ID */
    @Excel(name = "关联的技术需求ID")
    private Long techRequirementId;

    /** 技术需求标题（关联查询字段） */
    @Excel(name = "技术需求标题")
    private String requirementTitle;

    /** 单位名称 */
    @Excel(name = "单位名称")
    private String companyName;

    /** 供给类型（1资金 2技术 3资金+技术） */
    @Excel(name = "供给类型", readConverterExp = "1=资金,2=技术,3=资金+技术")
    private String supplyType;

    /** 供给内容描述 */
    @Excel(name = "供给内容描述")
    private String supplyContent;

    /** 供给资金金额（元） */
    @Excel(name = "供给资金金额", readConverterExp = "元=")
    private BigDecimal supplyAmount;

    /** 技术领域 */
    @Excel(name = "技术领域")
    private String technologyField;

    /** 技术描述 */
    @Excel(name = "技术描述")
    private String technologyDescription;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    private String contactEmail;

    /** 公司地址 */
    @Excel(name = "公司地址")
    private String companyAddress;

    /** 经营范围 */
    @Excel(name = "经营范围")
    private String businessScope;

    /** 合作方式 */
    @Excel(name = "合作方式")
    private String cooperationMode;

    /** 供方状态（0正常 1停用） */
    @Excel(name = "供方状态", readConverterExp = "0=正常,1=停用")
    private String supplierStatus;

    /** 资质等级 */
    @Excel(name = "资质等级")
    private String certificationLevel;

    /** 附件地址 */
    @Excel(name = "附件地址")
    private String attachmentUrl;

    public void setSupplierId(Long supplierId) 
    {
        this.supplierId = supplierId;
    }

    public Long getSupplierId()
    {
        return supplierId;
    }

    public void setTechRequirementId(Long techRequirementId)
    {
        this.techRequirementId = techRequirementId;
    }

    public Long getTechRequirementId()
    {
        return techRequirementId;
    }

    public void setRequirementTitle(String requirementTitle)
    {
        this.requirementTitle = requirementTitle;
    }

    public String getRequirementTitle()
    {
        return requirementTitle;
    }

    public void setCompanyName(String companyName)
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    public void setSupplyType(String supplyType) 
    {
        this.supplyType = supplyType;
    }

    public String getSupplyType() 
    {
        return supplyType;
    }
    public void setSupplyContent(String supplyContent) 
    {
        this.supplyContent = supplyContent;
    }

    public String getSupplyContent() 
    {
        return supplyContent;
    }
    public void setSupplyAmount(BigDecimal supplyAmount) 
    {
        this.supplyAmount = supplyAmount;
    }

    public BigDecimal getSupplyAmount() 
    {
        return supplyAmount;
    }
    public void setTechnologyField(String technologyField) 
    {
        this.technologyField = technologyField;
    }

    public String getTechnologyField() 
    {
        return technologyField;
    }
    public void setTechnologyDescription(String technologyDescription) 
    {
        this.technologyDescription = technologyDescription;
    }

    public String getTechnologyDescription() 
    {
        return technologyDescription;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setContactEmail(String contactEmail) 
    {
        this.contactEmail = contactEmail;
    }

    public String getContactEmail() 
    {
        return contactEmail;
    }
    public void setCompanyAddress(String companyAddress) 
    {
        this.companyAddress = companyAddress;
    }

    public String getCompanyAddress() 
    {
        return companyAddress;
    }
    public void setBusinessScope(String businessScope) 
    {
        this.businessScope = businessScope;
    }

    public String getBusinessScope() 
    {
        return businessScope;
    }
    public void setCooperationMode(String cooperationMode) 
    {
        this.cooperationMode = cooperationMode;
    }

    public String getCooperationMode() 
    {
        return cooperationMode;
    }
    public void setSupplierStatus(String supplierStatus) 
    {
        this.supplierStatus = supplierStatus;
    }

    public String getSupplierStatus() 
    {
        return supplierStatus;
    }
    public void setCertificationLevel(String certificationLevel) 
    {
        this.certificationLevel = certificationLevel;
    }

    public String getCertificationLevel() 
    {
        return certificationLevel;
    }
    public void setAttachmentUrl(String attachmentUrl) 
    {
        this.attachmentUrl = attachmentUrl;
    }

    public String getAttachmentUrl() 
    {
        return attachmentUrl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("supplierId", getSupplierId())
            .append("techRequirementId", getTechRequirementId())
            .append("requirementTitle", getRequirementTitle())
            .append("companyName", getCompanyName())
            .append("supplyType", getSupplyType())
            .append("supplyContent", getSupplyContent())
            .append("supplyAmount", getSupplyAmount())
            .append("technologyField", getTechnologyField())
            .append("technologyDescription", getTechnologyDescription())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("contactEmail", getContactEmail())
            .append("companyAddress", getCompanyAddress())
            .append("businessScope", getBusinessScope())
            .append("cooperationMode", getCooperationMode())
            .append("supplierStatus", getSupplierStatus())
            .append("certificationLevel", getCertificationLevel())
            .append("attachmentUrl", getAttachmentUrl())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
