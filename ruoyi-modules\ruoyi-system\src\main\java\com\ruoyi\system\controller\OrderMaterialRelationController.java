package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.OrderMaterialRelation;
import com.ruoyi.system.service.IOrderMaterialRelationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 订单物料关联Controller
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
@RestController
@RequestMapping("/orderMaterialRelation")
public class OrderMaterialRelationController extends BaseController
{
    @Autowired
    private IOrderMaterialRelationService orderMaterialRelationService;

    /**
     * 查询订单物料关联列表
     */
    @GetMapping("/list")
    public TableDataInfo list(OrderMaterialRelation orderMaterialRelation)
    {
        startPage();
        List<OrderMaterialRelation> list = orderMaterialRelationService.selectOrderMaterialRelationList(orderMaterialRelation);
        return getDataTable(list);
    }

    /**
     * 导出订单物料关联列表
     */
    @Log(title = "订单物料关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrderMaterialRelation orderMaterialRelation)
    {
        List<OrderMaterialRelation> list = orderMaterialRelationService.selectOrderMaterialRelationList(orderMaterialRelation);
        ExcelUtil<OrderMaterialRelation> util = new ExcelUtil<OrderMaterialRelation>(OrderMaterialRelation.class);
        util.exportExcel(response, list, "订单物料关联数据");
    }

    /**
     * 获取订单物料关联详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(orderMaterialRelationService.selectOrderMaterialRelationById(id));
    }

    /**
     * 获取订单关联的物料列表
     */
    @GetMapping(value = "/order/{orderId}")
    public AjaxResult getOrderMaterials(@PathVariable("orderId") Long orderId)
    {
        return success(orderMaterialRelationService.selectOrderMaterialRelationByOrderId(orderId));
    }

    /**
     * 获取物料关联的订单列表
     */
    @GetMapping(value = "/material/{materialId}")
    public AjaxResult getMaterialOrders(@PathVariable("materialId") Long materialId)
    {
        return success(orderMaterialRelationService.selectOrderMaterialRelationByMaterialId(materialId));
    }

    /**
     * 新增订单物料关联
     */
    @Log(title = "订单物料关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OrderMaterialRelation orderMaterialRelation)
    {
        return toAjax(orderMaterialRelationService.insertOrderMaterialRelation(orderMaterialRelation));
    }

    /**
     * 修改订单物料关联
     */
    @Log(title = "订单物料关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrderMaterialRelation orderMaterialRelation)
    {
        return toAjax(orderMaterialRelationService.updateOrderMaterialRelation(orderMaterialRelation));
    }

    /**
     * 删除订单物料关联
     */
    @Log(title = "订单物料关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(orderMaterialRelationService.deleteOrderMaterialRelationByIds(ids));
    }

    /**
     * 删除订单的所有物料关联
     */
    @Log(title = "订单物料关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/order/{orderId}")
    public AjaxResult removeByOrderId(@PathVariable("orderId") Long orderId)
    {
        return toAjax(orderMaterialRelationService.deleteOrderMaterialRelationByOrderId(orderId));
    }

    /**
     * 删除物料的所有订单关联
     */
    @Log(title = "订单物料关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/material/{materialId}")
    public AjaxResult removeByMaterialId(@PathVariable("materialId") Long materialId)
    {
        return toAjax(orderMaterialRelationService.deleteOrderMaterialRelationByMaterialId(materialId));
    }
}
