package com.ruoyi.portalweb.mapper;

import com.ruoyi.portalweb.api.domain.SolutionAdvantage;
import com.ruoyi.portalweb.api.domain.SolutionCase;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 解决方案方案优势Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface SolutionAdvantageMapper 
{
    /**
     * 查询解决方案方案优势
     * 
     * @param solutionAdvantageId 解决方案方案优势主键
     * @return 解决方案方案优势
     */
    public SolutionAdvantage selectSolutionAdvantageBySolutionAdvantageId(Long solutionAdvantageId);

    /**
     * 查询解决方案方案优势列表
     * 
     * @param solutionAdvantage 解决方案方案优势
     * @return 解决方案方案优势集合
     */
    public List<SolutionAdvantage> selectSolutionAdvantageList(SolutionAdvantage solutionAdvantage);

    /**
	 * 查询列表按解决方案id
	 */
	public List<SolutionAdvantage> selectListBySolutionId(@Param("solutionId") Long solutionId);

    /**
     * 新增解决方案方案优势
     * 
     * @param solutionAdvantage 解决方案方案优势
     * @return 结果
     */
    public int insertSolutionAdvantage(SolutionAdvantage solutionAdvantage);

    /**
     * 新增解决方案方案优势
     *
     * @param solutionAdvantageList 解决方案方案优势
     * @return 结果
     */
    public int insertSolutionAdvantageList(List<SolutionAdvantage> solutionAdvantageList);

    /**
     * 修改解决方案方案优势
     * 
     * @param solutionAdvantage 解决方案方案优势
     * @return 结果
     */
    public int updateSolutionAdvantage(SolutionAdvantage solutionAdvantage);

    /**
     * 删除解决方案方案优势
     * 
     * @param solutionAdvantageId 解决方案方案优势主键
     * @return 结果
     */
    public int deleteSolutionAdvantageBySolutionAdvantageId(Long solutionAdvantageId);

    /**
     * 批量删除解决方案方案优势
     * 
     * @param solutionAdvantageIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSolutionAdvantageBySolutionAdvantageIds(Long[] solutionAdvantageIds);

    /**
     * 批量删除解决方案方案优势
     *
     * @param solutionId 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSolutionAdvantageBySolutionId(Long solutionId);

    /**
     * 批量删除解决方案方案优势
     *
     * @param solutionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSolutionAdvantageBySolutionIds(Long[] solutionIds);

    public List<SolutionAdvantage> selectSolutionAdvantageListBySolutionIds(List<Long> solutionIds);
}
