package com.ruoyi.portalweb.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalweb.api.domain.Recommendation;
import com.ruoyi.portalweb.service.IRecommendationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 建议及反馈Controller
 * 
 * <AUTHOR>
 * @date 2024-08-06
 */
@RestController
@RequestMapping("/recommendation")
public class RecommendationController extends BaseController
{
    @Autowired
    private IRecommendationService recommendationService;

    /**
     * 查询建议及反馈列表
     */
    @RequiresPermissions("portalweb:recommendation:list")
    @GetMapping("/list")
    public TableDataInfo list(Recommendation recommendation)
    {
        startPage();
        List<Recommendation> list = recommendationService.selectRecommendationList(recommendation);
        return getDataTable(list);
    }

    /**
     * 导出建议及反馈列表
     */
    @RequiresPermissions("portalweb:recommendation:export")
    @Log(title = "建议及反馈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Recommendation recommendation)
    {
        List<Recommendation> list = recommendationService.selectRecommendationList(recommendation);
        ExcelUtil<Recommendation> util = new ExcelUtil<Recommendation>(Recommendation.class);
        util.exportExcel(response, list, "建议及反馈数据");
    }

    /**
     * 获取建议及反馈详细信息
     */
    @RequiresPermissions("portalweb:recommendation:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recommendationService.selectRecommendationById(id));
    }

    /**
     * 新增建议及反馈
     */
    @Log(title = "建议及反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Recommendation recommendation)
    {
        return toAjax(recommendationService.insertRecommendation(recommendation));
    }

    /**
     * 修改建议及反馈
     */
    @RequiresPermissions("portalweb:recommendation:edit")
    @Log(title = "建议及反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Recommendation recommendation)
    {
        return toAjax(recommendationService.updateRecommendation(recommendation));
    }

    /**
     * 删除建议及反馈
     */
    @RequiresPermissions("portalweb:recommendation:remove")
    @Log(title = "建议及反馈", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recommendationService.deleteRecommendationByIds(ids));
    }
}
