package com.ruoyi.system.api;

import com.ruoyi.system.api.domain.TalentInfoApi;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;

import com.ruoyi.system.api.factory.RemoteTalentFallbackFactory;

/**
 * 人才服务
 */
@FeignClient(contextId = "remoteTalentService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteTalentFallbackFactory.class)
public interface RemoteTalentService
{
    /**
     * 根据用户ID获取人才信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    @GetMapping("/info/getByUserId/{userId}")
    public R<TalentInfoApi> getTalentByUserId(@PathVariable("userId") Long userId);
}
