package com.ruoyi.portalconsole.enums;

public enum CompanyStatus {

    NONE("0","未认证"),
    PENDING("1","认证中"),
    REJECTED("","认证驳回"),
    ACCEPTED("3","已认证");

    private String code;

    private String label;

    CompanyStatus(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }
}
