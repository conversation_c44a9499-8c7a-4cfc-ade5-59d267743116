package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.domain.dto.MaterialWithOrderDTO;
import com.ruoyi.system.service.IMaterialInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.MaterialInfoMapper;
import com.ruoyi.system.domain.MaterialInfo;

/**
 * 物料信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Service
public class MaterialInfoServiceImpl implements IMaterialInfoService
{
    @Autowired
    private MaterialInfoMapper materialInfoMapper;

    /**
     * 查询物料信息
     *
     * @param id 物料信息主键
     * @return 物料信息
     */
    @Override
    public MaterialInfo selectMaterialInfoById(Long id)
    {
        return materialInfoMapper.selectMaterialInfoById(id);
    }

    /**
     * 查询物料信息列表
     *
     * @param materialInfo 物料信息
     * @return 物料信息
     */
    @Override
    public List<MaterialInfo> selectMaterialInfoList(MaterialInfo materialInfo)
    {
        return materialInfoMapper.selectMaterialInfoList(materialInfo);
    }
    
    /**
     * 查询物料信息列表（包含订单关联信息）
     *
     * @param materialInfo 物料信息
     * @return 物料信息与订单关联数据集合
     */
    @Override
    public List<MaterialWithOrderDTO> selectMaterialInfoWithOrderList(MaterialInfo materialInfo)
    {
        return materialInfoMapper.selectMaterialInfoWithOrderList(materialInfo);
    }

    /**
     * 新增物料信息
     *
     * @param materialInfo 物料信息
     * @return 结果
     */
    @Override
    public int insertMaterialInfo(MaterialInfo materialInfo)
    {
        materialInfo.setCreateTime(DateUtils.getNowDate());
        return materialInfoMapper.insertMaterialInfo(materialInfo);
    }

    /**
     * 修改物料信息
     *
     * @param materialInfo 物料信息
     * @return 结果
     */
    @Override
    public int updateMaterialInfo(MaterialInfo materialInfo)
    {
        materialInfo.setUpdateTime(DateUtils.getNowDate());
        return materialInfoMapper.updateMaterialInfo(materialInfo);
    }

    /**
     * 批量删除物料信息
     *
     * @param ids 需要删除的物料信息主键
     * @return 结果
     */
    @Override
    public int deleteMaterialInfoByIds(Long[] ids)
    {
        return materialInfoMapper.deleteMaterialInfoByIds(ids);
    }

    /**
     * 删除物料信息信息
     *
     * @param id 物料信息主键
     * @return 结果
     */
    @Override
    public int deleteMaterialInfoById(Long id)
    {
        return materialInfoMapper.deleteMaterialInfoById(id);
    }
}
