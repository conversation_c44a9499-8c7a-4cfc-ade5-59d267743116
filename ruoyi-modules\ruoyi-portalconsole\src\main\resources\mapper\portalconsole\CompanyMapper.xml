<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.CompanyMapper">
    
    <resultMap type="Company" id="CompanyResult">
        <result property="companyId"    column="company_id"    />
        <result property="companyName"    column="company_name"    />
        <result property="companyEmail"    column="company_email"    />
        <result property="companyEmpower"    column="company_empower"    />
        <result property="companyStatus"    column="company_status"    />
        <result property="businessLicenseImageUrl"    column="business_license_image_url"    />
        <result property="socialUnityCreditCode"    column="social_unity_credit_code"    />
        <result property="serviceIndustry"    column="service_industry"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="companySize"    column="company_size"    />
        <result property="phone"    column="phone"    />
        <result property="address"    column="address"    />
        <result property="registeredCapital"    column="registered_capital"    />
        <result property="intrduction"    column="intrduction"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="companyRealName" column="company_real_name"/>
    </resultMap>

    <sql id="selectCompanyVo">
        select company_id, company_name, company_email, company_empower, company_status, business_license_image_url, social_unity_credit_code, service_industry, submit_time, company_size, phone, address, registered_capital, intrduction, del_flag, create_by, create_time, update_by, update_time, remark, company_real_name from company
    </sql>

    <select id="selectCompanyList" parameterType="Company" resultMap="CompanyResult">
        <include refid="selectCompanyVo"/>
        <where>  
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="companyEmail != null  and companyEmail != ''"> and company_email = #{companyEmail}</if>
            <if test="companyEmpower != null  and companyEmpower != ''"> and company_empower = #{companyEmpower}</if>
            <if test="companyStatus != null  and companyStatus != ''"> and company_status = #{companyStatus}</if>
            <if test="socialUnityCreditCode != null  and socialUnityCreditCode != ''"> and social_unity_credit_code = #{socialUnityCreditCode}</if>
            <if test="serviceIndustry != null  and serviceIndustry != ''"> and service_industry like concat('%', #{serviceIndustry}, '%')</if>
            <if test="submitTime != null "> and submit_time = #{submitTime}</if>
            <if test="companySize != null  and companySize != ''"> and company_size = #{companySize}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="registeredCapital != null  and registeredCapital != ''"> and registered_capital like concat('%', #{registeredCapital}, '%')</if>
            <if test="intrduction != null  and intrduction != ''"> and intrduction like concat('%', #{intrduction}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectCompanyByCompanyId" parameterType="Long" resultMap="CompanyResult">
        <include refid="selectCompanyVo"/>
        where company_id = #{companyId}
    </select>
    <select id="selectMembersByCompanyId" resultType="java.lang.Long">
        select member_id, member_phone from company join member on member.member_company_id = company.company_id
        <where>
            company_id IN
            <foreach collection="list" open="(" close=")" separator="," item="companyId">
                #{companyId}
            </foreach>
        </where>
    </select>

    <insert id="insertCompany" parameterType="Company" useGeneratedKeys="true" keyProperty="companyId">
        insert into company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="companyEmail != null">company_email,</if>
            <if test="companyEmpower != null">company_empower,</if>
            <if test="companyStatus != null">company_status,</if>
            <if test="businessLicenseImageUrl != null and businessLicenseImageUrl != ''">business_license_image_url,</if>
            <if test="socialUnityCreditCode != null and socialUnityCreditCode != ''">social_unity_credit_code,</if>
            <if test="serviceIndustry != null">service_industry,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="companySize != null">company_size,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="registeredCapital != null and registeredCapital != ''">registered_capital,</if>
            <if test="intrduction != null">intrduction,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="companyRealName != null and companyRealName != ''">company_real_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="companyEmail != null">#{companyEmail},</if>
            <if test="companyEmpower != null">#{companyEmpower},</if>
            <if test="companyStatus != null">#{companyStatus},</if>
            <if test="businessLicenseImageUrl != null and businessLicenseImageUrl != ''">#{businessLicenseImageUrl},</if>
            <if test="socialUnityCreditCode != null and socialUnityCreditCode != ''">#{socialUnityCreditCode},</if>
            <if test="serviceIndustry != null">#{serviceIndustry},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="companySize != null">#{companySize},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="registeredCapital != null and registeredCapital != ''">#{registeredCapital},</if>
            <if test="intrduction != null">#{intrduction},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="companyRealName != null and companyRealName != '' ">#{companyRealName},</if>
         </trim>
    </insert>

    <update id="updateCompany" parameterType="Company">
        update company
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="companyEmail != null">company_email = #{companyEmail},</if>
            <if test="companyEmpower != null">company_empower = #{companyEmpower},</if>
            <if test="companyStatus != null">company_status = #{companyStatus},</if>
            <if test="businessLicenseImageUrl != null and businessLicenseImageUrl != ''">business_license_image_url = #{businessLicenseImageUrl},</if>
            <if test="socialUnityCreditCode != null and socialUnityCreditCode != ''">social_unity_credit_code = #{socialUnityCreditCode},</if>
            <if test="serviceIndustry != null">service_industry = #{serviceIndustry},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="companySize != null">company_size = #{companySize},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="registeredCapital != null and registeredCapital != ''">registered_capital = #{registeredCapital},</if>
            <if test="intrduction != null">intrduction = #{intrduction},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="companyRealName != null and companyRealName != '' ">company_real_name = #{companyRealName},</if>
        </trim>
        where company_id = #{companyId}
    </update>
    <update id="auditCompanyBatch" parameterType="CompanyVO">
        UPDATE company SET company_status = #{companyStatus}
        <where>
            company_id IN
            <foreach collection="companyIds" open="(" separator="," close=")" item="companyId">
                #{companyId}
            </foreach>
        </where>
    </update>

    <delete id="deleteCompanyByCompanyId" parameterType="Long">
        delete from company where company_id = #{companyId}
    </delete>

    <delete id="deleteCompanyByCompanyIds" parameterType="String">
        delete from company where company_id in 
        <foreach item="companyId" collection="array" open="(" separator="," close=")">
            #{companyId}
        </foreach>
    </delete>
</mapper>