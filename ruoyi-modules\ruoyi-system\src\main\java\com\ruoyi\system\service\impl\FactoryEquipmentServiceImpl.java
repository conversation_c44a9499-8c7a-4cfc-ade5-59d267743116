package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.FactoryEquipmentMapper;
import com.ruoyi.system.domain.FactoryEquipment;
import com.ruoyi.system.service.IFactoryEquipmentService;

/**
 * 工厂设备信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@Service
public class FactoryEquipmentServiceImpl implements IFactoryEquipmentService 
{
    @Autowired
    private FactoryEquipmentMapper factoryEquipmentMapper;

    /**
     * 查询工厂设备信息
     * 
     * @param id 工厂设备信息主键
     * @return 工厂设备信息
     */
    @Override
    public FactoryEquipment selectFactoryEquipmentById(Long id)
    {
        return factoryEquipmentMapper.selectFactoryEquipmentById(id);
    }

    /**
     * 查询工厂设备信息列表
     * 
     * @param factoryEquipment 工厂设备信息
     * @return 工厂设备信息
     */
    @Override
    public List<FactoryEquipment> selectFactoryEquipmentList(FactoryEquipment factoryEquipment)
    {
        return factoryEquipmentMapper.selectFactoryEquipmentList(factoryEquipment);
    }

    /**
     * 新增工厂设备信息
     * 
     * @param factoryEquipment 工厂设备信息
     * @return 结果
     */
    @Override
    public int insertFactoryEquipment(FactoryEquipment factoryEquipment)
    {
        factoryEquipment.setCreateTime(DateUtils.getNowDate());
        return factoryEquipmentMapper.insertFactoryEquipment(factoryEquipment);
    }

    /**
     * 修改工厂设备信息
     * 
     * @param factoryEquipment 工厂设备信息
     * @return 结果
     */
    @Override
    public int updateFactoryEquipment(FactoryEquipment factoryEquipment)
    {
        factoryEquipment.setUpdateTime(DateUtils.getNowDate());
        return factoryEquipmentMapper.updateFactoryEquipment(factoryEquipment);
    }

    /**
     * 批量删除工厂设备信息
     * 
     * @param ids 需要删除的工厂设备信息主键
     * @return 结果
     */
    @Override
    public int deleteFactoryEquipmentByIds(Long[] ids)
    {
        return factoryEquipmentMapper.deleteFactoryEquipmentByIds(ids);
    }

    /**
     * 删除工厂设备信息信息
     * 
     * @param id 工厂设备信息主键
     * @return 结果
     */
    @Override
    public int deleteFactoryEquipmentById(Long id)
    {
        return factoryEquipmentMapper.deleteFactoryEquipmentById(id);
    }
}
