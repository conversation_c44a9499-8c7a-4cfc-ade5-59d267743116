package com.ruoyi.portalweb.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.portalweb.api.domain.Application;
import com.ruoyi.portalweb.api.factory.RemoteApplicationFallbackFactory;

/**
 * 用户服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteApplicationService", value = ServiceNameConstants.PORTALWEB_SERVICE, fallbackFactory = RemoteApplicationFallbackFactory.class)
public interface RemoteApplicationService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/Application/applicationbyappid/{appid}")
    public R<Application> selectApplicationByAppId(@PathVariable("appid") String appid);

}
