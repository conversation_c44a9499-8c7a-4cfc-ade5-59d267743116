package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.file.FileTypeUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.SysFileInfo;
import com.ruoyi.portalweb.service.ISysFileInfoService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.SysFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件信息Controller
 * 
 * <AUTHOR>
 * @date 2024-05-22
 */
@RestController
@RequestMapping("/SysFileInfo")
public class SysFileInfoController extends BaseController
{
    @Autowired
    private ISysFileInfoService sysFileInfoService;
    
    @Autowired
    private RemoteFileService remoteFileService;
    
    
    /**
     * 头像上传
     */
    @Log(title = "附件上传", businessType = BusinessType.INSERT)
    @PostMapping("/upload")
    public R<SysFileInfo> upload(@RequestParam("file") MultipartFile file)
    {
        if (!file.isEmpty())
        {
            String extension = FileTypeUtils.getExtension(file);
            String name = FileTypeUtils.getFileName(file);
            R<SysFile> fileResult = remoteFileService.upload(file);
            if (StringUtils.isNull(fileResult) || StringUtils.isNull(fileResult.getData()))
            {
                return R.fail("文件服务异常，请联系管理员");
            }
            SysFileInfo sysFileInfo = new SysFileInfo();
            sysFileInfo.setFileName(name);
            sysFileInfo.setFilePath(fileResult.getData().getUrl());
            sysFileInfo.setFileFullPath(fileResult.getData().getFullUrl());
            sysFileInfo.setFileExtension(extension);
            
            sysFileInfo.setCreateBy(SecurityUtils.getUserId().toString());
            
            sysFileInfoService.insertSysFileInfo(sysFileInfo);
            return R.ok(sysFileInfo);
        }
        return R.fail("文件异常，请联系管理员");
    }
}
