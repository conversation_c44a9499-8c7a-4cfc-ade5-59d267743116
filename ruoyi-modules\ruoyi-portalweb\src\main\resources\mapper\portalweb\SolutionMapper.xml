<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.SolutionMapper">

    <resultMap id="SolutionResult" type="com.ruoyi.portalweb.vo.SolutionVO">
        <result property="solutionId" column="solution_id"/>
        <result property="solutionTypeId" column="solution_type_id"/>
        <result property="solutionName" column="solution_name"/>
        <result property="solutionBanner" column="solution_banner"/>
        <result property="solutionIntroduction" column="solution_introduction"/>
        <result property="solutionOverview" column="solution_overview"/>
        <result property="solutionImg" column="solution_img"/>
        <result property="solutionStatus" column="solution_status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberId" column="member_id"/>

        <result property="solutionTypeName" column="solution_type_name"/>
        <result property="category" column="category"/>
        <result property="categoryName" column="category_name"/>
    </resultMap>

    <sql id="selectSolutionVo">
        select solution_id, solution_type_id, solution_name, solution_banner, solution_introduction, solution_overview,
        solution_img, solution_status, del_flag, create_by, create_time, update_by, update_time, remark, member_id from solution
    </sql>

    <sql id="Base_Column_List">
        a.*, b.solution_type_name
    </sql>

    <sql id="Base_Table_List">
        FROM solution a
        LEFT JOIN solution_type b ON a.solution_type_id = b.solution_type_id
    </sql>

    <select id="selectSolutionList" parameterType="SolutionVO" resultMap="SolutionResult">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Table_List"/>
        <where>
        	a.del_flag = 0 
            <if test="keywords != null  and keywords != ''">
                AND (
                    a.solution_name like concat(concat('%',#{keywords}),'%')
                    OR a.solution_introduction like concat(concat('%',#{keywords}),'%')
                )
            </if>
            <if test="solutionTypeId != null ">
                 and a.solution_type_id in (
                    WITH recursive tmp_type AS (
                        SELECT * FROM solution_type WHERE solution_type_id = #{solutionTypeId}
                        UNION ALL
                        SELECT a.*
                        FROM solution_type a
                        INNER JOIN tmp_type b ON a.parent_id = b.solution_type_id
                    )
                    SELECT solution_type_id FROM tmp_type
                )
            </if>
            <if test="category != null and category != ''">
                and a.solution_type_id in (
                    WITH recursive tmp_type AS (
                        SELECT * FROM solution_type WHERE category = #{category}
						UNION ALL
						SELECT a.*
						FROM solution_type a
						INNER JOIN tmp_type b ON a.parent_id = b.solution_type_id
                    )
                    SELECT solution_type_id FROM tmp_type
                )
            </if>
            <if test="solutionName != null and solutionName != ''">
               and a.solution_name like concat('%', #{solutionName}, '%')
            </if>
            <if test="solutionBanner != null and solutionBanner != ''">and a.solution_banner = #{solutionBanner}</if>
            <if test="solutionIntroduction != null and solutionIntroduction != ''">
               and a.solution_introduction = #{solutionIntroduction}
            </if>
            <if test="solutionOverview != null and solutionOverview != ''">
               and a.solution_overview = #{solutionOverview}
            </if>
            <if test="solutionImg != null and solutionImg != ''">and a.solution_img = #{solutionImg}</if>
            <if test="solutionStatus != null and solutionStatus != ''">and a.solution_status = #{solutionStatus}</if>
        </where>
    </select>

    <select id="selectSolutionBySolutionId" parameterType="Long" resultMap="SolutionResult">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Table_List"/>
        where a.solution_id = #{solutionId}
    </select>

    <insert id="insertSolution" parameterType="Solution" useGeneratedKeys="true" keyProperty="solutionId">
        insert into solution
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="solutionTypeId != null">solution_type_id,</if>
            <if test="solutionName != null">solution_name,</if>
            <if test="solutionBanner != null">solution_banner,</if>
            <if test="solutionIntroduction != null">solution_introduction,</if>
            <if test="solutionOverview != null">solution_overview,</if>
            <if test="solutionImg != null">solution_img,</if>
            <if test="solutionStatus != null">solution_status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="memberId != null">member_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="solutionTypeId != null">#{solutionTypeId},</if>
            <if test="solutionName != null">#{solutionName},</if>
            <if test="solutionBanner != null">#{solutionBanner},</if>
            <if test="solutionIntroduction != null">#{solutionIntroduction},</if>
            <if test="solutionOverview != null">#{solutionOverview},</if>
            <if test="solutionImg != null">#{solutionImg},</if>
            <if test="solutionStatus != null">#{solutionStatus},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="memberId != null">#{memberId},</if>
        </trim>
    </insert>

    <update id="updateSolution" parameterType="Solution">
        update solution
        <trim prefix="SET" suffixOverrides=",">
            <if test="solutionTypeId != null">solution_type_id = #{solutionTypeId},</if>
            <if test="solutionName != null">solution_name = #{solutionName},</if>
            <if test="solutionBanner != null">solution_banner = #{solutionBanner},</if>
            <if test="solutionIntroduction != null">solution_introduction = #{solutionIntroduction},</if>
            <if test="solutionOverview != null">solution_overview = #{solutionOverview},</if>
            <if test="solutionImg != null">solution_img = #{solutionImg},</if>
            <if test="solutionStatus != null">solution_status = #{solutionStatus},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
        </trim>
        where solution_id = #{solutionId}
    </update>

    <delete id="deleteSolutionBySolutionId" parameterType="Long">
        delete from solution where solution_id = #{solutionId}
    </delete>

    <delete id="deleteSolutionBySolutionIds" parameterType="String">
        delete from solution where solution_id in
        <foreach item="solutionId" collection="array" open="(" separator="," close=")">
            #{solutionId}
        </foreach>
    </delete>

    <!-- 查询解决方案列表首页用 -->
    <select id="selectSolutionToDesk" resultMap="SolutionResult">
        SELECT so.solution_id,so.solution_name,st.total_id as solution_type_id,tp.solution_type_name,tp.category,s1.dict_label AS category_name
        FROM solution so
        LEFT JOIN (
            WITH recursive tmp_type AS (
                SELECT a1.*,a1.solution_type_id as total_id FROM solution_type a1 WHERE a1.parent_id IS NULL
                UNION ALL
                SELECT a2.*,b2.total_id
                FROM solution_type a2
                INNER JOIN tmp_type b2 ON a2.parent_id = b2.solution_type_id
            )
            SELECT solution_type_id,total_id FROM tmp_type
        ) st ON st.solution_type_id = so.solution_type_id
        LEFT JOIN solution_type tp ON tp.solution_type_id = st.total_id
        LEFT JOIN sys_dict_data s1 ON tp.category = s1.dict_value AND s1.dict_type = 'solution_type_category'
        WHERE so.del_flag = 0
    </select>
    <select id="selectSolutionListByMemberIds" resultMap="SolutionResult"
            parameterType="List">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Table_List"/>
        where a.solution_status = 0 AND
              a.member_id IN
        <foreach collection="list" open="(" close=")" separator="," item="memberId">
            #{memberId}
        </foreach>
    </select>
</mapper>