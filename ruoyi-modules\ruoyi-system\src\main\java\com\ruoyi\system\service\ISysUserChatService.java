package com.ruoyi.system.service;

import com.alibaba.fastjson.JSONArray;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.domain.UserChatMessage;

/**
 * 用户交互相关
 */
public interface ISysUserChatService
{
    /**
     * 获取用户融云token
     * @param userName
     * @return
     */
    AjaxResult getRongToken(String userName);

    /**
     * 根据用户信息获取用户详情
     * @param userName 登录用户名称
     * @param searchUserName 查询的用户名称
     * @return
     */
    AjaxResult getUserDetal(String userName,String searchUserName);

    /**
     *
     * @param userChatMessage
     *         targetId :接收方手机号或群聊id
     *         content：消息内容（内容、多媒体二选一，多媒体只有一个）
     *         mediaUrl：多媒体链接（内容、多媒体二选一，图片只有一个）
     *         contentType 消息类型 0 文字消息 1图片消息 2视频 3音频 4 文件
     *         conversationType 所属会话类型 单聊	1 群聊	3 聊天室	4 系统	6
     *         fileName 文件名
     *
     * @return
     */
    AjaxResult sendMessage(String userName, UserChatMessage userChatMessage);

    /**
     * 同步用户状态
     * @param jsonArray
     * @return
     */
    Integer reciveUserRongYunState(JSONArray jsonArray);

    /**
     * 初始化老用户融云token
     * @return
     * @param page
     * @param rows
     */
    AjaxResult initializeRongToken(Integer page, Integer rows);

    /**
     * 用户绑定微信公众号，发送离线消息
     * @param userName 用户手机号
     * @param code 微信返回的code码
     * @return
     */
    AjaxResult boundWeiXin(String userName, String code);

    /**
     * 解除微信绑定
     * @param userName
     * @return
     */
    AjaxResult unboundWeiXin(String userName);
}
