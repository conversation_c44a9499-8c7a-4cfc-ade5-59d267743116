package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysSupplierInfo;

/**
 * 供方信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface SysSupplierInfoMapper 
{
    /**
     * 查询供方信息
     * 
     * @param supplierId 供方信息主键
     * @return 供方信息
     */
    public SysSupplierInfo selectSysSupplierInfoBySupplierId(Long supplierId);

    /**
     * 查询供方信息列表
     * 
     * @param sysSupplierInfo 供方信息
     * @return 供方信息集合
     */
    public List<SysSupplierInfo> selectSysSupplierInfoList(SysSupplierInfo sysSupplierInfo);

    /**
     * 新增供方信息
     * 
     * @param sysSupplierInfo 供方信息
     * @return 结果
     */
    public int insertSysSupplierInfo(SysSupplierInfo sysSupplierInfo);

    /**
     * 修改供方信息
     * 
     * @param sysSupplierInfo 供方信息
     * @return 结果
     */
    public int updateSysSupplierInfo(SysSupplierInfo sysSupplierInfo);

    /**
     * 删除供方信息
     * 
     * @param supplierId 供方信息主键
     * @return 结果
     */
    public int deleteSysSupplierInfoBySupplierId(Long supplierId);

    /**
     * 批量删除供方信息
     * 
     * @param supplierIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysSupplierInfoBySupplierIds(Long[] supplierIds);
}
