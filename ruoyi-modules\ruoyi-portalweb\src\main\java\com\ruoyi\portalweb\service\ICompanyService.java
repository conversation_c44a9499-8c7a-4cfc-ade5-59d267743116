package com.ruoyi.portalweb.service;


import com.ruoyi.portalweb.api.domain.Company;
import com.ruoyi.portalweb.vo.CompanyVO;
import com.ruoyi.portalweb.vo.CustomerAudingVO;

import java.util.List;

/**
 * 企业信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface ICompanyService 
{
    /**
     * 查询企业信息
     * 
     * @param companyId 企业信息主键
     * @return 企业信息
     */
    public CompanyVO selectCompanyByCompanyId(Long companyId);

    /**
     * 查询单个用户企业信息
     *
     * @param companyId 企业信息主键
     * @param memberId 会员信息主键
     * @return 企业信息
     */
    public CompanyVO selectCompanyByCompanyIdAndMemberId(Long companyId, Long memberId);

    /**
     * 查询企业信息列表
     * 
     * @param company 企业信息
     * @return 企业信息集合
     */
    public List<Company> selectCompanyList(Company company);

    /**
     * 新增企业信息
     * 
     * @param company 企业信息
     * @return 结果
     */
    public int insertCompany(CompanyVO company);

    /**
     * 修改企业信息
     * 
     * @param company 企业信息
     * @return 结果
     */
    public int updateCompany(CompanyVO company);

    /**
     * 查询天眼查企业信息
     *
     * @param keywords 企业信息
     * @return 结果
     */
    public CustomerAudingVO searchByCustomerName(String keywords);

    /**
     * 根据web输入的公司关键字，查询所有企业信息
     */
    public List<String> searchByKeywords(String keywords);

    /**
     * 批量删除企业信息
     * 
     * @param companyIds 需要删除的企业信息主键集合
     * @return 结果
     */
    public int deleteCompanyByCompanyIds(Long[] companyIds);

    /**
     * 删除企业信息信息
     * 
     * @param companyId 企业信息主键
     * @return 结果
     */
    public int deleteCompanyByCompanyId(Long companyId);
}
