package com.ruoyi.portalweb.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.portalweb.api.domain.SolutionPain;
import com.ruoyi.portalweb.mapper.SolutionPainMapper;
import com.ruoyi.portalweb.service.ISolutionPainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 解决方案行业痛点Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class SolutionPainServiceImpl implements ISolutionPainService
{
    @Autowired
    private SolutionPainMapper solutionPainMapper;

    /**
     * 查询解决方案行业痛点
     * 
     * @param solutionPainId 解决方案行业痛点主键
     * @return 解决方案行业痛点
     */
    @Override
    public SolutionPain selectSolutionPainBySolutionPainId(Long solutionPainId)
    {
        return solutionPainMapper.selectSolutionPainBySolutionPainId(solutionPainId);
    }

    /**
     * 查询解决方案行业痛点列表
     * 
     * @param solutionPain 解决方案行业痛点
     * @return 解决方案行业痛点
     */
    @Override
    public List<SolutionPain> selectSolutionPainList(SolutionPain solutionPain)
    {
        return solutionPainMapper.selectSolutionPainList(solutionPain);
    }

    /**
     * 新增解决方案行业痛点
     * 
     * @param solutionPain 解决方案行业痛点
     * @return 结果
     */
    @Override
    public int insertSolutionPain(SolutionPain solutionPain)
    {
        solutionPain.setCreateTime(DateUtils.getNowDate());
        return solutionPainMapper.insertSolutionPain(solutionPain);
    }

    /**
     * 修改解决方案行业痛点
     * 
     * @param solutionPain 解决方案行业痛点
     * @return 结果
     */
    @Override
    public int updateSolutionPain(SolutionPain solutionPain)
    {
        solutionPain.setUpdateTime(DateUtils.getNowDate());
        return solutionPainMapper.updateSolutionPain(solutionPain);
    }

    /**
     * 批量删除解决方案行业痛点
     * 
     * @param solutionPainIds 需要删除的解决方案行业痛点主键
     * @return 结果
     */
    @Override
    public int deleteSolutionPainBySolutionPainIds(Long[] solutionPainIds)
    {
        return solutionPainMapper.deleteSolutionPainBySolutionPainIds(solutionPainIds);
    }

    /**
     * 删除解决方案行业痛点信息
     * 
     * @param solutionPainId 解决方案行业痛点主键
     * @return 结果
     */
    @Override
    public int deleteSolutionPainBySolutionPainId(Long solutionPainId)
    {
        return solutionPainMapper.deleteSolutionPainBySolutionPainId(solutionPainId);
    }
}
