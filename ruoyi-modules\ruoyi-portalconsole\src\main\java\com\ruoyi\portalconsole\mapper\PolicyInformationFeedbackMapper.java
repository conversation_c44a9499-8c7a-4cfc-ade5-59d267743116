package com.ruoyi.portalconsole.mapper;

import java.util.List;
import com.ruoyi.portalconsole.domain.PolicyInformationFeedback;
import com.ruoyi.portalconsole.domain.vo.PolicyInformationFeedbackVO;

/**
 * 政策意见反馈Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-26
 */
public interface PolicyInformationFeedbackMapper 
{
    /**
     * 查询政策意见反馈
     * 
     * @param policyInformationFeedbackId 政策意见反馈主键
     * @return 政策意见反馈
     */
    public PolicyInformationFeedbackVO selectPolicyInformationFeedbackByPolicyInformationFeedbackId(Long policyInformationFeedbackId);

    /**
     * 查询政策意见反馈列表
     * 
     * @param policyInformationFeedback 政策意见反馈
     * @return 政策意见反馈集合
     */
    public List<PolicyInformationFeedbackVO> selectPolicyInformationFeedbackList(PolicyInformationFeedback policyInformationFeedback);

    /**
     * 新增政策意见反馈
     * 
     * @param policyInformationFeedback 政策意见反馈
     * @return 结果
     */
    public int insertPolicyInformationFeedback(PolicyInformationFeedback policyInformationFeedback);

    /**
     * 修改政策意见反馈
     * 
     * @param policyInformationFeedback 政策意见反馈
     * @return 结果
     */
    public int updatePolicyInformationFeedback(PolicyInformationFeedback policyInformationFeedback);

    /**
     * 删除政策意见反馈
     * 
     * @param policyInformationFeedbackId 政策意见反馈主键
     * @return 结果
     */
    public int deletePolicyInformationFeedbackByPolicyInformationFeedbackId(Long policyInformationFeedbackId);

    /**
     * 批量删除政策意见反馈
     * 
     * @param policyInformationFeedbackIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePolicyInformationFeedbackByPolicyInformationFeedbackIds(Long[] policyInformationFeedbackIds);
}
