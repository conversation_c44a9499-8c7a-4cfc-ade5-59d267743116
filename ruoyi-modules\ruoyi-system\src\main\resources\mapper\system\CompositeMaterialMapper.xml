<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CompositeMaterialMapper">

    <resultMap type="CompositeMaterial" id="CompositeMaterialResult">
        <result property="id"    column="id"    />
        <result property="materialName"    column="material_name"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="imageList"    column="image_list"    />
        <result property="description"    column="description"    />
        <result property="productType"    column="product_type"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCompositeMaterialVo">
        select id, material_name, image_url, image_list, description, product_type, sort_order, status, del_flag, create_by, create_time, update_by, update_time, remark from composite_material
    </sql>

    <select id="selectCompositeMaterialList" parameterType="CompositeMaterial" resultMap="CompositeMaterialResult">
        <include refid="selectCompositeMaterialVo"/>
        <where>
            <if test="materialName != null  and materialName != ''"> and material_name like concat('%', #{materialName}, '%')</if>
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="imageList != null  and imageList != ''"> and image_list = #{imageList}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectCompositeMaterialById" parameterType="Long" resultMap="CompositeMaterialResult">
        <include refid="selectCompositeMaterialVo"/>
        where id = #{id}
    </select>

    <!-- 根据产品ID查询关联的展厅列表 -->
    <select id="selectCompositeMaterialListByProductId" parameterType="Long" resultMap="CompositeMaterialResult">
        SELECT cm.id, cm.material_name, cm.image_url, cm.image_list, cm.description,
               cm.product_type, cm.sort_order, cm.status, cm.del_flag,
               cm.create_by, cm.create_time, cm.update_by, cm.update_time, cm.remark
        FROM composite_material cm
        INNER JOIN sys_product sp ON cm.id = sp.exhibition_hall_id
        WHERE sp.product_id = #{productId}
        AND cm.status = '0'
        AND cm.del_flag = '0'
        ORDER BY cm.sort_order ASC, cm.create_time DESC
    </select>

    <insert id="insertCompositeMaterial" parameterType="CompositeMaterial" useGeneratedKeys="true" keyProperty="id">
        insert into composite_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialName != null and materialName != ''">material_name,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="imageList != null">image_list,</if>
            <if test="description != null">description,</if>
            <if test="productType != null">product_type,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialName != null and materialName != ''">#{materialName},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="imageList != null">#{imageList},</if>
            <if test="description != null">#{description},</if>
            <if test="productType != null">#{productType},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCompositeMaterial" parameterType="CompositeMaterial">
        update composite_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialName != null and materialName != ''">material_name = #{materialName},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="imageList != null">image_list = #{imageList},</if>
            <if test="description != null">description = #{description},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCompositeMaterialById" parameterType="Long">
        delete from composite_material where id = #{id}
    </delete>

    <delete id="deleteCompositeMaterialByIds" parameterType="String">
        delete from composite_material where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>