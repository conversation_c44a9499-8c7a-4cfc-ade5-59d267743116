<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.AppStoreMapper">

    <resultMap id="AppStoreResult" type="com.ruoyi.portalconsole.domain.vo.AppStoreVO">
        <result property="appStoreId" column="app_store_id"/>
        <result property="companyId" column="company_id"/>
        <result property="appStoreName" column="app_store_name"/>
        <result property="appStoreType" column="app_store_type"/>
        <result property="appStoreIntroduction" column="app_store_introduction"/>
        <result property="appStoreContent" column="app_store_content"/>
        <result property="appStoreImg" column="app_store_img"/>
        <result property="appStoreContactsName" column="app_store_contacts_name"/>
        <result property="appStoreContactsPhone" column="app_store_contacts_phone"/>
        <result property="appStorePrice" column="app_store_price"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberId" column="member_id"/>
        <result property="appLabel" column="app_label"/>
        <result property="appStoreOrderTotal" column="app_store_order_total"/>
        <result property="recommend" column="recommend"/>
        <result property="deliveryMethod" column="delivery_method"/>
        <result property="sort" column="sort"/>
<!--        <result property="erweima" column="erweima"/>-->
<!--        <result property="downloadUrl" column="download_url"/>-->
        <result property="supply" column="supply"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="onShow" column="on_show"/>
        <result property="appServer" column="app_server"/>

        <result property="companyName" column="company_name"/>
        <result property="filePath" column="file_path"/>
        <result property="fileFullPath" column="file_full_path"/>
        <result property="appStoreTypeName" column="app_store_type_name"/>
        <result property="deliveryMethodName" column="delivery_method_name"/>
        
    </resultMap>

    <sql id="selectAppStoreVo">
        select app_store_id, company_id, app_store_name, app_store_type, app_store_introduction, app_store_content,
        app_store_img, app_store_contacts_name, app_store_contacts_phone, app_store_price, del_flag, create_by,
        create_time, update_by, update_time, remark ,member_id,app_label,app_store_order_total,recommend,delivery_method,sort
        , supply, on_show, app_server, audit_status
        from app_store
    </sql>

    <sql id="Base_Column_List">
        a.*, b.company_name, c.file_path, c.file_full_path
		,s1.dict_label as app_store_type_name,s2.dict_label as delivery_method_name
    </sql>

    <sql id="Base_Table_List">
        FROM app_store a
        LEFT JOIN company b ON a.company_id = b.company_id
        LEFT JOIN sys_file_info c ON a.app_store_img = c.file_id
        LEFT JOIN sys_dict_data s1 ON a.app_store_type = s1.dict_value AND s1.dict_type = 'app_store_type'
		LEFT JOIN sys_dict_data s2 ON a.delivery_method = s2.dict_value AND s2.dict_type = 'delivery_method'
    </sql>

    <select id="selectAppStoreList" parameterType="AppStore" resultMap="AppStoreResult">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Table_List"/>
        <where>
            <if test="companyId != null ">and a.company_id = #{companyId}</if>
            <if test="appStoreName != null  and appStoreName != ''">and a.app_store_name like concat('%',
                #{appStoreName}, '%')
            </if>
            <if test="appStoreType != null  and appStoreType != ''">and a.app_store_type = #{appStoreType}</if>
            <if test="appStoreIntroduction != null  and appStoreIntroduction != ''">and a.app_store_introduction =
                #{appStoreIntroduction}
            </if>
            <if test="appStoreContent != null  and appStoreContent != ''">and a.app_store_content = #{appStoreContent}
            </if>
            <if test="appStoreImg != null  and appStoreImg != ''">and a.app_store_img = #{appStoreImg}</if>
            <if test="appStoreContactsName != null  and appStoreContactsName != ''">and a.app_store_contacts_name like
                concat('%', #{appStoreContactsName}, '%')
            </if>
            <if test="appStoreContactsPhone != null  and appStoreContactsPhone != ''">and a.app_store_contacts_phone =
                #{appStoreContactsPhone}
            </if>
            <if test="appStorePrice != null ">and a.app_store_price = #{appStorePrice}</if>
            <if test="memberId != null ">and a.member_id = #{memberId}</if>
            <if test="appLabel != null ">and a.app_label = #{appLabel}</if>
            <if test="appStoreOrderTotal != null ">and a.app_store_order_total = #{appStoreOrderTotal}</if>
            <if test="recommend != null ">and a.recommend = #{recommend}</if>
            <if test="deliveryMethod != null and deliveryMethod != ''">and a.delivery_method = #{deliveryMethod}</if>
            <if test="sort != null ">and a.sort = #{sort}</if>
            <if test="auditStatus != null ">and a.audit_status = #{auditStatus}</if>
            <if test="onShow != null ">and a.on_show = #{onShow}</if>
            <if test="appServer != null ">and a.app_server = #{appServer}</if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <select id="selectAppStoreByAppStoreId" parameterType="Long" resultMap="AppStoreResult">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Table_List"/>
        where app_store_id = #{appStoreId}
    </select>

    <insert id="insertAppStore" parameterType="AppStore" useGeneratedKeys="true" keyProperty="appStoreId">
        insert into app_store
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">company_id,</if>
            <if test="appStoreName != null">app_store_name,</if>
            <if test="appStoreType != null">app_store_type,</if>
            <if test="appStoreIntroduction != null">app_store_introduction,</if>
            <if test="appStoreContent != null">app_store_content,</if>
            <if test="appStoreImg != null">app_store_img,</if>
            <if test="appStoreContactsName != null">app_store_contacts_name,</if>
            <if test="appStoreContactsPhone != null">app_store_contacts_phone,</if>
            <if test="appStorePrice != null">app_store_price,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="memberId != null">member_id,</if>
            <if test="appLabel != null">app_label,</if>
            <if test="appStoreOrderTotal != null">app_store_order_total,</if>
            <if test="recommend != null">recommend,</if>
            <if test="deliveryMethod != null">delivery_method,</if>
            <if test="sort != null">sort,</if>
<!--            <if test="erweima != null">erweima,</if>-->
<!--            <if test="downloadUrl != null">download_url,</if>-->
            <if test="supply != null">supply,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="onShow != null">on_show,</if>
            <if test="appServer != null">app_server,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">#{companyId},</if>
            <if test="appStoreName != null">#{appStoreName},</if>
            <if test="appStoreType != null">#{appStoreType},</if>
            <if test="appStoreIntroduction != null">#{appStoreIntroduction},</if>
            <if test="appStoreContent != null">#{appStoreContent},</if>
            <if test="appStoreImg != null">#{appStoreImg},</if>
            <if test="appStoreContactsName != null">#{appStoreContactsName},</if>
            <if test="appStoreContactsPhone != null">#{appStoreContactsPhone},</if>
            <if test="appStorePrice != null">#{appStorePrice},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="appLabel != null">#{appLabel},</if>
            <if test="appStoreOrderTotal != null">#{appStoreOrderTotal},</if>
            <if test="recommend != null">#{recommend},</if>
            <if test="deliveryMethod != null">#{deliveryMethod},</if>
            <if test="sort != null">#{sort},</if>
<!--            <if test="erweima != null">#{erweima},</if>-->
<!--            <if test="downloadUrl != null">#{downloadUrl},</if>-->
            <if test="supply != null">#{supply},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="onShow != null">#{onShow},</if>
            <if test="appServer != null">#{appServer},</if>
        </trim>
    </insert>

    <update id="updateAppStore" parameterType="AppStore">
        update app_store
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="appStoreName != null">app_store_name = #{appStoreName},</if>
            <if test="appStoreType != null">app_store_type = #{appStoreType},</if>
            <if test="appStoreIntroduction != null">app_store_introduction = #{appStoreIntroduction},</if>
            <if test="appStoreContent != null">app_store_content = #{appStoreContent},</if>
            <if test="appStoreImg != null">app_store_img = #{appStoreImg},</if>
            <if test="appStoreContactsName != null">app_store_contacts_name = #{appStoreContactsName},</if>
            <if test="appStoreContactsPhone != null">app_store_contacts_phone = #{appStoreContactsPhone},</if>
            <if test="appStorePrice != null">app_store_price = #{appStorePrice},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="appLabel != null">app_label = #{appLabel},</if>
            <if test="appStoreOrderTotal != null">app_store_order_total = #{appStoreOrderTotal},</if>
            <if test="recommend != null">recommend = #{recommend},</if>
            <if test="deliveryMethod != null">delivery_method = #{deliveryMethod},</if>
            <if test="sort != null">sort = #{sort},</if>
<!--            <if test="erweima != null">erweima = #{erweima},</if>-->
<!--            <if test="downloadUrl != null">download_url = #{downloadUrl},</if>-->
            <if test="supply != null">supply = #{supply},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="onShow != null">on_show = #{onShow},</if>
            <if test="appServer != null">app_server = #{appServer},</if>
        </trim>
        where app_store_id = #{appStoreId}
    </update>
    <update id="auditAppStore" >
        update app_store set audit_status = #{auditStatus}
        <where>
            app_store_id IN
            <foreach collection="appStoreIds" open="(" close=")" separator="," item="appStoreId">
                #{appStoreId}
            </foreach>
        </where>
    </update>

    <delete id="deleteAppStoreByAppStoreId" parameterType="Long">
        delete from app_store where app_store_id = #{appStoreId}
    </delete>

    <delete id="deleteAppStoreByAppStoreIds">
        delete from app_store where app_store_id in
        <foreach item="appStoreId" collection="appStoreIds" open="(" separator="," close=")">
            #{appStoreId}
        </foreach>
    </delete>
</mapper>