package com.ruoyi.portalconsole.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.IndustryType;
import com.ruoyi.portalconsole.service.IIndustryTypeService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 行业类别Controller
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@RestController
@RequestMapping("/industryType")
public class IndustryTypeController extends BaseController
{
    @Autowired
    private IIndustryTypeService industryTypeService;

    /**
     * 查询行业类别列表
     */
    @RequiresPermissions("portalconsole:industryType:list")
    @GetMapping("/list")
    public TableDataInfo list(IndustryType industryType)
    {
        startPage();
        List<IndustryType> list = industryTypeService.selectIndustryTypeList(industryType);
        return getDataTable(list);
    }

    /**
     * 导出行业类别列表
     */
    @RequiresPermissions("portalconsole:industryType:export")
    @Log(title = "行业类别", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IndustryType industryType)
    {
        List<IndustryType> list = industryTypeService.selectIndustryTypeList(industryType);
        ExcelUtil<IndustryType> util = new ExcelUtil<IndustryType>(IndustryType.class);
        util.exportExcel(response, list, "行业类别数据");
    }

    /**
     * 获取行业类别详细信息
     */
    @RequiresPermissions("portalconsole:industryType:query")
    @GetMapping(value = "/{industryTypeId}")
    public AjaxResult getInfo(@PathVariable("industryTypeId") Long industryTypeId)
    {
        return success(industryTypeService.selectIndustryTypeByIndustryTypeId(industryTypeId));
    }

    /**
     * 新增行业类别
     */
    @RequiresPermissions("portalconsole:industryType:add")
    @Log(title = "行业类别", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IndustryType industryType)
    {
        return toAjax(industryTypeService.insertIndustryType(industryType));
    }

    /**
     * 修改行业类别
     */
    @RequiresPermissions("portalconsole:industryType:edit")
    @Log(title = "行业类别", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IndustryType industryType)
    {
        return toAjax(industryTypeService.updateIndustryType(industryType));
    }

    /**
     * 删除行业类别
     */
    @RequiresPermissions("portalconsole:industryType:remove")
    @Log(title = "行业类别", businessType = BusinessType.DELETE)
	@DeleteMapping("/{industryTypeIds}")
    public AjaxResult remove(@PathVariable Long[] industryTypeIds)
    {
        return toAjax(industryTypeService.deleteIndustryTypeByIndustryTypeIds(industryTypeIds));
    }
}
