package com.ruoyi.portalweb.wxpay.pay;

import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Random;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.DateUtil;

/**
 * 微信手机支付配置类
 * 
 * <AUTHOR>
 *
 */
public class WxPayUtil {

	/**
	 * 查询当前时间 yyyyMMddHHmmss
	 * 
	 * @return String
	 */
	public static String getCurrTime() {
		Date now = new Date();
		SimpleDateFormat outFormat = new SimpleDateFormat("yyyyMMddHHmmss");
		String s = outFormat.format(now);
		return s;
	}

	/**
	 * 查询微信支付异步通知回调地址
	 * 
	 * @param ticket
	 * @return
	 */
	public static String genNotifyUrl(String serverUrl, String ticket) {
		return serverUrl + "app/recharge!doRecharge.do?ticket=" + ticket;
	}

	/**
	 * 查询编码字符集
	 * 
	 * @param request
	 * @param response
	 * @return String
	 */

	public static String getCharacterEncoding(HttpServletRequest request, HttpServletResponse response) {
		if (null == request || null == response) {
			return "utf-8";
		}
		String enc = request.getCharacterEncoding();
		if (null == enc || "".equals(enc)) {
			enc = response.getCharacterEncoding();
		}
		if (null == enc || "".equals(enc)) {
			enc = "utf-8";
		}
		return enc;
	}

	/**
	 * 充值单号
	 * 
	 * @return
	 */
	public  static  String getOutTradeNo() {
		// 当前时间 yyMMddHHmmssSSSS
		String currTime = DateUtil.format(LocalDateTime.now(), "yyMMddHHmmssSSSS");
		String strReq = "QDN" + currTime;
		return strReq;
	}

	/**
	 * 退款单号
	 * 
	 * @return
	 */
	public  static String getOutReFundNo() {
		// 当前时间 yyMMddHHmmssSSSS
		String currTime = DateUtil.format(LocalDateTime.now(), "yyMMddHHmmssSSSS");
		String strReq = "DYGY" + currTime;
		return strReq;
	}

	/**
	 * 生成签名
	 * 
	 * @param alParam
	 * @param apiKey
	 * @return
	 */
	public static String genAppSign(List<Param> alParam, String apiKey) {
		StringBuilder sb = new StringBuilder();
		for (Param param : alParam) {
			sb.append(param.getName());
			sb.append('=');
			sb.append(param.getValue());
			sb.append('&');
		}
		sb.append("key=");
		sb.append(apiKey);
		String appSign = WxPayUtil.getMessageDigest(sb.toString()).toUpperCase();
		return appSign;
	}

	/**
	 * 取出一个指定长度大小的随机正整数.
	 * 
	 * @param length
	 *            int 设定所取出随机数的长度。length小于11
	 * @return int 返回生成的随机数。
	 */
	public static int buildRandom(int length) {
		int num = 1;
		double random = Math.random();
		if (random < 0.1) {
			random = random + 0.1;
		}
		for (int i = 0; i < length; i++) {
			num = num * 10;
		}
		return (int) ((random * num));
	}

	/**
	 * 生成时间戳
	 * 
	 * @return
	 */
	public static long genTimeStamp() {
		return System.currentTimeMillis() / 1000;
	}

	/**
	 * 查询随机字符串(32位)
	 * 
	 * @return
	 */
	public static String getNonceStr() {
		Random random = new Random();
		return WxPayUtil.getMessageDigest(String.valueOf(random.nextInt(10000)));
	}

	public static String MD5Encode(String origin, String charsetname) {
		String resultString = null;
		try {
			resultString = new String(origin);
			MessageDigest md = MessageDigest.getInstance("MD5");
			if (charsetname == null || "".equals(charsetname))
				resultString = byteArrayToHexString(md.digest(resultString.getBytes()));
			else
				resultString = byteArrayToHexString(md.digest(resultString.getBytes(charsetname)));
		} catch (Exception exception) {
		}
		return resultString;
	}

	private static String byteArrayToHexString(byte b[]) {
		StringBuffer resultSb = new StringBuffer();
		for (int i = 0; i < b.length; i++)
			resultSb.append(byteToHexString(b[i]));

		return resultSb.toString();
	}

	private static String byteToHexString(byte b) {
		int n = b;
		if (n < 0)
			n += 256;
		int d1 = n / 16;
		int d2 = n % 16;
		return hexDigits[d1] + hexDigits[d2];
	}

	private static final String hexDigits[] = { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d",
			"e", "f" };

	/**
	 * 加密
	 *
	 * @return
	 */
	public final static String getMessageDigest(String s) {
		char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };
		try {
			byte[] btInput = s.getBytes();
			// 获得MD5摘要算法的 MessageDigest 对象
			MessageDigest mdInst = MessageDigest.getInstance("MD5");
			// 使用指定的字节更新摘要
			mdInst.update(btInput);
			// 获得密文
			byte[] md = mdInst.digest();
			// 把密文转换成十六进制的字符串形式
			int j = md.length;
			char str[] = new char[j * 2];
			int k = 0;
			for (int i = 0; i < j; i++) {
				byte byte0 = md[i];
				str[k++] = hexDigits[byte0 >>> 4 & 0xf];
				str[k++] = hexDigits[byte0 & 0xf];
			}
			return new String(str);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
}
