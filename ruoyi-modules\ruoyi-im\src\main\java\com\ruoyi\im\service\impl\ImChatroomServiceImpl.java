package com.ruoyi.im.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.im.api.domain.ImChatroom;
import com.ruoyi.im.api.util.SqlUtils;
import com.ruoyi.im.mapper.ImChatroomMapper;
import com.ruoyi.im.service.ImChatroomService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ImChatroomServiceImpl extends ServiceImpl<ImChatroomMapper, ImChatroom> implements ImChatroomService
{

    @Resource
    private ImChatroomMapper imChatroomMapper;
    @Override
    public List<ImChatroom> queryPage(Page<ImChatroom> pageSearch, String chatroomName) {
        return imChatroomMapper.queryPage(pageSearch,
                StringUtils.isNotBlank(chatroomName) ? "%" + chatroomName + "%" : null);
    }
}