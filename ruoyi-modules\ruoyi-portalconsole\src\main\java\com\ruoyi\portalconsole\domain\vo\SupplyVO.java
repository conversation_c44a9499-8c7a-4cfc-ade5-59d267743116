package com.ruoyi.portalconsole.domain.vo;

import com.ruoyi.portalconsole.domain.Supply;
import io.swagger.annotations.ApiModelProperty;

/**
 * 服务供给对象 supply
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public class SupplyVO extends Supply
{
    private static final long serialVersionUID = 1L;

    private String imageUrlPath;

	@ApiModelProperty(value = "应用领域名称")
	private String applicationAreaName;

	public String getApplicationAreaName() {
		return applicationAreaName;
	}

	public void setApplicationAreaName(String applicationAreaName) {
		this.applicationAreaName = applicationAreaName;
	}

	public String getImageUrlPath() {
		return imageUrlPath;
	}

	public void setImageUrlPath(String imageUrlPath) {
		this.imageUrlPath = imageUrlPath;
	}

   
}
