<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserChatMessageMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.UserChatMessage">
    <!--@mbg.generated-->
    <!--@Table user_chat_message-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="send_user_name" jdbcType="VARCHAR" property="sendUserName" />
    <result column="target_id" jdbcType="VARCHAR" property="targetId" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="media_url" jdbcType="VARCHAR" property="mediaUrl" />
    <result column="content_type" jdbcType="INTEGER" property="contentType" />
    <result column="conversation_type" jdbcType="INTEGER" property="conversationType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_ timestamp" jdbcType="BIGINT" property="createTimestamp" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="conversation_id" jdbcType="VARCHAR" property="conversationId" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
    <result column="original_id" jdbcType="BIGINT" property="originalId" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />

  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, send_user_name, target_id, content, media_url, content_type, conversation_type,
    create_time, `create_ timestamp`, user_name, conversation_id, delete_flag, original_id,file_name
  </sql>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.system.domain.UserChatMessage" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into user_chat_message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sendUserName != null">
        send_user_name,
      </if>
      <if test="targetId != null">
        target_id,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="mediaUrl != null">
        media_url,
      </if>
      <if test="contentType != null">
        content_type,
      </if>
      <if test="conversationType != null">
        conversation_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createTimestamp != null">
        `create_ timestamp`,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="conversationId != null">
        conversation_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="originalId != null">
        original_id,
      </if>
      <if test="fileName != null and fileName != ''">
        file_name
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sendUserName != null">
        #{sendUserName,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null">
        #{targetId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="mediaUrl != null">
        #{mediaUrl,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null">
        #{contentType,jdbcType=INTEGER},
      </if>
      <if test="conversationType != null">
        #{conversationType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="conversationId != null">
        #{conversationId,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="originalId != null">
        #{originalId,jdbcType=BIGINT},
      </if>
      <if test="fileName != null and fileName != ''">
        #{fileName,jdbcType=VARCHAR}
      </if>
    </trim>
  </insert>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from user_chat_message
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from user_chat_message
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.system.domain.UserChatMessage" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into user_chat_message (send_user_name, target_id, content, 
      media_url, content_type, conversation_type,
      create_time, `create_ timestamp`, user_name, 
      conversation_id, delete_flag, original_id,file_name
      )
    values (#{sendUserName,jdbcType=VARCHAR}, #{targetId,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, 
      #{mediaUrl,jdbcType=VARCHAR}, #{contentType,jdbcType=INTEGER}, #{conversationType,jdbcType=INTEGER},
      #{createTime,jdbcType=TIMESTAMP}, #{createTimestamp,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR}, 
      #{conversationId,jdbcType=VARCHAR}, #{deleteFlag,jdbcType=INTEGER}, #{originalId,jdbcType=BIGINT},#{fileName,jdbcType=VARCHAR}
      )
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.system.domain.UserChatMessage">
    <!--@mbg.generated-->
    update user_chat_message
    <set>
      <if test="sendUserName != null">
        send_user_name = #{sendUserName,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null">
        target_id = #{targetId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="mediaUrl != null">
        media_url = #{mediaUrl,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null">
        content_type = #{contentType,jdbcType=INTEGER},
      </if>
      <if test="conversationType != null">
        conversation_type = #{conversationType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTimestamp != null">
        `create_ timestamp` = #{createTimestamp,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="conversationId != null">
        conversation_id = #{conversationId,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="originalId != null">
        original_id = #{originalId,jdbcType=BIGINT},
      </if>
      <if test="fileName != null and fileName != ''">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.system.domain.UserChatMessage">
    <!--@mbg.generated-->
    update user_chat_message
    set send_user_name = #{sendUserName,jdbcType=VARCHAR},
      target_id = #{targetId,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      media_url = #{mediaUrl,jdbcType=VARCHAR},
      content_type = #{contentType,jdbcType=INTEGER},
      conversation_type = #{conversationType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      `create_ timestamp` = #{createTimestamp,jdbcType=BIGINT},
      user_name = #{userName,jdbcType=VARCHAR},
      conversation_id = #{conversationId,jdbcType=VARCHAR},
      delete_flag = #{deleteFlag,jdbcType=INTEGER},
      original_id = #{originalId,jdbcType=BIGINT},
    file_name  = #{fileName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.system.domain.UserChatMessage" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into user_chat_message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      send_user_name,
      target_id,
      content,
      media_url,
      content_type,
      conversation_type,
      create_time,
      `create_ timestamp`,
      user_name,
      conversation_id,
      delete_flag,
      original_id,
      file_name,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{sendUserName,jdbcType=VARCHAR},
      #{targetId,jdbcType=VARCHAR},
      #{content,jdbcType=VARCHAR},
      #{mediaUrl,jdbcType=VARCHAR},
      #{contentType,jdbcType=INTEGER},
      #{conversationType,jdbcType=INTEGER},
      #{createTime,jdbcType=TIMESTAMP},
      #{createTimestamp,jdbcType=BIGINT},
      #{userName,jdbcType=VARCHAR},
      #{conversationId,jdbcType=VARCHAR},
      #{deleteFlag,jdbcType=INTEGER},
      #{originalId,jdbcType=BIGINT},
      #{fileName,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      send_user_name = #{sendUserName,jdbcType=VARCHAR},
      target_id = #{targetId,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      media_url = #{mediaUrl,jdbcType=VARCHAR},
      content_type = #{contentType,jdbcType=INTEGER},
      conversation_type = #{conversationType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      `create_ timestamp` = #{createTimestamp,jdbcType=BIGINT},
      user_name = #{userName,jdbcType=VARCHAR},
      conversation_id = #{conversationId,jdbcType=VARCHAR},
      delete_flag = #{deleteFlag,jdbcType=INTEGER},
      original_id = #{originalId,jdbcType=BIGINT},
      file_name = #{fileName,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.system.domain.UserChatMessage" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into user_chat_message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sendUserName != null">
        send_user_name,
      </if>
      <if test="targetId != null">
        target_id,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="mediaUrl != null">
        media_url,
      </if>
      <if test="contentType != null">
        content_type,
      </if>
      <if test="conversationType != null">
        conversation_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createTimestamp != null">
        `create_ timestamp`,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="conversationId != null">
        conversation_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="originalId != null">
        original_id,
      </if>
      <if test="fileName != null and fileName != ''">
        file_name,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sendUserName != null">
        #{sendUserName,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null">
        #{targetId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="mediaUrl != null">
        #{mediaUrl,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null">
        #{contentType,jdbcType=INTEGER},
      </if>
      <if test="conversationType != null">
        #{conversationType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="conversationId != null">
        #{conversationId,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="originalId != null">
        #{originalId,jdbcType=BIGINT},
      </if>
      <if test="fileName != null and fileName != ''">
        #{fileName,jdbcType=VARCHAR}
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="sendUserName != null">
        send_user_name = #{sendUserName,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null">
        target_id = #{targetId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="mediaUrl != null">
        media_url = #{mediaUrl,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null">
        content_type = #{contentType,jdbcType=INTEGER},
      </if>
      <if test="conversationType != null">
        conversation_type = #{conversationType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTimestamp != null">
        `create_ timestamp` = #{createTimestamp,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="conversationId != null">
        conversation_id = #{conversationId,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="originalId != null">
        original_id = #{originalId,jdbcType=BIGINT},
      </if>
      <if test="fileName != null and fileName != ''">
        file_name = #{fileName,jdbcType=VARCHAR}
      </if>
    </trim>
  </insert>
</mapper>