package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.JobInfoMapper;
import com.ruoyi.system.domain.JobInfo;
import com.ruoyi.system.service.IJobInfoService;

/**
 * 用工信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class JobInfoServiceImpl implements IJobInfoService 
{
    @Autowired
    private JobInfoMapper jobInfoMapper;

    /**
     * 查询用工信息
     * 
     * @param id 用工信息主键
     * @return 用工信息
     */
    @Override
    public JobInfo selectJobInfoById(Long id)
    {
        return jobInfoMapper.selectJobInfoById(id);
    }

    /**
     * 查询用工信息列表
     * 
     * @param jobInfo 用工信息
     * @return 用工信息
     */
    @Override
    public List<JobInfo> selectJobInfoList(JobInfo jobInfo)
    {
        return jobInfoMapper.selectJobInfoList(jobInfo);
    }

    /**
     * 新增用工信息
     * 
     * @param jobInfo 用工信息
     * @return 结果
     */
    @Override
    public int insertJobInfo(JobInfo jobInfo)
    {
        jobInfo.setCreateTime(DateUtils.getNowDate());
//        jobInfo.setCreateBy(SecurityUtils.getLoginMember().getMemberphone());
        return jobInfoMapper.insertJobInfo(jobInfo);
    }

    /**
     * 修改用工信息
     * 
     * @param jobInfo 用工信息
     * @return 结果
     */
    @Override
    public int updateJobInfo(JobInfo jobInfo)
    {
        jobInfo.setUpdateTime(DateUtils.getNowDate());
        jobInfo.setUpdateBy(SecurityUtils.getUserId()+"");
        return jobInfoMapper.updateJobInfo(jobInfo);
    }

    /**
     * 批量删除用工信息
     * 
     * @param ids 需要删除的用工信息主键
     * @return 结果
     */
    @Override
    public int deleteJobInfoByIds(Long[] ids)
    {
        return jobInfoMapper.deleteJobInfoByIds(ids);
    }

    /**
     * 删除用工信息信息
     * 
     * @param id 用工信息主键
     * @return 结果
     */
    @Override
    public int deleteJobInfoById(Long id)
    {
        return jobInfoMapper.deleteJobInfoById(id);
    }
}
