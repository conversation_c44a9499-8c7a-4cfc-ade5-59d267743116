package com.ruoyi.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 实验室检测项目关联对象 lab_testing_relation
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public class LabTestingRelation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 关联ID */
    private Long id;

    /** 实验室ID */
    @Excel(name = "实验室ID")
    private Long labId;

    /** 检测项目ID */
    @Excel(name = "检测项目ID")
    private Long testingId;

    /** 检测价格 */
    @Excel(name = "检测价格")
    private BigDecimal price;

    /** 检测周期(天) */
    @Excel(name = "检测周期(天)")
    private Long cycle;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setLabId(Long labId) 
    {
        this.labId = labId;
    }

    public Long getLabId() 
    {
        return labId;
    }
    public void setTestingId(Long testingId) 
    {
        this.testingId = testingId;
    }

    public Long getTestingId() 
    {
        return testingId;
    }
    public void setPrice(BigDecimal price) 
    {
        this.price = price;
    }

    public BigDecimal getPrice() 
    {
        return price;
    }
    public void setCycle(Long cycle) 
    {
        this.cycle = cycle;
    }

    public Long getCycle() 
    {
        return cycle;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("labId", getLabId())
            .append("testingId", getTestingId())
            .append("price", getPrice())
            .append("cycle", getCycle())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
