package com.ruoyi.portalweb.service;

import java.util.List;
import com.ruoyi.portalweb.api.domain.AppConfig;

/**
 * 应用配置Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-22
 */
public interface IAppConfigService 
{
    /**
     * 查询应用配置
     * 
     * @param appConfigId 应用配置主键
     * @return 应用配置
     */
    public AppConfig selectAppConfigByAppConfigId(Long appConfigId);

    /**
     * 查询应用配置列表
     * 
     * @param appConfig 应用配置
     * @return 应用配置集合
     */
    public List<AppConfig> selectAppConfigList(AppConfig appConfig);

    /**
     * 新增应用配置
     * 
     * @param appConfig 应用配置
     * @return 结果
     */
    public long insertAppConfig(AppConfig appConfig);

    /**
     * 修改应用配置
     * 
     * @param appConfig 应用配置
     * @return 结果
     */
    public int updateAppConfig(AppConfig appConfig);

    public int updateAppConfigByAppStoreId(AppConfig appConfig);

    /**
     * 批量删除应用配置
     * 
     * @param appConfigIds 需要删除的应用配置主键集合
     * @return 结果
     */
    public int deleteAppConfigByAppConfigIds(Long[] appConfigIds);

    /**
     * 删除应用配置信息
     * 
     * @param appConfigId 应用配置主键
     * @return 结果
     */
    public int deleteAppConfigByAppConfigId(Long appConfigId);

    AppConfig selectAppConfigByAppStoreId(Long appStoreId);
}
