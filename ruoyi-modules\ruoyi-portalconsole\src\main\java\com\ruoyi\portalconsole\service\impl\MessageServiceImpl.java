package com.ruoyi.portalconsole.service.impl;

import java.util.List;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalconsole.utils.SmsConfig;
import com.ruoyi.portalconsole.utils.SmsSendUtils;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.MessageMapper;
import com.ruoyi.portalconsole.domain.Message;
import com.ruoyi.portalconsole.service.IMessageService;

/**
 * 站内消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class MessageServiceImpl implements IMessageService 
{
    @Autowired
    private MessageMapper messageMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    @Value("${ali.policyTemplate}")
    private String policyTemplate;

    @Autowired
    private SmsConfig smsConfig;


    /**
     * 查询站内消息
     * 
     * @param messageId 站内消息主键
     * @return 站内消息
     */
    @Override
    public Message selectMessageByMessageId(Long messageId)
    {
        return messageMapper.selectMessageByMessageId(messageId);
    }

    /**
     * 查询站内消息列表
     * 
     * @param message 站内消息
     * @return 站内消息
     */
    @Override
    public List<Message> selectMessageList(Message message)
    {
        return messageMapper.selectMessageList(message);
    }

    /**
     * 新增站内消息
     * 
     * @param message 站内消息
     * @return 结果
     */
    @Override
    public int insertMessage(Message message)
    {
        message.setCreateTime(DateUtils.getNowDate());
        return messageMapper.insertMessage(message);
    }


    /**
     * 新增站内消息(异步)
     *
     * @param message 站内消息
     * @return 结果
     */
    @Override
    @Async
    public void insertMessageByAsync(Message message)
    {
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        message.setCreateBy(userNickName.getData());
        message.setUpdateBy(userNickName.getData());
        messageMapper.insertMessage(message);
    }


    /**
     * 修改站内消息
     * 
     * @param message 站内消息
     * @return 结果
     */
    @Override
    public int updateMessage(Message message)
    {
        message.setUpdateTime(DateUtils.getNowDate());
        return messageMapper.updateMessage(message);
    }

    /**
     * 批量删除站内消息
     * 
     * @param messageIds 需要删除的站内消息主键
     * @return 结果
     */
    @Override
    public int deleteMessageByMessageIds(Long[] messageIds)
    {
        return messageMapper.deleteMessageByMessageIds(messageIds);
    }

    /**
     * 删除站内消息信息
     * 
     * @param messageId 站内消息主键
     * @return 结果
     */
    @Override
    public int deleteMessageByMessageId(Long messageId)
    {
        return messageMapper.deleteMessageByMessageId(messageId);
    }

    @Override
    public void sendPhoneMessage(List<String> phones) {
        for (String phone : phones){
            SmsSendUtils.opAliyunCode(phone, smsConfig, policyTemplate);
        }
    }

}
