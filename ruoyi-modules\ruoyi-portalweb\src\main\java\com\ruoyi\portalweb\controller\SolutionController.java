package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.Demand;
import com.ruoyi.portalweb.api.domain.Solution;
import com.ruoyi.portalweb.service.ISolutionService;
import com.ruoyi.portalweb.vo.SolutionVO;
import com.ruoyi.system.api.RemoteUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 解决方案Controller
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/solution")
@Api(value = "6.解决方案", tags = "6.解决方案")
public class SolutionController extends BaseController {
    @Autowired
    private ISolutionService solutionService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询解决方案列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询解决方案列表", notes = "传入")
    public TableDataInfo list(SolutionVO solution) {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<SolutionVO> list = solutionService.selectSolutionList(solution);
        return getDataTable(list);
    }

    /**
     * 查询解决方案列表
     */
    @GetMapping("/listDesk")
    @ApiOperation(value = "查询解决方案列表", notes = "传入")
    public TableDataInfo listDesk(SolutionVO solution) {
        startPage();
        PageUtils.setOrderBy("create_time DESC");
        List<SolutionVO> list = solutionService.selectSolutionList(solution);
        return getDataTable(list);
    }

    /**
     * 查询解决方案列表
     */
    @GetMapping("/listDesk/CompanyRelated")
    @ApiOperation(value = "查询解决方案列表", notes = "传入")
    public TableDataInfo listDeskCompanyRelated(@RequestParam(value = "companyRelatedId") Long companyRelatedId) {
        List<SolutionVO> list = solutionService.listDeskCompanyRelated(companyRelatedId);
        return getDataTable(list);
    }


    /**
     * 导出解决方案列表
     */
    @Log(title = "解决方案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出解决方案列表", notes = "传入")
    public void export(HttpServletResponse response, SolutionVO solution) {
        List<SolutionVO> list = solutionService.selectSolutionList(solution);
        ExcelUtil<SolutionVO> util = new ExcelUtil<>(SolutionVO.class);
        util.exportExcel(response, list, "解决方案数据");
    }

//    /**
//     * 获取解决方案详细信息
//     */
//    @GetMapping(value = "/{solutionId}")
//    @ApiOperation(value = "获取解决方案详细信息", notes = "传入")
//    public AjaxResult getInfo(@PathVariable("solutionId") Long solutionId)
//    {
//        return success(solutionService.selectSolutionBySolutionId(solutionId));
//    }

    /**
     * 获取解决方案详细信息
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取解决方案详细信息", notes = "传入")
    public AjaxResult detail(Solution solution) {
        return success(solutionService.selectSolutionBySolutionId(solution.getSolutionId()));
    }

    /**
     * 获取解决方案详细信息
     */
    @GetMapping("/detailDesk")
    @ApiOperation(value = "获取解决方案详细信息", notes = "传入")
    public AjaxResult detailDesk(Solution solution) {
        return success(solutionService.selectSolutionBySolutionId(solution.getSolutionId()));
    }

    /**
     * 新增解决方案
     */
    @Log(title = "解决方案", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增解决方案", notes = "传入")
    public AjaxResult add(@RequestBody Solution solution) {
        return toAjax(solutionService.insertSolution(solution));
    }

    /**
     * 修改解决方案
     */
    @Log(title = "解决方案", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改解决方案", notes = "传入")
    public AjaxResult edit(@RequestBody Solution solution) {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        solution.setUpdateBy(userNickName.getData());
        return toAjax(solutionService.updateSolution(solution));
    }

    /**
     * 删除解决方案
     */
    @Log(title = "解决方案", businessType = BusinessType.DELETE)
    @DeleteMapping("/{solutionIds}")
    @ApiOperation(value = "删除解决方案", notes = "传入")
    public AjaxResult remove(@PathVariable Long[] solutionIds) {
        return toAjax(solutionService.deleteSolutionBySolutionIds(solutionIds));
    }

    /**
     * 查询解决方案列表首页用
     */
    @GetMapping("/solutionDeskList")
    @ApiOperation(value = "查询解决方案列表首页用", notes = "传入")
    public AjaxResult selectSolutionToDesk(Solution solution) {
        return success(solutionService.selectSolutionToDesk());
    }

    /**
     * 获取推荐解决方案列表
     */
    @GetMapping("/recommend")
    @ApiOperation(value = "获取推荐解决方案列表", notes = "传入")
    public TableDataInfo selectRecommendSolutionList(Solution solution) {
        List<SolutionVO> solutionVOS = solutionService.selectRecommendSolutionList(solution);
        return getDataTable(solutionVOS);
    }
}
