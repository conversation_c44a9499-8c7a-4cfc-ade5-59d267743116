package com.ruoyi.portalconsole.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 意向申请对象 intention_apply
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
public class IntentionApply extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 意向类型(字典):需求;供给 */
    @Excel(name = "意向类型(字典):需求;供给")
    private String intentionType;

    /** 意向描述 */
    @Excel(name = "意向描述")
    private String intentionContent;

     /** 供需意向id */
    @Excel(name = "供需意向id")
    private Long intentionId;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contacts;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 会员id */
    @Excel(name = "会员id")
    private Long memberId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setIntentionType(String intentionType) 
    {
        this.intentionType = intentionType;
    }

    public String getIntentionType() 
    {
        return intentionType;
    }
    public void setIntentionContent(String intentionContent) 
    {
        this.intentionContent = intentionContent;
    }

    public String getIntentionContent() 
    {
        return intentionContent;
    }
    public void setContacts(String contacts) 
    {
        this.contacts = contacts;
    }

    public String getContacts() 
    {
        return contacts;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }
    public void setMemberId(Long memberId) 
    {
        this.memberId = memberId;
    }

    public Long getMemberId() 
    {
        return memberId;
    }

    public void setIntentionId(Long intentionId)
    {
        this.intentionId = intentionId;
    }

    public Long getIntentionId()
    {
        return intentionId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("intentionType", getIntentionType())
            .append("intentionContent", getIntentionContent())
            .append("contacts", getContacts())
            .append("phone", getPhone())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("memberId", getMemberId())
            .append("intentionId", getIntentionId())
            .toString();
    }
}
