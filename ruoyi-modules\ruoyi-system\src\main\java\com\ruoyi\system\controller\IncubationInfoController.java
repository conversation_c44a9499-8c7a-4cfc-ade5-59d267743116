package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.IncubationInfo;
import com.ruoyi.system.service.IIncubationInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 创业孵化信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-11
 */
@RestController
@RequestMapping("/incubationInfo")
public class IncubationInfoController extends BaseController
{
    @Autowired
    private IIncubationInfoService incubationInfoService;

    /**
     * 查询创业孵化信息列表
     */

    @GetMapping("/list")
    public TableDataInfo list(IncubationInfo incubationInfo)
    {
        startPage();
        List<IncubationInfo> list = incubationInfoService.selectIncubationInfoList(incubationInfo);
        return getDataTable(list);
    }

    /**
     * 导出创业孵化信息列表
     */

    @Log(title = "创业孵化信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IncubationInfo incubationInfo)
    {
        List<IncubationInfo> list = incubationInfoService.selectIncubationInfoList(incubationInfo);
        ExcelUtil<IncubationInfo> util = new ExcelUtil<IncubationInfo>(IncubationInfo.class);
        util.exportExcel(response, list, "创业孵化信息数据");
    }

    /**
     * 获取创业孵化信息详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(incubationInfoService.selectIncubationInfoById(id));
    }

    /**
     * 新增创业孵化信息
     */

    @Log(title = "创业孵化信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IncubationInfo incubationInfo)
    {
        return toAjax(incubationInfoService.insertIncubationInfo(incubationInfo));
    }

    /**
     * 修改创业孵化信息
     */

    @Log(title = "创业孵化信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IncubationInfo incubationInfo)
    {
        return toAjax(incubationInfoService.updateIncubationInfo(incubationInfo));
    }

    /**
     * 删除创业孵化信息
     */

    @Log(title = "创业孵化信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(incubationInfoService.deleteIncubationInfoByIds(ids));
    }
}
