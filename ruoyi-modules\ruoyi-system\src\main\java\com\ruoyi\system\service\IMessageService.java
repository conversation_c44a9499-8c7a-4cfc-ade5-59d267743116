package com.ruoyi.system.service;

import com.ruoyi.system.api.domain.Message;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface IMessageService {

    @Async
    public CompletableFuture<Integer> sendMessageBatch(List<Message> messages);

    public Message getOne(Long memberId,String title, String body);

}

