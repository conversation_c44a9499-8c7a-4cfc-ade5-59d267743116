package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.Message;
import com.ruoyi.system.mapper.MessageMapper;
import com.ruoyi.system.service.IMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Service
public class MessageServiceImpl implements IMessageService {

    @Autowired
    private MessageMapper messageMapper;


    @Override
    public CompletableFuture<Integer> sendMessageBatch(List<Message> messages) {
        int i = messageMapper.insertMessages(messages);
        CompletableFuture<Integer> integerCompletableFuture = new CompletableFuture<>();
        integerCompletableFuture.complete(i);
        return integerCompletableFuture;
    }

    @Override
    public Message getOne(Long memberId,String title, String body) {
        Message message = new Message();
        message.setMemberId(memberId);
        message.setMessageTitle(title);
        message.setMessageBody(body);
        message.setMessageStatus("1");
        message.setCreateBy(SecurityUtils.getUsername());
        message.setUpdateBy(SecurityUtils.getUsername());
        message.setCreateTime(DateUtils.getNowDate());
        message.setUpdateTime(DateUtils.getNowDate());
        return message;
    }
}

