package com.ruoyi.portalweb.vo;


import com.ruoyi.portalweb.api.domain.NewsInformation;
import io.swagger.annotations.ApiModelProperty;

/**
 * 动态资讯
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public class NewsInformationVO extends NewsInformation {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "方案类型")
    private String solutionTypeName;
    @ApiModelProperty(value = "关键字")
    private String keywords;
    @ApiModelProperty(value = "排序")
    private String sort;

    public String getSolutionTypeName() {
        return solutionTypeName;
    }

    public void setSolutionTypeName(String solutionTypeName) {
        this.solutionTypeName = solutionTypeName;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }
}
