import request from '@/utils/request'

// 查询关联企业信息列表
export function listPortalconsole(query) {
  return request({
    url: '/portalconsole/companyRelated/list',
    method: 'get',
    params: query
  })
}

// 查询关联企业信息详细
export function getPortalconsole(companyRelatedId) {
  return request({
    url: '/portalconsole/companyRelated/' + companyRelatedId,
    method: 'get'
  })
}

// 新增关联企业信息
export function addPortalconsole(data) {
  return request({
    url: '/portalconsole/companyRelated',
    method: 'post',
    data: data
  })
}

// 修改关联企业信息
export function updatePortalconsole(data) {
  return request({
    url: '/portalconsole/companyRelated',
    method: 'put',
    data: data
  })
}

// 删除关联企业信息
export function delPortalconsole(companyRelatedId) {
  return request({
    url: '/portalconsole/companyRelated/' + companyRelatedId,
    method: 'delete'
  })
}
//导出模板
export function importTemplate() {
  return request({
    url: '/portalconsole/companyRelated/importTemplate' ,
    method: 'get',
    responseType: 'blob'
  })
}

//批量导入
export function importBatch(data) {
  return request({
    url: '/portalconsole/companyRelated/importBatch',
    method: 'post',
    headers:{'Content-Type': 'multipart/form-data'},
    data: data
  })
}