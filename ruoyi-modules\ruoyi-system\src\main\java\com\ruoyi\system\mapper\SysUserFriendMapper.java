package com.ruoyi.system.mapper;


import com.ruoyi.system.domain.SysUserFriend;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysUserFriendMapper {

    List<SysUserFriend> selectFriendList(@Param("userName") String userName);

    int deleteFriend(@Param("friendUseName") String friendUseName, @Param("userName") String userName);

    int updateFriend(SysUserFriend sysUserFriend);

    int insertSelective(SysUserFriend sysUserFriend);


    SysUserFriend selectFriendByFriendName(@Param("userName") String userName, @Param("friendUserName") String friendUserName);
}