package com.ruoyi.im.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.im.api.domain.ImChatroomMsg;

import java.util.List;

public interface ImChatroomMsgService extends IService<ImChatroomMsg>{

    List<ImChatroomMsg> findLatestMsg(List<String> ids);

    /**
     * 发送过消息的聊天室
     * @param telphone
     * @return
     */
    List<String> findSent(String telphone);
}
