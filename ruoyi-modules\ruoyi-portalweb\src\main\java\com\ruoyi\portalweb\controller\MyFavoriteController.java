package com.ruoyi.portalweb.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.portalweb.vo.MyFavoriteVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalweb.api.domain.MyFavorite;
import com.ruoyi.portalweb.service.IMyFavoriteService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 我的收藏Controller
 * 
 * <AUTHOR>
 * @date 2024-06-25
 */
@RestController
@RequestMapping("/MyFavorite")
public class MyFavoriteController extends BaseController
{
    @Autowired
    private IMyFavoriteService myFavoriteService;

    /**
     * 查询我的收藏列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MyFavorite myFavorite)
    {
        startPage();
        PageUtils.setOrderBy("create_time DESC");
        List<MyFavoriteVO> list = myFavoriteService.selectMyFavoriteList(myFavorite);
        return getDataTable(list);
    }

    /**
     * 导出我的收藏列表
     */
    @RequiresPermissions("portalweb:MyFavorite:export")
    @Log(title = "我的收藏", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MyFavorite myFavorite)
    {
        List<MyFavoriteVO> list = myFavoriteService.selectMyFavoriteList(myFavorite);
        ExcelUtil<MyFavoriteVO> util = new ExcelUtil<MyFavoriteVO>(MyFavoriteVO.class);
        util.exportExcel(response, list, "我的收藏数据");
    }

    /**
     * 获取我的收藏详细信息
     */
    @GetMapping(value = "/{myFavoriteId}")
    public AjaxResult getInfo(@PathVariable("myFavoriteId") Long myFavoriteId)
    {
        return success(myFavoriteService.selectMyFavoriteByMyFavoriteId(myFavoriteId));
    }

    /**
     * 获取我的收藏详细信息
     */
    @GetMapping(value = "/{type}/{issueId}")
    public AjaxResult getInfoByIssueId(@PathVariable("type") String type, @PathVariable("issueId") Long issueId)
    {
        return success(myFavoriteService.selectMyFavoriteByIssueId(type, issueId));
    }



    /**
     * 新增我的收藏
     */
    @Log(title = "我的收藏", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MyFavorite myFavorite)
    {

        return toAjax(myFavoriteService.insertMyFavorite(myFavorite));
    }

    /**
     * 修改我的收藏
     */
    @RequiresPermissions("portalweb:MyFavorite:edit")
    @Log(title = "我的收藏", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MyFavorite myFavorite)
    {
        return toAjax(myFavoriteService.updateMyFavorite(myFavorite));
    }

    /**
     * 删除我的收藏
     */
    @Log(title = "我的收藏", businessType = BusinessType.DELETE)
	@DeleteMapping("/{myFavoriteIds}")
    public AjaxResult remove(@PathVariable Long[] myFavoriteIds)
    {
        return toAjax(myFavoriteService.deleteMyFavoriteByMyFavoriteIds(myFavoriteIds));
    }
}
