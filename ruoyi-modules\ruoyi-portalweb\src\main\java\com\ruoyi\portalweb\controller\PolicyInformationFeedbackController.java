package com.ruoyi.portalweb.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalweb.api.domain.PolicyInformationFeedback;
import com.ruoyi.portalweb.service.IPolicyInformationFeedbackService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 政策意见反馈Controller
 * 
 * <AUTHOR>
 * @date 2024-06-26
 */
@RestController
@RequestMapping("/PolicyInformationFeedback")
public class PolicyInformationFeedbackController extends BaseController
{
    @Autowired
    private IPolicyInformationFeedbackService policyInformationFeedbackService;

    /**
     * 查询政策意见反馈列表
     */
    @RequiresPermissions("portalweb:PolicyInformationFeedback:list")
    @GetMapping("/list")
    public TableDataInfo list(PolicyInformationFeedback policyInformationFeedback)
    {
        startPage();
        List<PolicyInformationFeedback> list = policyInformationFeedbackService.selectPolicyInformationFeedbackList(policyInformationFeedback);
        return getDataTable(list);
    }

    /**
     * 导出政策意见反馈列表
     */
    @RequiresPermissions("portalweb:PolicyInformationFeedback:export")
    @Log(title = "政策意见反馈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PolicyInformationFeedback policyInformationFeedback)
    {
        List<PolicyInformationFeedback> list = policyInformationFeedbackService.selectPolicyInformationFeedbackList(policyInformationFeedback);
        ExcelUtil<PolicyInformationFeedback> util = new ExcelUtil<PolicyInformationFeedback>(PolicyInformationFeedback.class);
        util.exportExcel(response, list, "政策意见反馈数据");
    }

    /**
     * 获取政策意见反馈详细信息
     */
    @RequiresPermissions("portalweb:PolicyInformationFeedback:query")
    @GetMapping(value = "/{policyInformationFeedbackId}")
    public AjaxResult getInfo(@PathVariable("policyInformationFeedbackId") Long policyInformationFeedbackId)
    {
        return success(policyInformationFeedbackService.selectPolicyInformationFeedbackByPolicyInformationFeedbackId(policyInformationFeedbackId));
    }

    /**
     * 新增政策意见反馈
     */
//    @RequiresPermissions("portalweb:PolicyInformationFeedback:add")
    @Log(title = "政策意见反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PolicyInformationFeedback policyInformationFeedback)
    {
        return toAjax(policyInformationFeedbackService.insertPolicyInformationFeedback(policyInformationFeedback));
    }

    /**
     * 修改政策意见反馈
     */
    @RequiresPermissions("portalweb:PolicyInformationFeedback:edit")
    @Log(title = "政策意见反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PolicyInformationFeedback policyInformationFeedback)
    {
        return toAjax(policyInformationFeedbackService.updatePolicyInformationFeedback(policyInformationFeedback));
    }

    /**
     * 删除政策意见反馈
     */
    @RequiresPermissions("portalweb:PolicyInformationFeedback:remove")
    @Log(title = "政策意见反馈", businessType = BusinessType.DELETE)
	@DeleteMapping("/{policyInformationFeedbackIds}")
    public AjaxResult remove(@PathVariable Long[] policyInformationFeedbackIds)
    {
        return toAjax(policyInformationFeedbackService.deletePolicyInformationFeedbackByPolicyInformationFeedbackIds(policyInformationFeedbackIds));
    }
}
