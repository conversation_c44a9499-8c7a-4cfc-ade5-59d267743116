
package com.ruoyi.portalweb.wxpay.service.impl;

import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.nacos.api.model.v2.Result;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.portalweb.wxpay.pay.WxNativePayConfig;
import com.ruoyi.portalweb.wxpay.pay.WxPayConfig;
import com.ruoyi.portalweb.wxpay.pay.WxPayUtil;
import com.ruoyi.portalweb.wxpay.service.IWechatService;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.DefaultHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.BasicHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.ruoyi.portalweb.wxpay.pay.PayUtil;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import static com.ruoyi.portalweb.wxpay.pay.WXPayConstants.USER_AGENT;

/**
 * 微信接口实现类
 *
 * <AUTHOR>
 *
 */
@Service
@AllArgsConstructor
@Slf4j
public class WechatServiceImpl implements IWechatService {



	// 查询access_token的接口地址（GET） 限2000（次/天）
	public final static String access_token_url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET";
	public final static String get_user_info_url = "https://api.weixin.qq.com/cgi-bin/user/info?lang=zh_CN&access_token=ACCESS_TOKEN&openid=OPENID";
	public final static String message_custom_send_url = "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=ACCESS_TOKEN";
	public final static String qrcode_create = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=ACCESS_TOKEN";
	public final static String unifiedorder_url = "https://api.mch.weixin.qq.com/pay/unifiedorder";
	public final static String micropay_url = "https://api.mch.weixin.qq.com/pay/micropay";
	public final static String micropay_query_url = "https://api.mch.weixin.qq.com/pay/orderquery";
	public final static String micropay_reverse_url = "https://api.mch.weixin.qq.com/secapi/pay/reverse";
	public final static String refund_url = "https://api.mch.weixin.qq.com/secapi/pay/refund";
	public final static String refundquery_url = "https://api.mch.weixin.qq.com/pay/refundquery";
	public final static String get_wxpayface_authinfo_url = "https://payapp.weixin.qq.com/face/get_wxpayface_authinfo";
	public final static String get_tag_openids = "https://api.weixin.qq.com/cgi-bin/user/tag/get?access_token=ACCESS_TOKEN&tagid=TAGID";
	public final static String send_template = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=ACCESS_TOKEN";
	public final static String get_wxacode_url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=ACCESS_TOKEN";
	public final static String create_ticket_path = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=ACCESS_TOKEN";
	public final static String qrcode_show = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=TICKET";


	/**
	 * 微信扫码支付
	 *
	 * @param outTradeNo
	 * @param payMoney
	 * @param spbillCreateIp
	 * @param productId
	 * @param body
	 * @param tenantId
	 * @return
	 */
	public String wxNativePay(String outTradeNo, BigDecimal payMoney, String spbillCreateIp, Long productId,
			String body, String tenantId) {
		// todo 获取配置信息
		String wxAppId = "WXAPPID";
		String wxAppSecret = "WX_SECRET";
		String wxMchId = "WX_MCH_ID";
		String wxMchKey = "WX_MCH_KEY";
		String wxCallBackUrl = "WX_CALLBACK_URL";
		Integer totalFee = (payMoney.multiply(new BigDecimal(100))).intValue();// 支付金额
		String nonceStr = WxPayUtil.getCurrTime().substring(8, WxPayUtil.getCurrTime().length())
				+ String.valueOf(WxPayUtil.buildRandom(4));// 随机数
		String tradeType = "NATIVE";

		SortedMap<String, String> packageParams = new TreeMap<String, String>();
		packageParams.put("appid", wxAppId);
		packageParams.put("mch_id", wxMchId);
		packageParams.put("nonce_str", nonceStr);
		packageParams.put("body", body);
		packageParams.put("out_trade_no", outTradeNo);
		packageParams.put("total_fee", totalFee.toString());
		packageParams.put("spbill_create_ip", spbillCreateIp);
		packageParams.put("notify_url", wxCallBackUrl);
		packageParams.put("trade_type", tradeType);
		packageParams.put("product_id", productId.toString());
		PayUtil reqHandler = new PayUtil();
		reqHandler.init(wxAppId, wxAppSecret, wxMchKey);

		// 生成签名
		String sign = reqHandler.createSign(packageParams);

		String xml = "<xml>" + "<appid>" + wxAppId + "</appid>" + "<mch_id>" + wxMchId + "</mch_id>" + "<nonce_str>"
				+ nonceStr + "</nonce_str>" + "<sign>" + sign + "</sign>" + "<body><![CDATA[" + body + "]]></body>"
				+ "<out_trade_no>" + outTradeNo + "</out_trade_no>" + "<total_fee>" + totalFee + "</total_fee>"
				+ "<spbill_create_ip>" + spbillCreateIp + "</spbill_create_ip>" + "<notify_url>" + wxCallBackUrl
				+ "</notify_url>" + "<trade_type>" + tradeType + "</trade_type>" + "<product_id>" + productId
				+ "</product_id>" + "</xml>";

		String result = HttpUtil.post(unifiedorder_url, xml);
		Map<String, Object> map = XmlUtil.xmlToMap(result);
		if (map.get("return_code").equals("FAIL")) {
			String errorCode = map.get("return_code").toString();
			String errorMsg = map.get("return_msg").toString();
			throw new ServiceException(errorCode + ":" + errorMsg);
		}
		if (map.get("result_code").equals("FAIL")) {
			String errorCode = map.get("err_code").toString();
			String errorMsg = map.get("err_code_des").toString();
			throw new ServiceException(errorCode + ":" + errorMsg);
		}
		return map.get("code_url").toString();
	}

	/**
	 * 微信退款
	 * @param outTradeNo
	 * @param outRefundNo
	 * @param totalMoney
	 * @param refundMoney
	 * @param spbillCreateIp
	 * @param refundDesc
	 * @param wxCallBackUrl
	 * @param tenantId
	 *
	 * @return
	 */
	public R<Object> wxReturn(String outTradeNo, String outRefundNo, BigDecimal totalMoney, BigDecimal refundMoney,
							  String spbillCreateIp, String refundDesc, String wxCallBackUrl, String tenantId) {

		// todo 获取配置信息
		String wxAppId = WxNativePayConfig.APP_ID;
		String wxAppSecret = WxNativePayConfig.API_KEY;
		String wxMchId = WxNativePayConfig.MCH_ID;
		String wxMchKey = WxNativePayConfig.MCH_KEY;

		int totalFee = (totalMoney.multiply(new BigDecimal(100))).intValue();// 总金额
		int refundFee = (refundMoney.multiply(new BigDecimal(100))).intValue();// 退款金额
		String nonceStr = WxPayUtil.getCurrTime().substring(8, WxPayUtil.getCurrTime().length()) + String.valueOf(WxPayUtil.buildRandom(4));// 随机数

		SortedMap<String, String> packageParams = new TreeMap<String, String>();
		packageParams.put("appid", wxAppId);
		packageParams.put("mch_id", wxMchId);
		packageParams.put("nonce_str", nonceStr);
		packageParams.put("out_trade_no", outTradeNo);
		packageParams.put("out_refund_no", outRefundNo);
		packageParams.put("total_fee", Integer.toString(totalFee));
		packageParams.put("refund_fee", Integer.toString(refundFee));
		packageParams.put("refund_desc", refundDesc);
		packageParams.put("spbill_create_ip", spbillCreateIp);
		packageParams.put("notify_url", wxCallBackUrl);
		PayUtil reqHandler = new PayUtil();
		reqHandler.init(wxAppId, wxAppSecret, wxMchKey);

		// 生成签名
		String sign = reqHandler.createSign(packageParams);

		String xml = "<xml>" + "<appid>" + wxAppId + "</appid>" + "<mch_id>" + wxMchId + "</mch_id>" + "<nonce_str>"
				+ nonceStr + "</nonce_str>" + "<sign>" + sign + "</sign>" + "<out_trade_no>" + outTradeNo
				+ "</out_trade_no>" + "<out_refund_no>" + outRefundNo + "</out_refund_no>" + "<total_fee>" + totalFee
				+ "</total_fee>" + "<refund_fee>" + refundFee + "</refund_fee>" + "<refund_desc>" + refundDesc
				+ "</refund_desc>" + "<spbill_create_ip>" + spbillCreateIp + "</spbill_create_ip>" + "<notify_url>"
				+ wxCallBackUrl + "</notify_url>" + "</xml>";

		CloseableHttpClient httpClient = null;
		String result = null;
		HttpPost httpPost = null;
		BasicHttpClientConnectionManager connManager = null;
		try {
			// 证书, 写绝对路径，证书放在服务器上
			char[] password = wxMchId.toCharArray();
			File file = new File(WxNativePayConfig.certLocalPath);
			InputStream certStream = Files.newInputStream(file.toPath());
			KeyStore ks = KeyStore.getInstance("PKCS12");
			ks.load(certStream, password);
			// 实例化密钥库 & 初始化密钥工厂
			KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
			kmf.init(ks, password);

			// 创建 SSLContext
			SSLContext sslContext = SSLContext.getInstance("TLS");
			sslContext.init(kmf.getKeyManagers(), null, new SecureRandom());
			SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(sslContext,
					new String[] { "TLSv1" }, null, new DefaultHostnameVerifier());

			connManager = new BasicHttpClientConnectionManager(RegistryBuilder.<ConnectionSocketFactory>create()
					.register("http", PlainConnectionSocketFactory.getSocketFactory())
					.register("https", sslConnectionSocketFactory).build(), null, null, null);

			httpClient = HttpClientBuilder.create().setConnectionManager(connManager).build();

			httpPost = new HttpPost(refund_url);

			RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(WxPayConfig.getHttpReadTimeoutMs())
					.setConnectTimeout(WxPayConfig.getHttpConnectTimeoutMs()).build();
			httpPost.setConfig(requestConfig);

			StringEntity postEntity = new StringEntity(xml, "UTF-8");
			httpPost.addHeader("Content-Type", "text/xml");
			httpPost.addHeader("User-Agent", USER_AGENT + " " + wxMchId);
			httpPost.setEntity(postEntity);

			HttpResponse httpResponse = httpClient.execute(httpPost);
			HttpEntity httpEntity = httpResponse.getEntity();
			result = EntityUtils.toString(httpEntity, "UTF-8");

			Map<String, Object> map = XmlUtil.xmlToMap(result);
			if (map.get("return_code").equals("FAIL")) {
				String errorCode = map.get("return_code").toString();
				String errorMsg = map.get("return_msg").toString();
				throw new ServiceException(errorCode + ":" + errorMsg);
			}
			if (map.get("result_code").equals("FAIL")) {
				String errorCode = map.get("err_code").toString();
				String errorMsg = map.get("err_code_des").toString();
				throw new ServiceException(errorCode + ":" + errorMsg);
			}

		} catch (Exception e) {
			System.out.println("调用退款接口出现错误：" + e.getMessage());
			throw new ServiceException("调用退款接口出现错误：" + e.getMessage());
		} finally {
			if (httpPost != null) {
				httpPost.abort();
			}
		}
		return R.ok(Result.success().getMessage());
	}

}
