package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.Certificate;

/**
 * 证书信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface ICertificateService 
{
    /**
     * 查询证书信息
     * 
     * @param id 证书信息主键
     * @return 证书信息
     */
    public Certificate selectCertificateById(Long id);

    /**
     * 查询证书信息列表
     * 
     * @param certificate 证书信息
     * @return 证书信息集合
     */
    public List<Certificate> selectCertificateList(Certificate certificate);

    /**
     * 新增证书信息
     * 
     * @param certificate 证书信息
     * @return 结果
     */
    public int insertCertificate(Certificate certificate);

    /**
     * 修改证书信息
     * 
     * @param certificate 证书信息
     * @return 结果
     */
    public int updateCertificate(Certificate certificate);

    /**
     * 批量删除证书信息
     * 
     * @param ids 需要删除的证书信息主键集合
     * @return 结果
     */
    public int deleteCertificateByIds(Long[] ids);

    /**
     * 删除证书信息信息
     * 
     * @param id 证书信息主键
     * @return 结果
     */
    public int deleteCertificateById(Long id);
}
