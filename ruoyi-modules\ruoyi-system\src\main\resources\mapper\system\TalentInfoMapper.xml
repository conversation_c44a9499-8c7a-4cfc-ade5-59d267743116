<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TalentInfoMapper">

    <resultMap type="com.ruoyi.system.domain.TalentInfo" id="TalentInfoResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="birthDate"    column="birth_date"    />
        <result property="graduateSchool"    column="graduate_school"    />
        <result property="currentCompany"    column="current_company"    />
        <result property="position"    column="position"    />
        <result property="location"    column="location"    />
        <result property="settledStatus"    column="settled_status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="education"    column="education"    />
        <result property="positionType"    column="position_type"    />
        <result property="jobTitle"    column="job_title"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="workStatus"    column="work_status"    />
        <result property="skills"    column="skills"    />
        <result property="workExperience"    column="work_experience"    />
        <result property="photo"    column="photo"    />
        <result property="resumeFile"    column="resume_file"    />
    </resultMap>

    <resultMap type="com.ruoyi.system.api.domain.TalentInfoApi" id="TalentInfoApiResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="birthDate"    column="birth_date"    />
        <result property="graduateSchool"    column="graduate_school"    />
        <result property="currentCompany"    column="current_company"    />
        <result property="position"    column="position"    />
        <result property="location"    column="location"    />
        <result property="settledStatus"    column="settled_status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="education"    column="education"    />
        <result property="positionType"    column="position_type"    />

    </resultMap>

    <sql id="selectTalentInfoVo">
        select id, user_id, name, birth_date, graduate_school, current_company, position, location, settled_status, create_time, update_time, education, position_type, job_title, contact_phone, work_status, skills, work_experience, photo, resume_file
        from talent_info
    </sql>

    <select id="selectTalentInfoList" parameterType="com.ruoyi.system.domain.TalentInfo" resultMap="TalentInfoResult">
        <include refid="selectTalentInfoVo"/>
        <where>
            <if test="userId != null"> and user_id = #{userId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="birthDate != null  and birthDate != ''"> and birth_date = #{birthDate}</if>
            <if test="graduateSchool != null  and graduateSchool != ''"> and graduate_school = #{graduateSchool}</if>
            <if test="currentCompany != null  and currentCompany != ''"> and current_company = #{currentCompany}</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="location != null  and location != ''"> and location = #{location}</if>
            <if test="settledStatus != null  and settledStatus != ''"> and settled_status = #{settledStatus}</if>
            <if test="education != null  and education != ''"> and education = #{education}</if>
            <if test="positionType != null  and positionType != ''"> and position_type = #{positionType}</if>
            <if test="jobTitle != null  and jobTitle != ''"> and job_title = #{jobTitle}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="workStatus != null  and workStatus != ''"> and work_status = #{workStatus}</if>
            <if test="skills != null  and skills != ''"> and skills = #{skills}</if>
            <if test="workExperience != null  and workExperience != ''"> and work_experience = #{workExperience}</if>
            <if test="photo != null  and photo != ''"> and photo = #{photo}</if>
            <if test="resumeFile != null  and resumeFile != ''"> and resume_file = #{resumeFile}</if>
        </where>
    </select>

    <select id="selectTalentInfoById" parameterType="Long" resultMap="TalentInfoResult">
        <include refid="selectTalentInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectTalentInfoByUserId" parameterType="Long" resultMap="TalentInfoApiResult">
        <include refid="selectTalentInfoVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertTalentInfo" parameterType="com.ruoyi.system.domain.TalentInfo" useGeneratedKeys="true" keyProperty="id">
        insert into talent_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="birthDate != null">birth_date,</if>
            <if test="graduateSchool != null">graduate_school,</if>
            <if test="currentCompany != null">current_company,</if>
            <if test="position != null">position,</if>
            <if test="location != null">location,</if>
            <if test="settledStatus != null">settled_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="education != null">education,</if>
            <if test="positionType != null">position_type,</if>
            <if test="jobTitle != null">job_title,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="workStatus != null">work_status,</if>
            <if test="skills != null">skills,</if>
            <if test="workExperience != null">work_experience,</if>
            <if test="photo != null">photo,</if>
            <if test="resumeFile != null">resume_file,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="birthDate != null">#{birthDate},</if>
            <if test="graduateSchool != null">#{graduateSchool},</if>
            <if test="currentCompany != null">#{currentCompany},</if>
            <if test="position != null">#{position},</if>
            <if test="location != null">#{location},</if>
            <if test="settledStatus != null">#{settledStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="education != null">#{education},</if>
            <if test="positionType != null">#{positionType},</if>
            <if test="jobTitle != null">#{jobTitle},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="workStatus != null">#{workStatus},</if>
            <if test="skills != null">#{skills},</if>
            <if test="workExperience != null">#{workExperience},</if>
            <if test="photo != null">#{photo},</if>
            <if test="resumeFile != null">#{resumeFile},</if>
         </trim>
    </insert>

    <update id="updateTalentInfo" parameterType="com.ruoyi.system.domain.TalentInfo">
        update talent_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="birthDate != null">birth_date = #{birthDate},</if>
            <if test="graduateSchool != null">graduate_school = #{graduateSchool},</if>
            <if test="currentCompany != null">current_company = #{currentCompany},</if>
            <if test="position != null">position = #{position},</if>
            <if test="location != null">location = #{location},</if>
            <if test="settledStatus != null">settled_status = #{settledStatus},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="education != null">education = #{education},</if>
            <if test="positionType != null">position_type = #{positionType},</if>
            <if test="jobTitle != null">job_title = #{jobTitle},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="workStatus != null">work_status = #{workStatus},</if>
            <if test="skills != null">skills = #{skills},</if>
            <if test="workExperience != null">work_experience = #{workExperience},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="resumeFile != null">resume_file = #{resumeFile},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTalentInfoById" parameterType="Long">
        delete from talent_info where id = #{id}
    </delete>

    <delete id="deleteTalentInfoByIds" parameterType="String">
        delete from talent_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
