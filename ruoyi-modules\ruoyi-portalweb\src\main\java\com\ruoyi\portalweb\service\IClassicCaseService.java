package com.ruoyi.portalweb.service;


import com.ruoyi.portalweb.api.domain.ClassicCase;
import com.ruoyi.portalweb.vo.ClassicCaseVO;

import java.util.List;

/**
 * 典型案例Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface IClassicCaseService 
{
    /**
     * 查询典型案例
     * 
     * @param classicCaseId 典型案例主键
     * @return 典型案例
     */
    public ClassicCaseVO selectClassicCaseByClassicCaseId(Long classicCaseId);

    /**
     * 查询典型案例列表
     * 
     * @param classicCase 典型案例
     * @return 典型案例集合
     */
    public List<ClassicCaseVO> selectClassicCaseList(ClassicCaseVO classicCase);

    /**
     * 新增典型案例
     * 
     * @param classicCase 典型案例
     * @return 结果
     */
    public int insertClassicCase(ClassicCase classicCase);

    /**
     * 修改典型案例
     * 
     * @param classicCase 典型案例
     * @return 结果
     */
    public int updateClassicCase(ClassicCase classicCase);

    /**
     * 批量删除典型案例
     * 
     * @param classicCaseIds 需要删除的典型案例主键集合
     * @return 结果
     */
    public int deleteClassicCaseByClassicCaseIds(Long[] classicCaseIds);

    /**
     * 删除典型案例信息
     * 
     * @param classicCaseId 典型案例主键
     * @return 结果
     */
    public int deleteClassicCaseByClassicCaseId(Long classicCaseId);
}
