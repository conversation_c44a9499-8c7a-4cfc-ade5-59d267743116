package com.ruoyi.portalconsole.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalconsole.domain.Supply;
import com.ruoyi.portalconsole.domain.vo.SupplyVO;
import com.ruoyi.portalconsole.service.ISupplyService;
import com.ruoyi.system.api.RemoteUserService;

/**
 * 服务供给Controller
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/supply")
public class SupplyController extends BaseController
{
    @Autowired
    private ISupplyService supplyService;

    /**
     * 查询服务供给列表
     */
    @RequiresPermissions("portalconsole:supply:list")
    @GetMapping("/list")
    public TableDataInfo list(Supply supply)
    {
        startPage();
        PageUtils.setOrderBy("create_time DESC");
        List<SupplyVO> list = supplyService.selectSupplyList(supply);
        return getDataTable(list);
    }

    /**
     * 导出服务供给列表
     */
    @RequiresPermissions("portalconsole:supply:export")
    @Log(title = "服务供给", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Supply supply)
    {
        List<SupplyVO> list = supplyService.selectSupplyList(supply);
        ExcelUtil<SupplyVO> util = new ExcelUtil<SupplyVO>(SupplyVO.class);
        util.exportExcel(response, list, "服务供给数据");
    }

    /**
     * 获取服务供给详细信息
     */
    @RequiresPermissions("portalconsole:supply:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(supplyService.selectSupplyById(id));
    }

    /**
     * 新增服务供给
     */
    @RequiresPermissions("portalconsole:supply:add")
    @Log(title = "服务供给", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Supply supply)
    {
        return toAjax(supplyService.insertSupply(supply));
    }

    /**
     * 新增服务供给
     */
    @Log(title = "服务供给", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    public AjaxResult addByImport(@RequestBody Supply supply)
    {
        return toAjax(supplyService.insertSupply(supply));
    }

    /**
     * 修改服务供给
     */
    @RequiresPermissions("portalconsole:supply:edit")
    @Log(title = "服务供给", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Supply supply)
    {
        return toAjax(supplyService.updateSupply(supply));
    }

    /**
     * 审核服务供给
     */
    @RequiresPermissions("portalconsole:supply:audit")
    @Log(title = "服务供给", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody Supply supply)
    {
        return toAjax(supplyService.auditSupply(supply));
    }

    /**
     * 删除服务供给
     */
    @RequiresPermissions("portalconsole:supply:remove")
    @Log(title = "服务供给", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(supplyService.deleteSupplyByIds(ids));
    }
}
