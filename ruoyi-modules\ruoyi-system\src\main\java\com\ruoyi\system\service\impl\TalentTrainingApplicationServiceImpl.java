package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.TalentTrainingApplicationMapper;
import com.ruoyi.system.domain.TalentTrainingApplication;
import com.ruoyi.system.service.ITalentTrainingApplicationService;

/**
 * 人才培训基地申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
@Service
public class TalentTrainingApplicationServiceImpl implements ITalentTrainingApplicationService 
{
    @Autowired
    private TalentTrainingApplicationMapper talentTrainingApplicationMapper;

    /**
     * 查询人才培训基地申请
     * 
     * @param id 人才培训基地申请主键
     * @return 人才培训基地申请
     */
    @Override
    public TalentTrainingApplication selectTalentTrainingApplicationById(Long id)
    {
        return talentTrainingApplicationMapper.selectTalentTrainingApplicationById(id);
    }

    /**
     * 查询人才培训基地申请列表
     * 
     * @param talentTrainingApplication 人才培训基地申请
     * @return 人才培训基地申请
     */
    @Override
    public List<TalentTrainingApplication> selectTalentTrainingApplicationList(TalentTrainingApplication talentTrainingApplication)
    {
        return talentTrainingApplicationMapper.selectTalentTrainingApplicationList(talentTrainingApplication);
    }

    /**
     * 新增人才培训基地申请
     * 
     * @param talentTrainingApplication 人才培训基地申请
     * @return 结果
     */
    @Override
    public int insertTalentTrainingApplication(TalentTrainingApplication talentTrainingApplication)
    {
        talentTrainingApplication.setCreateTime(DateUtils.getNowDate());
        return talentTrainingApplicationMapper.insertTalentTrainingApplication(talentTrainingApplication);
    }

    /**
     * 修改人才培训基地申请
     * 
     * @param talentTrainingApplication 人才培训基地申请
     * @return 结果
     */
    @Override
    public int updateTalentTrainingApplication(TalentTrainingApplication talentTrainingApplication)
    {
        talentTrainingApplication.setUpdateTime(DateUtils.getNowDate());
        return talentTrainingApplicationMapper.updateTalentTrainingApplication(talentTrainingApplication);
    }

    /**
     * 批量删除人才培训基地申请
     * 
     * @param ids 需要删除的人才培训基地申请主键
     * @return 结果
     */
    @Override
    public int deleteTalentTrainingApplicationByIds(Long[] ids)
    {
        return talentTrainingApplicationMapper.deleteTalentTrainingApplicationByIds(ids);
    }

    /**
     * 删除人才培训基地申请信息
     * 
     * @param id 人才培训基地申请主键
     * @return 结果
     */
    @Override
    public int deleteTalentTrainingApplicationById(Long id)
    {
        return talentTrainingApplicationMapper.deleteTalentTrainingApplicationById(id);
    }
}
