package com.ruoyi.portalweb.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.portalweb.vo.CompanyRelatedVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalweb.api.domain.CompanyRelated;
import com.ruoyi.portalweb.service.ICompanyRelatedService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 关联企业信息Controller
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
@RestController
@RequestMapping("/companyRelated")
public class CompanyRelatedController extends BaseController
{
    @Autowired
    private ICompanyRelatedService companyRelatedService;

    /**
     * 查询关联企业信息列表
     */
    @RequiresPermissions("portalweb:portalweb:list")
    @GetMapping("/list")
    public TableDataInfo list(CompanyRelatedVO companyRelated)
    {
        startPage();
        List<CompanyRelated> list = companyRelatedService.selectCompanyRelatedList(companyRelated);
        return getDataTable(list);
    }

    /**
     * 查询关联企业信息列表
     */
    @GetMapping("/listDesk")
    public TableDataInfo listDesk(CompanyRelatedVO companyRelated)
    {
        startPage();
        PageUtils.setOrderBy("create_time DESC");
        List<CompanyRelated> list = companyRelatedService.selectCompanyRelatedList(companyRelated);
        return getDataTable(list);
    }


    /**
     * 导出关联企业信息列表
     */
    @RequiresPermissions("portalweb:portalweb:export")
    @Log(title = "关联企业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CompanyRelatedVO companyRelated)
    {
        List<CompanyRelated> list = companyRelatedService.selectCompanyRelatedList(companyRelated);
        ExcelUtil<CompanyRelated> util = new ExcelUtil<CompanyRelated>(CompanyRelated.class);
        util.exportExcel(response, list, "关联企业信息数据");
    }

    /**
     * 获取关联企业信息详细信息
     */
    @RequiresPermissions("portalweb:portalweb:query")
    @GetMapping(value = "/{companyRelatedId}")
    public AjaxResult getInfo(@PathVariable("companyRelatedId") Long companyRelatedId)
    {
        return success(companyRelatedService.selectCompanyRelatedByCompanyRelatedId(companyRelatedId));
    }

    /**
     * 获取关联企业信息详细信息
     */
    @GetMapping(value = "/detailDesk")
    public AjaxResult getInfoDetail(CompanyRelated companyRelated)
    {
        return success(companyRelatedService.getInfoDetail(companyRelated.getCompanyRelatedId()));
    }

    /**
     * 新增关联企业信息
     */
    @RequiresPermissions("portalweb:portalweb:add")
    @Log(title = "关联企业信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CompanyRelated companyRelated)
    {
        return toAjax(companyRelatedService.insertCompanyRelated(companyRelated));
    }

    /**
     * 修改关联企业信息
     */
    @RequiresPermissions("portalweb:portalweb:edit")
    @Log(title = "关联企业信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CompanyRelated companyRelated)
    {
        return toAjax(companyRelatedService.updateCompanyRelated(companyRelated));
    }

    /**
     * 删除关联企业信息
     */
    @RequiresPermissions("portalweb:portalweb:remove")
    @Log(title = "关联企业信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{companyRelatedIds}")
    public AjaxResult remove(@PathVariable Long[] companyRelatedIds)
    {
        return toAjax(companyRelatedService.deleteCompanyRelatedByCompanyRelatedIds(companyRelatedIds));
    }
}
