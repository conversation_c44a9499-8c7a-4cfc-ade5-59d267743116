package com.ruoyi.portalweb.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalweb.mapper.CompositeMaterialMapper;
import com.ruoyi.portalweb.domain.CompositeMaterial;
import com.ruoyi.portalweb.service.ICompositeMaterialService;

/**
 * 复材展厅Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class CompositeMaterialServiceImpl implements ICompositeMaterialService {
    @Autowired
    private CompositeMaterialMapper compositeMaterialMapper;

    /**
     * 查询复材展厅列表
     * 
     * @param compositeMaterial 复材展厅信息
     * @return 复材展厅
     */
    @Override
    public List<CompositeMaterial> selectCompositeMaterialList(CompositeMaterial compositeMaterial) {
        return compositeMaterialMapper.selectCompositeMaterialList(compositeMaterial);
    }

    /**
     * 查询复材展厅详细信息
     * 
     * @param id 复材展厅主键
     * @return 复材展厅
     */
    @Override
    public CompositeMaterial selectCompositeMaterialById(Long id) {
        return compositeMaterialMapper.selectCompositeMaterialById(id);
    }

    /**
     * 新增复材展厅
     * 
     * @param compositeMaterial 复材展厅信息
     * @return 结果
     */
    @Override
    public int insertCompositeMaterial(CompositeMaterial compositeMaterial) {
        return compositeMaterialMapper.insertCompositeMaterial(compositeMaterial);
    }

    /**
     * 修改复材展厅
     * 
     * @param compositeMaterial 复材展厅信息
     * @return 结果
     */
    @Override
    public int updateCompositeMaterial(CompositeMaterial compositeMaterial) {
        return compositeMaterialMapper.updateCompositeMaterial(compositeMaterial);
    }

    /**
     * 批量删除复材展厅
     * 
     * @param ids 需要删除的复材展厅主键
     * @return 结果
     */
    @Override
    public int deleteCompositeMaterialByIds(Long[] ids) {
        return compositeMaterialMapper.deleteCompositeMaterialByIds(ids);
    }

    /**
     * 删除复材展厅信息
     * 
     * @param id 复材展厅主键
     * @return 结果
     */
    @Override
    public int deleteCompositeMaterialById(Long id) {
        return compositeMaterialMapper.deleteCompositeMaterialById(id);
    }
}
