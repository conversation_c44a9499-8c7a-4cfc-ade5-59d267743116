package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 检测需求对象 testing_requirement
 * 
 * <AUTHOR>
 * @date 2025-03-08
 */
public class TestingRequirement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 需求ID */
    private Long id;

    /** 检测内容 */
    @Excel(name = "检测内容")
    private String testingContent;

    /** 检测要求 */
    @Excel(name = "检测要求")
    private String testingRequirements;

    /** 基础要求 */
    @Excel(name = "基础要求")
    private String basicRequirements;

    /** 场景图片URL */
    @Excel(name = "场景图片URL")
    private String imageUrl;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTestingContent(String testingContent) 
    {
        this.testingContent = testingContent;
    }

    public String getTestingContent() 
    {
        return testingContent;
    }
    public void setTestingRequirements(String testingRequirements) 
    {
        this.testingRequirements = testingRequirements;
    }

    public String getTestingRequirements() 
    {
        return testingRequirements;
    }
    public void setBasicRequirements(String basicRequirements) 
    {
        this.basicRequirements = basicRequirements;
    }

    public String getBasicRequirements() 
    {
        return basicRequirements;
    }
    public void setImageUrl(String imageUrl) 
    {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl() 
    {
        return imageUrl;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("testingContent", getTestingContent())
            .append("testingRequirements", getTestingRequirements())
            .append("basicRequirements", getBasicRequirements())
            .append("imageUrl", getImageUrl())
            .append("companyName", getCompanyName())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
