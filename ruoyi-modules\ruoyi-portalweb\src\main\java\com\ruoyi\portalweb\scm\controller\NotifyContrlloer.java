package com.ruoyi.portalweb.scm.controller;

import com.google.gson.Gson;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.portalweb.api.domain.AppStoreOrder;
import com.ruoyi.portalweb.api.domain.BuMemberOnlineRefund;
import com.ruoyi.portalweb.api.enums.AppStoreOrderStatus;
import com.ruoyi.portalweb.scm.config.WxPayConfigV3;
import com.ruoyi.portalweb.service.IAppStoreOrderService;
import com.ruoyi.portalweb.scm.utils.PayUtils;
import com.ruoyi.portalweb.scm.utils.WechatPay2ValidatorForRequest;
import com.ruoyi.portalweb.service.IBuMemberOnlineRefundService;
import com.wechat.pay.contrib.apache.httpclient.auth.Verifier;
import com.wechat.pay.contrib.apache.httpclient.util.AesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信回调
 */
@RestController
@RequestMapping("/notify")
@Slf4j
public class NotifyContrlloer {

    @Autowired
    private IAppStoreOrderService orderService;
    @Autowired
    private IBuMemberOnlineRefundService buMemberOnlineRefundService;


    @Resource
    private Verifier verifier;

    @Resource
    private WxPayConfigV3 wxPayConfigV3;


    @RequestMapping(value = "weixin")
    public AjaxResult wxNotify(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("===================进入回调==========================");
        Gson gson = new Gson();
        Map<String, String> map = new HashMap<>();//应答对象
        try {
            //处理通知参数
            String body = PayUtils.reciverWx(request);
            Map<String, Object> bodyMap = gson.fromJson(body, HashMap.class);
            String requestId = (String) bodyMap.get("id");
            log.info("支付通知的id ===> {}", requestId);
            //签名的验证
            WechatPay2ValidatorForRequest wechatPay2ValidatorForRequest
                    = new WechatPay2ValidatorForRequest(verifier, requestId, body);
            if (!wechatPay2ValidatorForRequest.validate(request)) {
                log.error("通知验签失败");
                //失败应答
                response.setStatus(500);
                map.put("code", "ERROR");
                map.put("message", "通知验签失败");

            }
            //解密报文
            String plainText = decryptFromResource(bodyMap);
            HashMap plainTextMap = gson.fromJson(plainText, HashMap.class);
            String orderNo = (String)plainTextMap.get("out_trade_no");
            BuMemberOnlineRefund buMemberOnlineRefund1 = buMemberOnlineRefundService.selectBuMemberOnlineRefundByRefundOrderNo(orderNo);
            if (buMemberOnlineRefund1 == null) {
                map.put("code", "ERROR");
                map.put("message", "invalid out_trade_no");
                return AjaxResult.error(map.get("message"));
            }


            AppStoreOrder order = new AppStoreOrder();
            order.setAppStoreOrderNo(buMemberOnlineRefund1.getAppStoreOrderNo());
            order.setOrderStatus(AppStoreOrderStatus.REFUNDED.getCode());
            order.setPayTime(DateUtils.getNowDate());
            orderService.updateAppStoreOrderByOrderNo(order);


            BuMemberOnlineRefund buMemberOnlineRefund = new BuMemberOnlineRefund();
            buMemberOnlineRefund.setRefundOrderNo(orderNo);
            buMemberOnlineRefund.setRefundStatus("2");// TODO
            buMemberOnlineRefundService.updateBuMemberOnlineRefundByRefundOrderNo(buMemberOnlineRefund);
            log.info("通知验签成功");
            return AjaxResult.success("回调成功");
        } catch (Exception e) {
            e.printStackTrace();
            //失败应答
            response.setStatus(500);

            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 对称解密
     * @param bodyMap
     * @return
     */
    private String decryptFromResource(Map<String, Object> bodyMap) throws GeneralSecurityException {

        log.info("密文解密");

        //通知数据
        Map<String, String> resourceMap = (Map) bodyMap.get("resource");
        //数据密文
        String ciphertext = resourceMap.get("ciphertext");
        //随机串
        String nonce = resourceMap.get("nonce");
        //附加数据
        String associatedData = resourceMap.get("associated_data");


        AesUtil aesUtil = new AesUtil(wxPayConfigV3.getApiV3Key().getBytes(StandardCharsets.UTF_8));
        String plainText = aesUtil.decryptToString(associatedData.getBytes(StandardCharsets.UTF_8),
                nonce.getBytes(StandardCharsets.UTF_8),
                ciphertext);

        return plainText;
    }


}
