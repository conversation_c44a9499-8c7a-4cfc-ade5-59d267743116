package com.ruoyi.portalweb.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalweb.domain.CompositeMaterial;
import com.ruoyi.portalweb.service.ICompositeMaterialService;

/**
 * 复材展厅Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/composite/material")
public class CompositeMaterialController extends BaseController {
    @Autowired
    private ICompositeMaterialService compositeMaterialService;

    /**
     * 查询复材展厅列表
     */
    @RequiresPermissions("portalweb:material:list")
    @GetMapping("/list")
    public TableDataInfo list(CompositeMaterial compositeMaterial) {
        startPage();
        List<CompositeMaterial> list = compositeMaterialService.selectCompositeMaterialList(compositeMaterial);
        return getDataTable(list);
    }

    /**
     * 导出复材展厅列表
     */
    @RequiresPermissions("portalweb:material:export")
    @Log(title = "复材展厅", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CompositeMaterial compositeMaterial) {
        List<CompositeMaterial> list = compositeMaterialService.selectCompositeMaterialList(compositeMaterial);
        ExcelUtil<CompositeMaterial> util = new ExcelUtil<CompositeMaterial>(CompositeMaterial.class);
        util.exportExcel(response, list, "复材展厅数据");
    }

    /**
     * 获取复材展厅详细信息
     */
    @RequiresPermissions("portalweb:material:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(compositeMaterialService.selectCompositeMaterialById(id));
    }

    /**
     * 新增复材展厅
     */
    @RequiresPermissions("portalweb:material:add")
    @Log(title = "复材展厅", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CompositeMaterial compositeMaterial) {
        return toAjax(compositeMaterialService.insertCompositeMaterial(compositeMaterial));
    }

    /**
     * 修改复材展厅
     */
    @RequiresPermissions("portalweb:material:edit")
    @Log(title = "复材展厅", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CompositeMaterial compositeMaterial) {
        return toAjax(compositeMaterialService.updateCompositeMaterial(compositeMaterial));
    }

    /**
     * 删除复材展厅
     */
    @RequiresPermissions("portalweb:material:remove")
    @Log(title = "复材展厅", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(compositeMaterialService.deleteCompositeMaterialByIds(ids));
    }
    
    /**
     * 获取前台展示列表
     */
    @GetMapping("/portal/list")
    public AjaxResult portalList() {
        CompositeMaterial query = new CompositeMaterial();
        query.setStatus("0"); // 只查询正常状态的
        List<CompositeMaterial> list = compositeMaterialService.selectCompositeMaterialList(query);
        return success(list);
    }
}
