package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.WorkshopInfoMapper;
import com.ruoyi.system.domain.WorkshopInfo;
import com.ruoyi.system.service.IWorkshopInfoService;

/**
 * 车间信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class WorkshopInfoServiceImpl implements IWorkshopInfoService 
{
    @Autowired
    private WorkshopInfoMapper workshopInfoMapper;

    /**
     * 查询车间信息
     * 
     * @param id 车间信息主键
     * @return 车间信息
     */
    @Override
    public WorkshopInfo selectWorkshopInfoById(Long id)
    {
        return workshopInfoMapper.selectWorkshopInfoById(id);
    }

    /**
     * 查询车间信息列表
     * 
     * @param workshopInfo 车间信息
     * @return 车间信息
     */
    @Override
    public List<WorkshopInfo> selectWorkshopInfoList(WorkshopInfo workshopInfo)
    {
        return workshopInfoMapper.selectWorkshopInfoList(workshopInfo);
    }

    /**
     * 新增车间信息
     * 
     * @param workshopInfo 车间信息
     * @return 结果
     */
    @Override
    public int insertWorkshopInfo(WorkshopInfo workshopInfo)
    {
        workshopInfo.setCreateTime(DateUtils.getNowDate());
//        workshopInfo.setCreateBy(SecurityUtils.getUserId()+"");
        return workshopInfoMapper.insertWorkshopInfo(workshopInfo);
    }

    /**
     * 修改车间信息
     * 
     * @param workshopInfo 车间信息
     * @return 结果
     */
    @Override
    public int updateWorkshopInfo(WorkshopInfo workshopInfo)
    {
        workshopInfo.setUpdateTime(DateUtils.getNowDate());
        workshopInfo.setUpdateBy(SecurityUtils.getUserId()+"");
        return workshopInfoMapper.updateWorkshopInfo(workshopInfo);
    }

    /**
     * 批量删除车间信息
     * 
     * @param ids 需要删除的车间信息主键
     * @return 结果
     */
    @Override
    public int deleteWorkshopInfoByIds(Long[] ids)
    {
        return workshopInfoMapper.deleteWorkshopInfoByIds(ids);
    }

    /**
     * 删除车间信息信息
     * 
     * @param id 车间信息主键
     * @return 结果
     */
    @Override
    public int deleteWorkshopInfoById(Long id)
    {
        return workshopInfoMapper.deleteWorkshopInfoById(id);
    }
}
