package com.ruoyi.portalweb.service.impl;

import com.ruoyi.portalweb.api.domain.TreeNode;
import com.ruoyi.portalweb.mapper.PolicySubmitMapper;
import com.ruoyi.portalweb.mapper.RegionMapper;
import com.ruoyi.portalweb.service.IRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 行政区划表Service业务层处理
 */
@Service
public class RegionServiceImpl implements IRegionService
{
    @Autowired
    private RegionMapper regionMapper;

    @Override
	public List<TreeNode> tree() {
		// 获取列表(按照编码升序排列)
		List<TreeNode> alTreeNodes = regionMapper.tree();
		Map<String, TreeNode> map = new HashMap<String, TreeNode>();
		for (int i = 0; i < alTreeNodes.size(); i++) {
			TreeNode child = alTreeNodes.get(i);
			map.put(child.getId(), child);
			if (i == 0) {
				continue;
			}
			TreeNode parent = map.get(child.getParentId());
			if (parent != null) {
				parent.getChildren().add(child);
			}
		}
//		if(map.size()>0){
//			return map.get(0L).getChildren();
//		}else{
//			return alTreeNodes;
//		}
		return map.get("00").getChildren();
	}
}
