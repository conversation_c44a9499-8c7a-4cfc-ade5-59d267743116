package com.ruoyi.portalconsole.service;

import java.util.List;
import com.ruoyi.portalconsole.domain.PolicyLabel;

/**
 * 政策标签Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface IPolicyLabelService 
{
    /**
     * 查询政策标签
     * 
     * @param policyLabelId 政策标签主键
     * @return 政策标签
     */
    public PolicyLabel selectPolicyLabelByPolicyLabelId(Long policyLabelId);

    /**
     * 查询政策标签列表
     * 
     * @param policyLabel 政策标签
     * @return 政策标签集合
     */
    public List<PolicyLabel> selectPolicyLabelList(PolicyLabel policyLabel);

    /**
     * 新增政策标签
     * 
     * @param policyLabel 政策标签
     * @return 结果
     */
    public int insertPolicyLabel(PolicyLabel policyLabel);

    /**
     * 修改政策标签
     * 
     * @param policyLabel 政策标签
     * @return 结果
     */
    public int updatePolicyLabel(PolicyLabel policyLabel);

    /**
     * 批量删除政策标签
     * 
     * @param policyLabelIds 需要删除的政策标签主键集合
     * @return 结果
     */
    public int deletePolicyLabelByPolicyLabelIds(Long[] policyLabelIds);

    /**
     * 删除政策标签信息
     * 
     * @param policyLabelId 政策标签主键
     * @return 结果
     */
    public int deletePolicyLabelByPolicyLabelId(Long policyLabelId);
}
