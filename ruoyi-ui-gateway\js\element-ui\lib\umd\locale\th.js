(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('element/locale/th', ['module', 'exports'], factory);
  } else if (typeof exports !== "undefined") {
    factory(module, exports);
  } else {
    var mod = {
      exports: {}
    };
    factory(mod, mod.exports);
    global.ELEMENT.lang = global.ELEMENT.lang || {};
    global.ELEMENT.lang.th = mod.exports;
  }
})(this, function (module, exports) {
  'use strict';

  exports.__esModule = true;
  exports.default = {
    el: {
      colorpicker: {
        confirm: 'ตกลง',
        clear: 'ล้างข้อมูล'
      },
      datepicker: {
        now: 'ตอนนี้',
        today: 'วันนี้',
        cancel: 'ยกเลิก',
        clear: 'ล้างข้อมูล',
        confirm: 'ตกลง',
        selectDate: 'เลือกวันที่',
        selectTime: 'เลือกเวลา',
        startDate: 'วันที่เริ่มต้น',
        startTime: 'เวลาเริ่มต้น',
        endDate: 'วันที่สิ้นสุด',
        endTime: 'เวลาสิ้นสุด',
        prevYear: 'ปีก่อนหน้า',
        nextYear: 'ปีถัดไป',
        prevMonth: 'เดือนก่อนหน้า',
        nextMonth: 'เดือนถัดไป',
        year: 'ปี',
        month1: 'มกราคม',
        month2: 'กุมภาพันธ์',
        month3: 'มีนาคม',
        month4: 'เมษายน',
        month5: 'พฤษภาคม',
        month6: 'มิถุนายน',
        month7: 'กรกฎาคม',
        month8: 'สิงหาคม',
        month9: 'กันยายน',
        month10: 'ตุลาคม',
        month11: 'พฤศจิกายน',
        month12: 'ธันวาคม',
        // week: 'week',
        weeks: {
          sun: 'อา',
          mon: 'จ',
          tue: 'อ',
          wed: 'พ',
          thu: 'พฤ',
          fri: 'ศ',
          sat: 'ส'
        },
        months: {
          jan: 'มกรา',
          feb: 'กุมภา',
          mar: 'มีนา',
          apr: 'เมษา',
          may: 'พฤษภา',
          jun: 'มิถุนา',
          jul: 'กรกฎา',
          aug: 'สิงหา',
          sep: 'กันยา',
          oct: 'ตุลา',
          nov: 'พฤศจิกา',
          dec: 'ธันวา'
        }
      },
      select: {
        loading: 'กำลังโหลด',
        noMatch: 'ไม่พบข้อมูลที่ตรงกัน',
        noData: 'ไม่พบข้อมูล',
        placeholder: 'เลือก'
      },
      cascader: {
        noMatch: 'ไม่พบข้อมูลที่ตรงกัน',
        loading: 'กำลังโหลด',
        placeholder: 'เลือก',
        noData: 'ไม่พบข้อมูล'
      },
      pagination: {
        goto: 'ไปที่',
        pagesize: '/หน้า',
        total: 'ทั้งหมด {total}',
        pageClassifier: ''
      },
      messagebox: {
        title: 'ข้อความ',
        confirm: 'ตกลง',
        cancel: 'ยกเลิก',
        error: 'คุณป้อนข้อมูลไม่ถูกต้อง'
      },
      upload: {
        deleteTip: 'กดปุ่ม "ลบ" เพื่อลบออก',
        delete: 'ลบ',
        preview: 'ตัวอย่าง',
        continue: 'ทำต่อ'
      },
      table: {
        emptyText: 'ไม่พบข้อมูล',
        confirmFilter: 'ยืนยัน',
        resetFilter: 'รีเซ็ต',
        clearFilter: 'ทั้งหมด',
        sumText: 'รวม'
      },
      tree: {
        emptyText: 'ไม่พบข้อมูล'
      },
      transfer: {
        noMatch: 'ไม่พบข้อมูลที่ตรงกัน',
        noData: 'ไม่พบข้อมูล',
        titles: ['List 1', 'List 2'], // to be translated
        filterPlaceholder: 'กรอกคีย์เวิร์ด',
        noCheckedFormat: '{total} items', // to be translated
        hasCheckedFormat: '{checked}/{total} checked' // to be translated
      },
      image: {
        error: 'FAILED' // to be translated
      },
      pageHeader: {
        title: 'ย้อนกลับ'
      },
      popconfirm: {
        confirmButtonText: 'Yes', // to be translated
        cancelButtonText: 'No' // to be translated
      },
      empty: {
        description: 'ไม่พบข้อมูล'
      }
    }
  };
  module.exports = exports['default'];
});
