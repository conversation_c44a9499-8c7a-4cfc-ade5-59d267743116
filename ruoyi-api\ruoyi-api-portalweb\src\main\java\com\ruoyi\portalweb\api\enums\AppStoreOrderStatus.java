package com.ruoyi.portalweb.api.enums;

public enum AppStoreOrderStatus {
    CLOSED("0", "CLOSED"),
    UNPAID("1","UNPAID"),
    PAID("2","PAID"),
    REFUNDING("3","REFUNDING"),
    REFUNDED("4", "REFUNDED"),
    DELIVERED("5", "DELIVERED"),
    CONFIRMED("6", "CONFIRMED");

    private String code;
    private String label;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    AppStoreOrderStatus(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
