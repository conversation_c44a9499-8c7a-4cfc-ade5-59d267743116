package com.ruoyi.portalweb.utils;

import com.ruoyi.common.core.utils.DateUtils;

import java.util.Date;

/**
 * 编号生成工具类
 */
public class NumberGeneraterUtil {

	private static NumberGeneraterUtil numberGeneraterUtil = null;
	private static int inc = 0;

	private NumberGeneraterUtil() {

	}

	/**
	 * 取得PrimaryGenerater的单例实现
	 *
	 * @return
	 */
	public static NumberGeneraterUtil getInstance() {
		if (numberGeneraterUtil == null) {
			synchronized (NumberGeneraterUtil.class) {
				if (numberGeneraterUtil == null) {
					numberGeneraterUtil = new NumberGeneraterUtil();
				}
			}
		}
		return numberGeneraterUtil;
	}

	public String generateTimeStampCode(String typeName){
        String dateStr = DateUtils.dateTimeNow("yyyyMMddHHmmssSSS");
		dateStr = dateStr.substring(0, dateStr.length() - 2);
		if (dateStr.length() < 15) {
			dateStr = dateStr + "9";
		}
		return typeName + dateStr;
    }

	public static void main(String[] args) {
//		String dateStr = DateUtil.format(new Date(), "yyMMddHHmmss");
//		String str = "690".concat(String.valueOf(dateStr));
//		System.out.println(str);
	}
}
