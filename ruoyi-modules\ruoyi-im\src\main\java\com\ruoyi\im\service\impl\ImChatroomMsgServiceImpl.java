package com.ruoyi.im.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.im.api.domain.ImChatroomMsg;
import com.ruoyi.im.mapper.ImChatroomMsgMapper;
import com.ruoyi.im.service.ImChatroomMsgService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ImChatroomMsgServiceImpl extends ServiceImpl<ImChatroomMsgMapper, ImChatroomMsg> implements ImChatroomMsgService
{
    @Resource
    private ImChatroomMsgMapper imChatroomMsgMapper;

    @Override
    public List<ImChatroomMsg> findLatestMsg(List<String> ids) {
        return imChatroomMsgMapper.findLatestMsg(ids);
    }

    @Override
    public List<String> findSent(String telphone) {
        return imChatroomMsgMapper.findSent(telphone);
    }
}