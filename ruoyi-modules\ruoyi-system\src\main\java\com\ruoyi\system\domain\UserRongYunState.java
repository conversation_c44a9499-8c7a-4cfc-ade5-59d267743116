package com.ruoyi.system.domain;

import java.io.Serializable;
import java.util.Date;

/**
    * 用户融云在线状态
    */
public class UserRongYunState implements Serializable {
    private Long id;

    /**
    * 用户登录账号
    */
    private String userName;

    /**
     * 在线的客户端总数
     */
    private Integer onlineNum;

    /**
    * 最后状态更新时间
    */
    private Long timestamp;

    /**
    * 最后的ip地址及端口号
    */
    private String clientip;

    /**
    * 绑定的微信公众号id
    */
    private String openId;

    /**
    * 最新绑定时间
    */
    private Date wxUpdateTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }


    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getClientip() {
        return clientip;
    }

    public void setClientip(String clientip) {
        this.clientip = clientip;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Date getWxUpdateTime() {
        return wxUpdateTime;
    }

    public void setWxUpdateTime(Date wxUpdateTime) {
        this.wxUpdateTime = wxUpdateTime;
    }

    public Integer getOnlineNum() {
        return onlineNum;
    }

    public void setOnlineNum(Integer onlineNum) {
        this.onlineNum = onlineNum;
    }
}