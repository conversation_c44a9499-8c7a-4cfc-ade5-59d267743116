package com.ruoyi.portalweb.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.portalweb.api.domain.Supply;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 服务供给对象 supply
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public class SupplyVO extends Supply {
    private static final long serialVersionUID = 1L;

    @JsonIgnore
    private Date startTime;
    @JsonIgnore
    private Date endTime;
    @JsonIgnore
    private String keyword;

    private String imageUrlPath;

    public String getImageUrlPath() {
        return imageUrlPath;
    }

    public void setImageUrlPath(String imageUrlPath) {
        this.imageUrlPath = imageUrlPath;
    }

    @ApiModelProperty(value = "供给类型")
    private String typeName;

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    @ApiModelProperty(value = "查询方式,我的供给queryType='my'")
    private String queryType;

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    @ApiModelProperty(value = "应用领域名称")
    private String applicationAreaName;

    public String getApplicationAreaName() {
        return applicationAreaName;
    }

    public void setApplicationAreaName(String applicationAreaName) {
        this.applicationAreaName = applicationAreaName;
    }


    @ApiModelProperty(value = "技术类别")
    private String technologyCategoryName;
    @ApiModelProperty(value = "合作方式")
    private String supplyCooperationName;
    @ApiModelProperty(value = "产品阶段")
    private String supplyProcessName;
    @ApiModelProperty(value = "审核状态名称")
    private String auditStatusName;
    @ApiModelProperty(value = "产品类别名称")
    private String productTypeName;

    @ApiModelProperty(value = "传入附件")
    private List<FileDetailVO> alFileDetailVOs = new ArrayList<FileDetailVO>();

    @ApiModelProperty(value = "传入附件")
    private List<FileDetailVO> alFile0601 = new ArrayList<FileDetailVO>();

    @ApiModelProperty(value = "传入附件")
    private List<FileDetailVO> alFile0602 = new ArrayList<FileDetailVO>();

    public List<FileDetailVO> getAlFile0601() {
        return alFile0601;
    }

    public void setAlFile0601(List<FileDetailVO> alFile0601) {
        this.alFile0601 = alFile0601;
    }

    public List<FileDetailVO> getAlFile0602() {
        return alFile0602;
    }

    public void setAlFile0602(List<FileDetailVO> alFile0602) {
        this.alFile0602 = alFile0602;
    }

    public List<FileDetailVO> getAlFileDetailVOs() {
        return alFileDetailVOs;
    }

    public void setAlFileDetailVOs(List<FileDetailVO> alFileDetailVOs) {
        this.alFileDetailVOs = alFileDetailVOs;
    }


    public String getTechnologyCategoryName() {
        return technologyCategoryName;
    }

    public void setTechnologyCategoryName(String technologyCategoryName) {
        this.technologyCategoryName = technologyCategoryName;
    }

    public String getSupplyCooperationName() {
        return supplyCooperationName;
    }

    public void setSupplyCooperationName(String supplyCooperationName) {
        this.supplyCooperationName = supplyCooperationName;
    }

    public String getSupplyProcessName() {
        return supplyProcessName;
    }

    public void setSupplyProcessName(String supplyProcessName) {
        this.supplyProcessName = supplyProcessName;
    }

    public String getAuditStatusName() {
        return auditStatusName;
    }

    public void setAuditStatusName(String auditStatusName) {
        this.auditStatusName = auditStatusName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

	public String getProductTypeName() {
		return productTypeName;
	}

	public void setProductTypeName(String productTypeName) {
		this.productTypeName = productTypeName;
	}
}
