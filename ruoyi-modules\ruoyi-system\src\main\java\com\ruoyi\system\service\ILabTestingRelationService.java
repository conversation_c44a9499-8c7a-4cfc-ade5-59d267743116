package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.LabTestingRelation;

/**
 * 实验室检测项目关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface ILabTestingRelationService 
{
    /**
     * 查询实验室检测项目关联
     * 
     * @param id 实验室检测项目关联主键
     * @return 实验室检测项目关联
     */
    public LabTestingRelation selectLabTestingRelationById(Long id);

    /**
     * 查询实验室检测项目关联列表
     * 
     * @param labTestingRelation 实验室检测项目关联
     * @return 实验室检测项目关联集合
     */
    public List<LabTestingRelation> selectLabTestingRelationList(LabTestingRelation labTestingRelation);

    /**
     * 新增实验室检测项目关联
     * 
     * @param labTestingRelation 实验室检测项目关联
     * @return 结果
     */
    public int insertLabTestingRelation(LabTestingRelation labTestingRelation);

    /**
     * 修改实验室检测项目关联
     * 
     * @param labTestingRelation 实验室检测项目关联
     * @return 结果
     */
    public int updateLabTestingRelation(LabTestingRelation labTestingRelation);

    /**
     * 批量删除实验室检测项目关联
     * 
     * @param ids 需要删除的实验室检测项目关联主键集合
     * @return 结果
     */
    public int deleteLabTestingRelationByIds(Long[] ids);

    /**
     * 删除实验室检测项目关联信息
     * 
     * @param id 实验室检测项目关联主键
     * @return 结果
     */
    public int deleteLabTestingRelationById(Long id);
}
