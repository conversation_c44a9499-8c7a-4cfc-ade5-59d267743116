package com.ruoyi.portalconsole.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 解决方案对象 solution
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public class Solution extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 解决方案ID */
    private Long solutionId;

    /** 方案类型ID */
    @Excel(name = "方案类型ID")
    private Long solutionTypeId;

    /** 名称 */
    @Excel(name = "名称")
    private String solutionName;

    /** banner图片 */
    @Excel(name = "banner图片")
    private String solutionBanner;

    /** 方案简介 */
    @Excel(name = "方案简介")
    private String solutionIntroduction;

    /** 方案概述 */
    @Excel(name = "方案概述")
    private String solutionOverview;

    /** 方案概述 */
    @Excel(name = "方案概述")
    private String solutionImg;

    /** 状态 */
    @Excel(name = "状态")
    private String solutionStatus;

    /** 推荐状态 */
    @Excel(name = "推荐状态")
    private String recommend;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setSolutionId(Long solutionId) 
    {
        this.solutionId = solutionId;
    }

    public Long getSolutionId() 
    {
        return solutionId;
    }
    public void setSolutionTypeId(Long solutionTypeId)
    {
        this.solutionTypeId = solutionTypeId;
    }

    public Long getSolutionTypeId()
    {
        return solutionTypeId;
    }
    public void setSolutionName(String solutionName) 
    {
        this.solutionName = solutionName;
    }

    public String getSolutionName() 
    {
        return solutionName;
    }
    public void setSolutionBanner(String solutionBanner) 
    {
        this.solutionBanner = solutionBanner;
    }

    public String getSolutionBanner() 
    {
        return solutionBanner;
    }
    public void setSolutionIntroduction(String solutionIntroduction) 
    {
        this.solutionIntroduction = solutionIntroduction;
    }

    public String getSolutionIntroduction() 
    {
        return solutionIntroduction;
    }
    public void setSolutionOverview(String solutionOverview) 
    {
        this.solutionOverview = solutionOverview;
    }

    public String getSolutionOverview() 
    {
        return solutionOverview;
    }
    public void setSolutionImg(String solutionImg) 
    {
        this.solutionImg = solutionImg;
    }

    public String getSolutionImg() 
    {
        return solutionImg;
    }
    public void setSolutionStatus(String solutionStatus) 
    {
        this.solutionStatus = solutionStatus;
    }

    public String getSolutionStatus() 
    {
        return solutionStatus;
    }

    public String getRecommend() {
        return recommend;
    }

    public void setRecommend(String recommend) {
        this.recommend = recommend;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("solutionId", getSolutionId())
            .append("solutionTypeId", getSolutionTypeId())
            .append("solutionName", getSolutionName())
            .append("solutionBanner", getSolutionBanner())
            .append("solutionIntroduction", getSolutionIntroduction())
            .append("solutionOverview", getSolutionOverview())
            .append("solutionImg", getSolutionImg())
            .append("solutionStatus", getSolutionStatus())
            .append("recommend", getRecommend())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
