package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.portalweb.api.domain.IntentionApply;
import com.ruoyi.portalweb.service.IIntentionApplyService;
import com.ruoyi.portalweb.vo.IntentionApplyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 意向申请Controller
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
@RestController
@RequestMapping("/IntentionApply")
public class IntentionApplyController extends BaseController
{
    @Autowired
    private IIntentionApplyService intentionApplyService;

    /**
     * 查询意向申请列表
     */
    @GetMapping("/list")
    public TableDataInfo list(IntentionApplyVO intentionApply)
    {
        startPage();
        List<IntentionApplyVO> list = intentionApplyService.selectIntentionApplyList(intentionApply);
        return getDataTable(list);
    }

    /**
     * 导出意向申请列表
     */
    @Log(title = "意向申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IntentionApplyVO intentionApply)
    {
        List<IntentionApplyVO> list = intentionApplyService.selectIntentionApplyList(intentionApply);
        ExcelUtil<IntentionApplyVO> util = new ExcelUtil<>(IntentionApplyVO.class);
        util.exportExcel(response, list, "意向申请数据");
    }

    /**
     * 获取意向申请详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(intentionApplyService.selectIntentionApplyById(id));
    }

    /**
     * 新增意向申请
     */
    @Log(title = "意向申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IntentionApply intentionApply)
    {
        return toAjax(intentionApplyService.insertIntentionApply(intentionApply));
    }

    /**
     * 修改意向申请
     */
    @Log(title = "意向申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IntentionApply intentionApply)
    {
        return toAjax(intentionApplyService.updateIntentionApply(intentionApply));
    }

    /**
     * 删除意向申请
     */
    @Log(title = "意向申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(intentionApplyService.deleteIntentionApplyByIds(ids));
    }
}
