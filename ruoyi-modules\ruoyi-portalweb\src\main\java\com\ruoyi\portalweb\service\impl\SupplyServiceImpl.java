package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.FileDetail;
import com.ruoyi.portalweb.api.domain.Member;
import com.ruoyi.portalweb.api.domain.Supply;
import com.ruoyi.portalweb.api.enums.AuditStatus;
import com.ruoyi.portalweb.api.enums.MyFavoriteStatus;
import com.ruoyi.portalweb.api.enums.MyFavoriteType;
import com.ruoyi.portalweb.mapper.MyFavoriteMapper;
import com.ruoyi.portalweb.mapper.SupplyMapper;
import com.ruoyi.portalweb.service.IFileDetailService;
import com.ruoyi.portalweb.service.IMemberService;
import com.ruoyi.portalweb.service.ISupplyService;
import com.ruoyi.portalweb.vo.FileDetailVO;
import com.ruoyi.portalweb.vo.MemberVO;
import com.ruoyi.portalweb.vo.SupplyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.ruoyi.common.core.utils.PageUtils.startPage;

/**
 * 服务供给Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class SupplyServiceImpl implements ISupplyService {
    @Autowired
    private SupplyMapper supplyMapper;
    @Autowired
    private IFileDetailService fileDetailService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private MyFavoriteMapper myFavoriteMapper;

    public static final Long AUDIT_STATUS_PASS = 2L;
    public static final Long AUDIT_STATUS_TERMINAL = 4L;
    public static final Long ON_SHOW_Y = 0L;
    

    /**
     * 查询服务供给
     *
     * @param id 服务供给主键
     * @return 服务供给
     */
    @Override
    public SupplyVO selectSupplyById(Long id) {
        SupplyVO supplyVO = supplyMapper.selectSupplyById(id);
        if (supplyVO != null) {
            List<FileDetailVO> alPictureVOs = fileDetailService.selectPictureList(supplyVO.getId(),"supply", "");
            List<FileDetailVO> list0601 = new ArrayList<>();
            List<FileDetailVO> list0602 = new ArrayList<>();
            for (FileDetailVO item : alPictureVOs) {
                if ("0601".equals(item.getFileType())) {
                    list0601.add(item);
                }
                if ("0602".equals(item.getFileType())) {
                    list0602.add(item);
                }
            }
            supplyVO.setAlFile0601(list0601);
            supplyVO.setAlFile0602(list0602);
        }
        return supplyVO;
    }

    @Override
    public SupplyVO detailDesk(Long id) {
        supplyMapper.addSupplyViewCount(id);
        return this.selectSupplyById(id);
    }

    /**
     * 查询服务供给列表
     *
     * @param supply 服务供给
     * @return 服务供给
     */
    @Override
    public List<SupplyVO> selectSupplyList(SupplyVO supply) {
        if (StringUtils.isNotEmpty(supply.getQueryType()) && "my".equals(supply.getQueryType())) {
            supply.setMemberId(SecurityUtils.getUserId());
        }else {
            supply.setOnShow(ON_SHOW_Y);
            supply.setAuditStatus(AUDIT_STATUS_PASS);
        }
        List<SupplyVO> list = supplyMapper.selectSupplyList(supply);
        for (SupplyVO item : list) {
            List<FileDetailVO> alPictureVOs = fileDetailService.selectPictureList(item.getId(),"supply", "");
            item.setAlFileDetailVOs(alPictureVOs);
        }
        return list;
    }

    /**
     * 新增服务供给
     *
     * @param supply 服务供给
     * @return 结果
     */
    @Override
    public int insertSupply(SupplyVO supply) {
        supply.setMemberId(SecurityUtils.getUserId());
        MemberVO memberVO =memberService.selectMemberByMemberId(SecurityUtils.getUserId());
        supply.setOrganization(memberVO.getCompanyName());
        
        Integer res = supplyMapper.insertSupply(supply);
        if (res == 1) {
            // 删除原先附件,重保存
            fileDetailService.removeBybillId(supply.getId(),"supply", "");
            // 保存图片
            if (supply.getAlFileDetailVOs() != null && supply.getAlFileDetailVOs().size() > 0) {
                for (FileDetailVO tmpPic : supply.getAlFileDetailVOs()) {

                    FileDetail saveFile = tmpPic;
                    tmpPic.setId(null);
                    tmpPic.setParentType("supply");
                    tmpPic.setParentId(supply.getId());
                    fileDetailService.insertFileDetail(saveFile);
                }
            }
        }
        return res;
    }

    /**
     * 修改服务供给
     *
     * @param supply 服务供给
     * @return 结果
     */
    @Override
    public int updateSupply(Supply supply) {
        MemberVO memberVO = memberService.selectMemberByMemberId(SecurityUtils.getUserId());
        supply.setUpdateBy(memberVO.getMemberRealName());
        SupplyVO supplyVO = supplyMapper.selectSupplyById(supply.getId());
        if (!Objects.equals(supplyVO.getAuditStatus(), AUDIT_STATUS_TERMINAL) && !Objects.equals(supply.getAuditStatus(), AUDIT_STATUS_TERMINAL)) {
            supply.setAuditStatus(Long.parseLong(AuditStatus.PENDING.getValue()));
        }
        return supplyMapper.updateSupply(supply);
    }

    /**
     * 批量删除服务供给
     *
     * @param ids 需要删除的服务供给主键
     * @return 结果
     */
    @Override
    public int deleteSupplyByIds(Long[] ids) {
        int i = supplyMapper.deleteSupplyByIds(ids);
        for (Long id : ids) {
            try {
                myFavoriteMapper.updateMyFavoriteStatus(id, MyFavoriteType.DEMAND.getType(), MyFavoriteStatus.INVALID.getValue());
            }catch(Exception e){
                e.printStackTrace();
            }
        }
        return i;
    }

    /**
     * 删除服务供给信息
     *
     * @param id 服务供给主键
     * @return 结果
     */
    @Override
    public int deleteSupplyById(Long id) {
        int i = supplyMapper.deleteSupplyById(id);
        try {
            myFavoriteMapper.updateMyFavoriteStatus(id, MyFavoriteType.DEMAND.getType(), MyFavoriteStatus.INVALID.getValue());
        }catch(Exception e){
            e.printStackTrace();
        }
        return i;
    }

    @Override
    public List<SupplyVO> listDeskCompanyRelated(Long companyRelatedId) {
        Member m = new Member();
        m.setCompanyRelatedId(companyRelatedId);
        List<MemberVO> memberVOS = memberService.selectMemberList(m);
        List<Long> memberIds = new ArrayList<>();
        for (MemberVO memberVO : memberVOS) {
            memberIds.add(memberVO.getMemberId());
        }

        if(memberIds.isEmpty()){
            return new ArrayList<>();
        }
        startPage();
        PageUtils.setOrderBy("a.create_time DESC");
        List<SupplyVO> list = supplyMapper.selectDemandListByMemberIds(memberIds);
        if (list.isEmpty()){
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<Supply> selectSupplyListBySupplyIds(List<Long> supplyIds) {
        return supplyMapper.selectSupplyListBySupplyIds(supplyIds);
    }
}
