<template>
    <!-- 企业信息详情 -->
    <el-dialog :title="title" :visible.sync="open" width="70%">
        <el-form ref="form" :model="form" label-width="140px">
            <el-row>
                <h1 style="width: 100%;text-align: center;">组织基本信息</h1>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="公司名称:" prop="">
                        {{  form.companyName?form.companyName:''}}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="公司邮箱:" prop="">
                        {{ form.companyEmail }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="授权书:" prop="">

                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="审核状态:" prop="">
                        <div v-if="form.companyStatus==='3'">审核通过</div>
                        <div v-else-if="form.companyStatus==='2'">审核不通过</div>
                        <div v-else-if="form.companyStatus==='0'">待提交</div>
                        <div v-else-if="form.companyStatus==='1'">待审核</div>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="营业执照:">

                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="详细地址:">
                        {{ form.address?form.address:'' }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="服务行业:">
                        {{ form.serviceIndustry?form.serviceIndustry:'' }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="社会统一信用代码:">
                        {{ form.socialUnityCreditCode?form.socialUnityCreditCode:'' }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="提交时间:">
                        {{ form.submitTime?form.submitTime:'' }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="企业规模:">
                        {{ form.companySize?form.companySize:'' }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="联系电话:">
                        {{ form.phone?form.phone:'' }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="注册资金:">
                        {{ form.registeredCapital?form.registeredCapital:'' }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="企业简介:">
                        {{ form.intrduction?form.intrduction:'' }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="真实姓名:">
                        {{ form.companyRealName?form.companyRealName:'' }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row v-if="form.companyStatus!=='3'">
                <div class="btn">
                    <el-button type="success" plain @click="edit('3')">认证通过</el-button>
                    <el-button type="danger" plain @click="edit('2')">认证拒绝</el-button>
                </div>
            </el-row>
        </el-form>

    </el-dialog>
</template>
<script>
import axios from 'axios'
import {updateCompany} from "@/api/portalconsole/Company";
export default {
    name: "detailDialog",
    props: {

    },
    data() {
        return {
            title: '企业信息详情',
            open: false,
            id: '',
            form: {},
        };
    },
    created() {

    },
    methods: {
        /**
          * 显示弹框
          */
        async show(form) {
            this.form = form
            this.open = true; // 切换显示
        },

        //审批
        edit(status){
            let data={
                companyId:this.form.companyId,
                companyStatus:status
            }
            updateCompany(data).then(response => {
              this.$modal.msgSuccess("操作成功");
              this.open = false;
              this.$emit("submit", 'updata');
              if(status==='3'){
                //把信息传给园区
                let data={
                    creditNo:this.form.socialUnityCreditCode,
                    areaName:this.form.address
                }
                axios.post('https://cyjc.qdniiot.com/nmd_parkwatch/ent_filling/companyResigter',data).then(res=>{

                })

              }
            })
        }
    }
};
</script>
<style scoped>
h3 {
    color: black;
}
.btn{
    width: 30%;
    margin: 0 auto;
}
</style>
