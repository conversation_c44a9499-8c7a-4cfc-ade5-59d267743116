package com.ruoyi.cas.auth.model;

import java.io.Serializable;

import com.ruoyi.portalweb.api.model.LoginMember;


/**
 * 用户信息
 *
 * <AUTHOR>
 */
public class ApiMember implements Serializable
{
    private static final long serialVersionUID = 1L;


    /**
     * 用户名id
     */
    private Long memberid;

    /**
     * 用户名
     */
    private String memberPhone;

    /**
     * 真实姓名
     */
    private String memberRealName;


    /**
     * 微信号
     */
    private String memberWechat;
    
    /**
     * 职位
     */
    private String memberPost;


    /**
     * 认证公司名称
     */
    private String companyName;
    
    
    
    

	public ApiMember(LoginMember memberInfo) {
		this.memberid = memberInfo.getMemberid();
		this.memberPhone = memberInfo.getMemberphone();
		this.memberRealName = memberInfo.getMember().getMemberRealName();
		this.memberWechat = memberInfo.getMember().getMemberWechat();
		this.memberPost = memberInfo.getMember().getMemberPost();
		if(memberInfo.getCompany()!=null){
			this.companyName = memberInfo.getCompany().getCompanyName();
		}
		
	}
	
	

	public Long getMemberid() {
		return memberid;
	}

	public void setMemberid(Long memberid) {
		this.memberid = memberid;
	}


	public String getMemberPhone() {
		return memberPhone;
	}


	public void setMemberPhone(String memberPhone) {
		this.memberPhone = memberPhone;
	}



	public String getMemberRealName() {
		return memberRealName;
	}

	public void setMemberRealName(String memberRealName) {
		this.memberRealName = memberRealName;
	}

	public String getMemberWechat() {
		return memberWechat;
	}

	public void setMemberWechat(String memberWechat) {
		this.memberWechat = memberWechat;
	}

	public String getMemberPost() {
		return memberPost;
	}

	public void setMemberPost(String memberPost) {
		this.memberPost = memberPost;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}


    
    
    
}
