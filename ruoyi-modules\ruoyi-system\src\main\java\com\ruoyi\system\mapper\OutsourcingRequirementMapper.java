package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.OutsourcingRequirement;

/**
 * 工序外协需求Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-08
 */
public interface OutsourcingRequirementMapper 
{
    /**
     * 查询工序外协需求
     * 
     * @param id 工序外协需求主键
     * @return 工序外协需求
     */
    public OutsourcingRequirement selectOutsourcingRequirementById(Long id);

    /**
     * 查询工序外协需求列表
     * 
     * @param outsourcingRequirement 工序外协需求
     * @return 工序外协需求集合
     */
    public List<OutsourcingRequirement> selectOutsourcingRequirementList(OutsourcingRequirement outsourcingRequirement);

    /**
     * 新增工序外协需求
     * 
     * @param outsourcingRequirement 工序外协需求
     * @return 结果
     */
    public int insertOutsourcingRequirement(OutsourcingRequirement outsourcingRequirement);

    /**
     * 修改工序外协需求
     * 
     * @param outsourcingRequirement 工序外协需求
     * @return 结果
     */
    public int updateOutsourcingRequirement(OutsourcingRequirement outsourcingRequirement);

    /**
     * 删除工序外协需求
     * 
     * @param id 工序外协需求主键
     * @return 结果
     */
    public int deleteOutsourcingRequirementById(Long id);

    /**
     * 批量删除工序外协需求
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOutsourcingRequirementByIds(Long[] ids);
}
