package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.LaboratoryInfo;
import com.ruoyi.system.domain.dto.LaboratoryWithTestingItemsDTO;
import com.ruoyi.system.domain.dto.LabTypeTestingItemsDTO;

/**
 * 实验室信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface ILaboratoryInfoService 
{
    /**
     * 查询实验室信息
     * 
     * @param id 实验室信息主键
     * @return 实验室信息
     */
    public LaboratoryInfo selectLaboratoryInfoById(Long id);

    /**
     * 查询实验室信息列表
     * 
     * @param laboratoryInfo 实验室信息
     * @return 实验室信息集合
     */
    public List<LaboratoryInfo> selectLaboratoryInfoList(LaboratoryInfo laboratoryInfo);

    /**
     * 新增实验室信息
     * 
     * @param laboratoryInfo 实验室信息
     * @return 结果
     */
    public int insertLaboratoryInfo(LaboratoryInfo laboratoryInfo);

    /**
     * 修改实验室信息
     * 
     * @param laboratoryInfo 实验室信息
     * @return 结果
     */
    public int updateLaboratoryInfo(LaboratoryInfo laboratoryInfo);

    /**
     * 批量删除实验室信息
     * 
     * @param ids 需要删除的实验室信息主键集合
     * @return 结果
     */
    public int deleteLaboratoryInfoByIds(Long[] ids);

    /**
     * 删除实验室信息信息
     * 
     * @param id 实验室信息主键
     * @return 结果
     */
    public int deleteLaboratoryInfoById(Long id);
    
    /**
     * 查询实验室及其关联的检测项目
     * 
     * @param id 实验室信息主键
     * @return 实验室及关联检测项目信息
     */
    public LaboratoryWithTestingItemsDTO selectLaboratoryWithTestingItemsById(Long id);
    
    /**
     * 查询实验室及其关联的检测项目列表
     * 
     * @param laboratoryInfo 实验室信息
     * @return 实验室及关联检测项目信息集合
     */
    public List<LaboratoryWithTestingItemsDTO> selectLaboratoryWithTestingItemsList(LaboratoryInfo laboratoryInfo);
    
    /**
     * 根据实验室类型查询所有检测项目
     * 
     * @param labType 实验室类型
     * @return 实验室类型及关联检测项目信息
     */
    public LabTypeTestingItemsDTO selectTestingItemsByLabType(String labType);
}
