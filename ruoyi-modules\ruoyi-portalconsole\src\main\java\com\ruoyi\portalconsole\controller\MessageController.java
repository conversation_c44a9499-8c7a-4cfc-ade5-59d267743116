package com.ruoyi.portalconsole.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.Message;
import com.ruoyi.portalconsole.service.IMessageService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 站内消息Controller
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/Message")
public class MessageController extends BaseController
{
    @Autowired
    private IMessageService messageService;

    @Autowired
    private RemoteUserService remoteUserService;


    /**
     * 查询站内消息列表
     */
    @RequiresPermissions("portalconsole:Message:list")
    @GetMapping("/list")
    public TableDataInfo list(Message message)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<Message> list = messageService.selectMessageList(message);
        return getDataTable(list);
    }

    /**
     * 导出站内消息列表
     */
    @RequiresPermissions("portalconsole:Message:export")
    @Log(title = "站内消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Message message)
    {
        List<Message> list = messageService.selectMessageList(message);
        ExcelUtil<Message> util = new ExcelUtil<Message>(Message.class);
        util.exportExcel(response, list, "站内消息数据");
    }

    /**
     * 获取站内消息详细信息
     */
    @RequiresPermissions("portalconsole:Message:query")
    @GetMapping(value = "/{messageId}")
    public AjaxResult getInfo(@PathVariable("messageId") Long messageId)
    {
        return success(messageService.selectMessageByMessageId(messageId));
    }

    /**
     * 新增站内消息
     */
    @RequiresPermissions("portalconsole:Message:add")
    @Log(title = "站内消息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Message message)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        message.setUpdateBy(userNickName.getData());
        message.setCreateBy(userNickName.getData());
        return toAjax(messageService.insertMessage(message));
    }

    /**
     * 修改站内消息
     */
    @RequiresPermissions("portalconsole:Message:edit")
    @Log(title = "站内消息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Message message)
    {
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        message.setUpdateBy(userNickName.getData());
        return toAjax(messageService.updateMessage(message));
    }

    /**
     * 删除站内消息
     */
    @RequiresPermissions("portalconsole:Message:remove")
    @Log(title = "站内消息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{messageIds}")
    public AjaxResult remove(@PathVariable Long[] messageIds)
    {
        return toAjax(messageService.deleteMessageByMessageIds(messageIds));
    }
}
