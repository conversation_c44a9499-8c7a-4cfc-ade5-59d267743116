package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 证书信息对象 certificate
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public class Certificate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 身份证号码 */
    @Excel(name = "身份证号码")
    private String idCard;

    /** 职位名称 */
    @Excel(name = "职位名称")
    private String positionName;

    /** 职位级别 */
    @Excel(name = "职位级别")
    private String positionLevel;

    /** 工作单位 */
    @Excel(name = "工作单位")
    private String company;

    /** 证书名称 */
    @Excel(name = "证书名称")
    private String certificateName;

    /** 证书编号 */
    @Excel(name = "证书编号")
    private String certificateNo;

    /** 证书类型 */
    @Excel(name = "证书类型")
    private String certificateType;

    /** 发证日期 */
    @Excel(name = "发证日期")
    private String issueDate;

    /** 有效期至 */
    @Excel(name = "有效期至")
    private String validUntil;

    /** 状态（0正常 1已过期） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=已过期")
    private String status;

    /** 证书图片 */
    @Excel(name = "证书图片")
    private String certificateImage;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setPositionName(String positionName) 
    {
        this.positionName = positionName;
    }

    public String getPositionName() 
    {
        return positionName;
    }
    public void setPositionLevel(String positionLevel) 
    {
        this.positionLevel = positionLevel;
    }

    public String getPositionLevel() 
    {
        return positionLevel;
    }
    public void setCompany(String company) 
    {
        this.company = company;
    }

    public String getCompany() 
    {
        return company;
    }
    public void setCertificateName(String certificateName) 
    {
        this.certificateName = certificateName;
    }

    public String getCertificateName() 
    {
        return certificateName;
    }
    public void setCertificateNo(String certificateNo) 
    {
        this.certificateNo = certificateNo;
    }

    public String getCertificateNo() 
    {
        return certificateNo;
    }
    public void setCertificateType(String certificateType) 
    {
        this.certificateType = certificateType;
    }

    public String getCertificateType() 
    {
        return certificateType;
    }
    public void setIssueDate(String issueDate) 
    {
        this.issueDate = issueDate;
    }

    public String getIssueDate() 
    {
        return issueDate;
    }
    public void setValidUntil(String validUntil) 
    {
        this.validUntil = validUntil;
    }

    public String getValidUntil() 
    {
        return validUntil;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setCertificateImage(String certificateImage) 
    {
        this.certificateImage = certificateImage;
    }

    public String getCertificateImage() 
    {
        return certificateImage;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("idCard", getIdCard())
            .append("positionName", getPositionName())
            .append("positionLevel", getPositionLevel())
            .append("company", getCompany())
            .append("certificateName", getCertificateName())
            .append("certificateNo", getCertificateNo())
            .append("certificateType", getCertificateType())
            .append("issueDate", getIssueDate())
            .append("validUntil", getValidUntil())
            .append("status", getStatus())
            .append("certificateImage", getCertificateImage())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
