package com.ruoyi.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 站内消息对象 message
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public class Message extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 消息ID */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long messageId;

    /** 消息类型：政策申报、加入企业、企业认证、需求审核、供给审核 */
    @Excel(name = "消息类型：政策申报、加入企业、企业认证、需求审核、供给审核")
    private String messageTitle;

    /** 消息内容 */
    @Excel(name = "消息内容")
    private String messageBody;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date messageTime;

    /** 状态，业务字典 */
    @Excel(name = "状态，业务字典")
    private String messageStatus;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 会员id */
    @Excel(name = "会员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long memberId;

    public void setMessageId(Long messageId) 
    {
        this.messageId = messageId;
    }

    public Long getMessageId() 
    {
        return messageId;
    }
    public void setMessageTitle(String messageTitle) 
    {
        this.messageTitle = messageTitle;
    }

    public String getMessageTitle() 
    {
        return messageTitle;
    }
    public void setMessageBody(String messageBody) 
    {
        this.messageBody = messageBody;
    }

    public String getMessageBody() 
    {
        return messageBody;
    }
    public void setMessageTime(Date messageTime) 
    {
        this.messageTime = messageTime;
    }

    public Date getMessageTime() 
    {
        return messageTime;
    }
    public void setMessageStatus(String messageStatus) 
    {
        this.messageStatus = messageStatus;
    }

    public String getMessageStatus() 
    {
        return messageStatus;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setMemberId(Long memberId)
    {
        this.memberId = memberId;
    }

    public Long getMemberId()
    {
        return memberId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("messageId", getMessageId())
            .append("messageTitle", getMessageTitle())
            .append("messageBody", getMessageBody())
            .append("messageTime", getMessageTime())
            .append("messageStatus", getMessageStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("memberId", getMemberId())
            .toString();
    }
}
