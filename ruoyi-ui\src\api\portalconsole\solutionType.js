import request from '@/utils/request'

// 查询解决方案类型列表
export function listSolutionType(query) {
  return request({
    url: '/portalconsole/solutionType/list',
    method: 'get',
    params: query
  })
}

// 查询解决方案类型详细
export function getSolutionType(solutionTypeId) {
  return request({
    url: '/portalconsole/solutionType/' + solutionTypeId,
    method: 'get'
  })
}

// 新增解决方案类型
export function addSolutionType(data) {
  return request({
    url: '/portalconsole/solutionType',
    method: 'post',
    data: data
  })
}

// 修改解决方案类型
export function updateSolutionType(data) {
  return request({
    url: '/portalconsole/solutionType',
    method: 'put',
    data: data
  })
}

// 删除解决方案类型
export function delSolutionType(solutionTypeId) {
  return request({
    url: '/portalconsole/solutionType/' + solutionTypeId,
    method: 'delete'
  })
}
