package com.ruoyi.portalconsole.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalconsole.domain.vo.EcologyVO;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.Ecology;
import com.ruoyi.portalconsole.service.IEcologyService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 生态协作Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/Ecology")
public class EcologyController extends BaseController
{
    @Autowired
    private IEcologyService ecologyService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询生态协作列表
     */
    @RequiresPermissions("portalconsole:Ecology:list")
    @GetMapping("/list")
    public TableDataInfo list(Ecology ecology)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<EcologyVO> list = ecologyService.selectEcologyList(ecology);
        return getDataTable(list);
    }

    /**
     * 导出生态协作列表
     */
    @RequiresPermissions("portalconsole:Ecology:export")
    @Log(title = "生态协作", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Ecology ecology)
    {
        List<EcologyVO> list = ecologyService.selectEcologyList(ecology);
        ExcelUtil<EcologyVO> util = new ExcelUtil<EcologyVO>(EcologyVO.class);
        util.exportExcel(response, list, "生态协作数据");
    }

    /**
     * 获取生态协作详细信息
     */
    @RequiresPermissions("portalconsole:Ecology:query")
    @GetMapping(value = "/{ecologyId}")
    public AjaxResult getInfo(@PathVariable("ecologyId") Long ecologyId)
    {
        return success(ecologyService.selectEcologyByEcologyId(ecologyId));
    }

    /**
     * 新增生态协作
     */
    @RequiresPermissions("portalconsole:Ecology:add")
    @Log(title = "生态协作", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Ecology ecology)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        ecology.setUpdateBy(userNickName.getData());
        ecology.setCreateBy(userNickName.getData());
        return toAjax(ecologyService.insertEcology(ecology));
    }

    /**
     * 修改生态协作
     */
    @RequiresPermissions("portalconsole:Ecology:edit")
    @Log(title = "生态协作", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Ecology ecology)
    {

        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        ecology.setUpdateBy(userNickName.getData());
        return toAjax(ecologyService.updateEcology(ecology));
    }

    /**
     * 删除生态协作
     */
    @RequiresPermissions("portalconsole:Ecology:remove")
    @Log(title = "生态协作", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ecologyIds}")
    public AjaxResult remove(@PathVariable Long[] ecologyIds)
    {
        return toAjax(ecologyService.deleteEcologyByEcologyIds(ecologyIds));
    }
}
