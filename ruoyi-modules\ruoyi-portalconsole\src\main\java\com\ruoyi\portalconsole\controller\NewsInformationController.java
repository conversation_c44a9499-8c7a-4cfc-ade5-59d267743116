package com.ruoyi.portalconsole.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.NewsInformation;
import com.ruoyi.portalconsole.service.INewsInformationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 动态资讯Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/NewsInformation")
public class NewsInformationController extends BaseController
{
    @Autowired
    private INewsInformationService newsInformationService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询动态资讯列表
     */
    @RequiresPermissions("portalconsole:NewsInformation:list")
    @GetMapping("/list")
    public TableDataInfo list(NewsInformation newsInformation)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<NewsInformation> list = newsInformationService.selectNewsInformationList(newsInformation);
        return getDataTable(list);
    }

    /**
     * 导出动态资讯列表
     */
    @RequiresPermissions("portalconsole:NewsInformation:export")
    @Log(title = "动态资讯", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NewsInformation newsInformation)
    {
        List<NewsInformation> list = newsInformationService.selectNewsInformationList(newsInformation);
        ExcelUtil<NewsInformation> util = new ExcelUtil<NewsInformation>(NewsInformation.class);
        util.exportExcel(response, list, "动态资讯数据");
    }

    /**
     * 获取动态资讯详细信息
     */
    @RequiresPermissions("portalconsole:NewsInformation:query")
    @GetMapping(value = "/{newsInformationId}")
    public AjaxResult getInfo(@PathVariable("newsInformationId") Long newsInformationId)
    {
        return success(newsInformationService.selectNewsInformationByNewsInformationId(newsInformationId));
    }

    /**
     * 新增动态资讯
     */
    @RequiresPermissions("portalconsole:NewsInformation:add")
    @Log(title = "动态资讯", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NewsInformation newsInformation)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        newsInformation.setUpdateBy(userNickName.getData());
        newsInformation.setCreateBy(userNickName.getData());
        return toAjax(newsInformationService.insertNewsInformation(newsInformation));
    }
    /**
     * 新增动态资讯
     */
    @Log(title = "动态资讯", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    public AjaxResult addByImport(@RequestBody NewsInformation newsInformation)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        newsInformation.setUpdateBy(userNickName.getData());
        newsInformation.setCreateBy(userNickName.getData());
        return toAjax(newsInformationService.insertNewsInformation(newsInformation));
    }

    /**
     * 修改动态资讯
     */
    @RequiresPermissions("portalconsole:NewsInformation:edit")
    @Log(title = "动态资讯", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NewsInformation newsInformation)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        newsInformation.setUpdateBy(userNickName.getData());
        return toAjax(newsInformationService.updateNewsInformation(newsInformation));
    }

    /**
     * 删除动态资讯
     */
    @RequiresPermissions("portalconsole:NewsInformation:remove")
    @Log(title = "动态资讯", businessType = BusinessType.DELETE)
	@DeleteMapping("/{newsInformationIds}")
    public AjaxResult remove(@PathVariable Long[] newsInformationIds)
    {
        return toAjax(newsInformationService.deleteNewsInformationByNewsInformationIds(newsInformationIds));
    }
}
