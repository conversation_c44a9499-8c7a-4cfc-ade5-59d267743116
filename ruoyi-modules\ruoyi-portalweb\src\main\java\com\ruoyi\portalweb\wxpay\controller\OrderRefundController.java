/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.ruoyi.portalweb.wxpay.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.portalweb.api.domain.AppStoreOrder;
import com.ruoyi.portalweb.wxpay.service.IOrderRefundService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;

/**
 * 售后订单管理 控制器
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("/refund")
@Api(value = "2、售后管理", tags = "2、售后管理")
public class OrderRefundController{

	@Autowired
	private IOrderRefundService orderReturnService;


	/**
	 * 申请退款
	 */
	@PostMapping("/apply")
	@ApiOperation(value = "申请退款")
	public AjaxResult refund(@Valid @RequestBody AppStoreOrder appStoreOrder) {
		//todo
		String remoteAddr = "????";
		return AjaxResult.success(orderReturnService.applyRefund(appStoreOrder, remoteAddr));
	}

}
