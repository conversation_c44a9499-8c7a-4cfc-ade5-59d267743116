package com.ruoyi.portalweb.mapper;

import java.util.List;
import com.ruoyi.portalweb.domain.CompositeMaterial;

/**
 * 复材展厅Mapper接口
 * 
 * <AUTHOR>
 */
public interface CompositeMaterialMapper {
    /**
     * 查询复材展厅列表
     * 
     * @param compositeMaterial 复材展厅信息
     * @return 复材展厅集合
     */
    public List<CompositeMaterial> selectCompositeMaterialList(CompositeMaterial compositeMaterial);

    /**
     * 查询复材展厅详细信息
     * 
     * @param id 复材展厅主键
     * @return 复材展厅
     */
    public CompositeMaterial selectCompositeMaterialById(Long id);

    /**
     * 新增复材展厅
     * 
     * @param compositeMaterial 复材展厅信息
     * @return 结果
     */
    public int insertCompositeMaterial(CompositeMaterial compositeMaterial);

    /**
     * 修改复材展厅
     * 
     * @param compositeMaterial 复材展厅信息
     * @return 结果
     */
    public int updateCompositeMaterial(CompositeMaterial compositeMaterial);

    /**
     * 删除复材展厅
     * 
     * @param id 复材展厅主键
     * @return 结果
     */
    public int deleteCompositeMaterialById(Long id);

    /**
     * 批量删除复材展厅
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCompositeMaterialByIds(Long[] ids);
}
