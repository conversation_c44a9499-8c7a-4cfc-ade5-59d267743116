<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.SolutionPainMapper">

    <resultMap type="SolutionPain" id="SolutionPainResult">
        <result property="solutionPainId" column="solution_pain_id"/>
        <result property="solutionId" column="solution_id"/>
        <result property="solutionPainName" column="solution_pain_name"/>
        <result property="solutionPainContent" column="solution_pain_content"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectSolutionPainVo">
        select solution_pain_id, solution_id, solution_pain_name, solution_pain_content, del_flag, create_by,
        create_time, update_by, update_time, remark from solution_pain
    </sql>

    <select id="selectSolutionPainList" parameterType="SolutionPain" resultMap="SolutionPainResult">
        <include refid="selectSolutionPainVo"/>
        <where>
            <if test="solutionId != null ">and solution_id = #{solutionId}</if>
            <if test="solutionPainName != null  and solutionPainName != ''">and solution_pain_name like concat('%',
                #{solutionPainName}, '%')
            </if>
            <if test="solutionPainContent != null  and solutionPainContent != ''">and solution_pain_content =
                #{solutionPainContent}
            </if>
        </where>
    </select>

    <select id="selectListBySolutionId" resultMap="SolutionPainResult">
        SELECT * FROM solution_pain
        WHERE solution_id = #{solutionId}
    </select>

    <select id="selectSolutionPainBySolutionPainId" parameterType="Long" resultMap="SolutionPainResult">
        <include refid="selectSolutionPainVo"/>
        where solution_pain_id = #{solutionPainId}
    </select>
    <select id="selectSolutionPainListBySolutionIds" resultMap="SolutionPainResult" parameterType="List">
        <include refid="selectSolutionPainVo"/>
        WHERE solution_id IN
        <foreach collection="list" open="(" close=")" separator="," item="solutionId">
            #{solutionId}
        </foreach>

    </select>

    <insert id="insertSolutionPain" parameterType="SolutionPain" useGeneratedKeys="true" keyProperty="solutionPainId">
        insert into solution_pain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="solutionId != null">solution_id,</if>
            <if test="solutionPainName != null">solution_pain_name,</if>
            <if test="solutionPainContent != null">solution_pain_content,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="solutionId != null">#{solutionId},</if>
            <if test="solutionPainName != null">#{solutionPainName},</if>
            <if test="solutionPainContent != null">#{solutionPainContent},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>
    <insert id="insertSolutionPainList"
            parameterType="java.util.List">
        insert into solution_pain (solution_id, solution_pain_name, solution_pain_content, create_by, update_by, remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.solutionId},#{item.solutionPainName},#{item.solutionPainContent},#{item.createBy},#{item.updateBy},#{item.remark})
        </foreach>
    </insert>

    <update id="updateSolutionPain" parameterType="SolutionPain">
        update solution_pain
        <trim prefix="SET" suffixOverrides=",">
            <if test="solutionId != null">solution_id = #{solutionId},</if>
            <if test="solutionPainName != null">solution_pain_name = #{solutionPainName},</if>
            <if test="solutionPainContent != null">solution_pain_content = #{solutionPainContent},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where solution_pain_id = #{solutionPainId}
    </update>

    <delete id="deleteSolutionPainBySolutionPainId" parameterType="Long">
        delete from solution_pain where solution_pain_id = #{solutionPainId}
    </delete>

    <delete id="deleteSolutionPainBySolutionPainIds" parameterType="String">
        delete from solution_pain where solution_pain_id in
        <foreach item="solutionPainId" collection="array" open="(" separator="," close=")">
            #{solutionPainId}
        </foreach>
    </delete>
    <delete id="deleteSolutionPainBySolutionId" parameterType="java.lang.Long">
        delete from solution_pain where solution_id = #{solutionId}
    </delete>
    <delete id="deleteSolutionPainBySolutionIds">
        delete from solution_pain where solution_id in
        <foreach collection="array" item="solutionId" separator="," open="(" close=")">
            #{solutionId}
        </foreach>
    </delete>
</mapper>