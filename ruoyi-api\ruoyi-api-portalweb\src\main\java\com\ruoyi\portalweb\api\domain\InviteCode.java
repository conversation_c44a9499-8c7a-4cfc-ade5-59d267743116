package com.ruoyi.portalweb.api.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 企业邀请码对象 invite_code
 * 
 * <AUTHOR>
 * @date 2024-08-06
 */
public class InviteCode extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 邀请码 */
    @Excel(name = "邀请码")
    private String code;

    /** 关联企业id */
    @Excel(name = "关联企业id")
    private Long companyRelatedId;

    /** 是否有效 */
    @Excel(name = "是否有效")
    private String isValid;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }
    public void setCompanyRelatedId(Long companyRelatedId) 
    {
        this.companyRelatedId = companyRelatedId;
    }

    public Long getCompanyRelatedId() 
    {
        return companyRelatedId;
    }
    public void setIsValid(String isValid) 
    {
        this.isValid = isValid;
    }

    public String getIsValid() 
    {
        return isValid;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("code", getCode())
            .append("companyRelatedId", getCompanyRelatedId())
            .append("createTime", getCreateTime())
            .append("isValid", getIsValid())
            .toString();
    }
}
