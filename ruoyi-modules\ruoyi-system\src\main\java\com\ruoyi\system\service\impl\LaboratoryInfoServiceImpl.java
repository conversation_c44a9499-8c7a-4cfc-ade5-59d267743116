package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.domain.TestingItem;
import com.ruoyi.system.domain.dto.LaboratoryWithTestingItemsDTO;
import com.ruoyi.system.domain.dto.LabTypeTestingItemsDTO;
import com.ruoyi.system.mapper.LabTestingRelationMapper;
import com.ruoyi.system.mapper.TestingItemMapper;
import com.ruoyi.system.domain.LabTestingRelation;
import com.ruoyi.system.service.ILaboratoryInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.LaboratoryInfoMapper;
import com.ruoyi.system.domain.LaboratoryInfo;

/**
 * 实验室信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Service
public class LaboratoryInfoServiceImpl implements ILaboratoryInfoService
{
    @Autowired
    private LaboratoryInfoMapper laboratoryInfoMapper;
    
    @Autowired
    private LabTestingRelationMapper labTestingRelationMapper;
    
    @Autowired
    private TestingItemMapper testingItemMapper;

    /**
     * 查询实验室信息
     *
     * @param id 实验室信息主键
     * @return 实验室信息
     */
    @Override
    public LaboratoryInfo selectLaboratoryInfoById(Long id)
    {
        return laboratoryInfoMapper.selectLaboratoryInfoById(id);
    }

    /**
     * 查询实验室信息列表
     *
     * @param laboratoryInfo 实验室信息
     * @return 实验室信息
     */
    @Override
    public List<LaboratoryInfo> selectLaboratoryInfoList(LaboratoryInfo laboratoryInfo)
    {
        return laboratoryInfoMapper.selectLaboratoryInfoList(laboratoryInfo);
    }

    /**
     * 新增实验室信息
     *
     * @param laboratoryInfo 实验室信息
     * @return 结果
     */
    @Override
    public int insertLaboratoryInfo(LaboratoryInfo laboratoryInfo)
    {
        laboratoryInfo.setCreateTime(DateUtils.getNowDate());
        return laboratoryInfoMapper.insertLaboratoryInfo(laboratoryInfo);
    }

    /**
     * 修改实验室信息
     *
     * @param laboratoryInfo 实验室信息
     * @return 结果
     */
    @Override
    public int updateLaboratoryInfo(LaboratoryInfo laboratoryInfo)
    {
        laboratoryInfo.setUpdateTime(DateUtils.getNowDate());
        return laboratoryInfoMapper.updateLaboratoryInfo(laboratoryInfo);
    }

    /**
     * 批量删除实验室信息
     *
     * @param ids 需要删除的实验室信息主键
     * @return 结果
     */
    @Override
    public int deleteLaboratoryInfoByIds(Long[] ids)
    {
        return laboratoryInfoMapper.deleteLaboratoryInfoByIds(ids);
    }

    /**
     * 删除实验室信息信息
     *
     * @param id 实验室信息主键
     * @return 结果
     */
    @Override
    public int deleteLaboratoryInfoById(Long id)
    {
        return laboratoryInfoMapper.deleteLaboratoryInfoById(id);
    }
    
    /**
     * 查询实验室及其关联的检测项目
     *
     * @param id 实验室信息主键
     * @return 实验室及关联检测项目信息
     */
    @Override
    public LaboratoryWithTestingItemsDTO selectLaboratoryWithTestingItemsById(Long id)
    {
        // 查询实验室基本信息
        LaboratoryInfo laboratoryInfo = laboratoryInfoMapper.selectLaboratoryInfoById(id);
        if (laboratoryInfo == null) {
            return null;
        }
        
        // 查询实验室关联的检测项目关系
        List<LabTestingRelation> relations = labTestingRelationMapper.selectLabTestingRelationByLabId(id);
        
        // 查询关联的检测项目详情
        List<TestingItem> testingItems = new ArrayList<>();
        if (relations != null && !relations.isEmpty()) {
            for (LabTestingRelation relation : relations) {
                TestingItem testingItem = testingItemMapper.selectTestingItemById(relation.getTestingId());
                if (testingItem != null) {
                    testingItems.add(testingItem);
                }
            }
        }
        
        // 组装返回数据
        LaboratoryWithTestingItemsDTO dto = new LaboratoryWithTestingItemsDTO();
        dto.setLaboratoryInfo(laboratoryInfo);
        dto.setTestingItems(testingItems);
        dto.setRelations(relations);
        
        return dto;
    }
    
    /**
     * 查询实验室及其关联的检测项目列表
     *
     * @param laboratoryInfo 实验室信息
     * @return 实验室及关联检测项目信息集合
     */
    @Override
    public List<LaboratoryWithTestingItemsDTO> selectLaboratoryWithTestingItemsList(LaboratoryInfo laboratoryInfo)
    {
        // 查询实验室列表
        List<LaboratoryInfo> labList = laboratoryInfoMapper.selectLaboratoryInfoList(laboratoryInfo);
        List<LaboratoryWithTestingItemsDTO> dtoList = new ArrayList<>();
        
        if (labList != null && !labList.isEmpty()) {
            for (LaboratoryInfo lab : labList) {
                // 查询每个实验室的关联检测项目
                LaboratoryWithTestingItemsDTO dto = selectLaboratoryWithTestingItemsById(lab.getId());
                if (dto != null) {
                    dtoList.add(dto);
                }
            }
        }
        
        return dtoList;
    }
    
    /**
     * 根据实验室类型查询所有检测项目
     *
     * @param labType 实验室类型
     * @return 实验室类型及关联检测项目信息
     */
    @Override
    public LabTypeTestingItemsDTO selectTestingItemsByLabType(String labType)
    {
        // 创建返回对象
        LabTypeTestingItemsDTO dto = new LabTypeTestingItemsDTO();
        dto.setLabType(labType);
        
        // 根据实验室类型查询实验室列表
        LaboratoryInfo queryParam = new LaboratoryInfo();
        queryParam.setLabType(labType);
        List<LaboratoryInfo> laboratories = laboratoryInfoMapper.selectLaboratoryInfoList(queryParam);
        dto.setLaboratories(laboratories);
        
        // 存储所有检测项目，使用Set去重
        Set<TestingItem> testingItemSet = new HashSet<>();
        
        // 遍历实验室列表，查询每个实验室关联的检测项目
        if (laboratories != null && !laboratories.isEmpty()) {
            for (LaboratoryInfo lab : laboratories) {
                // 查询实验室关联的检测项目关系
                List<LabTestingRelation> relations = labTestingRelationMapper.selectLabTestingRelationByLabId(lab.getId());
                
                // 查询关联的检测项目详情
                if (relations != null && !relations.isEmpty()) {
                    for (LabTestingRelation relation : relations) {
                        TestingItem testingItem = testingItemMapper.selectTestingItemById(relation.getTestingId());
                        if (testingItem != null) {
                            testingItemSet.add(testingItem);
                        }
                    }
                }
            }
        }
        
        // 将Set转为List
        List<TestingItem> testingItems = new ArrayList<>(testingItemSet);
        dto.setTestingItems(testingItems);
        
        return dto;
    }
}
