<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TalentTrainingApplicationMapper">
    
    <resultMap type="TalentTrainingApplication" id="TalentTrainingApplicationResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="contact"    column="contact"    />
        <result property="gender"    column="gender"    />
        <result property="trainingCourse"    column="training_course"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTalentTrainingApplicationVo">
        select id, name, contact, gender, training_course, apply_time, create_time, update_time, status, remark from talent_training_application
    </sql>

    <select id="selectTalentTrainingApplicationList" parameterType="TalentTrainingApplication" resultMap="TalentTrainingApplicationResult">
        <include refid="selectTalentTrainingApplicationVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="gender != null  and gender != ''"> and gender = #{gender}</if>
            <if test="trainingCourse != null  and trainingCourse != ''"> and training_course = #{trainingCourse}</if>
            <if test="applyTime != null "> and apply_time = #{applyTime}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectTalentTrainingApplicationById" parameterType="Long" resultMap="TalentTrainingApplicationResult">
        <include refid="selectTalentTrainingApplicationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTalentTrainingApplication" parameterType="TalentTrainingApplication" useGeneratedKeys="true" keyProperty="id">
        insert into talent_training_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="contact != null and contact != ''">contact,</if>
            <if test="gender != null">gender,</if>
            <if test="trainingCourse != null">training_course,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="contact != null and contact != ''">#{contact},</if>
            <if test="gender != null">#{gender},</if>
            <if test="trainingCourse != null">#{trainingCourse},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTalentTrainingApplication" parameterType="TalentTrainingApplication">
        update talent_training_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="trainingCourse != null">training_course = #{trainingCourse},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTalentTrainingApplicationById" parameterType="Long">
        delete from talent_training_application where id = #{id}
    </delete>

    <delete id="deleteTalentTrainingApplicationByIds" parameterType="String">
        delete from talent_training_application where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>