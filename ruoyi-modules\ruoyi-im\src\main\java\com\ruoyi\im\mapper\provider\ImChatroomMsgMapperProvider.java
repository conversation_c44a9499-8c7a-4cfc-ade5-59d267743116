package com.ruoyi.im.mapper.provider;

import com.ruoyi.common.core.utils.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class ImChatroomMsgMapperProvider {

    public String findLatestMsg(Map map) {
        List<String> ids = (List<String>)map.get("ids");
        StringBuilder sb = new StringBuilder("select * from im_chatroom_msg where id in (select max(id) from im_chatroom_msg where toUserId in (");
        for (int i = 0; i < ids.size(); i++) {
            sb.append("'").append(ids.get(i)).append("'");
            if (i < ids.size() - 1) {
                sb.append(",");
            }
        }
        sb.append(") ");
        sb.append("group by toUserId)");
        return sb.toString();
    }
}
