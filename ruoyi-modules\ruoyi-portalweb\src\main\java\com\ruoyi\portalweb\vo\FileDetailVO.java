package com.ruoyi.portalweb.vo;

import com.ruoyi.portalweb.api.domain.FileDetail;
import com.ruoyi.portalweb.api.domain.Supply;
import io.swagger.annotations.ApiModelProperty;

/**
 * 附件子表 file_detail
 */
public class FileDetailVO extends FileDetail {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "文件名")
    private String fileName;
    @ApiModelProperty(value = "文件路径")
    private String filePath;
    @ApiModelProperty(value = "完整url")
    private String fileFullPath;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileFullPath() {
        return fileFullPath;
    }

    public void setFileFullPath(String fileFullPath) {
        this.fileFullPath = fileFullPath;
    }
}
