package com.ruoyi.im.api;

import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.im.api.domain.ImNotice;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.api
 * @ClassName: RemoteImNoticeService
 * @Author: ${maguojun}
 * @Description: ${description}
 * @Date: 2022/3/16 13:52
 * @Version: 1.0
 */
@FeignClient(contextId = "remoteImNoticeService", value = ServiceNameConstants.IM_SERVICE)
public interface RemoteImNoticeService {

    /***
     * ImNotice分页条件搜索实现
     * @param imNotice
     * @param page
     * @param size
     * @return
     */
     @PostMapping(value = "/im/notice/search/{page}/{size}" )
     R<TableDataInfo> findPage(@RequestBody(required = false) ImNotice imNotice, @PathVariable("page")  int page, @PathVariable("size")  int size, @RequestParam(value = "fields",required = false) String fields);


    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/im/notice/{id}" )
    R<Boolean> delete(@PathVariable("id") Long id);

    /***
     * 修改ImNotice数据
     * @param imNotice
     * @param
     * @return
     */
     @PutMapping(value="/im/notice/update")
     R<Boolean> update(@RequestBody  ImNotice imNotice);

    /**保存
     * @param imNotice
     * @return
     */
    @PostMapping(value = "/im/notice/save" )
    R<Boolean> save(@RequestBody ImNotice imNotice);
}
