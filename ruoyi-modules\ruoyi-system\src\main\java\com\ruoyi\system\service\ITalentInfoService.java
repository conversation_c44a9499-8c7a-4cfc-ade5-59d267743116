package com.ruoyi.system.service;

import java.util.List;

import com.ruoyi.system.api.domain.TalentInfoApi;
import com.ruoyi.system.domain.TalentInfo;

/**
 * 人才信息Service接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
public interface ITalentInfoService
{
    /**
     * 查询人才信息
     *
     * @param id 人才信息主键
     * @return 人才信息
     */
    public TalentInfo selectTalentInfoById(Long id);

    /**
     * 根据用户ID查询人才信息
     *
     * @param userId 用户ID
     * @return 人才信息
     */
    public TalentInfoApi selectTalentInfoByUserId(Long userId);

    /**
     * 查询人才信息列表
     *
     * @param talentInfo 人才信息
     * @return 人才信息集合
     */
    public List<TalentInfo> selectTalentInfoList(TalentInfo talentInfo);

    /**
     * 新增人才信息
     *
     * @param talentInfo 人才信息
     * @return 结果
     */
    public int insertTalentInfo(TalentInfo talentInfo);

    /**
     * 修改人才信息
     *
     * @param talentInfo 人才信息
     * @return 结果
     */
    public int updateTalentInfo(TalentInfo talentInfo);

    /**
     * 批量删除人才信息
     *
     * @param ids 需要删除的人才信息主键集合
     * @return 结果
     */
    public int deleteTalentInfoByIds(Long[] ids);

    /**
     * 删除人才信息信息
     *
     * @param id 人才信息主键
     * @return 结果
     */
    public int deleteTalentInfoById(Long id);
}
