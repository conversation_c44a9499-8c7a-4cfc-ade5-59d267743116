package com.ruoyi.im.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.im.api.domain.ImUser;
import com.ruoyi.im.api.util.RongyunUtils;
import com.ruoyi.im.service.ImUserService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/im/user")
public class ImUserController {

    @Resource
    private ImUserService imUserService;

    @Resource
    private RongyunUtils rongyunUtils;

    /***
     * ImUser分页条件搜索实现
     * @param
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/search/{page}/{size}")
    public TableDataInfo findPage(@PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields) {
        Page<ImUser> pageSearch = new Page<>(page, size);
        QueryWrapper<ImUser> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(fields)) {
            wrapper.like("userId", fields).or().like("name", fields);
        }
        wrapper.orderByDesc("id");
        imUserService.page(pageSearch, wrapper);
        TableDataInfo tableDataInfo = new TableDataInfo(pageSearch.getRecords(), (int) pageSearch.getTotal());
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setMsg("success");
        return tableDataInfo;
    }

    /***
     * ImUser分页条件搜索实现
     * @param
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/query/{page}/{size}")
    public TableDataInfo queryPage(@RequestBody(required = false) ImUser imUser, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields) {
        Page<ImUser> pageSearch = new Page<>(page, size);
        QueryWrapper<ImUser> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(imUser.getUserId())) {
            wrapper.like("userId", imUser.getUserId());
        }
        if (StringUtils.isNotBlank(imUser.getName())) {
            wrapper.like("name", imUser.getName());
        }
        if (StringUtils.isNotBlank(imUser.getCustomer())) {
            wrapper.like("customer", imUser.getCustomer());
        }
        if (StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        imUserService.page(pageSearch, wrapper);
        TableDataInfo tableDataInfo = new TableDataInfo(pageSearch.getRecords(), (int) pageSearch.getTotal());
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setMsg("success");
        return tableDataInfo;
    }

    /***
     * 多条件搜索数据
     * @param imUser
     * @return
     */
    @PostMapping(value = "/search")
    public R<List<ImUser>> findList(@RequestBody(required = false) ImUser imUser, @RequestParam(value = "fields", required = false) String fields) {
        QueryWrapper<ImUser> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(imUser.getUserId())) {
            wrapper.like("userId", imUser.getUserId());
        }
        if (StringUtils.isNotBlank(imUser.getCustomer())) {
            wrapper.like("customer", imUser.getCustomer());
        }
        if (StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        return R.ok(imUserService.list(wrapper));
    }


    /***
     * 多条件搜索数据
     * @return
     */
    @GetMapping(value = "/customer")
    public R<List<ImUser>> findCustomer(@RequestParam(value = "module", required = false) String module) {
        QueryWrapper<ImUser> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(module)) {
            wrapper.like("customer", module);
        } else {
            wrapper.ne("customer", "");
        }
        wrapper.orderByDesc("id");
        return R.ok(imUserService.list(wrapper));
    }

    /***
     * 修改ImUser数据
     * @param imUser
     * @return
     */
    @PostMapping(value = "/update")
    public R<Boolean> update(@RequestBody ImUser imUser) {
        if (imUser.getId() == null || imUser.getId() <= 0) {
            Long id = imUserService.getOne(new QueryWrapper<ImUser>().eq("userId", imUser.getUserId())).getId();
            imUser.setId(id);
        }
        if (imUserService.updateById(imUser)) {
            return R.ok(true);
        }
        ImUser user = imUserService.getById(imUser.getId());
        if(StringUtils.isNotBlank(imUser.getRoomId())){
            rongyunUtils.register(user.getUserId(),user.getName(),user.getPortraitUri());
            rongyunUtils.messageSend(user.getUserId(),imUser.getRoomId(),"您好，我是柠檬豆平台客服，有什么需求帮您？");
        }
        return R.ok(false);
    }

    /***
     * 接受融云状态
     * @param jsonArray
     * @return
     */
    @PostMapping(value = "/token")
    public R<Boolean> token(@RequestBody JSONArray jsonArray) {
        if (jsonArray != null && jsonArray.size() > 0) {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                ImUser imUser = imUserService.getOne(new QueryWrapper<ImUser>().eq("userId", jsonObject.getString("userid")));
                if (ObjectUtil.isNotEmpty(imUser)) {
                    imUser.setTime(jsonObject.getLong("time"));
                    imUser.setClientIp(jsonObject.getString("clientIp"));
                    imUser.setOs(jsonObject.getString("os"));
                    if (jsonObject.getInteger("status") != 0) {
                        if (imUser.getOnline() <= 0) {
                            continue;
                        }
                        //用户多端在线数减1
                        imUser.setOnline(imUser.getOnline() - 1);
                    } else {
                        //用户多端在线数+1
                        imUser.setOnline(imUser.getOnline() + 1);
                    }
                    imUserService.updateById(imUser);
                }
            }
        }
        return R.ok(false);
    }

    /***
     * 新增ImUser数据注册账户
     * @param imUser
     * @return
     */
    @PostMapping(value = "/add")
    public R<JSONObject> add(@RequestBody ImUser imUser) {
        if (ObjectUtils.isNotEmpty(imUser)) {
            JSONObject retVal = new JSONObject();
            //根据手机号查找用户是否存在
            ImUser user = imUserService.getOne(new QueryWrapper<ImUser>().eq("userId", imUser.getUserId()));

            if (ObjectUtils.isEmpty(user)) {
                //注冊融云账户
                String register = rongyunUtils.register(imUser.getUserId(), imUser.getName(), imUser.getPortraitUri());
                if (StringUtils.isNotBlank(register)) {
                    JSONObject jsonStr = JSONObject.parseObject(register);
                    //校验是否有token
                    if (StringUtils.isNotBlank(jsonStr.getString("token"))) {
                        imUser.setToken(jsonStr.getString("token"));
                        if (imUserService.save(imUser)) {
                            retVal.put("token", imUser.getToken());
                            retVal.put("customer", "");
                            return R.ok(retVal);
                        }
                    }
                }
            } else {
                retVal.put("token", user.getToken());
                retVal.put("customer", user.getCustomer());
                return R.ok(retVal);
            }

        }
        return R.fail(400, "缺少必要的请求参数");
    }


    @PostMapping(value = "/im/user/sync")
    public R<Boolean> syncUser(@RequestParam("userName") String userName, @RequestParam("nickName") String nickName,
                                    @RequestParam("avatar") String avatar, @RequestParam("rongYunToken") String rongYunToken) {
        //根据手机号查找用户是否存在
        ImUser user = imUserService.getOne(new QueryWrapper<ImUser>().eq("userId", userName));
        if (ObjectUtils.isEmpty(user)) {
            user = new ImUser();
            user.setUserId(userName);
            user.setName(nickName);
            user.setPortraitUri(avatar);
            user.setToken(rongYunToken);
            imUserService.save(user);
        }
        return R.ok(true);
    }

    /***
     * 根据telphone查询ImUser数据
     * @param userId
     * @return
     */
    @GetMapping("/{userId}")
    public R<ImUser> findByUserid(@PathVariable("userId") String userId) {
        return R.ok(imUserService.getOne(new QueryWrapper<ImUser>().eq("userId", userId)));
    }

    /***
     * 根据userId查询ImUser数据
     * @param userId
     * @return
     */
    @GetMapping("/userlist")
    public R<List<ImUser>> findByUseridList(@RequestParam("userId") String userId) {
        String[] userIds = userId.split(",");
        List<ImUser> userList = imUserService.list(new QueryWrapper<ImUser>().in("userId", userIds));
        return R.ok(userList);
    }

    /**
     * @Description:
     * @Param:
     * @return:
     * @Author: 查看好友的基本信息
     * @Date:
     */
    @PostMapping(value = "/detail")
    public R<ImUser> detail(@RequestParam("friendId") String
                                         friendId) {
        if (StringUtils.isNotBlank(friendId)) {
            ImUser imUser = imUserService.getOne(new QueryWrapper<ImUser>().eq("userId", friendId));
            return R.ok(imUser);
        }

        return R.ok(null);
    }



    @GetMapping(value = "/detail/userid" )
    public R<JSONObject> getUserByUserId(@RequestParam("userId") String userId){
        ImUser imUser = imUserService.getOne(new QueryWrapper<ImUser>().eq("userId", userId));
        if(imUser==null){
            return R.ok(null);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id",imUser.getId());
        jsonObject.put("userId",imUser.getUserId());
        jsonObject.put("online",imUser.getOnline());
        jsonObject.put("openid",StringUtils.trimToEmpty(imUser.getOpenid()));
        return R.ok(jsonObject);
    }

    @PostMapping(value = "/openid" )
    public R<Boolean> updateOpenid(@RequestBody JSONObject imUser){
        ImUser user = imUserService.getById(imUser.getLong("id"));
        user.setOpenid(imUser.getString("openid"));
        return R.ok(imUserService.updateById(user));
    }

}
