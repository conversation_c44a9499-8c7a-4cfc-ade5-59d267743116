package com.ruoyi.portalweb.mapper;

import java.util.List;
import com.ruoyi.portalweb.api.domain.CompanyRelated;
import com.ruoyi.portalweb.vo.CompanyRelatedVO;

/**
 * 关联企业信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
public interface CompanyRelatedMapper 
{
    /**
     * 查询关联企业信息
     * 
     * @param companyRelatedId 关联企业信息主键
     * @return 关联企业信息
     */
    public CompanyRelated selectCompanyRelatedByCompanyRelatedId(Long companyRelatedId);

    /**
     * 查询关联企业信息列表
     * 
     * @param companyRelated 关联企业信息
     * @return 关联企业信息集合
     */
    public List<CompanyRelated> selectCompanyRelatedList(CompanyRelatedVO companyRelated);

    /**
     * 新增关联企业信息
     * 
     * @param companyRelated 关联企业信息
     * @return 结果
     */
    public int insertCompanyRelated(CompanyRelated companyRelated);

    /**
     * 修改关联企业信息
     * 
     * @param companyRelated 关联企业信息
     * @return 结果
     */
    public int updateCompanyRelated(CompanyRelated companyRelated);

    /**
     * 删除关联企业信息
     * 
     * @param companyRelatedId 关联企业信息主键
     * @return 结果
     */
    public int deleteCompanyRelatedByCompanyRelatedId(Long companyRelatedId);

    /**
     * 批量删除关联企业信息
     * 
     * @param companyRelatedIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCompanyRelatedByCompanyRelatedIds(Long[] companyRelatedIds);

    /**
     * 查询关联企业信息
     *
     * @param companyName 关联企业名称
     * @return 关联企业信息
     */
    List<CompanyRelated> selectCompanyRelatedByCompanyName(String companyName);

    public void addCompanyRelatedViewCount(Long companyRelatedId);
}
