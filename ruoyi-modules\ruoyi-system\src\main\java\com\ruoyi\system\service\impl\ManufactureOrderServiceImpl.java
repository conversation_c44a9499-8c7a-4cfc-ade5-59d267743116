package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.domain.MaterialInfo;
import com.ruoyi.system.domain.OrderMaterialRelation;
import com.ruoyi.system.domain.dto.OrderWithMaterialsDTO;
import com.ruoyi.system.service.IMaterialInfoService;
import com.ruoyi.system.service.IOrderMaterialRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.mapper.ManufactureOrderMapper;
import com.ruoyi.system.domain.ManufactureOrder;
import com.ruoyi.system.service.IManufactureOrderService;

/**
 * 制造订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Service
public class ManufactureOrderServiceImpl implements IManufactureOrderService
{
    @Autowired
    private ManufactureOrderMapper manufactureOrderMapper;

    @Autowired
    private IMaterialInfoService materialInfoService;

    @Autowired
    private IOrderMaterialRelationService orderMaterialRelationService;

    /**
     * 查询制造订单
     *
     * @param id 制造订单主键
     * @return 制造订单
     */
    @Override
    public ManufactureOrder selectManufactureOrderById(Long id)
    {
        return manufactureOrderMapper.selectManufactureOrderById(id);
    }

    /**
     * 查询制造订单列表
     *
     * @param manufactureOrder 制造订单
     * @return 制造订单
     */
    @Override
    public List<ManufactureOrder> selectManufactureOrderList(ManufactureOrder manufactureOrder)
    {
        return manufactureOrderMapper.selectManufactureOrderList(manufactureOrder);
    }

    /**
     * 新增制造订单
     *
     * @param manufactureOrder 制造订单
     * @return 结果
     */
    @Override
    public int insertManufactureOrder(ManufactureOrder manufactureOrder)
    {
        manufactureOrder.setCreateTime(DateUtils.getNowDate());
        return manufactureOrderMapper.insertManufactureOrder(manufactureOrder);
    }

    /**
     * 新增制造订单（包含物料信息）
     *
     * @param orderWithMaterials 制造订单及物料信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertOrderWithMaterials(OrderWithMaterialsDTO orderWithMaterials)
    {
        // 1. 保存订单信息
        orderWithMaterials.setCreateTime(DateUtils.getNowDate());
        int rows = manufactureOrderMapper.insertManufactureOrder(orderWithMaterials);

        if (rows > 0)
        {
            Long orderId = orderWithMaterials.getId();

            // 2. 保存物料信息
            List<MaterialInfo> materials = orderWithMaterials.getMaterials();
            if (materials != null && !materials.isEmpty())
            {
                for (MaterialInfo material : materials)
                {
                    if (material.getId() == null)
                    {
                        // 如果是新物料，则插入
                        material.setCreateTime(DateUtils.getNowDate());
                        materialInfoService.insertMaterialInfo(material);
                    }
                    else
                    {
                        // 如果是已有物料，则更新
                        material.setUpdateTime(DateUtils.getNowDate());
                        materialInfoService.updateMaterialInfo(material);
                    }
                    OrderMaterialRelation relation = new OrderMaterialRelation();
                    relation.setMaterialId(material.getId());
                    relation.setOrderId(orderId);
                    relation.setCreateTime(DateUtils.getNowDate());
                    orderMaterialRelationService.insertOrderMaterialRelation(relation);
                }
            }
        }

        return rows;
    }

    /**
     * 修改制造订单
     *
     * @param manufactureOrder 制造订单
     * @return 结果
     */
    @Override
    public int updateManufactureOrder(ManufactureOrder manufactureOrder)
    {
        manufactureOrder.setUpdateTime(DateUtils.getNowDate());
        return manufactureOrderMapper.updateManufactureOrder(manufactureOrder);
    }

    /**
     * 修改制造订单（包含物料信息）
     *
     * @param orderWithMaterials 制造订单及物料信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOrderWithMaterials(OrderWithMaterialsDTO orderWithMaterials)
    {
        // 1. 更新订单信息
        orderWithMaterials.setUpdateTime(DateUtils.getNowDate());
        int rows = manufactureOrderMapper.updateManufactureOrder(orderWithMaterials);

        if (rows > 0)
        {
            Long orderId = orderWithMaterials.getId();

            // 2. 删除原有的订单物料关联
            orderMaterialRelationService.deleteOrderMaterialRelationByOrderId(orderId);

            // 3. 保存物料信息
            List<MaterialInfo> materials = orderWithMaterials.getMaterials();
            if (materials != null && !materials.isEmpty())
            {
                for (MaterialInfo material : materials)
                {
                    if (material.getId() == null)
                    {
                        // 如果是新物料，则插入
                        material.setCreateTime(DateUtils.getNowDate());
                        materialInfoService.insertMaterialInfo(material);
                    }
                    else
                    {
                        // 如果是已有物料，则更新
                        material.setUpdateTime(DateUtils.getNowDate());
                        materialInfoService.updateMaterialInfo(material);
                    }
                    
                    OrderMaterialRelation relation = new OrderMaterialRelation();
                    relation.setOrderId(orderId);
                    relation.setMaterialId(material.getId());
                    relation.setCreateTime(DateUtils.getNowDate());
                    orderMaterialRelationService.insertOrderMaterialRelation(relation);
                }
            }
        }

        return rows;
    }

    /**
     * 批量删除制造订单
     *
     * @param ids 需要删除的制造订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteManufactureOrderByIds(Long[] ids)
    {
        for (Long id : ids)
        {
            // 获取订单关联的所有物料关系
            List<OrderMaterialRelation> relations = orderMaterialRelationService.selectOrderMaterialRelationByOrderId(id);
            
            // 收集所有关联的物料ID
            List<Long> materialIds = new ArrayList<>();
            for (OrderMaterialRelation relation : relations) {
                materialIds.add(relation.getMaterialId());
            }
            
            // 删除订单关联的物料关系
            orderMaterialRelationService.deleteOrderMaterialRelationByOrderId(id);
            
            // 删除关联的物料信息
            if (!materialIds.isEmpty()) {
                materialInfoService.deleteMaterialInfoByIds(materialIds.toArray(new Long[0]));
            }
        }
        return manufactureOrderMapper.deleteManufactureOrderByIds(ids);
    }

    /**
     * 删除制造订单信息
     *
     * @param id 制造订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteManufactureOrderById(Long id)
    {
        // 删除订单关联的物料关系
        orderMaterialRelationService.deleteOrderMaterialRelationByOrderId(id);
        return manufactureOrderMapper.deleteManufactureOrderById(id);
    }
    
    /**
     * 查询制造订单及其关联的物料信息
     *
     * @param id 制造订单主键
     * @return 制造订单及物料信息
     */
    @Override
    public OrderWithMaterialsDTO selectOrderWithMaterialsById(Long id)
    {
        // 查询订单基本信息
        ManufactureOrder order = manufactureOrderMapper.selectManufactureOrderById(id);
        if (order == null) {
            return null;
        }
        
        // 创建返回对象
        OrderWithMaterialsDTO dto = new OrderWithMaterialsDTO();
        // 复制订单基本信息
        dto.setId(order.getId());
        dto.setDeadline(order.getDeadline());
        dto.setStatus(order.getStatus());
        dto.setOrderType(order.getOrderType());
        dto.setPrice(order.getPrice());
        dto.setDemandCompany(order.getDemandCompany());
        dto.setContactPerson(order.getContactPerson());
        dto.setContactPhone(order.getContactPhone());
        dto.setDeliveryAddress(order.getDeliveryAddress());
        dto.setFileRequirement(order.getFileRequirement());
        dto.setBankName(order.getBankName());
        dto.setPaymentAccount(order.getPaymentAccount());
        dto.setCreateTime(order.getCreateTime());
        dto.setUpdateTime(order.getUpdateTime());
        dto.setCreateBy(order.getCreateBy());
        dto.setUpdateBy(order.getUpdateBy());
        dto.setRemark(order.getRemark());
        dto.setAuditStatus(order.getAuditStatus());
        
        // 查询订单关联的物料关系
        List<OrderMaterialRelation> relations = orderMaterialRelationService.selectOrderMaterialRelationByOrderId(id);
        dto.setRelations(relations);
        
        // 查询关联的物料详情
        List<MaterialInfo> materials = new ArrayList<>();
        if (relations != null && !relations.isEmpty()) {
            for (OrderMaterialRelation relation : relations) {
                MaterialInfo material = materialInfoService.selectMaterialInfoById(relation.getMaterialId());
                if (material != null) {
                    materials.add(material);
                }
            }
        }
        dto.setMaterials(materials);
        
        return dto;
    }
}
