package com.ruoyi.system.domain.dto;

import com.ruoyi.system.domain.LaboratoryInfo;
import com.ruoyi.system.domain.LabTestingRelation;
import com.ruoyi.system.domain.TestingItem;

import java.util.List;

/**
 * 实验室与关联检测项目数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-03-08
 */
public class LaboratoryWithTestingItemsDTO {
    
    /** 实验室信息 */
    private LaboratoryInfo laboratoryInfo;
    
    /** 关联的检测项目列表 */
    private List<TestingItem> testingItems;
    
    /** 关联关系列表 */
    private List<LabTestingRelation> relations;

    public LaboratoryInfo getLaboratoryInfo() {
        return laboratoryInfo;
    }

    public void setLaboratoryInfo(LaboratoryInfo laboratoryInfo) {
        this.laboratoryInfo = laboratoryInfo;
    }

    public List<TestingItem> getTestingItems() {
        return testingItems;
    }

    public void setTestingItems(List<TestingItem> testingItems) {
        this.testingItems = testingItems;
    }

    public List<LabTestingRelation> getRelations() {
        return relations;
    }

    public void setRelations(List<LabTestingRelation> relations) {
        this.relations = relations;
    }
}
