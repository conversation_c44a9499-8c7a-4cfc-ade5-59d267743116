<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.NewsInformationMapper">

    <resultMap id="NewsInformationResult" type="com.ruoyi.portalweb.vo.NewsInformationVO">
        <result property="newsInformationId" column="news_information_id"/>
        <result property="newsInformationPlateId" column="news_information_plate_id"/>
        <result property="solutionTypeId" column="solution_type_id"/>
        <result property="newsInformationSource" column="news_information_source"/>
        <result property="newsInformationAuthor" column="news_information_author"/>
        <result property="newsInformationName" column="news_information_name"/>
        <result property="newsInformationIntroduction" column="news_information_introduction"/>
        <result property="newsInformationImg" column="news_information_img"/>
        <result property="newsInformationContent" column="news_information_content"/>
        <result property="newsInformationFrequency" column="news_information_frequency"/>
        <result property="newsInformationDate" column="news_information_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>

        <result property="solutionTypeName" column="solution_type_name"/>
    </resultMap>

    <sql id="selectNewsInformationVo">
        select news_information_id, news_information_plate_id, solution_type_id, news_information_source,
        news_information_author,
        news_information_name, news_information_introduction, news_information_img,
        news_information_content, news_information_frequency, news_information_date,
        del_flag, create_by, create_time, update_by, update_time, remark
        from news_information
    </sql>

    <sql id="Base_Column_List">
        a.*, b.solution_type_name
    </sql>

    <sql id="Base_Table_List">
        FROM news_information a
        LEFT JOIN solution_type b ON a.solution_type_id = b.solution_type_id
    </sql>

    <select id="selectNewsInformationList" parameterType="NewsInformationVO" resultMap="NewsInformationResult">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Table_List"/>
        <where>
            <if test="keywords != null  and keywords != ''">
                AND (
                    a.news_information_author like concat(concat('%',#{keywords}),'%')
                    OR a.news_information_name like concat(concat('%',#{keywords}),'%')
                )
            </if>
            <if test="newsInformationPlateId != null ">and a.news_information_plate_id = #{newsInformationPlateId}</if>
            <if test="solutionTypeId != null ">
                and a.solution_type_id in (
                    WITH recursive tmp_type AS (
                        SELECT * FROM solution_type WHERE solution_type_id = #{solutionTypeId}
                        UNION ALL
                        SELECT a.*
                        FROM solution_type a
                        INNER JOIN tmp_type b ON a.parent_id = b.solution_type_id
                    )
                    SELECT solution_type_id FROM tmp_type
                )
            </if>
            <if test="newsInformationSource != null  and newsInformationSource != ''">
                and a.news_information_source = #{newsInformationSource}
            </if>
            <if test="newsInformationAuthor != null  and newsInformationAuthor != ''">
                and a.news_information_author = #{newsInformationAuthor}
            </if>
            <if test="newsInformationName != null  and newsInformationName != ''">
                and a.news_information_name like concat('%', #{newsInformationName}, '%')
            </if>
            <if test="newsInformationIntroduction != null  and newsInformationIntroduction != ''">
                and a.news_information_introduction = #{newsInformationIntroduction}
            </if>
            <if test="newsInformationImg != null  and newsInformationImg != ''">
                and a.news_information_img = #{newsInformationImg}
            </if>
            <if test="newsInformationContent != null  and newsInformationContent != ''">
                and a.news_information_content = #{newsInformationContent}
            </if>
            <if test="newsInformationFrequency != null ">
                and a.news_information_frequency = #{newsInformationFrequency}
            </if>
            <if test="newsInformationDate != null ">and a.news_information_date = #{newsInformationDate}</if>
        </where>
        <choose>
			<when test="sort!=null and sort != ''">
				ORDER BY ${sort}
			</when>
			<otherwise>
				ORDER BY a.news_information_date DESC
			</otherwise>
		</choose>
    </select>

    <select id="selectNewsInformationByNewsInformationId" parameterType="Long" resultMap="NewsInformationResult">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Table_List"/>
        where a.news_information_id = #{newsInformationId}
    </select>

    <insert id="insertNewsInformation" parameterType="NewsInformation" useGeneratedKeys="true"
            keyProperty="newsInformationId">
        insert into news_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="newsInformationPlateId != null">news_information_plate_id,</if>
            <if test="solutionTypeId != null">solution_type_id,</if>
            <if test="newsInformationSource != null">news_information_source,</if>
            <if test="newsInformationAuthor != null">news_information_author,</if>
            <if test="newsInformationName != null">news_information_name,</if>
            <if test="newsInformationIntroduction != null">news_information_introduction,</if>
            <if test="newsInformationImg != null">news_information_img,</if>
            <if test="newsInformationContent != null">news_information_content,</if>
            <if test="newsInformationFrequency != null">news_information_frequency,</if>
            <if test="newsInformationDate != null">news_information_date,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="newsInformationPlateId != null">#{newsInformationPlateId},</if>
            <if test="solutionTypeId != null">#{solutionTypeId},</if>
            <if test="newsInformationSource != null">#{newsInformationSource},</if>
            <if test="newsInformationAuthor != null">#{newsInformationAuthor},</if>
            <if test="newsInformationName != null">#{newsInformationName},</if>
            <if test="newsInformationIntroduction != null">#{newsInformationIntroduction},</if>
            <if test="newsInformationImg != null">#{newsInformationImg},</if>
            <if test="newsInformationContent != null">#{newsInformationContent},</if>
            <if test="newsInformationFrequency != null">#{newsInformationFrequency},</if>
            <if test="newsInformationDate != null">#{newsInformationDate},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateNewsInformation" parameterType="NewsInformation">
        update news_information
        <trim prefix="SET" suffixOverrides=",">
            <if test="newsInformationPlateId != null">news_information_plate_id = #{newsInformationPlateId},</if>
            <if test="solutionTypeId != null">solution_type_id = #{solutionTypeId},</if>
            <if test="newsInformationSource != null">news_information_source = #{newsInformationSource},</if>
            <if test="newsInformationAuthor != null">news_information_author = #{newsInformationAuthor},</if>
            <if test="newsInformationName != null">news_information_name = #{newsInformationName},</if>
            <if test="newsInformationIntroduction != null">news_information_introduction =
                #{newsInformationIntroduction},
            </if>
            <if test="newsInformationImg != null">news_information_img = #{newsInformationImg},</if>
            <if test="newsInformationContent != null">news_information_content = #{newsInformationContent},</if>
            <if test="newsInformationFrequency != null">news_information_frequency = #{newsInformationFrequency},</if>
            <if test="newsInformationDate != null">news_information_date = #{newsInformationDate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where news_information_id = #{newsInformationId}
    </update>

    <delete id="deleteNewsInformationByNewsInformationId" parameterType="Long">
        delete from news_information where news_information_id = #{newsInformationId}
    </delete>

    <delete id="deleteNewsInformationByNewsInformationIds" parameterType="String">
        delete from news_information where news_information_id in
        <foreach item="newsInformationId" collection="array" open="(" separator="," close=")">
            #{newsInformationId}
        </foreach>
    </delete>
</mapper>