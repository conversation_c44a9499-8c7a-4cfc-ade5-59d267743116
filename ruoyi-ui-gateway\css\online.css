[v-cloak]{
    display: none;
}
.chat-box {
    position: fixed;
    width: 560px;
    height: 600px;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    z-index: 9999;
    border-radius: 4px;
    overflow: hidden;
    /*box-shadow: 5px 5px 10px 10px #eee;*/
    background: #F3F7F8;
    border: 1px solid #eee;
}
.chat-box .chat-head{
    padding:0 20px 0 30px;
    height: 49px;
    /* display: flex; */
    justify-content: space-between;
    align-items: center;
    background-color: #3986E9;
    border-bottom: 1px solid #EEEEEE;
    color: #fff;
}
.chat-box .chat-head .name{
    color: #fff;
    font-size: 20px;
    line-height: 20px;
}
.chat-box .chat-head .chat-close{
    width: 12px;
    height: 12px;
    cursor: pointer;
}
.chat-box .chat-head .detail-show{
    width: 6px;
    height: 12px;
    margin-left: 24px;
    cursor: pointer;
}
.chat-box .chat-content{
    padding: 0 7px 0 11px;
    height: 420px;
    overflow: auto;
}
.chat-box .chat-content ul{
    padding: 10px 0;
}
.chat-box .chat-content li .item{
    display: flex;
}
.chat-box .chat-content li .head{
    width: 46px;
    height: 46px;
    border-radius: 50%;
    margin: 0 6px;
}
.chat-box .chat-content li .msg{
    padding: 4px 8px;
    font-size: 14px;
    line-height: 20px;
    max-width: 300px;
    background: #fff;
    height: auto;
    border-radius: 4px;
}
.chat-box .chat-content li .image-box{
    max-width: 350px;
    height: 160px;
    display: flex;
    justify-content:  center;
    overflow: hidden;
}
.chat-box .chat-content li .image{
    max-width: 1000%;
    height: 160px;
}
.chat-box .chat-content li .video{
    max-width: 350px;
    height: 230px;
}
.chat-box .chat-content li .audio{
    max-width: 350px;
}
.chat-box .chat-content li .file{
    display: block;
    background: #fff;
    border: 1px solid #eee;
    border-radius: 5px;
    padding: 5px 10px;
    padding-left: 50px;
    height: 50px;
    width: 180px;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: wrap;
    word-break: break-all;
    background-image: url("../../images/chat/file_download.png");
    background-size: 40px;
    background-position: 5px center;
    background-repeat: no-repeat;
}
.chat-box .chat-content .receive .item{
    justify-content: flex-start;
    align-items: flex-start;
    margin-bottom: 10px;
}
.chat-box .chat-content .receive .flex{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.chat-box .chat-content .send .flex{
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}
.chat-box .chat-content .send .item{
    flex-direction: row-reverse;
    align-items: flex-start;
    margin-bottom: 10px;
}
.chat-box .chat-content .sendonly .flex{
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}
.chat-box .chat-content .send .msg{
    background: #1684FC;
    color: #fff;
}
.chat-box .chat-content .time{
    display: flex;
    justify-content: center;
}
.chat-box .chat-content .time .timer{
    padding: 2px 8px;
    font-size: 14px;
    line-height: 20px;
    border-radius: 4px;
    background: #DADADA;
    color: #fff;
}
.chat-box .import{
    height: 100px;
    position: relative;
    padding: 5px 15px;
    background: #fff;
}
.chat-box .import textarea{
    box-sizing: border-box;
    width: 100%;
    height: 110px;
    resize: none;
    background: #fff;
    border: none;
    outline: none;
    padding: 20px 16px;
    font-size: 14px;
    line-height: 20px;
}
.chat-box .import .entry{
    width: 66px;
    height: 26px;
    position: absolute;
    bottom: 6px;
    right: 14px;
    cursor: pointer;
}
.chat-box .update{
    height: 20px;
    padding: 2px 10px;
    display: flex;
    background: #fff;
}
.chat-box .update .update-item{
    margin: 0 10px;
}
.chat-box .update label{
    display: block;
    width: 16px;
    height: 16px;
    background: url(../images/chat/file.png);
    background-size: 100% 100%;
}
.chat-box .update input{
    display: none;
}
.chat-box .import .text-box{
    height: 70px;
    overflow-y: auto;
    border: none;
    outline: none;
}
.loading{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(0,0,0,0.4);
    height:100%;
    vertical-align: middle;
    border-radius: 1px;
    z-index: 10020;
    /* display:none */
}
.loading-item{
    width:2rem;
    height: 2rem;
    border: .1rem #fff solid;
    border-radius: 50%;
    -webkit-animation: rotation 1s ease-in-out infinite;
    -moz-animation: rotation 1s ease-in-out infinite;
    animation: rotation 1s ease-in-out infinite;
    position: absolute;
    top:50%;
    left:50%;
    margin-top:-.5rem;
    margin-left:-.5rem;
}
.loading-item:after{
    width: .15rem;
    height: .15rem;
    background-color: #fff;
    border-radius: 100%;
    position: absolute;
    content: "";
    left:.06rem;
    top:.1rem;

}
@-webkit-keyframes rotation{
    0%{-webkit-transform: rotate(0deg);}
    100%{-webkit-transform: rotate(360deg);}
}
@-moz-keyframes rotation{
    0%{-moz-transform: rotate(0deg);}
    100%{-moz-transform: rotate(360deg);}
}
@keyframes rotation{
    0%{transform: rotate(0deg);}
    100%{transform: rotate(360deg);}
}