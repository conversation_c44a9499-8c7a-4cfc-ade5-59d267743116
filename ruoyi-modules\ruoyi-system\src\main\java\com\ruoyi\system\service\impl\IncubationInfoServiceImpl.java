package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.IncubationInfoMapper;
import com.ruoyi.system.domain.IncubationInfo;
import com.ruoyi.system.service.IIncubationInfoService;

/**
 * 创业孵化信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-11
 */
@Service
public class IncubationInfoServiceImpl implements IIncubationInfoService
{
    @Autowired
    private IncubationInfoMapper incubationInfoMapper;

    /**
     * 查询创业孵化信息
     *
     * @param id 创业孵化信息主键
     * @return 创业孵化信息
     */
    @Override
    public IncubationInfo selectIncubationInfoById(Long id)
    {
        return incubationInfoMapper.selectIncubationInfoById(id);
    }

    /**
     * 查询创业孵化信息列表
     *
     * @param incubationInfo 创业孵化信息
     * @return 创业孵化信息
     */
    @Override
    public List<IncubationInfo> selectIncubationInfoList(IncubationInfo incubationInfo)
    {
        return incubationInfoMapper.selectIncubationInfoList(incubationInfo);
    }

    /**
     * 新增创业孵化信息
     *
     * @param incubationInfo 创业孵化信息
     * @return 结果
     */
    @Override
    public int insertIncubationInfo(IncubationInfo incubationInfo)
    {
        incubationInfo.setCreateTime(DateUtils.getNowDate());
        return incubationInfoMapper.insertIncubationInfo(incubationInfo);
    }

    /**
     * 修改创业孵化信息
     *
     * @param incubationInfo 创业孵化信息
     * @return 结果
     */
    @Override
    public int updateIncubationInfo(IncubationInfo incubationInfo)
    {
        incubationInfo.setUpdateTime(DateUtils.getNowDate());
        return incubationInfoMapper.updateIncubationInfo(incubationInfo);
    }

    /**
     * 批量删除创业孵化信息
     *
     * @param ids 需要删除的创业孵化信息主键
     * @return 结果
     */
    @Override
    public int deleteIncubationInfoByIds(Long[] ids)
    {
        return incubationInfoMapper.deleteIncubationInfoByIds(ids);
    }

    /**
     * 删除创业孵化信息信息
     *
     * @param id 创业孵化信息主键
     * @return 结果
     */
    @Override
    public int deleteIncubationInfoById(Long id)
    {
        return incubationInfoMapper.deleteIncubationInfoById(id);
    }
}
