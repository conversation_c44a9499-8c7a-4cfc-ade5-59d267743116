package com.ruoyi.system.domain;

import java.io.Serializable;
import java.util.Date;

/**
    * 发送信息表
    */
public class UserChatMessage implements Serializable {
    private Long id;

    /**
    * 发送人手机号
    */
    private String sendUserName;

    /**
    * 接收方手机号或群聊号
    */
    private String targetId;

    /**
    * 发送内容（内容对媒体2选一）
    */
    private String content;

    /**
    *发送的多媒体信息（内容对媒体2选一）
    */
    private String mediaUrl;

    /**
    * 消息类型 0 文字消息 1图片消息 2视频 3音频 4 文件
     *
    */
    private Integer contentType;

    /**
    * 所属会话类型 单聊	1 群聊	3 聊天室	4 系统	6
    */
    private Integer conversationType;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 创建时间的时间戳
    */
    private Long createTimestamp;

    /**
    * 属于谁的聊天信息（单聊使用）
    */
    private String userName;

    /**
    * 会话标识（手机号 或 群聊号）
    */
    private String conversationId;

    /**
    * 0 未删除 1已删除
    */
    private Integer deleteFlag;

    /**
    * 原评论id（单聊时，评论存两边，发送方一遍，接收方一遍，方便拉取评论列表）
    */
    private Long originalId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 发送人头像 名称
     */
    private String conversationName;
    private String conversationNameTwo;

    /**
     * 接收人头像 名称
     */
    private String conversationPic;
    private String conversationPicTwo;

    private Boolean sendable = true;


    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSendUserName() {
        return sendUserName;
    }

    public void setSendUserName(String sendUserName) {
        this.sendUserName = sendUserName;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }


    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public Integer getConversationType() {
        return conversationType;
    }

    public void setConversationType(Integer conversationType) {
        this.conversationType = conversationType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Long createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Long getOriginalId() {
        return originalId;
    }

    public void setOriginalId(Long originalId) {
        this.originalId = originalId;
    }

    public String getConversationName() {
        return conversationName;
    }

    public void setConversationName(String conversationName) {
        this.conversationName = conversationName;
    }

    public String getConversationPic() {
        return conversationPic;
    }

    public void setConversationPic(String conversationPic) {
        this.conversationPic = conversationPic;
    }

    public String getConversationPicTwo() {
        return conversationPicTwo;
    }

    public void setConversationPicTwo(String conversationPicTwo) {
        this.conversationPicTwo = conversationPicTwo;
    }

    public String getConversationNameTwo() {
        return conversationNameTwo;
    }

    public void setConversationNameTwo(String conversationNameTwo) {
        this.conversationNameTwo = conversationNameTwo;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getMediaUrl() {
        return mediaUrl;
    }

    public void setMediaUrl(String mediaUrl) {
        this.mediaUrl = mediaUrl;
    }

    public Boolean getSendable() {
        return sendable;
    }

    public void setSendable(Boolean sendable) {
        this.sendable = sendable;
    }
}