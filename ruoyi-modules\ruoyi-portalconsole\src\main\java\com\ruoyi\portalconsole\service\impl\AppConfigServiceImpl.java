package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.AppConfigMapper;
import com.ruoyi.portalconsole.domain.AppConfig;
import com.ruoyi.portalconsole.service.IAppConfigService;

/**
 * 应用配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-22
 */
@Service
public class AppConfigServiceImpl implements IAppConfigService 
{
    @Autowired
    private AppConfigMapper appConfigMapper;

    /**
     * 查询应用配置
     * 
     * @param appConfigId 应用配置主键
     * @return 应用配置
     */
    @Override
    public AppConfig selectAppConfigByAppConfigId(Long appConfigId)
    {
        return appConfigMapper.selectAppConfigByAppConfigId(appConfigId);
    }

    /**
     * 查询应用配置列表
     * 
     * @param appConfig 应用配置
     * @return 应用配置
     */
    @Override
    public List<AppConfig> selectAppConfigList(AppConfig appConfig)
    {
        return appConfigMapper.selectAppConfigList(appConfig);
    }

    /**
     * 新增应用配置
     * 
     * @param appConfig 应用配置
     * @return 结果
     */
    @Override
    public int insertAppConfig(AppConfig appConfig)
    {
        appConfig.setCreateTime(DateUtils.getNowDate());
        return appConfigMapper.insertAppConfig(appConfig);
    }

    /**
     * 修改应用配置
     * 
     * @param appConfig 应用配置
     * @return 结果
     */
    @Override
    public int updateAppConfig(AppConfig appConfig)
    {
        appConfig.setUpdateTime(DateUtils.getNowDate());
        return appConfigMapper.updateAppConfig(appConfig);
    }

    /**
     * 批量删除应用配置
     * 
     * @param appConfigIds 需要删除的应用配置主键
     * @return 结果
     */
    @Override
    public int deleteAppConfigByAppConfigIds(Long[] appConfigIds)
    {
        return appConfigMapper.deleteAppConfigByAppConfigIds(appConfigIds);
    }

    /**
     * 删除应用配置信息
     * 
     * @param appConfigId 应用配置主键
     * @return 结果
     */
    @Override
    public int deleteAppConfigByAppConfigId(Long appConfigId)
    {
        return appConfigMapper.deleteAppConfigByAppConfigId(appConfigId);
    }
}
