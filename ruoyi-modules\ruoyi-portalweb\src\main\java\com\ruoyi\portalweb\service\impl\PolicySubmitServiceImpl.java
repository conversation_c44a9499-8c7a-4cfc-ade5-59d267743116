package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.portalweb.api.domain.PolicySubmit;
import com.ruoyi.portalweb.mapper.PolicySubmitMapper;
import com.ruoyi.portalweb.service.IPolicySubmitService;
import com.ruoyi.portalweb.vo.PolicySubmitVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 政策申报Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class PolicySubmitServiceImpl implements IPolicySubmitService
{
    @Autowired
    private PolicySubmitMapper policySubmitMapper;

    /**
     * 查询政策申报
     * 
     * @param policySubmitId 政策申报主键
     * @return 政策申报
     */
    @Override
    public PolicySubmit selectPolicySubmitByPolicySubmitId(Long policySubmitId)
    {
        return policySubmitMapper.selectPolicySubmitByPolicySubmitId(policySubmitId);
    }

    /**
     * 查询政策申报列表
     * 
     * @param policySubmit 政策申报
     * @return 政策申报
     */
    @Override
    public List<PolicySubmitVO> selectPolicySubmitList(PolicySubmitVO policySubmit)
    {
        return policySubmitMapper.selectPolicySubmitList(policySubmit);
    }

    /**
     * 查询政策申报公司名称列表
     *
     * @param policySubmit 政策申报
     * @return 政策申报公司名称
     */
    @Override
    public List<String> selectPolicySubmitByPolicySubmitCompanyList(PolicySubmitVO policySubmit) {
        return policySubmitMapper.selectPolicySubmitCompanyList(policySubmit);
    }

    /**
     * 新增政策申报
     * 
     * @param policySubmit 政策申报
     * @return 结果
     */
    @Override
    public int insertPolicySubmit(PolicySubmit policySubmit)
    {
        policySubmit.setCreateTime(DateUtils.getNowDate());
        return policySubmitMapper.insertPolicySubmit(policySubmit);
    }

    /**
     * 修改政策申报
     * 
     * @param policySubmit 政策申报
     * @return 结果
     */
    @Override
    public int updatePolicySubmit(PolicySubmit policySubmit)
    {
        policySubmit.setUpdateTime(DateUtils.getNowDate());
        return policySubmitMapper.updatePolicySubmit(policySubmit);
    }

    /**
     * 批量删除政策申报
     * 
     * @param policySubmitIds 需要删除的政策申报主键
     * @return 结果
     */
    @Override
    public int deletePolicySubmitByPolicySubmitIds(Long[] policySubmitIds)
    {
        return policySubmitMapper.deletePolicySubmitByPolicySubmitIds(policySubmitIds);
    }

    /**
     * 删除政策申报信息
     * 
     * @param policySubmitId 政策申报主键
     * @return 结果
     */
    @Override
    public int deletePolicySubmitByPolicySubmitId(Long policySubmitId)
    {
        return policySubmitMapper.deletePolicySubmitByPolicySubmitId(policySubmitId);
    }
}
