package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 入驻工厂对象 settled_factory
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
public class SettledFactory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 企业名称 */
    @Excel(name = "企业名称")
    private String companyName;

    /** 行业 */
    @Excel(name = "行业")
    private String industry;

    /** 工厂类型 */
    @Excel(name = "工厂类型", readConverterExp = "1=生产型工厂,2=加工型工厂,3=组装型工厂,4=研发型工厂,5=综合型工厂")
    private String factoryType;

    /** 公司地址 */
    @Excel(name = "公司地址")
    private String companyAddress;

    /** 社会信用代码 */
    @Excel(name = "社会信用代码")
    private String socialCreditCode;

    /** 经营范围 */
    @Excel(name = "经营范围")
    private String businessScope;

    /** 注册资本 */
    @Excel(name = "注册资本")
    private String registeredCapital;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 二维码 */
    @Excel(name = "二维码")
    private String qrCode;

    /** 入驻状态（0待审核 1已入驻 2未通过） */
    @Excel(name = "入驻状态", readConverterExp = "0=待审核,1=已入驻,2=未通过")
    private String settledStatus;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCompanyName(String companyName)
    {
        this.companyName = companyName;
    }

    public String getCompanyName()
    {
        return companyName;
    }
    public void setIndustry(String industry)
    {
        this.industry = industry;
    }

    public String getIndustry()
    {
        return industry;
    }
    public void setFactoryType(String factoryType)
    {
        this.factoryType = factoryType;
    }

    public String getFactoryType()
    {
        return factoryType;
    }
    public void setCompanyAddress(String companyAddress)
    {
        this.companyAddress = companyAddress;
    }

    public String getCompanyAddress()
    {
        return companyAddress;
    }
    public void setSocialCreditCode(String socialCreditCode)
    {
        this.socialCreditCode = socialCreditCode;
    }

    public String getSocialCreditCode()
    {
        return socialCreditCode;
    }
    public void setBusinessScope(String businessScope)
    {
        this.businessScope = businessScope;
    }

    public String getBusinessScope()
    {
        return businessScope;
    }
    public void setRegisteredCapital(String registeredCapital)
    {
        this.registeredCapital = registeredCapital;
    }

    public String getRegisteredCapital()
    {
        return registeredCapital;
    }
    public void setContactPhone(String contactPhone)
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone()
    {
        return contactPhone;
    }
    public void setQrCode(String qrCode)
    {
        this.qrCode = qrCode;
    }

    public String getQrCode()
    {
        return qrCode;
    }
    public void setSettledStatus(String settledStatus)
    {
        this.settledStatus = settledStatus;
    }

    public String getSettledStatus()
    {
        return settledStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("companyName", getCompanyName())
                .append("industry", getIndustry())
                .append("factoryType", getFactoryType())
                .append("companyAddress", getCompanyAddress())
                .append("socialCreditCode", getSocialCreditCode())
                .append("businessScope", getBusinessScope())
                .append("registeredCapital", getRegisteredCapital())
                .append("contactPhone", getContactPhone())
                .append("qrCode", getQrCode())
                .append("settledStatus", getSettledStatus())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
