package com.ruoyi.portalconsole.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.EcologyCategory;
import com.ruoyi.portalconsole.service.IEcologyCategoryService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 生态类别Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/EcologyCategory")
public class EcologyCategoryController extends BaseController
{
    @Autowired
    private IEcologyCategoryService ecologyCategoryService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询生态类别列表
     */
    @RequiresPermissions("portalconsole:EcologyCategory:list")
    @GetMapping("/list")
    public TableDataInfo list(EcologyCategory ecologyCategory)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<EcologyCategory> list = ecologyCategoryService.selectEcologyCategoryList(ecologyCategory);
        return getDataTable(list);
    }

    /**
     * 导出生态类别列表
     */
    @RequiresPermissions("portalconsole:EcologyCategory:export")
    @Log(title = "生态类别", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EcologyCategory ecologyCategory)
    {
        List<EcologyCategory> list = ecologyCategoryService.selectEcologyCategoryList(ecologyCategory);
        ExcelUtil<EcologyCategory> util = new ExcelUtil<EcologyCategory>(EcologyCategory.class);
        util.exportExcel(response, list, "生态类别数据");
    }

    /**
     * 获取生态类别详细信息
     */
    @RequiresPermissions("portalconsole:EcologyCategory:query")
    @GetMapping(value = "/{ecologyCategoryId}")
    public AjaxResult getInfo(@PathVariable("ecologyCategoryId") Long ecologyCategoryId)
    {
        return success(ecologyCategoryService.selectEcologyCategoryByEcologyCategoryId(ecologyCategoryId));
    }

    /**
     * 新增生态类别
     */
    @RequiresPermissions("portalconsole:EcologyCategory:add")
    @Log(title = "生态类别", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EcologyCategory ecologyCategory)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        ecologyCategory.setUpdateBy(userNickName.getData());
        ecologyCategory.setCreateBy(userNickName.getData());
        return toAjax(ecologyCategoryService.insertEcologyCategory(ecologyCategory));
    }

    /**
     * 修改生态类别
     */
    @RequiresPermissions("portalconsole:EcologyCategory:edit")
    @Log(title = "生态类别", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EcologyCategory ecologyCategory)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        ecologyCategory.setUpdateBy(userNickName.getData());
        return toAjax(ecologyCategoryService.updateEcologyCategory(ecologyCategory));
    }

    /**
     * 删除生态类别
     */
    @RequiresPermissions("portalconsole:EcologyCategory:remove")
    @Log(title = "生态类别", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ecologyCategoryIds}")
    public AjaxResult remove(@PathVariable Long[] ecologyCategoryIds)
    {
        return toAjax(ecologyCategoryService.deleteEcologyCategoryByEcologyCategoryIds(ecologyCategoryIds));
    }
}
