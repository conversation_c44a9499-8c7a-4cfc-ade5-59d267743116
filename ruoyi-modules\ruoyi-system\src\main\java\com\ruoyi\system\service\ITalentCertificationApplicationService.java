package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.TalentCertificationApplication;

/**
 * 衡水市职业技能鉴定中心申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
public interface ITalentCertificationApplicationService 
{
    /**
     * 查询衡水市职业技能鉴定中心申请
     * 
     * @param id 衡水市职业技能鉴定中心申请主键
     * @return 衡水市职业技能鉴定中心申请
     */
    public TalentCertificationApplication selectTalentCertificationApplicationById(Long id);

    /**
     * 查询衡水市职业技能鉴定中心申请列表
     * 
     * @param talentCertificationApplication 衡水市职业技能鉴定中心申请
     * @return 衡水市职业技能鉴定中心申请集合
     */
    public List<TalentCertificationApplication> selectTalentCertificationApplicationList(TalentCertificationApplication talentCertificationApplication);

    /**
     * 新增衡水市职业技能鉴定中心申请
     * 
     * @param talentCertificationApplication 衡水市职业技能鉴定中心申请
     * @return 结果
     */
    public int insertTalentCertificationApplication(TalentCertificationApplication talentCertificationApplication);

    /**
     * 修改衡水市职业技能鉴定中心申请
     * 
     * @param talentCertificationApplication 衡水市职业技能鉴定中心申请
     * @return 结果
     */
    public int updateTalentCertificationApplication(TalentCertificationApplication talentCertificationApplication);

    /**
     * 批量删除衡水市职业技能鉴定中心申请
     * 
     * @param ids 需要删除的衡水市职业技能鉴定中心申请主键集合
     * @return 结果
     */
    public int deleteTalentCertificationApplicationByIds(Long[] ids);

    /**
     * 删除衡水市职业技能鉴定中心申请信息
     * 
     * @param id 衡水市职业技能鉴定中心申请主键
     * @return 结果
     */
    public int deleteTalentCertificationApplicationById(Long id);
}
