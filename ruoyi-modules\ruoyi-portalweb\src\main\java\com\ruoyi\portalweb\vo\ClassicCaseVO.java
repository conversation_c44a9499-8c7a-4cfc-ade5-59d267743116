package com.ruoyi.portalweb.vo;


import com.ruoyi.portalweb.api.domain.ClassicCase;
import io.swagger.annotations.ApiModelProperty;

/**
 * 典型案例对象
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public class ClassicCaseVO extends ClassicCase {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "方案类型")
    private String solutionTypeName;
    @ApiModelProperty(value = "关键字")
    private String keywords;
    @ApiModelProperty(value = "服务范围")
    private String category;
    
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getSolutionTypeName() {
        return solutionTypeName;
    }

    public void setSolutionTypeName(String solutionTypeName) {
        this.solutionTypeName = solutionTypeName;
    }

}
