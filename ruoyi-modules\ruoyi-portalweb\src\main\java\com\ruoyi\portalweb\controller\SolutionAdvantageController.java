package com.ruoyi.portalweb.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.SolutionAdvantage;
import com.ruoyi.portalweb.service.ISolutionAdvantageService;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 解决方案方案优势Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/SolutionAdvantage")
public class SolutionAdvantageController extends BaseController
{
    @Autowired
    private ISolutionAdvantageService solutionAdvantageService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询解决方案方案优势列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SolutionAdvantage solutionAdvantage)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<SolutionAdvantage> list = solutionAdvantageService.selectSolutionAdvantageList(solutionAdvantage);
        return getDataTable(list);
    }

    /**
     * 导出解决方案方案优势列表
     */
    @Log(title = "解决方案方案优势", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SolutionAdvantage solutionAdvantage)
    {
        List<SolutionAdvantage> list = solutionAdvantageService.selectSolutionAdvantageList(solutionAdvantage);
        ExcelUtil<SolutionAdvantage> util = new ExcelUtil<SolutionAdvantage>(SolutionAdvantage.class);
        util.exportExcel(response, list, "解决方案方案优势数据");
    }

    /**
     * 获取解决方案方案优势详细信息
     */
    @GetMapping(value = "/{solutionAdvantageId}")
    public AjaxResult getInfo(@PathVariable("solutionAdvantageId") Long solutionAdvantageId)
    {
        return success(solutionAdvantageService.selectSolutionAdvantageBySolutionAdvantageId(solutionAdvantageId));
    }

    /**
     * 新增解决方案方案优势
     */
    @Log(title = "解决方案方案优势", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SolutionAdvantage solutionAdvantage)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        solutionAdvantage.setUpdateBy(userNickName.getData());
        solutionAdvantage.setCreateBy(userNickName.getData());
        return toAjax(solutionAdvantageService.insertSolutionAdvantage(solutionAdvantage));
    }

    /**
     * 修改解决方案方案优势
     */
    @Log(title = "解决方案方案优势", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SolutionAdvantage solutionAdvantage)
    {
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        solutionAdvantage.setUpdateBy(userNickName.getData());
        return toAjax(solutionAdvantageService.updateSolutionAdvantage(solutionAdvantage));
    }

    /**
     * 删除解决方案方案优势
     */
    @Log(title = "解决方案方案优势", businessType = BusinessType.DELETE)
	@DeleteMapping("/{solutionAdvantageIds}")
    public AjaxResult remove(@PathVariable Long[] solutionAdvantageIds)
    {
        return toAjax(solutionAdvantageService.deleteSolutionAdvantageBySolutionAdvantageIds(solutionAdvantageIds));
    }
}
