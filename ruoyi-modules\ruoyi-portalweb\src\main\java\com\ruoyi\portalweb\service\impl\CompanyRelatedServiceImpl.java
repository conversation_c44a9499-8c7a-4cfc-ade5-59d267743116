package com.ruoyi.portalweb.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.portalweb.api.enums.MyFavoriteStatus;
import com.ruoyi.portalweb.api.enums.MyFavoriteType;
import com.ruoyi.portalweb.mapper.MyFavoriteMapper;
import com.ruoyi.portalweb.vo.CompanyRelatedVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalweb.mapper.CompanyRelatedMapper;
import com.ruoyi.portalweb.api.domain.CompanyRelated;
import com.ruoyi.portalweb.service.ICompanyRelatedService;

/**
 * 关联企业信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
@Service
public class CompanyRelatedServiceImpl implements ICompanyRelatedService 
{
    @Autowired
    private CompanyRelatedMapper companyRelatedMapper;
    @Autowired
    private MyFavoriteMapper myFavoriteMapper;

    /**
     * 查询关联企业信息
     * 
     * @param companyRelatedId 关联企业信息主键
     * @return 关联企业信息
     */
    @Override
    public CompanyRelated selectCompanyRelatedByCompanyRelatedId(Long companyRelatedId)
    {
        return companyRelatedMapper.selectCompanyRelatedByCompanyRelatedId(companyRelatedId);
    }

    @Override
    public CompanyRelated getInfoDetail(Long companyRelatedId) {
        companyRelatedMapper.addCompanyRelatedViewCount(companyRelatedId);
       return this.selectCompanyRelatedByCompanyRelatedId(companyRelatedId);
    }

    /**
     * 查询关联企业信息列表
     * 
     * @param companyRelated 关联企业信息
     * @return 关联企业信息
     */
    @Override
    public List<CompanyRelated> selectCompanyRelatedList(CompanyRelatedVO companyRelated)
    {
        return companyRelatedMapper.selectCompanyRelatedList(companyRelated);
    }

    @Override
    public List<CompanyRelated> selectCompanyRelatedByCompanyName(String companyName) {
        return companyRelatedMapper.selectCompanyRelatedByCompanyName(companyName);
    }

    /**
     * 新增关联企业信息
     * 
     * @param companyRelated 关联企业信息
     * @return 结果
     */
    @Override
    public int insertCompanyRelated(CompanyRelated companyRelated)
    {
        companyRelated.setCreateTime(DateUtils.getNowDate());
        return companyRelatedMapper.insertCompanyRelated(companyRelated);
    }

    /**
     * 修改关联企业信息
     * 
     * @param companyRelated 关联企业信息
     * @return 结果
     */
    @Override
    public int updateCompanyRelated(CompanyRelated companyRelated)
    {
        companyRelated.setUpdateTime(DateUtils.getNowDate());
        return companyRelatedMapper.updateCompanyRelated(companyRelated);
    }

    /**
     * 批量删除关联企业信息
     * 
     * @param companyRelatedIds 需要删除的关联企业信息主键
     * @return 结果
     */
    @Override
    public int deleteCompanyRelatedByCompanyRelatedIds(Long[] companyRelatedIds)
    {
        int i =  companyRelatedMapper.deleteCompanyRelatedByCompanyRelatedIds(companyRelatedIds);
        for (Long id : companyRelatedIds) {
            try {
                myFavoriteMapper.updateMyFavoriteStatus(id, MyFavoriteType.DEMAND.getType(), MyFavoriteStatus.INVALID.getValue());
            }catch(Exception e){
                e.printStackTrace();
            }
        }
        return i;
    }

    /**
     * 删除关联企业信息信息
     * 
     * @param companyRelatedId 关联企业信息主键
     * @return 结果
     */
    @Override
    public int deleteCompanyRelatedByCompanyRelatedId(Long companyRelatedId)
    {
        int i = companyRelatedMapper.deleteCompanyRelatedByCompanyRelatedId(companyRelatedId);
        try {
            myFavoriteMapper.updateMyFavoriteStatus(companyRelatedId, MyFavoriteType.DEMAND.getType(), MyFavoriteStatus.INVALID.getValue());
        }catch(Exception e){
            e.printStackTrace();
        }
        return i;
    }
}
