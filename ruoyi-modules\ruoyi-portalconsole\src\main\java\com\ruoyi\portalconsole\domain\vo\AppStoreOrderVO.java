package com.ruoyi.portalconsole.domain.vo;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.portalconsole.domain.AppStoreOrder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 应用商店订单对象 app_store_order
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
public class AppStoreOrderVO extends AppStoreOrder {
    private static final long serialVersionUID = 1L;

    /**
     * 销售公司名称
     */
    @Excel(name = "销售公司名称")
    private String saleCompanyName;

    /**
     * 购买公司名称
     */
    @Excel(name = "购买公司名称")
    private String buyCompanyName;

    public String getSaleCompanyName() {
        return saleCompanyName;
    }

    public void setSaleCompanyName(String saleCompanyName) {
        this.saleCompanyName = saleCompanyName;
    }

    public String getBuyCompanyName() {
        return buyCompanyName;
    }

    public void setBuyCompanyName(String buyCompanyName) {
        this.buyCompanyName = buyCompanyName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("appStoreOrderId", getAppStoreOrderId())
                .append("appStoreOrderNo", getAppStoreOrderNo())
                .append("saleCompanyId", getSaleCompanyId())
                .append("buyCompanyId", getBuyCompanyId())
                .append("orderTime", getOrderTime())
                .append("appStorePrice", getAppStorePrice())
                .append("saleCompanyName", getSaleCompanyName())
                .append("buyCompanyName", getBuyCompanyName())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
