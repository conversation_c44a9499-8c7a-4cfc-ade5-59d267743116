package com.ruoyi.portalconsole.service.impl;

import java.util.List;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalconsole.domain.Message;
import com.ruoyi.portalconsole.domain.vo.DemandVO;
import com.ruoyi.portalconsole.enums.MessageStatus;
import com.ruoyi.portalconsole.enums.Title;
import com.ruoyi.portalconsole.service.IMessageService;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.DemandMapper;
import com.ruoyi.portalconsole.domain.Demand;
import com.ruoyi.portalconsole.service.IDemandService;

/**
 * 服务需求(NEW)Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class DemandServiceImpl implements IDemandService 
{
    @Autowired
    private DemandMapper demandMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private IMessageService messageService;

    /**
     * 查询服务需求(NEW)
     * 
     * @param id 服务需求(NEW)主键
     * @return 服务需求(NEW)
     */
    @Override
    public DemandVO selectDemandById(Long id)
    {
        return demandMapper.selectDemandById(id);
    }

    /**
     * 查询服务需求(NEW)列表
     * 
     * @param demand 服务需求(NEW)
     * @return 服务需求(NEW)
     */
    @Override
    public List<DemandVO> selectDemandList(Demand demand)
    {
        return demandMapper.selectDemandList(demand);
    }

    /**
     * 新增服务需求(NEW)
     * 
     * @param demand 服务需求(NEW)
     * @return 结果
     */
    @Override
    public int insertDemand(Demand demand)
    {

        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        demand.setUpdateBy(userNickName.getData());
        demand.setCreateBy(userNickName.getData());
        demand.setPublisher(userNickName.getData());
        return demandMapper.insertDemand(demand);
    }

    /**
     * 修改服务需求(NEW)
     * 
     * @param demand 服务需求(NEW)
     * @return 结果
     */
    @Override
    public int updateDemand(Demand demand)
    {
        // 获取用户信息存入修改需求数据
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        demand.setUpdateBy(userNickName.getData());
        return demandMapper.updateDemand(demand);
    }

    /**
     * 审核服务需求(NEW)
     *
     * @param demand 服务需求(NEW)
     * @return 结果
     */
    @Override
    public int auditDemand(Demand demand) {
        // 查询需求信息
        DemandVO demand1 = selectDemandById(demand.getId());

        // 修改审核状态
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        Demand audit = new Demand();
        audit.setUpdateBy(userNickName.getData());
        audit.setAuditStatus(demand.getAuditStatus());
        audit.setId(demand.getId());
        int i = updateDemand(audit);

        // 通知用户消息
        Message message = new Message().initData(Title.DEMAND.getName(),"您提交的"+ Title.DEMAND.getName() +"已审核", MessageStatus.UNREAD.getCode(),demand1.getMemberId());
        messageService.insertMessageByAsync(message);
        return i;
    }

    /**
     * 批量删除服务需求(NEW)
     * 
     * @param ids 需要删除的服务需求(NEW)主键
     * @return 结果
     */
    @Override
    public int deleteDemandByIds(Long[] ids)
    {
        return demandMapper.deleteDemandByIds(ids);
    }

    /**
     * 删除服务需求(NEW)信息
     * 
     * @param id 服务需求(NEW)主键
     * @return 结果
     */
    @Override
    public int deleteDemandById(Long id)
    {
        return demandMapper.deleteDemandById(id);
    }
}
