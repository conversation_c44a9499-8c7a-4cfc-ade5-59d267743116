package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.ApplicationFieldMapper;
import com.ruoyi.portalconsole.domain.ApplicationField;
import com.ruoyi.portalconsole.service.IApplicationFieldService;

/**
 * 应用领域Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@Service
public class ApplicationFieldServiceImpl implements IApplicationFieldService 
{
    @Autowired
    private ApplicationFieldMapper applicationFieldMapper;

    /**
     * 查询应用领域
     * 
     * @param applicationFieldId 应用领域主键
     * @return 应用领域
     */
    @Override
    public ApplicationField selectApplicationFieldByApplicationFieldId(Long applicationFieldId)
    {
        return applicationFieldMapper.selectApplicationFieldByApplicationFieldId(applicationFieldId);
    }

    /**
     * 查询应用领域列表
     * 
     * @param applicationField 应用领域
     * @return 应用领域
     */
    @Override
    public List<ApplicationField> selectApplicationFieldList(ApplicationField applicationField)
    {
        return applicationFieldMapper.selectApplicationFieldList(applicationField);
    }

    /**
     * 新增应用领域
     * 
     * @param applicationField 应用领域
     * @return 结果
     */
    @Override
    public int insertApplicationField(ApplicationField applicationField)
    {
        applicationField.setCreateTime(DateUtils.getNowDate());
        return applicationFieldMapper.insertApplicationField(applicationField);
    }

    /**
     * 修改应用领域
     * 
     * @param applicationField 应用领域
     * @return 结果
     */
    @Override
    public int updateApplicationField(ApplicationField applicationField)
    {
        applicationField.setUpdateTime(DateUtils.getNowDate());
        return applicationFieldMapper.updateApplicationField(applicationField);
    }

    /**
     * 批量删除应用领域
     * 
     * @param applicationFieldIds 需要删除的应用领域主键
     * @return 结果
     */
    @Override
    public int deleteApplicationFieldByApplicationFieldIds(Long[] applicationFieldIds)
    {
        return applicationFieldMapper.deleteApplicationFieldByApplicationFieldIds(applicationFieldIds);
    }

    /**
     * 删除应用领域信息
     * 
     * @param applicationFieldId 应用领域主键
     * @return 结果
     */
    @Override
    public int deleteApplicationFieldByApplicationFieldId(Long applicationFieldId)
    {
        return applicationFieldMapper.deleteApplicationFieldByApplicationFieldId(applicationFieldId);
    }
}
