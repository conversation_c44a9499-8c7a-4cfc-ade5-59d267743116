package com.ruoyi.portalweb.mapper;

import java.util.List;

import com.ruoyi.portalweb.api.domain.Member;
import com.ruoyi.portalweb.vo.MemberVO;
import org.apache.ibatis.annotations.Param;

/**
 * 会员Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface MemberMapper 
{
    /**
     * 查询会员
     * 
     * @param memberId 会员主键
     * @return 会员
     */
    public MemberVO selectMemberByMemberId(Long memberId);

    /**
     * 查询会员列表
     * 
     * @param member 会员
     * @return 会员集合
     */
    public List<MemberVO> selectMemberList(Member member);

    /**
     * 新增会员
     * 
     * @param member 会员
     * @return 结果
     */
    public int insertMember(Member member);

    /**
     * 修改会员
     * 
     * @param member 会员
     * @return 结果
     */
    public int updateMember(Member member);

    /**
     * 删除会员
     * 
     * @param memberId 会员主键
     * @return 结果
     */
    public int deleteMemberByMemberId(Long memberId);

    /**
     * 批量删除会员
     * 
     * @param memberIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMemberByMemberIds(Long[] memberIds);

    /**
     * 根据手机号查询会员信息
     */
	public MemberVO selectMemberByPhone(String memberPhone);

    public int updateMemberPassword(Member member);

    /**
     *  退出关联企业
     */
    public int quitCompanyRelated(@Param("memberId") Long memberId, @Param("companyRelatedId") Long companyRelatedId);
}
