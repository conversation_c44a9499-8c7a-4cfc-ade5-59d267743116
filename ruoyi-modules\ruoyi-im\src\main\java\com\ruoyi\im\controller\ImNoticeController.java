package com.ruoyi.im.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.im.api.domain.ImNotice;
import com.ruoyi.im.api.util.RongyunUtils;
import com.ruoyi.im.service.ImNoticeService;
import com.ruoyi.im.api.util.SqlUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

/**
 * @Author:micah
 **/


@RestController
@RequestMapping("/im/notice")
public class ImNoticeController {

    @Resource
    private ImNoticeService imNoticeService;

    @Resource
    private RongyunUtils rongyunUtils;

    /***
     * ImNotice分页条件搜索实现
     * @param imNotice
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/search/{page}/{size}" )
    public TableDataInfo findPage(@RequestBody(required = false) ImNotice imNotice, @PathVariable("page")  int page, @PathVariable("size")  int size, @RequestParam(value = "fields",required = false) String fields){
        Page<ImNotice> pageSearch = new Page<>(page,size);
        QueryWrapper<ImNotice> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        imNoticeService.page(pageSearch,wrapper);
        TableDataInfo tableDataInfo = new TableDataInfo(pageSearch.getRecords(), (int) pageSearch.getTotal());
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setMsg("success");
        return tableDataInfo;
    }

    /***
     * 多条件搜索数据
     * @param imNotice
     * @return
     */
    @PostMapping(value = "/search" )
    public R<List<ImNotice>> findList(@RequestBody(required = false)  ImNotice imNotice, @RequestParam(value = "fields",required = false) String fields){
        QueryWrapper<ImNotice> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        return R.ok(imNoticeService.list(wrapper)) ;
    }

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/{id}" )
    public R<Boolean> delete(@PathVariable("id") Long id){
        return R.ok(imNoticeService.removeById(id));
    }

    /***
     * 修改ImNotice数据
     * @param imNotice
     * @return
     */
    @PutMapping(value="/update")
    public R<Boolean> update(@RequestBody  ImNotice imNotice){
        return R.ok(imNoticeService.updateById(imNotice)) ;
    }

    /***
     * 根据ID查询ImNotice数据
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public R<ImNotice> findById(@PathVariable("id") Long id){
        return R.ok(imNoticeService.getById(id)) ;
    }

    /***
     * 根据ID批量删除ImNotice数据
     * @param opid
     * @return
     */
    @PostMapping("/batch/delete")
    public R<Boolean> batchDelete(@RequestParam("opid") String opid){
        return R.ok(imNoticeService.removeByIds(SqlUtils.str2LongList(opid,",")));
    }


    /**保存
     * @param imNotice
     * @return
     */
    @PostMapping(value = "/save" )
    public R<Boolean> save(@RequestBody ImNotice imNotice){
        if(imNoticeService.save(imNotice)){
            rongyunUtils.systemMessage("xipinadmin","RC:TxtMsg",imNotice.getTitle());
            return R.ok(true);
        }
        return R.ok(false);
    }
}
