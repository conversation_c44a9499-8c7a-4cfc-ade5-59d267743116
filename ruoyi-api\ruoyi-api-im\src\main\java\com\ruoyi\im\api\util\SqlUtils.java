package com.ruoyi.im.api.util;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * sql字符串拼接工具类
 * <AUTHOR>
 *
 */
public class SqlUtils {

    public static List<Long> str2LongList(String ids,String split) {
		List<Long> retVal = new ArrayList<Long>();
		if(StringUtils.isNotBlank(ids)){
			for(String id: StringUtils.split(ids,split)){
				retVal.add(Long.valueOf(id));
			}
		}
		return retVal;
    }
}
