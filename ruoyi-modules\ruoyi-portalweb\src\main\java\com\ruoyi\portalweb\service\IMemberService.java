package com.ruoyi.portalweb.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.portalweb.api.domain.Company;
import com.ruoyi.portalweb.api.domain.CompanyRelated;
import com.ruoyi.portalweb.api.domain.Member;
import com.ruoyi.portalweb.api.model.LoginMember;
import com.ruoyi.portalweb.vo.CompanyVO;
import com.ruoyi.portalweb.vo.MemberVO;

import com.ruoyi.system.api.domain.TalentInfoApi;

/**
 * 会员Service接口
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface IMemberService
{
    /**
     * 查询会员
     *
     * @param memberId 会员主键
     * @return 会员
     */
    public MemberVO selectMemberByMemberId(Long memberId);

    /**
     * 查询会员列表
     *
     * @param member 会员
     * @return 会员集合
     */
    public List<MemberVO> selectMemberList(Member member);

    /**
     * 新增会员
     *
     * @param member 会员
     * @return 结果
     */
    public int insertMember(Member member);

    /**
     * 企业认证
     */
    public int saveCompany(CompanyVO company);

    /**
     * 修改会员
     *
     * @param member 会员
     * @return 结果
     */
    public void updateMemberInfo(Member member);

    /**
     * 更新会员
     *
     * @param member 会员
     * @return 结果
     */
    public int updateMember(Member member);

    /**
     * 修改会员
     *
     * @return 结果
     */
    public int quitCompanyRelated();

    public void removeMemberFromCompanyRelated(List<Long> memberIds);

    /**
     * 审核会员
     *
     * @param member 会员
     * @return 结果
     */
   public int auditMember(Member member);

    /**
     * 批量删除会员
     *
     * @param memberIds 需要删除的会员主键集合
     * @return 结果
     */
    public int deleteMemberByMemberIds(Long[] memberIds);

    /**
     * 获取我的企业信息
     */
    public Company getMyCompany();

    /**
     * 获取我的企业信息
     */
    public CompanyRelated getMyCompanyRelated();

    /**
     * 删除会员信息
     *
     * @param memberId 会员主键
     * @return 结果
     */
    public int deleteMemberByMemberId(Long memberId);

    /**
     * 根据手机号，查询会员信息
     * @param memberphone
     * @return
     */
	public LoginMember selectMemberByPhone(String memberphone);

	/**
     * 根据手机号，查询会员信息
     */
	public LoginMember selectMemberBySmsCode(String memberphone);

	/**
	 * 会员注册
	 * @param member
	 * @return
	 */
	public LoginMember registerMember(Member member);

    /**
     * 修改用户密码
     *
     * @param member
     */
    public int updateMemberPassword(Member member);

    /**
     * 根据用户ID查询人才信息
     *
     * @param userId 用户ID
     * @return 人才信息
     */
    public TalentInfoApi selectTalentInfoByUserId(Long userId);
}
