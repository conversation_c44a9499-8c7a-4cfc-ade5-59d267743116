package com.ruoyi.portalweb.api.enums;

/**
 * 用户状态
 * 
 * <AUTHOR>
 */
public enum MemberStatus
{
    PENDING("0", "待审核"),
    OK("1", "正常"),
    DISABLED("2", "停用"),
    REJECTED("3", "驳回");

    private final String code;
    private final String info;

    MemberStatus(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
