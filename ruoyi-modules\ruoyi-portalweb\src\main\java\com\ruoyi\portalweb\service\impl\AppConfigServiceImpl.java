package com.ruoyi.portalweb.service.impl;

import java.util.List;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.portalweb.api.enums.OnShow;
import com.ruoyi.portalweb.mapper.AppStoreMapper;
import com.ruoyi.portalweb.vo.AppStoreVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalweb.mapper.AppConfigMapper;
import com.ruoyi.portalweb.api.domain.AppConfig;
import com.ruoyi.portalweb.service.IAppConfigService;

/**
 * 应用配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-22
 */
@Service
public class AppConfigServiceImpl implements IAppConfigService 
{
    @Autowired
    private AppConfigMapper appConfigMapper;

    @Autowired
    private AppStoreMapper appStoreMapper;

    /**
     * 查询应用配置
     * 
     * @param appConfigId 应用配置主键
     * @return 应用配置
     */
    @Override
    public AppConfig selectAppConfigByAppConfigId(Long appConfigId)
    {
        return appConfigMapper.selectAppConfigByAppConfigId(appConfigId);
    }

    /**
     * 查询应用配置列表
     * 
     * @param appConfig 应用配置
     * @return 应用配置
     */
    @Override
    public List<AppConfig> selectAppConfigList(AppConfig appConfig)
    {
        return appConfigMapper.selectAppConfigList(appConfig);
    }

    /**
     * 新增应用配置
     * 
     * @param appConfig 应用配置
     * @return 结果
     */
    @Override
    public long insertAppConfig(AppConfig appConfig)
    {
        AppStoreVO appStoreVO = appStoreMapper.selectAppStoreByAppStoreId(appConfig.getAppStoreId());
        if (appStoreVO == null) {
            throw new ServiceException("invalid app_store_id");
        }
        AppStoreVO updateAppStore = new AppStoreVO();
        updateAppStore.setAppStoreId(appStoreVO.getAppStoreId());
        updateAppStore.setOnShow(OnShow.PENDING.getValue());
        appStoreMapper.updateAppStore(updateAppStore);


        appConfig.setCreateTime(DateUtils.getNowDate());
        appConfigMapper.insertAppConfig(appConfig);
        return appConfig.getAppConfigId();
    }

    /**
     * 修改应用配置
     * 
     * @param appConfig 应用配置
     * @return 结果
     */
    @Override
    public int updateAppConfig(AppConfig appConfig)
    {
        appConfig.setUpdateTime(DateUtils.getNowDate());
        return appConfigMapper.updateAppConfig(appConfig);
    }

    /**
     * 修改应用配置
     *
     * @param appConfig 应用配置
     * @return 结果
     */
    @Override
    public int updateAppConfigByAppStoreId(AppConfig appConfig)
    {
        appConfig.setUpdateTime(DateUtils.getNowDate());
        return appConfigMapper.updateAppConfigByAppStoreId(appConfig);
    }

    /**
     * 批量删除应用配置
     * 
     * @param appConfigIds 需要删除的应用配置主键
     * @return 结果
     */
    @Override
    public int deleteAppConfigByAppConfigIds(Long[] appConfigIds)
    {
        return appConfigMapper.deleteAppConfigByAppConfigIds(appConfigIds);
    }

    /**
     * 删除应用配置信息
     * 
     * @param appConfigId 应用配置主键
     * @return 结果
     */
    @Override
    public int deleteAppConfigByAppConfigId(Long appConfigId)
    {
        return appConfigMapper.deleteAppConfigByAppConfigId(appConfigId);
    }

    @Override
    public AppConfig selectAppConfigByAppStoreId(Long appStoreId) {
        return appConfigMapper.selectAppConfigByAppStoreId(appStoreId);
    }
}
