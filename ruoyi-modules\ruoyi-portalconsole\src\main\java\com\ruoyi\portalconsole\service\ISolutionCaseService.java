package com.ruoyi.portalconsole.service;

import java.util.List;
import com.ruoyi.portalconsole.domain.SolutionCase;

/**
 * 解决方案实施案例Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface ISolutionCaseService 
{
    /**
     * 查询解决方案实施案例
     * 
     * @param solutionCaseId 解决方案实施案例主键
     * @return 解决方案实施案例
     */
    public SolutionCase selectSolutionCaseBySolutionCaseId(Long solutionCaseId);

    /**
     * 查询解决方案实施案例列表
     * 
     * @param solutionCase 解决方案实施案例
     * @return 解决方案实施案例集合
     */
    public List<SolutionCase> selectSolutionCaseList(SolutionCase solutionCase);

    /**
     * 新增解决方案实施案例
     * 
     * @param solutionCase 解决方案实施案例
     * @return 结果
     */
    public int insertSolutionCase(SolutionCase solutionCase);

    /**
     * 修改解决方案实施案例
     * 
     * @param solutionCase 解决方案实施案例
     * @return 结果
     */
    public int updateSolutionCase(SolutionCase solutionCase);

    /**
     * 批量删除解决方案实施案例
     * 
     * @param solutionCaseIds 需要删除的解决方案实施案例主键集合
     * @return 结果
     */
    public int deleteSolutionCaseBySolutionCaseIds(Long[] solutionCaseIds);

    /**
     * 删除解决方案实施案例信息
     * 
     * @param solutionCaseId 解决方案实施案例主键
     * @return 结果
     */
    public int deleteSolutionCaseBySolutionCaseId(Long solutionCaseId);
}
