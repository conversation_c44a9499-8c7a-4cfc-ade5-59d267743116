<template>
  <!-- 专家库 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="expertDatabaseName">
        <el-input
          v-model="queryParams.expertDatabaseName"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="职务" prop="expertDatabasePost">
        <el-input
          v-model="queryParams.expertDatabasePost"
          placeholder="请输入职务"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工作单位" prop="expertDatabaseWorkunit">
        <el-input
          v-model="queryParams.expertDatabaseWorkunit"
          placeholder="请输入工作单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="特约专家" prop="expertDatabaseExpert">
        <!-- <el-input
          v-model="queryParams.expertDatabaseExpert"
          placeholder="请输入特约专家"
          clearable
          @keyup.enter.native="handleQuery"
        /> -->
        <el-select v-model="queryParams.expertDatabaseExpert" placeholder="请选择特约专家" clearable size="small">
                <el-option v-for="dict in specialOptions" :key="dict.dictValue" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
      </el-form-item>
      <el-form-item label="研究领域" prop="expertDatabaseDirection">
        <el-input
          v-model="queryParams.expertDatabaseDirection"
          placeholder="请输入研究领域"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="平台对接人" prop="expertDatabaseContact">
        <el-input
          v-model="queryParams.expertDatabaseContact"
          placeholder="请输入平台对接人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="邮箱" prop="expertDatabaseEmail">
        <el-input
          v-model="queryParams.expertDatabaseEmail"
          placeholder="请输入邮箱"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="expertDatabasePhone">
        <el-input
          v-model="queryParams.expertDatabasePhone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="微信号" prop="expertDatabaseWechat">
        <el-input
          v-model="queryParams.expertDatabaseWechat"
          placeholder="请输入微信号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      
      <el-form-item label="技术类别" prop="expertDatabaseTechnology">
        <!-- <el-input
          v-model="queryParams.expertDatabaseTechnology"
          placeholder="请输入技术类别"
          clearable
          @keyup.enter.native="handleQuery"
        /> -->
        <el-select v-model="queryParams.expertDatabaseTechnology" placeholder="请选择技术类别">
          <el-option v-for="dict in dict.type.expert_database_technology" :key="dict.value" :label="dict.label"
            :value="dict.value"></el-option>
        </el-select>
      </el-form-item> 
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:ExpertDatabase:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['portalconsole:ExpertDatabase:edit']"
        >修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:ExpertDatabase:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:ExpertDatabase:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ExpertDatabaseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编码" align="center" prop="expertDatabaseId" />
      <el-table-column label="姓名" align="center" prop="expertDatabaseName" />
      <el-table-column label="工作单位" align="center" prop="expertDatabaseWorkunit" />
      <el-table-column label="主要成果" align="center" prop="expertDatabaseAchievement" />
      <el-table-column label="研究领域" align="center" prop="expertDatabaseDirection" />
      <el-table-column label="职务" align="center" prop="expertDatabasePost" />
      <el-table-column label="特约专家" align="center" prop="expertDatabaseExpert" />
      <el-table-column label="技术类别" align="center" prop="expertDatabaseTechnology" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.expert_database_technology" :value="scope.row.expertDatabaseTechnology"/>
        </template>
      </el-table-column>
      <!-- <el-table-column label="平台对接人" align="center" prop="expertDatabaseContact" />
      <el-table-column label="邮箱" align="center" prop="expertDatabaseEmail" />
      <el-table-column label="联系电话" align="center" prop="expertDatabasePhone" />
      <el-table-column label="微信号" align="center" prop="expertDatabaseWechat" />
      <el-table-column label="标签" align="center" prop="expertDatabaseTag" />
      
      <el-table-column label="专家简介" align="center" prop="expertDatabaseIntroduction" />
      <el-table-column label="头像" align="center" prop="expertDatabaseImghead" />
      <el-table-column label="专家介绍" align="center" prop="expertDatabaseIntroduce" />
      <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:ExpertDatabase:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:ExpertDatabase:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改专家库对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-row :span="24">
          <el-col :span="8">
            <el-form-item label="姓名" prop="expertDatabaseName">
              <el-input v-model="form.expertDatabaseName" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="特约专家" prop="expertDatabaseExpert">
              <!-- <el-input v-model="form.expertDatabaseExpert" placeholder="请输入特约专家" /> -->
              <el-select v-model="queryParams.special" placeholder="请选择特约专家" clearable size="small">
                <el-option v-for="dict in specialOptions" :key="dict.dictValue" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否展示" prop="opened">
              <el-select v-model="queryParams.opened" placeholder="请选择是否展示" clearable size="small">
                <el-option v-for="dict in openedOptions" :key="dict.dictValue" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="工作单位" prop="expertDatabaseWorkunit">
              <el-input v-model="form.expertDatabaseWorkunit" placeholder="请输入工作单位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职务" prop="expertDatabasePost">
              <el-input v-model="form.expertDatabasePost" placeholder="请输入职务" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="平台对接人" prop="expertDatabaseContact">
              <el-input v-model="form.expertDatabaseContact" placeholder="请输入平台对接人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="expertDatabaseEmail">
              <el-input v-model="form.expertDatabaseEmail" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="expertDatabasePhone">
              <el-input v-model="form.expertDatabasePhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="微信号" prop="expertDatabaseWechat">
              <el-input v-model="form.expertDatabaseWechat" placeholder="请输入微信号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="研究领域" prop="expertDatabaseDirection">
              <el-input v-model="form.expertDatabaseDirection" placeholder="请输入研究领域" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input v-model="form.sort" type="number" placeholder="请输入排序" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
          </el-col>
          <el-col :span="12">
          </el-col>
        </el-row>
        <el-form-item label="主要成果" prop="expertDatabaseAchievement">
          <el-input v-model="form.expertDatabaseAchievement" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="标签" prop="expertDatabaseTag">
          <el-input v-model="form.expertDatabaseTag" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="技术类别" prop="expertDatabaseTechnology">
          <el-select v-model="form.expertDatabaseTechnology" placeholder="请选择技术类别">
          <el-option v-for="dict in dict.type.expert_database_technology" :key="dict.value" :label="dict.label"
            :value="dict.value"></el-option>
        </el-select>
        </el-form-item>
        <el-form-item label="专家简介" prop="expertDatabaseIntroduction">
          <el-input v-model="form.expertDatabaseIntroduction" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="头像" prop="expertDatabaseImghead">
          <!-- <el-input v-model="form.expertDatabaseImghead" type="textarea" placeholder="请输入内容" /> -->
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :http-request="uploadFun"
            :before-upload="beforeAvatarUpload">
            <el-image
              v-if="form.expertDatabaseImghead"
              :src="form.expertDatabaseImghead"
              class="avatar"
            >
              <div slot="error" class="image-slot">
                <span>暂无图片</span>
              </div>
            </el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <!-- <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listExpertDatabase, getExpertDatabase, delExpertDatabase, addExpertDatabase, updateExpertDatabase } from "@/api/portalconsole/ExpertDatabase";
import {comUpload} from "@/api/portalconsole/uploadApi";
export default {
  name: "ExpertDatabase",
  dicts: ['expert_database_technology'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 专家库表格数据
      ExpertDatabaseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        expertDatabaseName: null,
        expertDatabaseExpert: null,
        expertDatabaseWorkunit: null,
        expertDatabasePost: null,
        expertDatabaseContact: null,
        expertDatabaseEmail: null,
        expertDatabasePhone: null,
        expertDatabaseWechat: null,
        expertDatabaseDirection: null,
        expertDatabaseAchievement: null,
        expertDatabaseTag: null,
        expertDatabaseTechnology: null,
        expertDatabaseIntroduction: null,
        expertDatabaseImghead: null,
        expertDatabaseIntroduce: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        expertDatabaseName:[
        { required: true, message: "姓名不能为空", trigger: "blur" }
        ]
      },
      //是否展示
      openedOptions: [{
          "dictValue": 1,
          "dictLabel": "展示"
        }, {
          "dictValue": 0,
          "dictLabel": "隐藏"
        }],
      //特约专家
      specialOptions: [{
          "dictValue": 1,
          "dictLabel": "已通过"
        }, {
          "dictValue": 0,
          "dictLabel": "申请中"
        }, {
          "dictValue": -1,
          "dictLabel": "未申请"
        }],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询专家库列表 */
    getList() {
      this.loading = true;
      listExpertDatabase(this.queryParams).then(response => {
        this.ExpertDatabaseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        expertDatabaseId: null,
        expertDatabaseName: null,
        expertDatabaseExpert: null,
        expertDatabaseWorkunit: null,
        expertDatabasePost: null,
        expertDatabaseContact: null,
        expertDatabaseEmail: null,
        expertDatabasePhone: null,
        expertDatabaseWechat: null,
        expertDatabaseDirection: null,
        expertDatabaseAchievement: null,
        expertDatabaseTag: null,
        expertDatabaseTechnology: null,
        expertDatabaseIntroduction: null,
        expertDatabaseImghead: null,
        expertDatabaseIntroduce: null,
        delFlag: null,
        // createBy: null,
        // createTime: null,
        // updateBy: null,
        // updateTime: null,
        // remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.expertDatabaseId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加专家库";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const expertDatabaseId = row.expertDatabaseId || this.ids
      getExpertDatabase(expertDatabaseId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改专家库";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.expertDatabaseId != null) {
            updateExpertDatabase(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExpertDatabase(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const expertDatabaseIds = row.expertDatabaseId || this.ids;
      this.$modal.confirm('是否确认删除专家库编号为"' + expertDatabaseIds + '"的数据项？').then(function() {
        return delExpertDatabase(expertDatabaseIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/ExpertDatabase/export', {
        ...this.queryParams
      }, `ExpertDatabase_${new Date().getTime()}.xlsx`)
    },
    // 图片上传
    uploadFun(params) {
        const file = params.file;
        let form = new FormData();
        form.append("file", file); // 文件对象
        comUpload(form).then(res => {
          let data = res.data;
          this.$set(this.form,'expertDatabaseImghead',data.fileFullPath)  // 图片全路径
          // this.$set(this.form,'imageUrl',data.fileId) // 图片Id
        })
      },
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/jpeg' || 'image/png' || 'image/jpg';
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isJPG) {
          this.$message.error('上传图片只能是 jpg、jpeg、png 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传头像图片大小不能超过 2MB!');
        }
        return isJPG && isLt2M;
      },
  }
};
</script>
<style  scoped>
>>>.el-table .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
    margin-left: 5px;
}
.avatar-uploader >>> .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>

