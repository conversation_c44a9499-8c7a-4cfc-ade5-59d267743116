package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.RemoteMemberService;
import com.ruoyi.system.api.domain.Member;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 会员服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteMemberFallbackFactory implements FallbackFactory<RemoteMemberService> {
    
    private static final Logger log = LoggerFactory.getLogger(RemoteMemberFallbackFactory.class);

    @Override
    public RemoteMemberService create(Throwable throwable) {
        log.error("会员服务调用失败:{}", throwable.getMessage());
        return new RemoteMemberService() {
            @Override
            public R<Member> getMemberInfo(String memberPhone, String source) {
                return R.fail("获取会员信息失败:" + throwable.getMessage());
            }

            @Override
            public R<Member> validateMemberPassword(String memberPhone, String password, String source) {
                return R.fail("验证会员密码失败:" + throwable.getMessage());
            }
        };
    }
}
