<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.CompanyMapper">

    <resultMap type="com.ruoyi.portalweb.vo.CompanyVO" id="CompanyResult">
        <result property="companyId" column="company_id"/>
        <result property="companyName" column="company_name"/>
        <result property="companyEmail" column="company_email"/>
        <result property="companyEmpower" column="company_empower"/>
        <result property="companyStatus" column="company_status"/>
        <result property="businessLicenseImageUrl" column="business_license_image_url"/>
        <result property="socialUnityCreditCode" column="social_unity_credit_code"/>
        <result property="serviceIndustry" column="service_industry"/>
        <result property="submitTime" column="submit_time"/>
        <result property="companySize" column="company_size"/>
        <result property="phone" column="phone"/>
        <result property="address" column="address"/>
        <result property="registeredCapital" column="registered_capital"/>
        <result property="intrduction" column="intrduction"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="companyRealName" column="company_real_name"/>

        <result property="memberRealName" column="member_real_name"/>
    </resultMap>

    <sql id="selectCompanyVo">
        select company_id, company_name, company_email, company_empower, company_status, business_license_image_url,
        social_unity_credit_code, service_industry, submit_time, company_size, phone, address, registered_capital,
        intrduction, del_flag, create_by, create_time, update_by, update_time, remark, company_real_name from company
    </sql>

    <sql id="Base_Column_List">
		a.*, b.member_real_name
	</sql>

	<sql id="Base_Table_List">
		FROM company a
        LEFT JOIN member b ON a.company_id = b.member_company_id
	</sql>

    <select id="selectCompanyList" parameterType="Company" resultMap="CompanyResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        <where>
            <if test="companyId != null ">and a.company_id = #{companyId}</if>
            <if test="companyName != null  and companyName != ''">and a.company_name like concat('%', #{companyName},
                '%')
            </if>
            <if test="companyEmail != null  and companyEmail != ''">and a.company_email = #{companyEmail}</if>
            <if test="companyEmpower != null  and companyEmpower != ''">and a.company_empower = #{companyEmpower}</if>
            <if test="companyStatus != null  and companyStatus != ''">and a.company_status = #{companyStatus}</if>
            <if test="socialUnityCreditCode != null  and socialUnityCreditCode != ''">and a.social_unity_credit_code =
                #{socialUnityCreditCode}
            </if>
            <if test="serviceIndustry != null  and serviceIndustry != ''">and a.service_industry like concat('%',
                #{serviceIndustry}, '%')
            </if>
            <if test="submitTime != null ">and a.submit_time = #{submitTime}</if>
            <if test="companySize != null  and companySize != ''">and a.company_size = #{companySize}</if>
            <if test="phone != null  and phone != ''">and a.phone = #{phone}</if>
            <if test="address != null  and address != ''">and a.address like concat('%', #{address}, '%')</if>
            <if test="registeredCapital != null  and registeredCapital != ''">and a.registered_capital like concat('%',
                #{registeredCapital}, '%')
            </if>
            <if test="intrduction != null  and intrduction != ''">and a.intrduction like concat('%', #{intrduction},
                '%')
            </if>
            <if test="createBy != null  and createBy != ''">and a.create_by = #{createBy}</if>
            <if test="createTime != null ">and a.create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''">and a.update_by = #{updateBy}</if>
            <if test="updateTime != null ">and a.update_time = #{updateTime}</if>
        </where>
    </select>

    <select id="selectCompanyByCompanyId" parameterType="Long" resultMap="CompanyResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        where a.company_id = #{companyId}
    </select>
    <select id="selectCompanyByCompanyIdAndMemberId" resultMap="CompanyResult">
        SELECT
        <include refid="Base_Column_List" />
        <include refid="Base_Table_List" />
        where a.company_id = #{companyId} and b.member_id = #{memberId}
    </select>

    <insert id="insertCompany" parameterType="Company" useGeneratedKeys="true" keyProperty="companyId">
        insert into company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="companyEmail != null">company_email,</if>
            <if test="companyEmpower != null">company_empower,</if>
            <if test="companyStatus != null">company_status,</if>
            <if test="businessLicenseImageUrl != null and businessLicenseImageUrl != ''">business_license_image_url,
            </if>
            <if test="socialUnityCreditCode != null and socialUnityCreditCode != ''">social_unity_credit_code,</if>
            <if test="serviceIndustry != null">service_industry,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="companySize != null">company_size,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="registeredCapital != null and registeredCapital != ''">registered_capital,</if>
            <if test="intrduction != null">intrduction,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="companyRealName != null and companyRealName != ''">company_real_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="companyEmail != null">#{companyEmail},</if>
            <if test="companyEmpower != null">#{companyEmpower},</if>
            <if test="companyStatus != null">#{companyStatus},</if>
            <if test="businessLicenseImageUrl != null and businessLicenseImageUrl != ''">#{businessLicenseImageUrl},
            </if>
            <if test="socialUnityCreditCode != null and socialUnityCreditCode != ''">#{socialUnityCreditCode},</if>
            <if test="serviceIndustry != null">#{serviceIndustry},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="companySize != null">#{companySize},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="registeredCapital != null and registeredCapital != ''">#{registeredCapital},</if>
            <if test="intrduction != null">#{intrduction},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="companyRealName != null and companyRealName != '' ">#{companyRealName},</if>
        </trim>
    </insert>

    <update id="updateCompany" parameterType="Company">
        update company
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="companyEmail != null">company_email = #{companyEmail},</if>
            <if test="companyEmpower != null">company_empower = #{companyEmpower},</if>
            <if test="companyStatus != null">company_status = #{companyStatus},</if>
            <if test="businessLicenseImageUrl != null and businessLicenseImageUrl != ''">business_license_image_url =
                #{businessLicenseImageUrl},
            </if>
            <if test="socialUnityCreditCode != null and socialUnityCreditCode != ''">social_unity_credit_code =
                #{socialUnityCreditCode},
            </if>
            <if test="serviceIndustry != null">service_industry = #{serviceIndustry},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="companySize != null">company_size = #{companySize},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="registeredCapital != null and registeredCapital != ''">registered_capital =
                #{registeredCapital},
            </if>
            <if test="intrduction != null">intrduction = #{intrduction},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="companyRealName != null and companyRealName != '' ">company_real_name = #{companyRealName},</if>
        </trim>
        where company_id = #{companyId}
    </update>

    <delete id="deleteCompanyByCompanyId" parameterType="Long">
        delete from company where company_id = #{companyId}
    </delete>

    <delete id="deleteCompanyByCompanyIds" parameterType="String">
        delete from company where company_id in
        <foreach item="companyId" collection="array" open="(" separator="," close=")">
            #{companyId}
        </foreach>
    </delete>
</mapper>