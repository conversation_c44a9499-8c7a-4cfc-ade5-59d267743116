package com.ruoyi.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.api.domain.Member;
import com.ruoyi.system.domain.UserChatGroup;
import com.ruoyi.system.domain.UserChatGroupMember;
import com.ruoyi.system.mapper.MemberMapper;
import com.ruoyi.system.mapper.UserChatGroupMapper;
import com.ruoyi.system.mapper.UserChatGroupMemberMapper;
import com.ruoyi.system.service.ISysChatGroupService;
import com.ruoyi.system.utils.RandomUtil;
import com.ruoyi.system.utils.SystemConstants;
import com.ruoyi.system.utils.RongTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.util.*;

/**
 * 用户交互
 * 群聊系统
 */
@Service
public class SysChatGroupServiceImpl implements ISysChatGroupService {
    private static final Logger log = LoggerFactory.getLogger(SysChatGroupServiceImpl.class);

    @Autowired
    private MemberMapper memberMapper;
    @Autowired
    private UserChatGroupMapper userChatGroupMapper;
    @Autowired
    private UserChatGroupMemberMapper userChatGroupMemberMapper;
    @Autowired
    private RongTool rongTool;


    /**
     * 创建群组
     *
     * @param userName       当前登录人手机号
     * @param groupUserNames 群组成员 多个逗号拼接
     * @param groupName      群组名称
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult createChatGroup(String userName, String groupUserNames, String groupName) {
        groupUserNames = userName + "," + groupUserNames;
        String[] groupUserArr = groupUserNames.split(",");
        //TODO 去重
        Set<String> set= new HashSet<>(Arrays.asList(groupUserArr));
        groupUserArr =  set.toArray(new String[]{});

        //TODO 生成群聊id
        Long groupId = RandomUtil.randomLong16();

        if (groupName.length() > 35) {
            groupName = groupName.substring(0, 35);
        }
        //TODO 调取融云生成群聊接口
        Integer code = rongTool.createGroup(groupId + "", groupName, groupUserArr);
        if (code != 200) {
            return AjaxResult.error("融云创建聊天室失败");
        }
        Date now = new Date();
        //TODO 保存数据库群聊关系表
        UserChatGroup userChatGroup = new UserChatGroup();
        userChatGroup.setId(groupId);
        userChatGroup.setGroupName(groupName);
        userChatGroup.setCreateTime(now);
        userChatGroup.setCreateBy(userName);
        userChatGroup.setDeleteFlag(1);
        userChatGroupMapper.insertSelective(userChatGroup);
        //TODO 保存群聊用户
        for (String groupUser : groupUserArr) {
            UserChatGroupMember userChatGroupMember = new UserChatGroupMember();
            userChatGroupMember.setGroupId(groupId);
            userChatGroupMember.setUserName(groupUser);
            userChatGroupMember.setCreateTime(now);
            userChatGroupMember.setCreateBy(userName);
            userChatGroupMember.setDeleteFlag(1);
            userChatGroupMember.setAdminFlag(0);
            if (groupUser.equals(userName)) {
                userChatGroupMember.setAdminFlag(1);

            }
            userChatGroupMemberMapper.insertSelective(userChatGroupMember);
        }
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put("userChatGroup",userChatGroup);
        return ajaxResult;
    }

    /**
     * 修改群组
     *
     * @param userName
     * @param userChatGroup id  群组id
     *                      groupName 群组名称
     *                      avatar 群头像
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateChatGroup(String userName, UserChatGroup userChatGroup) {
        userChatGroup.setUpdateBy(userName);
        userChatGroup.setUpdateTime(new Date());
        int i = userChatGroupMapper.updateByPrimaryKeySelective(userChatGroup);
        if (i == 0) {
            return AjaxResult.error("群组不存在");
        }
        //TODO 调融云修改群组名称
        int code = rongTool.updateGroup(userChatGroup.getId() + "", userChatGroup.getGroupName());
        if (code != 200) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("调用融云修改群组失败");
        }
        return AjaxResult.success();
    }

    /**
     * 解散群组
     *
     * @param userName
     * @param groupId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult dismissChatGroup(String userName, Long groupId) {
        Date now = new Date();
        /**
         * 解散群组
         */
        userChatGroupMapper.dismissChatGroup(groupId, userName, now);
        /**
         * 删除群成员
         */
        userChatGroupMemberMapper.dismissChatGroupMember(groupId, userName, now);
        //TODO 调用融云接口解散
        int code = rongTool.dismissGroup(groupId + "", userName);
        if (code != 200) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("融云解散群组失败");
        }
        return AjaxResult.success();
    }

    /**
     * 用户加入群组
     *
     * @param userName       登录用户的手机号
     * @param groupId        群组id
     * @param groupUserNames 要加入的用户手机号 多个逗号拼接
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult joinChatGroup(String userName, Long groupId, String groupUserNames) {

        UserChatGroup userChatGroup = userChatGroupMapper.selectById(groupId);
        if (ObjectUtil.isEmpty(userChatGroup)) {
            return AjaxResult.error("群组不存在！");
        }
        String[] groupUserArr = groupUserNames.split(",");
        //TODO 去重
        Set<String> set= new HashSet<>(Arrays.asList(groupUserArr));
        groupUserArr =  set.toArray(new String[]{});

        Date now = new Date();
        //TODO 保存群聊用户
        for (String groupUser : groupUserArr) {
            //TODO 查询用户是否在群组，如果在就不用添加了
            UserChatGroupMember userChatGroupMember = userChatGroupMemberMapper.selectByGroupIdAndUserName(groupId, groupUser);
            if (ObjectUtil.isNotEmpty(userChatGroupMember)) {
                continue;
            }
            Member sysUser = memberMapper.selectMemberByMemberPhone(groupUser);
            if(ObjectUtil.isEmpty(sysUser)){
                continue;
            }
            userChatGroupMember = new UserChatGroupMember();
            userChatGroupMember.setGroupId(groupId);
            userChatGroupMember.setUserName(groupUser);
            userChatGroupMember.setCreateTime(now);
            userChatGroupMember.setCreateBy(userName);
            userChatGroupMember.setDeleteFlag(1);
            userChatGroupMember.setAdminFlag(0);
            userChatGroupMemberMapper.insertSelective(userChatGroupMember);
        }
        //TODO 调用融云添加群聊用户接口
        int code = rongTool.joinGroup(groupId + "", userChatGroup.getGroupName(), groupUserArr);
        if (code != 200) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("融云添加群组用户失败");
        }
        return AjaxResult.success();
    }

    /**
     * 用户退出群组
     *
     * @param userName
     * @param groupId
     * @param groupUserNames
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult quitChatGroup(String userName, Long groupId, String groupUserNames) {
        String[] groupUserArr = groupUserNames.split(",");
        //TODO 去重
        Set<String> set= new HashSet<>(Arrays.asList(groupUserArr));
        groupUserArr =  set.toArray(new String[]{});

        Date now = new Date();
        UserChatGroupMember userChatGroupMember = new UserChatGroupMember();

        //TODO 删除群聊用户信息
        for (String groupUser : groupUserArr) {
            userChatGroupMember.setGroupId(groupId);
            userChatGroupMember.setUserName(groupUser);
            userChatGroupMember.setUpdateTime(now);
            userChatGroupMember.setUpdateBy(userName);
            userChatGroupMemberMapper.quitChatGroup(userChatGroupMember);
        }
        //TODO 调用融云退出用户
        int code = rongTool.quitGroup(groupId + "", groupUserArr);

        if (code != 200) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return AjaxResult.error("融云用户退出群组失败");
        }
        return AjaxResult.success();
    }

    /**
     * 查询用户群组列表
     *
     * @param userName
     * @return
     */
    @Override
    public AjaxResult getUserGroupList(String userName) {

        List<UserChatGroupMember> groupMemberList = userChatGroupMemberMapper.getUserGroupList(userName);
        List<UserChatGroup> groupList = new ArrayList<>();
        for (UserChatGroupMember groupMember : groupMemberList) {
            Long groupId = groupMember.getGroupId();
            //TODO 查询群聊信息
            UserChatGroup userChatGroup = userChatGroupMapper.selectById(groupId);
            if (ObjectUtil.isEmpty(userChatGroup)) {
                continue;
            }
            groupList.add(userChatGroup);
        }
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put("groupList", groupList);
        return ajaxResult;
    }

    /**
     * 查询群用户
     *
     * @param userName
     * @param groupId
     * @return
     */
    @Override
    public AjaxResult getGroupUserList(String userName, Long groupId) {
        //TODO 查询群成员
        List<UserChatGroupMember> groupMemberList = userChatGroupMemberMapper.getGroupUserList(groupId);
        for (UserChatGroupMember userChatGroupMember : groupMemberList) {
            userChatGroupMember.setAvatar(SystemConstants.DEFAULT_LOGO);
            userChatGroupMember.setNickName("用户不存在");
            //TODO 查询成员头像昵称
            Member sysUser = memberMapper.selectMemberByMemberPhone(userChatGroupMember.getUserName());
            if(ObjectUtil.isNotEmpty(sysUser)){
                if(ObjectUtil.isNotEmpty(sysUser.getAvatar())){
                    userChatGroupMember.setAvatar(sysUser.getAvatar());
                }
                userChatGroupMember.setNickName(sysUser.getNickname());
            }
        }
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put("groupMemberList", groupMemberList);
        Integer adminFlag = 0;
        //TODO 查询是否是管理员
        UserChatGroupMember userChatGroupMember = userChatGroupMemberMapper.selectByGroupIdAndUserName(groupId, userName);
        if(ObjectUtil.isNotEmpty(userChatGroupMember)){
            adminFlag = userChatGroupMember.getAdminFlag();
        }
        ajaxResult.put("adminFlag", adminFlag);
        return ajaxResult;
    }

}
