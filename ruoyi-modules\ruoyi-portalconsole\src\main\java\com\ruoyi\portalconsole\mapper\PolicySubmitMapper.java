package com.ruoyi.portalconsole.mapper;

import java.util.List;
import com.ruoyi.portalconsole.domain.PolicySubmit;

/**
 * 政策申报Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface PolicySubmitMapper 
{
    /**
     * 查询政策申报
     * 
     * @param policySubmitId 政策申报主键
     * @return 政策申报
     */
    public PolicySubmit selectPolicySubmitByPolicySubmitId(Long policySubmitId);

    /**
     * 查询政策申报列表
     * 
     * @param policySubmit 政策申报
     * @return 政策申报集合
     */
    public List<PolicySubmit> selectPolicySubmitList(PolicySubmit policySubmit);

    /**
     * 新增政策申报
     * 
     * @param policySubmit 政策申报
     * @return 结果
     */
    public int insertPolicySubmit(PolicySubmit policySubmit);

    /**
     * 修改政策申报
     * 
     * @param policySubmit 政策申报
     * @return 结果
     */
    public int updatePolicySubmit(PolicySubmit policySubmit);

    /**
     * 删除政策申报
     * 
     * @param policySubmitId 政策申报主键
     * @return 结果
     */
    public int deletePolicySubmitByPolicySubmitId(Long policySubmitId);

    /**
     * 批量删除政策申报
     * 
     * @param policySubmitIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePolicySubmitByPolicySubmitIds(Long[] policySubmitIds);
}
