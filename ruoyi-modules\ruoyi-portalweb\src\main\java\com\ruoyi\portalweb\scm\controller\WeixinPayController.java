package com.ruoyi.portalweb.scm.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.portalweb.api.domain.AppStoreOrder;
import com.ruoyi.portalweb.scm.service.IWxPayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/weixin/pay")
public class WeixinPayController {


    @Autowired
    private IWxPayService wxPayService;


    /**
     * native 下单
     * @param appStoreOrder
     * @return
     * @throws Exception
     */
    @PostMapping("/native")
    public AjaxResult nativePay(@RequestBody AppStoreOrder appStoreOrder) throws Exception {
        //返回支付二维码连接和订单号
        Map<String, Object> map = wxPayService.nativePay(appStoreOrder);
        return AjaxResult.success(map);
    }

    //@ResponseBody
    //@RequestMapping(value = "/pay",method = RequestMethod.POST,produces = "application/json;charset=utf-8")
    @PostMapping("/jsapi")
    public AjaxResult jsapiPay(@RequestBody AppStoreOrder appStoreOrder, HttpServletRequest request){
        return wxPayService.jsapiPay(appStoreOrder, request);
    }

//    /**
//     * native 下单
//     * @param appStoreOrder
//     * @return
//     * @throws Exception
//     */
//    @PostMapping("/native_model")
//    public AjaxResult nativePayModel(@RequestBody AppStoreOrder appStoreOrder) throws Exception {
//        //返回支付二维码连接和订单号
//        Map<String, Object> map = wxPayService.nativePayModel(appStoreOrder);
//        return AjaxResult.success(map);
//    }

}
