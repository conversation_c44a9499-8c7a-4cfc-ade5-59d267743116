package com.ruoyi.portalweb.vo;


import com.ruoyi.portalweb.api.domain.Solution;
import com.ruoyi.portalweb.api.domain.SolutionAdvantage;
import com.ruoyi.portalweb.api.domain.SolutionCase;
import com.ruoyi.portalweb.api.domain.SolutionPain;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * 解决方案
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public class SolutionVO extends Solution {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "方案类型")
    private String solutionTypeName;
    @ApiModelProperty(value = "关键字")
    private String keywords;
    @ApiModelProperty(value = "服务范围")
    private String category;
    @ApiModelProperty(value = "服务范围")
    private String categoryName;
    @ApiModelProperty(value = "方案优势")
    private List<SolutionAdvantage> alSolutionAdvantageVOs = new ArrayList<>();
    @ApiModelProperty(value = "实施案例")
    private List<SolutionCase> alSolutionCaseVOs = new ArrayList<>();
    @ApiModelProperty(value = "行业痛点")
    private List<SolutionPain> alSolutionPainVOs = new ArrayList<>();

    public List<SolutionAdvantage> getAlSolutionAdvantageVOs() {
        return alSolutionAdvantageVOs;
    }

    public void setAlSolutionAdvantageVOs(List<SolutionAdvantage> alSolutionAdvantageVOs) {
        this.alSolutionAdvantageVOs = alSolutionAdvantageVOs;
    }

    public List<SolutionCase> getAlSolutionCaseVOs() {
        return alSolutionCaseVOs;
    }

    public void setAlSolutionCaseVOs(List<SolutionCase> alSolutionCaseVOs) {
        this.alSolutionCaseVOs = alSolutionCaseVOs;
    }

    public List<SolutionPain> getAlSolutionPainVOs() {
        return alSolutionPainVOs;
    }

    public void setAlSolutionPainVOs(List<SolutionPain> alSolutionPainVOs) {
        this.alSolutionPainVOs = alSolutionPainVOs;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getSolutionTypeName() {
        return solutionTypeName;
    }

    public void setSolutionTypeName(String solutionTypeName) {
        this.solutionTypeName = solutionTypeName;
    }

}
