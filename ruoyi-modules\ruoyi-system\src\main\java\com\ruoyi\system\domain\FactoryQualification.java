package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 工厂资质证件对象 factory_qualification
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public class FactoryQualification extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 工厂ID */
    @Excel(name = "工厂ID")
    private Long factoryId;

    /** 资质名称 */
    @Excel(name = "资质名称")
    private String qualificationName;

    /** 附件 */
    @Excel(name = "附件")
    private String attachment;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setFactoryId(Long factoryId) 
    {
        this.factoryId = factoryId;
    }

    public Long getFactoryId() 
    {
        return factoryId;
    }
    public void setQualificationName(String qualificationName) 
    {
        this.qualificationName = qualificationName;
    }

    public String getQualificationName() 
    {
        return qualificationName;
    }
    public void setAttachment(String attachment) 
    {
        this.attachment = attachment;
    }

    public String getAttachment() 
    {
        return attachment;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("factoryId", getFactoryId())
            .append("qualificationName", getQualificationName())
            .append("attachment", getAttachment())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
