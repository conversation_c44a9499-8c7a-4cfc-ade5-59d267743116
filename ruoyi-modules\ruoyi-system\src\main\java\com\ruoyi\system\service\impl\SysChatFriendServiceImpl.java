package com.ruoyi.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.api.domain.Member;
import com.ruoyi.system.domain.SysUserFriend;
import com.ruoyi.system.domain.SysUserFriendHistory;
import com.ruoyi.system.mapper.MemberMapper;
import com.ruoyi.system.mapper.SysUserFriendHistoryMapper;
import com.ruoyi.system.mapper.SysUserFriendMapper;
import com.ruoyi.system.service.ISysChatFriendService;
import com.ruoyi.system.utils.SystemConstants;
import com.ruoyi.system.utils.RongTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * 用户交互
 * 好友系统
 */
@Service
public class SysChatFriendServiceImpl implements ISysChatFriendService {
    private static final Logger log = LoggerFactory.getLogger(SysChatFriendServiceImpl.class);


    @Autowired
    private MemberMapper memberMapper;

    @Autowired
    private SysUserFriendMapper sysUserFriendMapper;

    @Autowired
    private SysUserFriendHistoryMapper sysUserFriendHistoryMapper;

    @Autowired
    private RongTool rongTool;

    /**
     * 获取好友列表
     *
     * @param userName
     * @return
     */
    @Override
    public AjaxResult getFriendList(String userName) {
        AjaxResult ajaxResult = AjaxResult.success();
        List<SysUserFriend> friendList = sysUserFriendMapper.selectFriendList(userName);
        for(SysUserFriend sysUserFriend : friendList){
            sysUserFriend.setFriendNickName("未设置昵称");
            sysUserFriend.setFriendAvatar(SystemConstants.DEFAULT_LOGO);
            //TODO 查询用户信息
            Member sysUser = memberMapper.selectMemberByMemberPhone(sysUserFriend.getFriendUserName());
            if(ObjectUtil.isEmpty(sysUser)){
                sysUserFriend.setFriendNickName("用户不存在");
                sysUserFriend.setFriendAvatar(SystemConstants.DEFAULT_LOGO);
            }else {
                if(ObjectUtil.isNotEmpty(sysUser.getNickname())){
                    sysUserFriend.setFriendNickName(sysUser.getNickname());
                }
                if(ObjectUtil.isNotEmpty(sysUser.getAvatar())){
                    sysUserFriend.setFriendAvatar(sysUser.getAvatar());
                }
            }
        }

        ajaxResult.put("friendList", friendList);
        return ajaxResult;
    }

    /**
     * 删除好友
     *
     * @param friendUseName       好友friendUseName
     * @param userName 当前登录用户的手机号
     */
    @Override
    public AjaxResult deleteFriend(String friendUseName, String userName) {
        AjaxResult ajaxResult = AjaxResult.success();
        int i = sysUserFriendMapper.deleteFriend(friendUseName, userName);
        return ajaxResult;
    }

    /**
     * 修改好友信息
     * @param sysUserFriend
     *        friendUserName 好友表userName
     *        userName 当前登录用户手机号
     *        remark 备注
     */
    @Override
    public AjaxResult updateFriend(SysUserFriend sysUserFriend) {
        AjaxResult ajaxResult = AjaxResult.success();
        sysUserFriend.setUpdateTime(new Date());
        int i = sysUserFriendMapper.updateFriend(sysUserFriend);
        return ajaxResult;
    }

    /**
     * 查询申请列表
     *
     * @param userName 登录用户手机号
     * @param pageNum  页数
     * @param pageSize 条数
     * @return
     */
    @Override
    public AjaxResult getApplyFriendList(String userName, int pageNum, int pageSize) {
        AjaxResult ajaxResult = AjaxResult.success();
        PageHelper.startPage(pageNum, pageSize);
        List<SysUserFriendHistory> historyList = sysUserFriendHistoryMapper.getApplyFriendList(userName);
        for (SysUserFriendHistory sysUserFriendHistory : historyList) {

            sysUserFriendHistory.setApplyNickName("用户不存在");
            sysUserFriendHistory.setApplyAvatar(SystemConstants.DEFAULT_LOGO);
            Member sysUser = memberMapper.selectMemberByMemberPhone(sysUserFriendHistory.getAppyUserName());
            if(ObjectUtil.isNotEmpty(sysUser)){
                if(ObjectUtil.isNotEmpty(sysUser.getAvatar())){
                    sysUserFriendHistory.setApplyAvatar(sysUser.getAvatar());
                }
                sysUserFriendHistory.setApplyNickName(sysUser.getNickname());
            }

        }
        ajaxResult.put("historyList", historyList);

        long total = new PageInfo(historyList).getTotal();
        ajaxResult.put("total", total);
        long totalPage;
        if (total % pageSize == 0) {
            totalPage = total / pageSize;
        } else {
            totalPage = total / pageSize + 1;
        }
        ajaxResult.put("totalPage", totalPage);
        return ajaxResult;
    }

    /**
     * 更改申请状态
     *
     * @param sysUserFriendHistory id 表主键id
     *                             userName 登录人的电话号码
     *                             applyState 1 通过 2拒绝
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult changeApplyState(SysUserFriendHistory sysUserFriendHistory) {
        SysUserFriendHistory detail = sysUserFriendHistoryMapper.getDetail(sysUserFriendHistory.getId(), sysUserFriendHistory.getUserName());
        if (ObjectUtil.isEmpty(detail)) {
            return AjaxResult.error("未找到申请信息");
        }
        Date now = new Date();
        //TODO 拒绝申请
        if (sysUserFriendHistory.getApplyState() == 2) {
            detail.setApplyState(2);
            detail.setUpdateTime(now);
            int i = sysUserFriendHistoryMapper.updateState(detail);
            return AjaxResult.success();
        }


        //TODO 通过申请
//        0 申请中  1 已添加 2 已拒绝
        if (detail.getApplyState() == 1) {
            return AjaxResult.success();
        }

        detail.setApplyState(1);
        detail.setUpdateTime(now);
        int i = sysUserFriendHistoryMapper.updateState(detail);
        if (i > 0) {
            SysUserFriend sysUserFriend = sysUserFriendMapper.selectFriendByFriendName(sysUserFriendHistory.getUserName(), detail.getAppyUserName());
            if (ObjectUtil.isEmpty(sysUserFriend)) {
                sysUserFriend = new SysUserFriend();
                sysUserFriend.setUserName(sysUserFriendHistory.getUserName());
                sysUserFriend.setFriendUserName(detail.getAppyUserName());
                sysUserFriend.setCreateTime(now);
                sysUserFriend.setDeleteFlag(1);
                sysUserFriend.setFriendNickName("用户不存在");
                sysUserFriend.setFriendAvatar(SystemConstants.DEFAULT_LOGO);

                Member sysUser = memberMapper.selectMemberByMemberPhone(detail.getAppyUserName());
                if(ObjectUtil.isNotEmpty(sysUser)){
                    sysUserFriend.setFriendNickName(sysUser.getNickname());
                    if(ObjectUtil.isNotEmpty(sysUser.getAvatar())){
                        sysUserFriend.setFriendAvatar(sysUser.getAvatar());
                    }
                }


                sysUserFriendMapper.insertSelective(sysUserFriend);
            }
            sysUserFriend = sysUserFriendMapper.selectFriendByFriendName(detail.getAppyUserName(), sysUserFriendHistory.getUserName());
            if (ObjectUtil.isEmpty(sysUserFriend)) {
                sysUserFriend = new SysUserFriend();
                sysUserFriend.setUserName(detail.getAppyUserName());
                sysUserFriend.setFriendUserName(sysUserFriendHistory.getUserName());
                sysUserFriend.setCreateTime(now);
                sysUserFriend.setDeleteFlag(1);
                sysUserFriend.setFriendNickName("用户不存在");
                sysUserFriend.setFriendAvatar(SystemConstants.DEFAULT_LOGO);
                Member sysUser = memberMapper.selectMemberByMemberPhone(sysUserFriendHistory.getAppyUserName());
                if(ObjectUtil.isNotEmpty(sysUser)) {
                    sysUserFriend.setFriendNickName(sysUser.getNickname());
                    if (ObjectUtil.isNotEmpty(sysUser.getAvatar())) {
                        sysUserFriend.setFriendAvatar(sysUser.getAvatar());
                    }
                }

                sysUserFriendMapper.insertSelective(sysUserFriend);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 申请好友
     *
     * @param sysUserFriendHistory userName 申请人手机号
     *                             appyUserName 被申请人手机号
     *                             remark 申请备注
     * @return
     */
    @Override
    public AjaxResult applyFriend(SysUserFriendHistory sysUserFriendHistory) {

        SysUserFriend sysUserFriend = sysUserFriendMapper.selectFriendByFriendName(sysUserFriendHistory.getAppyUserName(), sysUserFriendHistory.getUserName());
        if (ObjectUtil.isNotEmpty(sysUserFriend)) {
            sysUserFriend = sysUserFriendMapper.selectFriendByFriendName(sysUserFriendHistory.getUserName(), sysUserFriendHistory.getAppyUserName());
            if (ObjectUtil.isNotEmpty(sysUserFriend)) {
                return AjaxResult.error("已经是好友啦，不需要重复申请！");
            }
        }
        //TODO 如果原先存在申请中的 更新
        Date now = new Date();
        sysUserFriendHistory.setCreateTime(now);
        sysUserFriendHistory.setApplyState(0);
        int i = sysUserFriendHistoryMapper.reloadApply(sysUserFriendHistory);
        if (i == 0) {
            sysUserFriendHistoryMapper.insertSelective(sysUserFriendHistory);
        }
        JSONObject jsonObject  = new JSONObject();
        jsonObject.put("type",1);
        jsonObject.put("targetId",sysUserFriendHistory.getUserName());
        jsonObject.put("senderUserId",sysUserFriendHistory.getAppyUserName());
        jsonObject.put("systemSender","systemFriendApply");
        jsonObject.put("remark","用户好友申请系统通知"+sysUserFriendHistory.getRemark());
        rongTool.sendSystemMessage(jsonObject);
        return AjaxResult.success();
    }

    /**
     * 查询用户列表
     * @param searchKey  查询内容（手机号 或者昵称）
     * @param pageNum
     * @param pageSize
     * @param userName
     * @return
     */
    @Override
    public AjaxResult searchUser(String searchKey, int pageNum, int pageSize, String userName) {
        AjaxResult ajaxResult = AjaxResult.success();
        PageHelper.startPage(pageNum, pageSize);
        List<Member> userList = memberMapper.selectByMemberPhoneOrNickname(searchKey);
        //TODO 移除自己的信息
        if (ObjectUtil.isNotEmpty(userName)) {
            Iterator<Member> iterator = userList.iterator();
            while (iterator.hasNext()) {
                Member sysUser = iterator.next();
                if (sysUser.getMemberPhone().equals(userName)) {
                    iterator.remove();
                    break;
                }
            }
        }

        ajaxResult.put("userList", userList);
        long total = new PageInfo(userList).getTotal();
        ajaxResult.put("total", total);
        long totalPage;
        if (total % pageSize == 0) {
            totalPage = total / pageSize;
        } else {
            totalPage = total / pageSize + 1;
        }
        ajaxResult.put("totalPage", totalPage);

        return ajaxResult;
    }


}
