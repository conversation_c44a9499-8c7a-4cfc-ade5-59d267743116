package com.ruoyi.portalconsole.domain.vo;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

public class EcologyVO  extends BaseEntity {
    private Long ecologyId;

    /** 生态类别ID */
    @Excel(name = "生态类别ID")
    private Long ecologyCategoryId;

    /** 公司id */
    @Excel(name = "公司id")
    private String companyId;

    /** 合作思路 */
    @Excel(name = "合作思路")
    private String ecologyIdeas;

    /** 联系人 */
    @Excel(name = "联系人")
    private String ecologyContact;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String ecologyPhone;

    /** 生态类别名称 */
    @Excel(name = "生态类别名称")
    private String ecologyCategoryName;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    public Long getEcologyId() {
        return ecologyId;
    }

    public void setEcologyId(Long ecologyId) {
        this.ecologyId = ecologyId;
    }

    public Long getEcologyCategoryId() {
        return ecologyCategoryId;
    }

    public void setEcologyCategoryId(Long ecologyCategoryId) {
        this.ecologyCategoryId = ecologyCategoryId;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getEcologyIdeas() {
        return ecologyIdeas;
    }

    public void setEcologyIdeas(String ecologyIdeas) {
        this.ecologyIdeas = ecologyIdeas;
    }

    public String getEcologyContact() {
        return ecologyContact;
    }

    public void setEcologyContact(String ecologyContact) {
        this.ecologyContact = ecologyContact;
    }

    public String getEcologyPhone() {
        return ecologyPhone;
    }

    public void setEcologyPhone(String ecologyPhone) {
        this.ecologyPhone = ecologyPhone;
    }

    public String getEcologyCategoryName() {
        return ecologyCategoryName;
    }

    public void setEcologyCategoryName(String ecologyCategoryName) {
        this.ecologyCategoryName = ecologyCategoryName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("EcologyVO{");
        sb.append("ecologyId=").append(ecologyId);
        sb.append(", ecologyCategoryId=").append(ecologyCategoryId);
        sb.append(", companyId='").append(companyId).append('\'');
        sb.append(", ecologyIdeas='").append(ecologyIdeas).append('\'');
        sb.append(", ecologyContact='").append(ecologyContact).append('\'');
        sb.append(", ecologyPhone='").append(ecologyPhone).append('\'');
        sb.append(", ecologyCategoryName='").append(ecologyCategoryName).append('\'');
        sb.append(", companyName='").append(companyName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
