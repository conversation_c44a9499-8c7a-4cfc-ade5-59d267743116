package com.ruoyi.auth.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Feign性能监控拦截器
 * 监控Feign调用的性能指标
 * 
 * <AUTHOR>
 */
@Component
public class FeignPerformanceInterceptor implements RequestInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(FeignPerformanceInterceptor.class);

    @Override
    public void apply(RequestTemplate template) {
        long startTime = System.currentTimeMillis();
        
        // 在请求头中添加开始时间，用于后续计算耗时
        template.header("X-Request-Start-Time", String.valueOf(startTime));
        
        logger.info("Feign请求开始: {} {}, 开始时间: {}", 
            template.method(), template.url(), startTime);
    }
}
