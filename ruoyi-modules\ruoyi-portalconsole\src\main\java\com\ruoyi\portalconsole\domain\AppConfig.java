package com.ruoyi.portalconsole.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 应用配置对象 app_config
 * 
 * <AUTHOR>
 * @date 2024-07-22
 */
public class AppConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 应用配置Id */
    private Long appConfigId;

    /** 应用商店Id */
    @Excel(name = "应用商店Id")
    private Long appStoreId;

    /** 服务器出口ip */
    @Excel(name = "服务器出口ip")
    private String ip;

    /** 应用web地址 */
    @Excel(name = "应用web地址")
    private String appWebUrl;

    /** 应用web地址(体验) */
    @Excel(name = "应用web地址(体验)")
    private String appWebTrialUrl;

    /** 健康检查服务地址 */
    @Excel(name = "健康检查服务地址")
    private String healthInspectionUrl;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contact;

    /** 测试token */
    @Excel(name = "测试token")
    private String testToken;

    @Excel(name = "应用地址(交付方式-SaaS使用时填写)")
    private String erweima;
    @Excel(name = "下载服务(交付方式-下载服务时填写)")
    private String downloadUrl;

    public void setAppConfigId(Long appConfigId) 
    {
        this.appConfigId = appConfigId;
    }

    public Long getAppConfigId() 
    {
        return appConfigId;
    }
    public void setAppStoreId(Long appStoreId) 
    {
        this.appStoreId = appStoreId;
    }

    public Long getAppStoreId() 
    {
        return appStoreId;
    }
    public void setIp(String ip) 
    {
        this.ip = ip;
    }

    public String getIp() 
    {
        return ip;
    }
    public void setAppWebUrl(String appWebUrl) 
    {
        this.appWebUrl = appWebUrl;
    }

    public String getAppWebUrl() 
    {
        return appWebUrl;
    }
    public void setAppWebTrialUrl(String appWebTrialUrl) 
    {
        this.appWebTrialUrl = appWebTrialUrl;
    }

    public String getAppWebTrialUrl() 
    {
        return appWebTrialUrl;
    }
    public void setHealthInspectionUrl(String healthInspectionUrl) 
    {
        this.healthInspectionUrl = healthInspectionUrl;
    }

    public String getHealthInspectionUrl() 
    {
        return healthInspectionUrl;
    }
    public void setContact(String contact) 
    {
        this.contact = contact;
    }

    public String getContact() 
    {
        return contact;
    }
    public void setTestToken(String testToken) 
    {
        this.testToken = testToken;
    }

    public String getTestToken() 
    {
        return testToken;
    }

    public String getErweima() {return erweima;}

    public void setErweima(String erweima) {this.erweima = erweima;}

    public String getDownloadUrl() {return downloadUrl;}

    public void setDownloadUrl(String downloadUrl) {this.downloadUrl = downloadUrl;}

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("appConfigId", getAppConfigId())
            .append("appStoreId", getAppStoreId())
            .append("ip", getIp())
            .append("appWebUrl", getAppWebUrl())
            .append("appWebTrialUrl", getAppWebTrialUrl())
            .append("healthInspectionUrl", getHealthInspectionUrl())
            .append("contact", getContact())
            .append("testToken", getTestToken())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
