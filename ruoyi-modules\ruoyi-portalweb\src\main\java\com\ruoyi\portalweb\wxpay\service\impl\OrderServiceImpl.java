package com.ruoyi.portalweb.wxpay.service.impl;


import java.util.*;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.portalweb.api.domain.AppStoreOrder;
import com.ruoyi.portalweb.api.enums.AppStoreOrderStatus;
import com.ruoyi.portalweb.api.domain.BuMemberOnlinePay;
import com.ruoyi.portalweb.service.IAppStoreOrderService;
import com.ruoyi.portalweb.vo.MemberVO;
import com.ruoyi.portalweb.wxpay.service.IOrderService;
import com.ruoyi.portalweb.wxpay.service.IWechatService;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.portalweb.wxpay.pay.WxPayUtil;
import com.ruoyi.portalweb.service.IBuMemberOnlinePayService;


/**
 * 订单接口实现类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor(force = true)
@Slf4j
@Service
public class OrderServiceImpl implements IOrderService {

	@Autowired
	private  IWechatService wechatService;

	@Autowired
	private  IAppStoreOrderService appStoreOrderService;

	@Autowired
	private  IBuMemberOnlinePayService memberOnlinePayService;

	public static final Long NO = 1L;
	public static final Long YES = 2L;


	/**
	 * 微信网页扫码支付
	 *
	 * @param appStoreOrderId
	 * @param remoteAddr
	 * @param member
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public String updateWechatNativePayInfo(Long appStoreOrderId, String remoteAddr, MemberVO member) {
		// 获取商城订单信息
		AppStoreOrder order = appStoreOrderService.selectAppStoreOrderByAppStoreOrderId(appStoreOrderId);
		if (order == null || Objects.equals(order.getAppStoreOrderNo(), "")) {
			throw new ServiceException("未获取到销售订单信息");
		}
		if (AppStoreOrderStatus.UNPAID.getCode().equals(order.getOrderStatus())) {
			throw new ServiceException("销售订单状态错误");
		}
		if (!Objects.equals(member.getMemberId(), order.getBuyMemberId())) {
			throw new ServiceException("当前登录客户信息和销售订单客户信息不一致");
		}

		// 支付订单号
		String outTradeNo = WxPayUtil.getOutTradeNo();
		// 保存微信支付信息
		BuMemberOnlinePay buMemberOnlinePay = new BuMemberOnlinePay();
		buMemberOnlinePay.setMemberId(member.getMemberId());
		buMemberOnlinePay.setPayOrderNo(outTradeNo);
		buMemberOnlinePay.setOnlinePayStyle("WX"); //todo
		buMemberOnlinePay.setMoney(order.getAppStorePrice());
		buMemberOnlinePay.setTenantId("??????"); // todo
		buMemberOnlinePay.setStatus(NO); //todo
		buMemberOnlinePay.setAppStoreOrderNo(order.getAppStoreOrderNo());

		int i = memberOnlinePayService.insertBuMemberOnlinePay(buMemberOnlinePay);
		if (i != 1) {
			throw new ServiceException("保存微信支付信息出现错误");
		}
		// 获取微信支付信息
		String codeUrl = wechatService.wxNativePay(outTradeNo, order.getAppStorePrice(), remoteAddr, //todo tenantID = ???
			order.getAppStoreId(), "订单号" + order.getAppStoreOrderNo(), member.getMemberCompanyId().toString());
		return codeUrl;
	}

	/**
	 * 支付回调
	 *
	 * @param outTradeNo
	 * @param isWxPadMsg 自助收银系统推送消息
	 * @param bankType
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public R<Object> rechargeCallBack(String outTradeNo, boolean isWxPadMsg, String bankType) {
		// 微信支付信息
		BuMemberOnlinePay wrapper = new BuMemberOnlinePay();
		wrapper.setPayOrderNo(outTradeNo);
		List<BuMemberOnlinePay> buMemberOnlinePays = memberOnlinePayService.selectBuMemberOnlinePayList(wrapper);
		if (Objects.isNull(buMemberOnlinePays)){
			throw new ServiceException("未获取商城订单信息");
		}
		if (buMemberOnlinePays.size()!= 1){
			throw new ServiceException("获取商城订单信息异常");
		}

		if (!Objects.equals(buMemberOnlinePays.get(0).getStatus(), NO)){
			if (Objects.equals(buMemberOnlinePays.get(0).getStatus(), YES)){
				return R.ok();
			}
			throw new ServiceException("商城订单状态信息异常");
		}

		// 更新微信支付订单状态
		BuMemberOnlinePay buMemberOnlinePay = new BuMemberOnlinePay();
		buMemberOnlinePay.setPayOrderNo(outTradeNo);
		buMemberOnlinePay.setStatus(YES);
		int i = memberOnlinePayService.updateBuMemberOnlinePay(buMemberOnlinePay);
		if (i != 1) {
			throw new ServiceException("未获取到在线支付信息");
		}
		// 更新商城订单状态
		AppStoreOrder order = new AppStoreOrder();
		order.setAppStoreOrderNo(buMemberOnlinePays.get(0).getAppStoreOrderNo());
		order.setOrderStatus(AppStoreOrderStatus.PAID.getCode());
		int i1 = appStoreOrderService.updateAppStoreOrderByOrderNo(order);
		return R.ok();
	}

}
