package com.ruoyi.auth.util.result;

import java.io.Serializable;
import java.util.List;

public class QueryResult<T> implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private List<T> records;
	private long total;

	public QueryResult(){

	}

	public QueryResult(List<T> records, long total) {
		this.records = records;
		this.total = total;
	}

	public List<T> getRecords() {
		return records;
	}

	public void setRecords(List<T> records) {
		this.records = records;
	}

	public long getTotal() {
		return total;
	}

	public void setTotal(long total) {
		this.total = total;
	}
}
