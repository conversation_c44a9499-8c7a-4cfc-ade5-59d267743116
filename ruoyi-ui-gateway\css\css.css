@charset "utf-8";

/* CSS Document */

.main_bg {
  background-color: #eee;
  min-width: 1200px;
  padding-bottom: 50px;
}

.main {
  width: 1200px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  margin-top: 0px;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  font-size: 14px;
  padding-top: 40px;
  padding-right: 0px;
  padding-bottom: 40px;
  padding-left: 0px;
}

.main1 {
  width: 1200px;
  margin-top: 70px;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  position: relative;
  padding-top: 0px;
}

.main2 {
  width: 1264px;
  margin-top: 50px;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  box-shadow: 0px 0px 15px #ccc;
  padding-top: 50px;
  padding-right: 80px;
  padding-bottom: 50px;
  padding-left: 80px;
}

.main3 {
  width: 75%;
  min-width: 1500px;
  margin-top: 0;
  margin-right: auto;
  margin-bottom: 30px;
  margin-left: auto;
}

.main4 {
  width: 1402px;
  margin-top: 50px;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  position: relative;
}

.main0 {
  width: 1200px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  margin-top: 40px;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  font-size: 16px;
  position: relative;
}

.bread {
  width: 385px;
  line-height: 30px;
  padding-top: 15px;
}

.bread2 {
  width: 285px;
  line-height: 30px;
  padding-top: 35px;
  color: #fff;
}

.bread2 a {
  color: #fff;
}

.search_box {
  width: 725px;
  display: flex;
  flex-flow: row wrap;
  justify-content: left;
}

.search_box2 {
  width: 875px;
  display: flex;
  flex-flow: row wrap;
  justify-content: left;
}

.fabu {
  width: 150px;
}

.fabu a {
  width: 150px;
  display: block;
  color: #fff;
  background-color: #3986e9;
  text-align: center;
  line-height: 60px;
  border-radius: 5px;
  font-size: 16px;
}

.hezuo {
  width: 500px;
  margin-top: 10px;
  display: flex;
  flex-flow: row wrap;
  justify-content: left;
}

.hezuoa2 {
  margin-top: 0px;
  margin-right: 10px;
  margin-bottom: 0px;
  margin-left: 10px;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #de791b;
  border-right-color: #de791b;
  border-bottom-color: #de791b;
  border-left-color: #de791b;
  line-height: 30px;
  height: 30px;
  color: #de791b;
  padding-top: 10px;
  padding-right: 5px;
  padding-bottom: 0px;
  padding-left: 5px;
  cursor: pointer;
}

.hezuoa {
  width: 150px;
  cursor: pointer;
  display: block;
  color: #fff;
  background-color: #3986e9;
  text-align: center;
  line-height: 40px;
  border-radius: 5px;
  font-size: 16px;
}

.yingyong_fenlei {
  width: 100%;
}

.yingyong_fenlei_left {
  width: 860px;
  margin: 0 20px;
  padding: 35px 0;
  float: left;
}

.yyflleft_text {
  border-bottom: 1px solid #dfdede;
}

.yyflleft_text_li {
  width: 100%;
  padding-bottom: 25px;
  display: flex;
}

.yyflleft_licon {
  width: 2px;
  height: 24px;
  margin-top: 8px;
  background: #296adc;
}

.yyflleft_active {
  border: 1px solid #397aeb;
  border-radius: 2px;
  color: #397aeb !important;
}

.yyflleft_libox {
  width: 750px;
  float: left;
}

.yyflleft_liboxli2 {
  font-size: 14px;
  color: #5a5a5a;
  line-height: 40px;
  padding: 0 15px;
  display: inline-block;
  margin-bottom: 10px;
}

.yyflleft_liboxli2:hover {
  cursor: pointer;
}

.yyflleft_liboxli {
  font-size: 14px;
  color: #5a5a5a;
  line-height: 40px;
  padding: 0 15px;
  display: inline-block;
  margin-bottom: 10px;
}

.yyflleft_liboxli2:hover {
  border: 1px solid #397aeb;
  border-radius: 2px;
  color: #397aeb !important;
}

.yyflleft_liboxli:hover {
  border: 1px solid #397aeb;
  border-radius: 2px;
  color: #397aeb !important;
}

.yyflleft_liboxlis {
  font-size: 14px;
  color: #5a5a5a;
  line-height: 40px;
  padding: 0 15px;
  display: inline-block;
  margin-bottom: 10px;
}

.yyflleft_liboxlis:hover {
  cursor: pointer;
  color: #296adc !important;
}

.yyflleft_liboxlis_active {
  color: #296adc !important;
}

.yyliboxlis_el-icon-bottom {
  transform: rotate(180deg);
  transition: all 0.5s;
}

.yingyong_fenlei_r {
  width: 300px;
  float: left;
}

.yingyong_fenlei_r img {
  width: 100%;
  height: 100%;
}

.yyflleft_liboxlis:hover > .el-icon-bottom {
  transform: rotate(180deg);
  transition: all 1s;
}

.yyflleft_liboxli:hover {
  cursor: pointer;
}

.yyflleft_litittle {
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  color: #333333;
  width: 64px;
  float: left;
  line-height: 40px;
  padding: 0 15px;
}

.hezuo a {
  width: 150px;
  display: block;
  color: #fff;
  background-color: #3986e9;
  text-align: center;
  line-height: 40px;
  border-radius: 5px;
  font-size: 16px;
}

.search_txt2 .el-input__inner {
  height: 50px !important;
  font-size: 14px;
  border-radius: 4px;
  color: #666;
  outline: none;
}

.search_txt3 {
  width: 742px;
  height: 56px;
  line-height: 56px;
  border: none;
  font-size: 14px;
  color: #666;
  outline: none;
  padding-left: 15px;
  border-top-width: 1px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-left-width: 1px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #ccc;
  border-right-color: #ccc;
  border-bottom-color: #ccc;
  border-left-color: #ccc;
  border-radius: 4px 0 0 4px;
}

.search_but2 {
  background-color: #3986e9;
  background-image: linear-gradient(to bottom, #3986e9, #3986e9);
  width: 100px;
  height: 50px;
  border: none;
  outline: none;
  color: #fff;
  font-size: 16px;
  border-radius: 0px 4px 4px 0px;
}

.search_but2:hover {
  box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.3);
  cursor: pointer;
}

.main_l {
  width: 855px;
}

.main_r {
  width: 320px;
}

.main_l2 {
  width: 765px;
  background-color: #fff;
  padding: 40px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

.main_r2 {
  width: 320px;
}

.ad {
  background-image: url("../images/new/newsDetail.png");
  height: 86px;
  background-size: cover;
  border-radius: 4px;
  line-height: 86px;
  font-size: 20px;
  text-align: center;
}

.ad img {
  width: 100%;
  margin-top: 15px;
  margin-right: 0px;
  margin-bottom: 15px;
  margin-left: 0px;
}

.ad_title,
.ad_title2 {
  line-height: 30px;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #ccc;
  border-right-color: #ccc;
  border-bottom-color: #ccc;
  border-left-color: #ccc;
  position: relative;
  height: 30px;
  padding: 0px;
}

.ad_title a {
  font-size: 16px;
  color: #0c61cc;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 3px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #0c61cc;
  border-right-color: #0c61cc;
  border-bottom-color: #0c61cc;
  border-left-color: #0c61cc;
  display: block;
  width: 70px;
  position: absolute;
  bottom: 0px;
  bottom: 0;
}

.ad_title2 a {
  font-size: 16px;
  color: #f0834e;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 3px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #f0834e;
  border-right-color: #f0834e;
  border-bottom-color: #f0834e;
  border-left-color: #f0834e;
  display: block;
  width: 70px;
  position: absolute;
  bottom: 0px;
  bottom: 0;
}

.ad_exr {
  padding-left: 20px;
  padding-top: 20px;
  background-color: #fff;
}

ul.ad_list {
  margin: 0px;
  padding-top: 20px;
  padding-right: 0px;
  padding-bottom: 20px;
  padding-left: 0px;
}

ul.ad_list li {
  margin: 0px;
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 5px;
  padding-left: 0px;
}

ul.ad_list li a {
  color: #666;
}

ul.chengguo_list {
  margin: 0px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  width: 100%;
  padding-top: 30px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 0px;
}

ul.chengguo_list li {
  width: 30%;
  padding-top: 10px;
  padding-bottom: 10px;
}

ul.chengguo_list li img {
  width: 100%;
  height: 162px;
}

.chengguo_title2 {
  padding-left: 3%;
  padding-right: 3%;
  font-size: 16px;
  line-height: 35px;
  height: 35px;
  margin-top: 8px;
  width: 100%;
  overflow: hidden;
  /*超出部分隐藏*/
  white-space: nowrap;
  /*不换行*/
  text-overflow: ellipsis;
  /*超出部分文字以...显示*/
}

.chengguo_gongsi {
  padding-left: 3%;
  padding-right: 3%;
  font-size: 14px;
  line-height: 30px;
  height: 30px;
  color: #666;
  width: 100%;
  overflow: hidden;
  /*超出部分隐藏*/
  white-space: nowrap;
  /*不换行*/
  text-overflow: ellipsis;
  /*超出部分文字以...显示*/
}

.pick:after {
  content: "";
  width: 280px;
  display: block;
}

.pick2:after {
  content: "";
  width: 380px;
  display: block;
}

.pick3:after {
  content: "";
  width: 600px;
  display: block;
}

ul.xuqiu_list {
  margin: 0px;
  width: 100%;
  padding-top: 30px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 0px;
}

ul.xuqiu_list li {
  border: 1px solid #eee;
  padding: 15px;
  margin-top: 5px;
  margin-right: 0px;
  margin-bottom: 20px;
  margin-left: 0px;
}

ul.xuqiu_list li:hover {
  border: 1px solid #3986e9;
}

ul.xuqiu_list li a {
  width: 100%;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

.xuqiu_title2 {
  font-size: 16px;
  line-height: 35px;
  height: 35px;
  margin-top: 00px;
}

.xuqiu_gongsi {
  font-size: 14px;
  line-height: 30px;
  height: 30px;
  color: #666;
}

.xuqiu_pic2 {
  width: 240px;
}

.xuqiu_r {
  width: 560px;
}

/*page*/

ul#page {
  width: 100%;
  margin: 0px;
  text-align: center;
  padding-top: 20px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 0px;
}

ul#page li {
  margin: 0px;
  padding: 2px;
  display: inline;
}

ul#page li a {
  color: #666;
  font-size: 14px;
  background-color: #fff;
  padding-top: 5px;
  padding-right: 13px;
  padding-bottom: 5px;
  padding-left: 13px;
  border: 1px solid #eee;
  border-radius: 5px;
}

ul#page li a:hover {
  color: #fff;
  background-color: #de791b;
  border: 1px solid #de791b;
}

ul#menu {
  margin: 0px;
  padding: 0px;
}

ul#menu li {
  margin: 0px;
  line-height: 40px;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 0px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #ccc;
  border-right-color: #ccc;
  border-bottom-color: #ccc;
  border-left-color: #ccc;
  font-size: 16px;
  position: relative;
  padding-left: 100px;
  padding-top: 10px;
  padding-right: 0px;
  padding-bottom: 10px;
}

ul#menu li span {
  width: 100px;
  position: absolute;
  left: 0;
}

ul#menu li a {
  padding-right: 10px;
  padding-left: 10px;
  margin-right: 5px;
  font-size: 16px;
  padding-top: 5px;
  padding-bottom: 5px;
}

ul#menu li a:hover,
.sel {
  color: #fff;
  background-color: #3986e9;
  text-align: center;
}

.queding {
  background-color: #3986e9;
  color: #fff;
  border-radius: 5px;
  line-height: 35px;
  height: 35px;
  display: block;
  text-align: center;
  width: 100px;
  padding: 0px;
  margin-top: 0px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: auto;
}

.banner3 {
  width: 1200px;
  margin: 0 auto;
}

.chengguo_l {
  width: 460px;
  position: relative;
  height: 280px;
}

.chengguo_l img {
  width: 100%;
}

.chengguo_r {
  width: 680px;
  font-size: 16px;
  line-height: 40px;
}

.chengguo_r strong {
  color: #666;
  font-weight: normal;
}

.chengguo_c {
  padding-top: 20px;
  font-size: 16px;
}

.chengguo_c_t {
  font-size: 16px;
  color: #333;
}

.chengguo_c_t strong {
  font-size: 18px;
  color: #f25e5e;
}

.chengguo_c_c {
  color: #999;
  padding-top: 10px;
  padding-bottom: 10px;
}

.chengguo_tt {
  line-height: 35px;
  font-size: 22px;
  font-weight: bold;
  color: #333;
  position: relative;
}

.chengguo_tt strong {
  font-size: 18px;
  color: #666;
}

.biaoqian {
  color: #3986e9 !important;
  border: 1px solid #3986e9;
  margin-right: 10px;
  padding-top: 3px;
  padding-right: 10px;
  padding-bottom: 3px;
  padding-left: 10px;
}

.shoucang {
  position: absolute;
  right: 0;
  color: #3986e9;
  font-size: 16px;
  font-weight: normal;
}

ul.huodong_list {
  margin: 0px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  padding-top: 30px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 0px;
}

ul.huodong_list li {
  width: 49%;
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 20px;
  padding-left: 0px;
}

ul.huodong_list li img {
  width: 100%;
  height: 260px;
}

.huodong_t {
  font-size: 24px;
  padding: 0px;
  width: 1200px;
  margin-top: 0px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
}

/*.logo3 {*/
/*  position: absolute;*/
/*  top: 20px;*/
/*  left: 20px;*/
/*  width: 195px;*/
/*  height: 50px;*/
/*}*/

.login {
  position: absolute;
  left: 50%;
  top: 350px;
  margin-left: 280px;
  background: #fff;
  padding: 60px 30px;
  box-sizing: border-box;
  border-radius: 6px;
  width: 350px;
}

.login_t {
  font-size: 18px;
  line-height: 30px;
  color: #3E86F9;
}

.biaodan {
  margin-top: 0px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
  width: 100%;
  border: 0px dotted #3E86F9;
}

.biaodan1 {
  line-height: 40px;
  background-size: 6%;
  background-repeat: no-repeat;
  background-position: left center;
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 10%;
  text-align: left;
  background-image: url(../images/shoujihao.png);
  width: 90%;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #ccc;
  border-right-color: #ccc;
  border-bottom-color: #ccc;
  border-left-color: #ccc;
  margin-top: 20px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 0px;
}

.biaodan2 {
  line-height: 40px;
  background-size: 6%;
  background-repeat: no-repeat;
  background-position: left center;
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 10%;
  background-color: #fff;
  margin-top: 20px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 0px;
  background-image: url(../images/yanzhengma.png);
  position: relative;
  width: 90%;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #ccc;
  border-right-color: #ccc;
  border-bottom-color: #ccc;
  border-left-color: #ccc;
}

.biaodan2 span {
}

.yanzhengma {
  color: #fff;
  background-color: #fff;
  background: transparent linear-gradient(105deg, #3E86F9 0%, #7AB2FF 100%) 0% 0% no-repeat padding-box;
  border: 0px solid #3E86F9;
  border-radius: 15px;
  margin-right: 0px;
  font-size: 12px;
  padding-top: 5px;
  padding-right: 10px;
  padding-bottom: 5px;
  padding-left: 10px;
  position: absolute;
  right: 0%;
}

.txt1 {
  line-height: 30px;
  background-color: transparent;
  width: 85%;
  color: #999999;
  margin-top: 0px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: auto;
  font-size: 14px;
  border: 0px solid #eee;
}

input:focus {
  outline: none;
}

.but1 {
  background: transparent linear-gradient(105deg, #3E86F9 0%, #7AB2FF 100%) 0% 0% no-repeat padding-box;
  padding: 0px;
  height: 51px;
  width: 100%;
  margin-top: 30px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 0px;
  border-left-width: 0px;
  font-size: 16px;
  color: #fff;
  cursor: pointer;
}

.but2 {
  background: transparent linear-gradient(105deg, #3E86F9 0%, #7AB2FF 100%) 0% 0% no-repeat padding-box;
  padding: 0px;
  height: 51px;
  width: 30%;
  margin-top: 30px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 0px;
  border-left-width: 0px;
  font-size: 16px;
  color: #fff;
  border-radius: 5px;
}

.chenggong-box {
  position: fixed;
  width: 80%;
  left: 10%;
  right: 0;
  top: 40%;
  z-index: 999;
}

.chenggong {
  color: #f37e00;
  text-align: center;
  background-color: #ffe2c4;
  line-height: 50px;
  border-radius: 5px;
  width: 50%;
  margin-top: 0;
  margin-right: auto;
  margin-bottom: 30px;
  margin-left: auto;
}

ul.biaodan_list {
  padding: 0px;
  width: 70%;
  margin-top: 30px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
}

ul.biaodan_list li {
  width: 100%;
  line-height: 40px;
  font-size: 16px;
  text-align: right;
  padding-top: 10px;
  padding-bottom: 10px;
  clear: both;
}

ul.biaodan_list li strong {
  color: #d32525;
  padding-right: 5px;
}

ul.biaodan_list li span {
  float: right;
  width: 87%;
  text-align: left;
  padding-left: 3%;
}

.text1 {
  width: 94%;
  border: 1px solid #ccc;
  line-height: 40px;
  height: 40px;
  margin: 0px;
  padding-top: 0px;
  padding-right: 0%;
  padding-bottom: 0px;
  padding-left: 3%;
  border-radius: 5px;
  font-size: 14px;
  outline: none;
}

.text2 {
  width: 97%;
  border: 1px solid #ccc;
  line-height: 40px;
  height: 40px;
  margin: 0px;
  padding-top: 0px;
  padding-right: 0%;
  padding-bottom: 0px;
  padding-left: 3%;
  border-radius: 5px;
  font-size: 14px;
  outline: none;
  color: #666;
}

.textarea {
  width: 94%;
  border: 1px solid #ccc;
  line-height: 40px;
  height: 120px;
  margin: 0px;
  padding-top: 0px;
  padding-right: 0%;
  padding-bottom: 0px;
  padding-left: 3%;
  border-radius: 5px;
  font-size: 14px;
  outline: none;
  font-family: "微软雅黑";
}

.textarea2 {
  width: 94%;
  border: 1px solid #ccc;
  line-height: 40px;
  height: 120px;
  margin: 0px;
  padding-top: 0px;
  padding-right: 0%;
  padding-bottom: 0px;
  padding-left: 3%;
  padding-right: 3%;
  border-radius: 5px;
  font-size: 14px;
  outline: none;
  font-family: "微软雅黑";
}

/*需求*/

.xuqiu_bg {
  background-image: url(../images/xuqiu_bg.png);
  background-repeat: no-repeat;
  background-position: center top;
  padding: 0px;
  min-height: 530px;
  margin-top: 0px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
  min-width: 1200px;
}

.xuqiu_l {
  width: 340px;
  position: relative;
  margin-top: 15px;
}

.chengguo_l img {
  width: 100%;
}

.xuqiu_r2 {
  width: 370px;
  font-size: 16px;
  line-height: 40px;
}

.xuqiu_r2 strong {
  color: #666;
  font-weight: normal;
}

.xuqiu_c2 {
  padding-top: 20px;
  font-size: 16px;
}

.xuqiu_c_t {
  font-size: 16px;
  color: #333;
}

.xuqiu_c_t strong {
  font-size: 18px;
  color: #f25e5e;
}

.xuqiu_c_c {
  color: #999;
  padding-top: 10px;
  padding-bottom: 10px;
}

.xuqiu_tt {
  line-height: 35px;
  font-size: 22px;
  font-weight: bold;
  color: #333;
  position: relative;
  width: 100%;
}

.xuqiu_tt strong {
  font-size: 18px;
  color: #666;
}

/*专家*/

ul.zhuanjia_list {
  margin: 0px;
  width: 100%;
  padding-top: 30px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 0px;
}

ul.zhuanjia_list li {
  border: 1px solid #eee;
  padding: 15px;
  margin-top: 5px;
  margin-right: 0px;
  margin-bottom: 20px;
  margin-left: 0px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

ul.zhuanjia_list li:hover {
  border: 1px solid #3986e9;
}

.zhuanjia_l {
  width: 150px;
}

.zhuanjia_l img {
  border-radius: 50%;
}

.zhuanjia_l2 {
  width: 120px;
  margin: 0 auto;
}

.zhuanjia_l2 img {
  border-radius: 50%;
}

.zhuanjia_m {
  width: 450px;
  color: #666;
}

.zhuanjia_r {
  width: 120px;
  padding-top: 60px;
}

.zhuanjia_r a {
  display: block;
  border: 1px solid #3986e9;
  text-align: center;
  color: #3986e9;
  border-radius: 5px;
}

.zhuanjia_r a:hover {
  background-color: #3986e9;
  color: #fff;
}

.zhuanjia_t {
  line-height: 40px;
}

.zhuanjia_t a {
  color: #666;
  font-size: 14px;
}

.zhuanjia_t a strong {
  font-size: 18px;
  color: #e7742e;
  padding-right: 15px;
}

.biaoqian2 {
  padding-top: 10px;
}

.biaoqian_list {
  padding-top: 20px;
}

.zhuanjia_title {
  font-size: 30px;
  color: #000000;
  line-height: 50px;
}

.zhuanjia_c {
  padding-top: 20px;
  padding-bottom: 20px;
}

.zhuanjia_c p {
  padding-top: 15px;
}

.zhuanjiajieshao {
  color: #666;
  padding-top: 50px;
  padding-right: 5%;
  padding-bottom: 0px;
  padding-left: 5%;
  color: #666;
}

.hezuoduijie a {
  display: block;
  border: 1px solid #3986e9;
  text-align: center;
  background-color: #3986e9;
  color: #fff;
  border-radius: 5px;
  line-height: 40px;
  width: 60%;
  margin-top: 10px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
}

/*新闻资讯*/

.bread_bg {
  line-height: 70px;
  background-color: #f0f2f5;
  height: 70px;
}

.bread3 {
  color: #333333;
  width: 1200px;
  margin: 0 auto;
  font-size: 18px;
  display: flex;
}

.bread3 a {
  height: 70px;
  display: block;
  width: 100px;
  text-align: center;
  margin-right: 25px;
}

.selected {
  color: #de791b;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 3px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #de791b;
  border-right-color: #de791b;
  border-bottom-color: #de791b;
  border-left-color: #de791b;
}

.selected2,
.selected2:hover {
  color: #de791b;
  font-size: 22px;
}

ul.news_list2 {
  width: 100%;
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 15px;
  padding-left: 0px;
}

ul.news_list2 li {
  width: 100%;
  line-height: 35px;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #eee;
  border-right-color: #eee;
  border-bottom-color: #eee;
  border-left-color: #eee;
  padding-top: 20px;
  padding-right: 0px;
  padding-bottom: 20px;
  padding-left: 0px;
}

ul.news_list2 li a {
  display: flex;
  width: 100%;
  flex-flow: row wrap;
  justify-content: space-between;
  width: 100%;
}

.news_l {
  width: 240px;
}

.news_r {
  width: 930px;
}

.news_t {
  font-size: 18px;
  line-height: 40px;
  color: #333333;
}

.news_date {
  color: #999;
}

.news_c {
  padding-top: 20px;
  padding-bottom: 20px;
  width: 100%;
}

.news_c h2 {
  font-size: 16px;
}

.news_c img {
  max-width: 100%;
}

.news_c p {
  padding-top: 15px;
  text-indent: 2em;
}

.xy_c {
  padding-top: 20px;
  padding-bottom: 20px;
  width: 100%;
}

.xy_c h2 {
  font-size: 16px;
}

.xy_c img {
  max-width: 100%;
}

.xy_c p {
  padding-top: 15px;
  text-indent: 0em;
}

ul.news_exr_list {
  margin: 0px;
  padding: 0px;
}

ul.news_exr_list li {
  margin: 0px;
  padding: 0px;
  background-image: url(../images/li.png);
  background-repeat: no-repeat;
  background-position: left center;
  padding-left: 30px;
  line-height: 40px;
}

ul.news_exr_list2 {
  margin: 0px;
  padding-top: 10px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 0px;
}

ul.news_exr_list2 li {
  padding: 12px;
  box-shadow: 0 2px 5px #eee;
  border-radius: 5px;
  margin-top: 15px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 0px;
  border-top-width: 1px;
  border-right-width: 1px;
  border-bottom-width: 0px;
  border-left-width: 1px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #eee;
  border-right-color: #eee;
  border-bottom-color: #eee;
  border-left-color: #eee;
}

ul.news_exr_list2 li a {
  display: flex;
  width: 100%;
  flex-flow: row wrap;
  justify-content: space-between;
}

.news_exr_l {
  width: 30%;
}

.news_exr_r {
  width: 65%;
  line-height: 20px;
}

.time {
  background-repeat: no-repeat;
  background-position: left center;
  padding-left: 20px;
  font-size: 12px;
  background-size: 15px;
}

/*科创资讯*/

ul.kechuang_list {
  width: 100%;
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 15px;
  padding-left: 0px;
}

ul.kechuang_list li {
  line-height: 35px;
  padding: 20px;
  border-radius: 5px;
  border-top-width: 1px;
  border-right-width: 1px;
  border-bottom-width: 0px;
  border-left-width: 1px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #fff;
  border-right-color: #fff;
  border-bottom-color: #fff;
  border-left-color: #fff;
}

ul.kechuang_list li:hover {
  box-shadow: 0 3px 5px #eee;
  border-top-width: 1px;
  border-right-width: 1px;
  border-bottom-width: 0px;
  border-left-width: 1px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #eee;
  border-right-color: #eee;
  border-bottom-color: #eee;
  border-left-color: #eee;
}

ul.kechuang_list li a {
  display: flex;
  width: 100%;
  flex-flow: row wrap;
  justify-content: space-between;
  width: 100%;
}

.kechuang_l {
  width: 200px;
}

.kechuang_r {
  width: 590px;
}

.kechuang_t {
  font-size: 18px;
  line-height: 40px;
  color: #333333;
}

.kechuang_date {
  color: #999;
}

.zx {
  color: #fff;
  background-color: #3986e9;
  font-size: 14px;
  margin-top: 0px;
  margin-right: 10px;
  margin-bottom: 0px;
  margin-left: 0px;
  padding-top: 5px;
  padding-right: 10px;
  padding-bottom: 5px;
  padding-left: 10px;
}

.rd {
  color: #fff;
  background-color: #f0834e;
  font-size: 14px;
  margin-top: 0px;
  margin-right: 10px;
  margin-bottom: 0px;
  margin-left: 0px;
  padding-top: 5px;
  padding-right: 10px;
  padding-bottom: 5px;
  padding-left: 10px;
}

.kechuang_banner {
  width: 100%;
  height: 280px;
  position: relative;
  margin-bottom: 30px;
}

ul.chanye_list {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

ul.chanye_list li {
  width: 25%;
  text-align: center;
}

.chanye_t {
  font-size: 24px;
  color: #333;
  background-image: url(../images/line2.png);
  background-repeat: no-repeat;
  background-position: center bottom;
  padding-top: 20px;
  padding-bottom: 20px;
  margin-bottom: 20px;
}

ul.zixun_list {
  margin: 0px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 40px;
  padding-left: 0px;
}

ul.zixun_list li {
  margin: 0px;
  width: 24%;
  text-align: right;
  color: #666;
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: px;
  padding-left: 0px;
  border-radius: 10px;
  border-top-width: 0px;
  border-right-width: 1px;
  border-bottom-width: 0px;
  border-left-width: 1px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #fff;
  border-right-color: #fff;
  border-bottom-color: #fff;
  border-left-color: #fff;
}

ul.zixun_list li img {
  width: 100%;
}

ul.zixun_list li a {
  font-size: 18px;
  line-height: 50px;
}

ul.zixun_list li:hover {
  box-shadow: 0 8px 10px #eee;
  border-top-width: 0px;
  border-right-width: 1px;
  border-bottom-width: 0px;
  border-left-width: 1px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #eee;
  border-right-color: #eee;
  border-bottom-color: #eee;
  border-left-color: #eee;
}

.zixun_c {
  padding: 15px;
  font-size: 14px;
  color: #666;
  line-height: 30px;
}

.zixun_c strong {
  font-size: 18px;
  font-weight: normal;
  color: #333;
}

ul.fangan_list {
  margin: 0px;
  padding: 0px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

ul.fangan_list li {
  width: 32%;
  position: relative;
  margin-top: 0px;
  margin-right: 0px;
  margin-bottom: 30px;
  margin-left: 0px;
}

ul.fangan_list li img {
  width: 100%;
}

.fangan_t {
  position: absolute;
  bottom: 0;
  line-height: 50px;
  width: 100%;
  color: #fff;
  background-image: url(../images/black.png);
  background-repeat: repeat;
  text-align: center;
  font-size: 16px;
}

ul.xuqiu_list2 {
  margin: 0px;
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 20px;
  padding-left: 0px;
}

ul.xuqiu_list2 li {
  padding: 0px;
  margin-top: 10px;
  margin-right: 0px;
  margin-bottom: 10px;
  margin-left: 0px;
}

ul.xuqiu_list2 li a {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  padding: 20px;
  color: #666;
  line-height: 40px;
}

ul.xuqiu_list2 li a i {
  font-style: normal;
  color: #de791b;
}

ul.xuqiu_list2 li a:hover {
  box-shadow: 0 0 10px #ccc;
  border-radius: 5px;
}

ul.shuju_list {
  margin: 0px;
  padding: 0px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

ul.shuju_list li {
  font-size: 18px;
  color: #fff;
  background-image: url(../images/danwei.png);
  background-repeat: no-repeat;
  background-position: center center;
  text-align: center;
  margin: 0px;
  height: 104px;
  width: 214px;
  padding-top: 40px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 0px;
}

ul.shuju_list li strong {
  font-size: 30px;
  font-weight: normal;
}

ul.nengli_list {
  margin: 0px;
  padding: 0px;
  display: flex;
  width: 100%;
  flex-flow: row wrap;
  justify-content: space-between;
}

ul.nengli_list li {
  width: 20%;
  text-align: center;
  padding-top: 25px;
  padding-right: 6%;
  padding-bottom: 25px;
  padding-left: 6%;
  color: #666;
}

ul.nengli_list li p strong {
  padding-top: 0px;
  color: #333;
}

ul.nengli_list li img {
  padding-bottom: 10px;
}

.qiye_l {
  width: 584px;
  float: left;
}

.qiye_r {
  width: 550px;
  float: right;
}

.qiye_t {
  font-size: 18px;
  line-height: 50px;
  font-weight: bold;
  color: #333;
}

.qiyedetail,
.qiyedetail:hover {
  font-size: 14px;
  color: #ffffff;
  line-height: 50px;
  background-color: #1357a6;
  height: 50px;
  width: 100px;
  display: block;
  text-align: center;
  margin-top: 0px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: auto;
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 0px;
}

/* section */

ul.news_qiye_list {
  display: flex;
  width: 100%;
  flex-flow: row wrap;
  justify-content: space-between;
}

ul.news_qiye_list li {
  width: 32%;
  height: 300px;
  overflow: hidden;
  position: relative;
  margin-top: 0px;
  margin-bottom: 30px;
}

.news_qiye_list_t {
  position: absolute;
  bottom: 20px;
  left: 6%;
  font-size: 18px;
  color: #fff;
}

ul.news_qiye_list li .photo {
  width: 100%;
  height: 300px;
  overflow: hidden;
}

ul.news_qiye_list li .photo img {
  width: 100%;
  height: 300px;
}

ul.news_qiye_list li .rsp {
  width: 88%;
  height: 140px;
  overflow: hidden;
  position: absolute;
  background: rgba(0, 0, 0, 0.5);
  top: 0px;
  left: 0px;
  padding-top: 160px;
  padding-right: 6%;
  padding-left: 6%;
}

ul.news_qiye_list li .rsp a {
  color: #ffffff;
  font-size: 18px;
}

.text a {
  display: block;
  background-image: linear-gradient(to right, #3a60ea, #42bfd8);
  width: 120px;
  font-size: 14px !important;
  line-height: 50px;
  text-align: center;
  font-weight: normal;
  margin-top: 10px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 0px;
}

.hangye_bg {
  border: 1px solid #ccc;
  position: relative;
  padding-left: 150px;
  padding-top: 15px;
  padding-right: 15px;
  padding-bottom: 15px;
}

.hangye_bg a {
  padding-left: 10px;
  padding-right: 10px;
}

.hangye_l {
  color: #fff;
  background-color: #de791b;
  text-align: center;
  width: 140px;
  position: absolute;
  left: 0;
  top: 0;
  height: 90px;
  line-height: 90px;
}

.paixu {
  padding: 0px;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #eee;
  border-right-color: #eee;
  border-bottom-color: #eee;
  border-left-color: #eee;
  margin-top: 40px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 0px;
  width: 100%;
}

.paixu a {
  padding: 0px;
  margin-top: 0px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 0px;
  width: 100px;
  display: inline-block;
  text-align: center;
}

.selected4 {
  color: #de791b;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 2px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #de791b;
  border-right-color: #de791b;
  border-bottom-color: #de791b;
  border-left-color: #de791b;
}

ul.hangye_list {
  margin: 0px;
  padding: 0px;
  display: flex;
  width: 100%;
  flex-flow: row wrap;
  justify-content: space-between;
}

ul.hangye_list li {
  padding: 0px;
  width: 285px;
  border: 1px solid #fff;
  margin-top: 20px;
  margin-right: 0px;
  margin-bottom: 10px;
  margin-left: 0px;
  border-radius: 5px;
}

ul.hangye_list li img {
  width: 100%;
  height: 160px;
}

ul.hangye_list li a {
  color: #666;
}

ul.hangye_list li a strong {
  color: #666;
  font-size: 16px;
}

ul.hangye_list li:hover {
  border: 1px solid #eee;
  box-shadow: 0 3px 5px #eee;
}

ul.hangye_list li:hover a strong {
  color: #de791b;
}

.gongyingshang {
  font-size: 16px;
  text-align: center;
  border-top-width: 1px;
  border-right-width: 0px;
  border-bottom-width: 0px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #eee;
  border-right-color: #eee;
  border-bottom-color: #eee;
  border-left-color: #eee;
  line-height: 35px;
}

.hangye_c {
  padding: 10px;
}

.case_top {
  width: 1200px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  margin-top: 0;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  padding-top: 20px;
  padding-bottom: 20px;
}

.case_top p {
  padding-top: 10px;
  padding-bottom: 10px;
}

.case_top p strong {
  font-size: 30px;
}

.case1_l {
  width: 530px;
  padding-top: 60px;
}

.case1_r {
  width: 50%;
}

.fangan2 {
  padding: 5%;
  border: 1px solid #eee;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  width: 90%;
}

.fangan2_l {
  width: 410px;
}

.fangan2_r {
  width: 600px;
  line-height: 30px;
  color: #666;
}

.fangan2_r p {
  padding-top: 10px;
}

.fangan2_r strong {
  font-size: 18px;
  font-weight: normal;
  color: #333;
}

.fangan2_r a {
  color: #de791b;
}

ul.fangan3 {
  padding: 0%;
  border: 0px solid #eee;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  width: 100%;
  border-collapse: collapse;
  margin-top: 30px;
}

ul.fangan3 li {
  border: 1px solid #eee;
  width: 519px;
  margin-top: -1px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: -1px;
  padding-top: 40px;
  padding-right: 40px;
  padding-bottom: 80px;
  padding-left: 40px;
  background: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  line-height: 40px;
}

ul.fangan3 li:hover {
  box-shadow: 2px 2px 5px #eee;
}

.xiaoren {
  position: absolute;
  right: -150px;
  top: 0;
  z-index: -1;
}

.changjing_tab {
  display: flex;
  flex-flow: row wrap;
  justify-content: center;
  width: 100%;
}

.changjing_tab a {
  padding-top: 0px;
  padding-right: 30px;
  padding-bottom: 30px;
  padding-left: 30px;
}

.selected5 {
  background-image: url(../images/sanjiao.png);
  background-repeat: no-repeat;
  background-position: center bottom;
  color: #de791b;
}

.changjing_c {
  background-color: #fff;
  width: 1100px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  padding-top: 5%;
  padding-right: 50px;
  padding-bottom: 5%;
  padding-left: 50px;
}

.changjing_l {
  width: 600px;
}

.changjing_r {
  width: 370px;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 0px;
  border-left-width: 1px;
  border-top-style: dotted;
  border-right-style: dotted;
  border-bottom-style: dotted;
  border-left-style: dotted;
  border-top-color: #666;
  border-right-color: #666;
  border-bottom-color: #666;
  border-left-color: #666;
  min-height: 350px;
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 50px;
}

ul.qiyelogo_list {
  padding: 0px;
  display: flex;
  flex-flow: row wrap;
  justify-content: left;
  margin-top: 20px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 0px;
}

ul.qiyelogo_list li {
  border: 1px solid #eee;
  width: 299px;
  margin-top: -1px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: -1px;
  text-align: center;
  padding-top: 10px;
  padding-right: 0px;
  padding-bottom: 10px;
  padding-left: 0px;
  background-color: #fff;
}

ul.qiyelogo_list li img {
  width: 70%;
  height: 80px;
}

ul.yingyong_list {
  margin: 0px;
  padding: 0px;
  /* display: flex; */
  width: 100%;
  /* flex-flow: row wrap;
    justify-content: space-between; */
}

ul.yingyong_list li {
  padding: 0px;
  width: 280px;
  float: left;
  border: 1px solid #eee;
  margin-top: 0px;
  margin-right: 18px;
  margin-bottom: 10px;
  margin-left: 0px;
  border-radius: 5px;
  text-align: center;
}

ul.yingyong_list li img {
  width: 100%;
}

ul.yingyong_list li a {
  color: #666;
}

ul.yingyong_list li a strong {
  color: #666;
  font-size: 16px;
}

ul.yingyong_list li:hover {
  border: 1px solid #eee;
  box-shadow: 0 3px 5px #eee;
}

ul.yingyong_list li:hover a strong {
  color: #de791b;
}

ul.yingyong_list2 {
  margin: 0px;
  padding: 0px;
  display: flex;
  width: 100%;
  flex-flow: row wrap;
  justify-content: space-between;
}

.yingyong_list2_new {
  margin: 0px;
  padding: 0px;
  display: flex;
  width: 100%;
  flex-flow: row wrap;
  justify-content: space-between;
}

.yy_list2new {
  width: 288px;
  height: 188px;
  border: 1px solid #eeeeee;
  position: relative;
}

.yy_list2new:hover {
  cursor: pointer;
  box-shadow: 0px 4px 10px 0px rgba(51, 51, 51, 0.18);
}

.yy_list2newimg {
  position: absolute;
  top: 0;
  right: 0;
}

.yy_list2newbox {
  width: 100%;
}

.yy_list2newboximg {
  width: 58px;
  margin: 46px 15px 0 10px;
  height: 58px;
  float: left;
}

.yy_list2newboximg img {
  width: 100%;
  height: 100%;
}

ul.yingyong_list2 li {
  width: 250px;
  border: 1px solid #eee;
  margin-top: 0px;
  margin-right: 0px;
  margin-bottom: 10px;
  margin-left: 0px;
  border-radius: 5px;
  padding-top: 5px;
  padding-right: 15px;
  padding-bottom: 15px;
  padding-left: 15px;
  color: #666;
}

ul.yingyong_list2 li:hover {
  box-shadow: 0 3px 5px #eee;
}

.yingyong_t {
  background-repeat: no-repeat;
  background-position: left center;
  padding-left: 40px;
  line-height: 60px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.yy_list2newtextbox {
  width: 188px;
  margin-top: 10px;
  float: left;
}

.yy_2newtextbox_t1 {
  color: #333333;
  font-size: 16px;
  line-height: 40px;
  font-weight: 500;
}

.yy_2newtextbox_t2 {
  color: #666666;
  font-size: 14px;
  line-height: 26px;
}

.yy_list2newbtnbox {
  width: 100%;
  float: left;
  display: flex;
  margin-top: 10px;
}

.tuijian1 {
  background-image: url(../images/tuijian1.png);
}

.tuijian2 {
  background-image: url(../images/tuijian2.png);
}

.tuijian3 {
  background-image: url(../images/tuijian3.png);
}

.tuijian4 {
  background-image: url(../images/tuijian4.png);
}

.biaoqian1 {
  margin: 0 auto;
}

.biaoqian1 a {
  color: #5283db;
  margin-right: 5px;
  border: 1px solid #5283db;
  border-radius: 5px;
  padding: 5px;
  margin-top: 5px;
}

.biaoqian22 a {
  color: #4cbb90;
  margin-right: 5px;
  border: 1px solid #4cbb90;
  border-radius: 5px;
  padding: 5px;
  margin-top: 5px;
}

.biaoqian3 a {
  color: #ad74e7;
  margin-right: 5px;
  border: 1px solid #ad74e7;
  border-radius: 5px;
  padding: 5px;
  margin-top: 5px;
}

.biaoqian4 a {
  color: #ed8865;
  margin-right: 5px;
  border: 1px solid #ed8865;
  border-radius: 5px;
  padding: 5px;
  margin-top: 5px;
}

.ljhq {
  padding-top: 10px;
}

.ljhq a,
.ljhq a:hover {
  color: #fff;
  background-color: #3193f0;
  margin-top: 0px;
  padding-top: 5px;
  padding-right: 15px;
  padding-bottom: 5px;
  padding-left: 15px;
  border-radius: 5px;
}

.ljhqas {
  color: #fff;
  background-color: #3193f0;
  margin-top: 0px;
  padding-top: 5px;
  padding-right: 15px;
  padding-bottom: 5px;
  padding-left: 15px;
  border-radius: 5px;
}

.yingyong_c {
  padding-top: 15px;
  padding-right: 15px;
  padding-bottom: 15px;
  padding-left: 15px;
  height: 130px;
}

.brief {
  line-height: 30px;
  height: 60px;
  overflow: hidden;
}

.ljhq:hover {
  cursor: pointer;
}

.yingyong_list_p_img {
  padding: 10px !important;
}

.yingyong_list_p_img {
  height: 181px;
}

.yingyong_list_p_img img {
  height: 181px;
}

.yingyong_c p {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.store {
  margin: 0px;
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 10px;
  padding-left: 0px;
}

.store_l {
  background-image: url(../images/store1.png);
  background-repeat: no-repeat;
  background-position: center bottom;
  width: 298px;
  border: 1px solid #eee;
  float: left;
  margin-top: 0px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 0px;
  text-align: center;
  height: 362px;
  border-radius: 0 0 0 10px;
  overflow: hidden;
  padding-top: 30px;
}

.store_l:hover {
  border: 1px solid #de791b;
}

.store_l a {
  font-size: 18px;
  font-weight: bold;
}

ul.store_list {
  display: flex;
  float: left;
  width: 900px;
  flex-flow: row wrap;
  justify-content: space-between;
}

ul.store_list li {
  border: 1px solid #eee;
  width: 268px;
  position: relative;
  padding-top: 25px;
  padding-right: 155px;
  padding-bottom: 80px;
  padding-left: 25px;
  margin-top: 0px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 0px;
}

ul.store_list li a {
  font-size: 16px;
  color: #666;
}

ul.store_list li:hover {
  border: 1px solid #de791b;
}

.store_pic {
  position: absolute;
  right: 25px;
}

.store_biaoqian {
  position: absolute;
  left: 25px;
  bottom: 25px;
}

.fabu_bg {
  padding: 50px;
  width: 1100px;
  margin-top: -50px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
  background-color: #fff;
  position: relative;
  border-radius: 5px;
}

.fabu_tab {
  display: flex;
  flex-flow: row wrap;
  justify-content: center;
}

.fabu_tab a {
  margin-left: 15px;
  margin-right: 15px;
  text-align: center;
  padding-top: 10px;
  padding-right: 50px;
  padding-bottom: 10px;
  padding-left: 50px;
  font-size: 18px;
}

.selected6,
.fabu_tab a:hover {
  background-color: #f37e00;
  color: #fff;
}

.shengtai_top {
  width: 1000px;
  margin: auto;
  color: #fff;
  padding-top: 100px;
  font-size: 16px;
}

.shengtai_top p {
  padding-top: 30px;
}

.shengtai_top p strong {
  font-size: 24px;
}

ul.zhaomu_list {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  width: 100%;
  padding-bottom: 50px;
}

ul.zhaomu_list li {
  width: 30%;
  border: 1px solid #eee;
}

.zhaomu_t {
  text-align: center;
  color: #333;
  line-height: 80px;
  background-color: #d2d2d2;
  font-size: 18px;
}

.zhaomu_c {
  padding: 25px;
}

.zhaomu_c a {
  color: #fff;
}

ul.zhaomu_list li:hover {
  border: 1px solid #ef8e34;
}

ul.zhaomu_list li:hover > .zhaomu_t {
  background-color: #ef8e34;
  color: #fff;
}

ul.zhaomu_list li:hover > .zhaomu_c a {
  color: #ef8e34;
}

.yingyonghangye {
  background: rgba(255, 255, 255, 0.8);
  width: 800px;
  margin-top: 0px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
  padding-top: 50px;
  padding-right: 100px;
  padding-bottom: 50px;
  padding-left: 100px;
}

.hangye_tab {
  text-align: center;
  line-height: 40px;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #333;
  border-right-color: #333;
  border-bottom-color: #333;
  border-left-color: #333;
}

.hangye_tab a {
  display: inline-block;
  padding-top: 0px;
  padding-right: 20px;
  padding-bottom: 0px;
  padding-left: 20px;
  font-size: 18px;
  line-height: 50px;
}

.selected7 {
  color: #de791b;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 2px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #de791b;
  border-right-color: #de791b;
  border-bottom-color: #de791b;
  border-left-color: #de791b;
}

.hangye_neirong {
  padding: 25px;
  height: 250px;
}

/*解决方案*/

.fangan_bg {
  background-color: #f5f7fa;
  padding-bottom: 50px;
}

.bread4 {
  padding: 0px;
  width: 1200px;
  margin-top: 0px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
  line-height: 70px;
}

.fangan1_bg {
  background-color: #fff;
  width: 1140px;
  margin-top: 0px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  padding: 30px;
}

.fangan2_bg {
  background-color: #fff;
  width: 1140px;
  margin-top: 20px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
  padding: 30px;
}

.fangan1_l {
  width: 60%;
}

.fangan_title {
  font-size: 18px;
  color: #333;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #eee;
  border-right-color: #eee;
  border-bottom-color: #eee;
  border-left-color: #eee;
  line-height: 35px;
}
.fangan_title a {
  display: inline-block;
  margin-right: 15px;
  margin-left: 15px;
}
.selected13 {
  color: #de791b;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 2px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #de791b;
  border-right-color: #de791b;
  border-bottom-color: #de791b;
  border-left-color: #de791b;
}
.fangan_title span {
  font-size: 14px;
  color: #666;
  padding-left: 15px;
}

.fangan1_c {
  padding-top: 10px;
}

.fangan1_c p {
  padding-top: 10px;
}

.fangan1_r {
  width: 35%;
}

.biaoqian2 {
  padding-top: 15px;
}

.biaoqian2 a {
  border: 1px solid #de791b;
  margin-top: 0px;
  margin-right: 10px;
  margin-bottom: 0px;
  margin-left: 0px;
  color: #de791b;
  padding-top: 3px;
  padding-right: 5px;
  padding-bottom: 3px;
  padding-left: 5px;
}

.about {
  width: 100%;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  padding-top: 30px;
  padding-bottom: 30px;
}

.about_l2 {
  width: 55%;
}

.about_r2 {
  width: 40%;
  text-align: right;
}

.about_r2 img {
}

ul.youshi_list {
  width: 100%;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  padding-top: 30px;
  padding-bottom: 30px;
}

ul.youshi_list li {
  width: 30%;
  font-size: 14px;
  background-image: url(../images/li2.png);
  background-repeat: no-repeat;
  background-position: left center;
  padding-left: 18px;
  line-height: 35px;
}

ul.xuqiu_list3 {
  margin: 0px;
  padding: 0px;
}

ul.xuqiu_list3 li {
  width: 90%;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  padding-top: 30px;
  padding-bottom: 30px;
  border: 1px solid #eee;
  margin-top: 20px;
  margin-bottom: 10px;
  padding-right: 5%;
  padding-left: 5%;
  color: #666;
  line-height: 35px;
}

ul.xuqiu_list3 li:hover {
  border: 1px solid #de791b;
}

.xuqiu1 {
  text-align: left;
  width: 40%;
}

.xuqiu1 a {
  font-size: 16px;
}

.xuqiu1 strong {
  font-size: 16px;
  color: #de791b;
}

.xuqiu2 {
  width: 15%;
}

.xuqiu2 a {
  border: 1px solid #eee;
  padding: 5px;
  margin-top: 0px;
  margin-right: 10px;
  margin-bottom: 0px;
  margin-left: 0px;
}

.xuqiu3 {
  width: 15%;
}

.xuqiu4 {
  width: 15%;
}

/***招聘右侧的样式**/

.recruitment-tb {
  table-layout: auto;
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  text-align: center;
  margin-top: 15px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
}

.recruitment-tb th {
  background: #de791b;
  padding: 10px 0;
  color: #fff;
  font-size: 14px;
  font-weight: normal;
  height: 26px;
}

.recruitment-tb td:first-of-type {
}

.recruitment-tb td {
  border-bottom: 1px solid #ddd;
  padding: 10px 0;
  color: #4c4c4c;
  font-size: 14px;
}

.recruitment-tb .detailTd {
  padding: 0;
}

.u-arrow.down {
  top: -12px;
  border-color: transparent transparent #de791b transparent;
}

.u-arrow {
  border-width: 8px;
  border-style: solid;
  border-color: #de791b transparent transparent transparent;
  cursor: pointer;
  position: relative;
  top: 10px;
}

.recruitment-detail {
  text-align: left;
  background-color: #fbfbfb;
  padding: 30px 0;
  border: 1px solid #ddd;
}

.section {
  padding: 30px 20px 0 25px;
  line-height: 28px;
  font-size: 14px;
}

.section h3 {
  font-size: 14px;
  color: #737373;
  font-weight: bold;
  line-height: 35px;
}

.section p {
}

.section span {
  padding: 6px 16px;
  background: #eefafe;
  color: #74bad0;
  border-radius: 9px;
}

.section span:first-child {
  margin-left: 0px;
}

.anli {
  position: absolute;
  left: 10%;
  width: 80%;
  top: 70px;
  color: #fff;
  font-size: 14px;
  line-height: 25px;
  text-align: left;
}

.picon {
  clear: both;
  height: 320px;
  padding-left: 4px;
  overflow: hidden;
  position: relative;
}

.picon ul {
  position: absolute;
  top: 0;
  left: 0;
  height: 320px;
  width: 1200px;
}

.picon li {
  float: left;
  height: 320px;
  border-right: 0px solid #ddd;
  width: 230px;
  position: relative;
  overflow: hidden;
  margin-top: 0px;
  margin-right: 10px;
  margin-bottom: 0px;
  margin-left: 10px;
}

.picon li.cur div {
  display: block;
}

.picon li div {
  position: absolute;
  bottom: 0;
  left: 5%;
  z-index: 9;
  height: 76px;
  width: 90%;
  color: #fff;
  display: none;
  font-size: 16px;
}

.denglu {
  color: #de791b;
}

.yingyong_bg {
  width: 1200px;
  color: #fff;
  margin-top: 0;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  padding-top: 80px;
}

.yingyong_l {
  width: 800px;
  float: left;
}

.yingyong_r {
  width: 300px;
  float: right;
  background-color: #23282e;
  padding: 20px;
  margin-top: 30px;
}

.yingyong_title {
  font-size: 24px;
  line-height: 50px;
  font-weight: bold;
}

/*专精特效*/

.zhengce_l {
  width: 520px;
}

ul.zczx_list {
  margin: 0px;
  padding: 0px;
}

ul.zczx_list li {
  margin: 0px;
  color: #666;
  padding-top: 10px;
  padding-right: 0px;
  padding-bottom: 10px;
  padding-left: 0px;
}

ul.zczx_list li a {
  color: #333;
  font-size: 18px;
  font-weight: bold;
}

ul.zczx_list li a:hover {
  color: #de791b;
}

.zhengce_r {
  width: 620px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

.zhengce_r1 {
  width: 300px;
  position: relative;
}

.zhengce_r2 {
  width: 300px;
}

.zhengce_r2_c {
  position: relative;
  margin-top: 0px;
  margin-right: 0px;
  margin-bottom: 20px;
  margin-left: 0px;
}

.zhengce_r_t {
  position: absolute;
  width: 80%;
  left: 10%;
  top: 20px;
}

.zhengce_r_t a {
  color: #fff;
  font-size: 16px;
}

.shuju {
  width: 1130px;
  background-color: #fff;
  margin-top: -70px;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  position: relative;
  border-radius: 10px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  border: 1px solid #eee;
  padding-top: 15px;
  padding-right: 35px;
  padding-bottom: 15px;
  padding-left: 35px;
}

.shuju_c {
  text-align: center;
  width: 16%;
  border-top-width: 0px;
  border-right-width: 1px;
  border-bottom-width: 0px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #eee;
  border-right-color: #eee;
  border-bottom-color: #eee;
  border-left-color: #eee;
}

.shuju_c i {
  font-style: normal;
  color: #317be1;
}

.shuju_c strong {
  color: #317be1;
  font-size: 24px;
  font-weight: bold;
}

ul.fuwu_list {
  margin: 0px;
  padding: 0px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

ul.fuwu_list li {
  padding: 0px;
  width: 32%;
  box-shadow: 2px 2px 6px #eee;
  border-radius: 5px;
  height: 160px;
  position: relative;
  margin-top: 10px;
  margin-right: 0px;
  margin-bottom: 10px;
  margin-left: 0px;
}

ul.fuwu_list li strong {
  font-size: 18px;
}

.fuwu_top {
  padding: 15px;
}

ul.fuwu_list li a {
  display: block;
  text-align: center;
  line-height: 40px;
  color: #fff;
  background-color: #285fc0;
  display: none;
  position: absolute;
  bottom: 0;
  width: 100%;
}

ul.fuwu_list li:hover a {
  display: block;
}

ul.fuwu_list li:hover {
  box-shadow: 2px 2px 6px #ccc;
}

.case_bg {
  background-repeat: no-repeat;
  background-position: center center;
  height: 270px;
  color: #fff;
  padding-top: 190px;
  padding-right: 10%;
  padding-bottom: 0px;
  padding-left: 10%;
}

.case_bg1 {
  background-image: url(../images/case_bg.png);
}

.case_bg2 {
  background-image: url(../images/case_bg2.png);
}

.case_bg3 {
  background-image: url(../images/case_bg3.png);
}

.case_bg4 {
  background-image: url(../images/case_bg4.png);
}

.case_tab2 {
  width: 80%;
  position: absolute;
  left: 10%;
  top: 120px;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #fff;
  border-right-color: #fff;
  border-bottom-color: #fff;
  border-left-color: #fff;
  text-align: center;
  line-height: 30px;
}

.case_tab2 a {
  display: inline-block;
  padding: 0px;
  margin-top: 0px;
  margin-right: 25px;
  margin-bottom: 0px;
  margin-left: 25px;
  font-size: 16px;
  color: #fff;
}

.selected9 {
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 4px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #285fc0;
  border-right-color: #285fc0;
  border-bottom-color: #285fc0;
  border-left-color: #285fc0;
}

.c_title {
  font-size: 24px;
  line-height: 50px;
  margin-bottom: 15px;
}

.kechuang_fabu {
  width: 1100px;
  margin: 0 auto;
  padding-top: 350px;
}

.kechuang_fabu2 {
  width: 1230px;
  margin: 0 auto;
  top: 580px;
  position: absolute;
  z-index: 10;
  left: 0;
  right: 0;
}

.kechuang_fabu a,
.kechuang_fabu2 a {
  color: #fff;
  background-color: #285fc0;
  display: inline-block;
  padding-top: 8px;
  padding-right: 25px;
  padding-bottom: 8px;
  padding-left: 25px;
  margin-top: 0px;
  margin-right: 15px;
  margin-bottom: 0px;
  margin-left: 0px;
  border-radius: 5px;
}

.shuzihua {
  width: 1200px;
  position: relative;
  margin-top: -70px;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  font-size: 14px;
  padding-top: 40px;
  padding-right: 0px;
  padding-bottom: 40px;
  padding-left: 0px;
}

.shuzihua_tab {
  width: 100%;
  display: flex;
  flex-flow: row wrap;
  justify-content: center;
  background-color: #fff;
  border: 0px solid #eee;
  padding-top: 30px;
  line-height: 40px;
  box-shadow: 0 0 3px #ccc;
  border-radius: 5px;
}

.shuzihua_tab a {
  width: 80px;
  font-size: 18px;
  text-align: center;
  margin-top: 0px;
  margin-right: 30px;
  margin-bottom: 0px;
  margin-left: 30px;
}

.selected10 {
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 3px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #de791b;
  border-right-color: #de791b;
  border-bottom-color: #de791b;
  border-left-color: #de791b;
  color: #de791b;
}

ul.shuzihua_list {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  margin-top: 20px;
  padding-top: 20px;
  padding-bottom: 50px;
  min-height: 350px;
}

ul.shuzihua_list li {
  width: 33%;
  background-color: #efefef;
  margin-top: 15px;
  margin-bottom: 15px;
  padding-top: 15px;
  padding-right: 10%;
  padding-bottom: 15px;
  padding-left: 3%;
}

.shuzihua_r {
  text-align: right;
  padding-top: 0px;
  padding-right: 3% !important;
  padding-bottom: 0px;
  padding-left: 10% !important;
}

.shuzihua_map {
  position: absolute;
  left: 50%;
  top: 180px;
  margin-left: -160px;
}

.chanye_c {
  color: #fff;
  width: 700px;
  margin-top: 0;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  padding-right: 500px;
  padding-top: 150px;
  padding-bottom: 0px;
  padding-left: 0px;
}

.chanye_c strong {
  font-size: 18px;
  line-height: 40px;
}

ul.quyuyoushi,
ul.quyuzixun,
ul.quyuyoushi2 {
  margin: 0px;
  padding: 0px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  width: 100%;
}

ul.quyuyoushi li {
  text-align: center;
  width: 33%;
  margin-top: 5px;
  margin-right: 0px;
  margin-bottom: 5px;
  margin-left: 0px;
  padding: 0px;
}

ul.quyuyoushi2 li {
  text-align: center;
  width: 49%;
  margin-top: 5px;
  margin-right: 0px;
  margin-bottom: 5px;
  margin-left: 0px;
  padding: 0px;
  box-shadow: 0 0 5px #ccc;
}

ul.quyuyoushi li a {
  display: block;
  background-color: #fff;
  padding-top: 25px;
  padding-bottom: 25px;
  border: 1px solid #fff;
}

ul.quyuyoushi2 li a {
  display: block;
  background-color: #fff;
  border: 1px solid #fff;
  padding: 25px;
}

ul.quyuyoushi li a:hover,
ul.quyuyoushi2 li a:hover {
  border: 1px solid #eee;
}

ul.quyuzixun li {
  width: 550px;
  margin-top: 5px;
  margin-right: 0px;
  margin-bottom: 5px;
  margin-left: 0px;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #eee;
}

ul.quyuzixun li a {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

.qyzx_l {
  width: 200px;
}

.qyzx_r {
  width: 305px;
  text-align: left;
  line-height: 20px;
  color: #666;
}

.qyzx_t {
  line-height: 25px;
  padding-bottom: 10px;
  font-weight: bold;
}

ul.quyuxuqiu {
  background-color: #e8ebf0;
  width: 1140px;
  padding-top: 20px;
  padding-right: 30px;
  padding-bottom: 20px;
  padding-left: 30px;
  max-height: 150px;
  overflow: hidden;
}

ul.quyuxuqiu li {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  padding-top: 5px;
  padding-bottom: 5px;
  font-size: 16px;
}

.quyu1 {
  width: 45px;
  text-align: center;
  color: #fff;
  line-height: 30px;
  background-color: #c7000b;
  border-radius: 5px;
}

.quyu2 {
  width: 750px;
  height: 30px;
  line-height: 30px;
  overflow: hidden;
}

.quyu2 a {
  font-size: 16px;
}

.quyu3 {
  width: 50px;
  font-size: 16px;
  color: #c7000b;
}

.quyu4 {
  width: 220px;
}

.tesecase {
  background-image: url(../images/teseanli.png);
  background-repeat: no-repeat;
  background-position: center center;
  height: 280px;
  width: 100%;
}

.tesecase_c {
  background-color: #fff;
  width: 680px;
  padding-top: 20px;
  padding-right: 30px;
  padding-bottom: 20px;
  padding-left: 30px;
  line-height: 25px;
  margin-top: 80px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 400px;
}

ul.tongdian_list {
  margin: 0px;
  padding: 0px;
  display: flex;
  flex-flow: row wrap;
  justify-content: left;
  width: 100%;
}

ul.tongdian_list li {
  margin: 0px;
  width: 14%;
  text-align: center;
  padding-top: 40px;
  padding-right: 3%;
  padding-bottom: 20px;
  padding-left: 3%;
  font-size: 16px;
  color: #222222;
}

ul.tongdian_list li:nth-of-type(odd) {
  background: #eef4f9;
}

ul.tongdian_list li:nth-of-type(even) {
  background: #fff;
}

ul.hangyeyoushi_list {
  margin: 0px;
  padding: 0px;
  display: flex;
  flex-flow: row wrap;
  justify-content: left;
  width: 100%;
}

ul.hangyeyoushi_list li {
  width: 22%;
  text-align: center;
  box-shadow: 0 0 8px #ccc;
  border-radius: 5px;
  font-size: 18px;
  padding-top: 20px;
  padding-bottom: 20px;
  margin-top: 25px;
  margin-right: 1.5%;
  margin-left: 1.5%;
}

ul.hangyeyoushi_list li img {
  padding-bottom: 10px;
}

.ljqy {
  margin: 0 auto;
  width: 100%;
}

.ljqy a {
  display: block;
  background-color: #f37e00;
  background-image: linear-gradient(to right, #f37e00, #f37e00);
  padding: 0px;
  height: 51px;
  width: 30%;
  margin-top: 30px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 0px;
  border-left-width: 0px;
  font-size: 16px;
  color: #fff;
  border-radius: 5px;
  text-align: center;
  line-height: 51px;
}

.qianyue_bg {
  background-color: #fff;
  padding: 20px;
  width: 500px;
  margin-top: 200px;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
  border-radius: 5px;
  text-align: center;
  font-size: 18px;
  position: relative;
}

.qianyue_bg img {
  width: 40%;
}

.ljqy_close {
  position: absolute;
  right: 15px;
  top: 15px;
}

.ljqy_close img {
  width: 30px;
}

.main_title {
  padding-left: 10px;
  line-height: 30px;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 0px;
  border-left-width: 5px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #ffb300;
  border-right-color: #ffb300;
  border-bottom-color: #ffb300;
  border-left-color: #ffb300;
  width: 100%;
  font-size: 16px;
  margin-bottom: 15px;
  margin-top: 15px;
}

.xingye_l {
  width: 578px;
  float: left;
}

.xingye_r {
  width: 580px;
  float: right;
  font-size: 18px;
  line-height: 35px;
}

.xingye_r strong {
  font-size: 24px;
  color: #f18516;
}

.wysq {
  color: #fff;
  background-color: #ffb300;
  padding-top: 10px;
  padding-right: 30px;
  padding-bottom: 10px;
  padding-left: 30px;
  margin-top: 15px;
  border-radius: 15px;
  display: block;
  width: 100px;
  text-align: center;
}

/*提示*/

.tishi_bg,
.erweima_bg {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  z-index: 100;
  top: 0;
  left: 0;
}

.tixing_c {
  width: 820px;
  position: relative;
  margin-top: 8%;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0px 0px 4px 0px rgb(51 51 51 / 20%);
  padding: 30px;
}

.goumai_t {
  background-color: #eee;
  color: #333;
  font-size: 18px;
  line-height: 50px;
  padding-right: 30px;
  padding-left: 30px;
}

.goumai_t span {
  float: right;
}

.goumai_t span img {
  width: 25px;
}

.goumai_c {
  padding: 30px;
}

.goumai_bg {
  width: 600px;
  position: relative;
  margin-top: 8%;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 0px 4px 0px rgb(51 51 51 / 20%);
  padding: 0px;
  overflow: hidden;
}

.zhifu_bg {
  width: 400px;
  position: relative;
  margin-top: 8%;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 0px 4px 0px rgb(51 51 51 / 20%);
  padding: 0px;
  overflow: hidden;
}

.zhifuewm {
  width: 250px;
  padding: 10px;
  margin-top: 15px;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  border: 1px solid #ccc;
}

.zhifuewm img {
  width: 100%;
}

#myqrcode {
  width: 256px;
  padding: 10px;
  margin-top: 15px;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  border: 1px solid #ccc;
}

.appLabel a {
  color: #de791b;
}

.goumai_cont {
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #ccc;
  border-right-color: #ccc;
  border-bottom-color: #ccc;
  border-left-color: #ccc;
  padding-top: 10px;
  padding-bottom: 10px;
}

.goumai_cont p {
  padding-top: 5px;
  padding-bottom: 5px;
}

.tixing_t {
  font-size: 18px;
  color: #333333;
  font-weight: bold;
}

.tixing_t span {
  float: right;
}

.goumai_total {
  padding-top: 15px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

.goumai_total_l {
  width: 60%;
}

.goumai_total_l strong {
  color: #f00;
  font-size: 24px;
}

.goumai_total_r {
  width: 40%;
  text-align: right;
}

.goumai_total_r a {
  display: inline-block;
  line-height: 35px;
  text-align: center;
  border: 1px solid #ccc;
  width: 40%;
  margin-top: 0px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 2%;
}

.goumai_total_r a:hover {
  color: #fff;
  background-color: #428AFA;
  border: 1px solid #428AFA;
}

.tixing_t span a {
  width: 50px;
  display: block;
  font-size: 14px;
  color: #fff;
  background-color: #e34848;
  text-align: center;
  border-radius: 5px;
}

.tixing_pic {
  text-align: center;
  padding-top: 40px;
  padding-bottom: 20px;
}

.tixing_bottom {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  line-height: 40px;
}

.tixing_bottom a {
  color: #f9b42b;
}

.tishi {
  width: 820px;
  position: relative;
  margin-top: 10%;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  background-color: #fff;
  padding-top: 50px;
  padding-right: 80px;
  padding-bottom: 50px;
  padding-left: 80px;
  border-radius: 5px;
}

.tishi_t {
  text-align: center;
  padding-bottom: 15px;
}

.tishi_t strong {
  font-size: 24px;
}

.tishi_c {
  padding-top: 20px;
}

.erweima {
  width: 346px;
  padding: 50px;
  margin-top: 10%;
  margin-right: auto;
  margin-bottom: 0px;
  margin-left: auto;
  text-align: center;
  font-size: 18px;
  position: relative;
  background-color: #fff;
  border-radius: 5px;
}

.close2 {
  position: absolute;
  top: 10px;
  right: 10px;
}

ul.pinganyoushi_list {
  margin: 0px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  padding-top: 30px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 0px;
  width: 100%;
}

ul.qyd_list {
  display: flex;
  margin: 10px 0;
  flex-flow: row wrap;
  width: 100%;
  justify-content: space-between;
}

ul.qyd_list2 {
  display: flex;
  margin-top: 20px;
  flex-flow: row wrap;
  width: 100%;
  justify-content: space-between;
}

.qyd_cptdli_t1 {
  color: #333333;
  font-size: 21px;
  line-height: 22px;
  text-align: center;
}

.qyd_cptdli_t2 {
  font-size: 20px;
  color: #666666;
  line-height: 22px;
  text-align: center;
  margin-top: 18px;
}

.qyd_fwkq {
  width: 100%;
}

.qyd_fwkq_left {
  width: 400px;
  height: 266px;
  float: left;
}

.qyd_fwkq_left img {
  width: 100%;
}

.qyd_fwkq_right {
  width: 800px;
  height: 266px;
  display: block;
  float: left;
  background-color: #cfa364;
}

.qyd_fwkq_rightbox_text {
  font-size: 20px;
  color: #ffffff;
  line-height: 54px;
}

.qyd_fwkq_rightbox_liimg {
  width: 20px;
  height: 20px;
  float: left;
  margin: 0 18px;
}

.qyd_sqlc_lir {
  border-bottom: 2px solid #b5b5b5;
  float: left;
  margin: 38px auto;
  margin-left: 20px;
}

.qyd_sqlc_lir_text {
  font-size: 20px;
  height: 28px;
  color: #333333;
  line-height: 24px;
  padding-right: 40px;
}

.qyd_fwkq_rightbox {
  margin: 26px 0 20px 80px;
  width: 506px;
}

.qyd_fwkq_rightbox_li {
  line-height: 54px;
  height: 54px;
  width: 100%;
  border-bottom: 1px dashed #fff;
  opacity: 0.9;
}

.qyd_sqlc_lil {
  width: 83px;
  height: 95px;
  margin: 5px;
  float: left;
}

.qyd_sqlc_li {
  margin-right: 50px;
  float: left;
}

.qyd_ywzx {
  width: 100%;
  height: 88px;
  background-color: #eeeeee;
  text-align: center;
  color: #666;
  opacity: 0.59;
  font-size: 20px;
  line-height: 88px;
}

.qyd_cptdli .qyd_cptdli_img {
  width: 90px;
  height: 90px;
  margin: 32px auto;
  border-radius: 24px;
}

.qydintroduce {
  width: 100%;
  line-height: 40px;
  color: #c55716;
  font-size: 28px;
  margin-top: 40px;
}

.qydintroduce_t2 {
  font-size: 24px;
  color: #333333;
}

.qydintroduce_t3 {
  font-size: 28px;
  color: #de791b;
}

.qydintroduce_t1 {
}

.qyd_list_active {
  background-color: #f9f9f9;
  box-shadow: 0px 2px 9.9px 0.1px rgba(123, 123, 123, 0.47);
  border: solid 1px #de791b !important;
}

ul.qyd_list li:hover {
  background-color: #f9f9f9;
  box-shadow: 0px 2px 9.9px 0.1px rgba(123, 123, 123, 0.47);
  border: solid 1px #de791b !important;
}

ul.qyd_list li {
  height: 180px;
  width: 378px;
  background-color: #f9f9f9;
  border: solid 1px #e5e5e5;
}

.qyd_list2 .qyd_cptdli {
  width: 264px;
  height: 244px;
  background-color: #ffffff;
  box-shadow: 0px 3px 18px 0px rgba(123, 123, 123, 0.2);
  border-radius: 3px;
}

.qyd_list2 .qyd_cptdli:hover {
  box-shadow: 0px 2px 9.9px 0.1px rgba(123, 123, 123, 0.47);
}

.qyd_list .qyd_name {
  font-size: 51.1px;
  font-weight: normal;
  color: #de791b;
  width: 100%;
  line-height: 100px;
  text-align: center;
}

.qyd_list .qyd_text {
  height: 40px;
  line-height: 40px;
  background-color: #de791b;
  text-align: center;
  font-size: 18px;
  border-radius: 2px;
  margin: 0 20px;
  color: #ffffff;
}

ul.pinganyoushi_list li {
  margin: 0px;
  width: 26%;
  font-size: 18px;
  color: #fff;
  text-align: center;
  background-color: #ffb300;
  position: relative;
  padding-top: 50px;
  padding-right: 2%;
  padding-bottom: 2%;
  padding-left: 2%;
}

.pays_num {
  position: absolute;
  top: -33px;
  line-height: 66px;
  background-color: #fff;
  text-align: center;
  height: 66px;
  width: 66px;
  border: 2px solid #ee7337;
  font-size: 40px;
  color: #ffb300;
  border-radius: 50%;
  left: 50%;
  margin-left: -33px;
}

ul.pingantiaojian_list {
  margin: 0px;
  padding: 0px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  width: 100%;
}

ul.pingantiaojian_list li {
  margin: 0px;
  background-color: #f4f4f2;
  width: 24%;
  font-size: 18px;
  color: #333;
  background-image: url(../images/tiaojian.png);
  background-repeat: no-repeat;
  background-position: 5% center;
  padding-top: 2%;
  padding-right: 2%;
  padding-bottom: 2%;
  padding-left: 5%;
}

.price {
  color: #f00;
  margin-bottom: 5px;
  font-size: 16px;
  font-weight: bold;
}

.step {
  text-align: center;
  padding-top: 20px;
  width: 100%;
  padding-bottom: 20px;
}

.order_t {
  font-size: 14px;
  background-image: url(../images/step.png);
  background-repeat: no-repeat;
  background-position: left center;
  padding-left: 75px;
}

.order_t strong {
  font-size: 18px;
}

.order {
  width: 1000px;
  margin-top: 0;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  padding-bottom: 50px;
  min-height: 500px;
}

.order_t2 {
  padding-top: 30px;
}

table.tb_order {
  padding: 0px;
  border-collapse: collapse;
  width: 100%;
  border-top-width: 1px;
  border-right-width: 1px;
  border-bottom-width: 0px;
  border-left-width: 1px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #eee;
  border-right-color: #eee;
  border-bottom-color: #eee;
  border-left-color: #eee;
  margin-top: 15px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 0px;
  color: #fff;
}

table.tb_order tr {
  margin: 0px;
  padding: 0px;
}

table.tb_order tr td {
  margin: 0px;
  padding: 0px;
  text-align: center;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #eee;
  border-right-color: #eee;
  border-bottom-color: #eee;
  border-left-color: #eee;
  line-height: 50px;
  color: #333;
}

.zhifu_list {
  margin: 0px;
  padding-top: 15px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 0px;
}

.zhifu_list a {
  border: 1px solid #eee;
  width: 150px;
  display: inline-block;
  padding-top: 0px;
  padding-right: 15px;
  padding-bottom: 0px;
  padding-left: 15px;
  margin-top: 0px;
  margin-right: 15px;
  margin-bottom: 0px;
  margin-left: 0px;
}

.selected11 {
  border: 1px solid #de791b !important;
}

.lijizhifu {
  padding-top: 15px;
  text-align: right;
}

.lijizhifu a {
  display: inline-block;
  border: 1px solid #eee;
  line-height: 40px;
  padding-top: 0px;
  padding-right: 25px;
  padding-bottom: 0px;
  padding-left: 25px;
  margin-left: 10px;
}

.lijizhifu a:hover,
.selected12 {
  color: #fff;
  background-color: #de791b;
}

.success_tishi {
  text-align: center;
}

.success_tishi strong {
  color: #f00;
  font-size: 16px;
}

.success_tishi a {
  color: #de791b;
}

.yyong_2_newli {
  width: 583px;
  height: 161px;
  background: #f8f8f8;
  margin-top: 29px;
}

.yyong_2_newli:hover {
  box-shadow: 0px 4px 10px 0px rgba(51, 51, 51, 0.18);
  cursor: pointer;
}

.yyong_2_newliboxl {
  width: 382px;
  float: left;
}

.yyong_2_newlibox {
  margin: 25px;
  margin-top: 15px;
}

.yyong_2_newliboxT1 {
  color: #333333;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 30px;
  font-weight: bold;
  font-size: 16px;
}

.yyong_2_newliboxT2 {
  color: #666666;
  line-height: 25px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.yyong_2_newliboxr {
  float: left;
  height: 104px;
  width: 141px;
  border-radius: 10px;
  margin-top: 10px;
}

.yyong_2_newliboxr img {
  width: 100%;
  height: 100%;
}
