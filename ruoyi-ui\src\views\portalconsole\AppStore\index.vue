<template>
  <!-- 应用商店 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="应用编码" prop="companyId">
        <el-input
          v-model="queryParams.companyId"
          placeholder="请输入应用编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="应用名称" prop="appStoreName">
        <el-input
          v-model="queryParams.appStoreName"
          placeholder="请输入应用名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="应用类型" prop="appStoreType">
        <el-select v-model="queryParams.appStoreType" placeholder="请选择应用类型" style="width: 100%">
          <el-option v-for="dict in dict.type.app_store_type" :key="dict.value"
          :label="dict.label"
          :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="价格" prop="appStorePrice">
        <el-input
          v-model="queryParams.appStorePrice"
          type="number"
          placeholder="请输入价格"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:AppStore:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['portalconsole:AppStore:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:AppStore:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:AppStore:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <el-table v-loading="loading" :data="AppStoreList" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <!-- <el-table-column label="典型案例ID" align="center" prop="appStoreId" /> -->
      <el-table-column label="应用编码" align="center" prop="appStoreId" />
      <el-table-column label="应用名称" align="center" prop="appStoreName" />
      <el-table-column label="价格" align="center" prop="appStorePrice" />
      <el-table-column label="应用类型" align="center" prop="appStoreType" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.app_store_type" :value="scope.row.appStoreType"/>
          <!-- <div v-if="scope.row.appStoreType===1">研发设计</div>
          <div v-else-if="scope.row.appStoreType===1">研发设计</div>
          <div v-else-if="scope.row.appStoreType===2">生产制造</div>
          <div v-else-if="scope.row.appStoreType===3">运营管理</div>
          <div v-else-if="scope.row.appStoreType===4">质量管控</div>
          <div v-else-if="scope.row.appStoreType===5">仓储物流</div>
          <div v-else-if="scope.row.appStoreType===6">安全生产</div>
          <div v-else-if="scope.row.appStoreType===7">节能减排</div>
          <div v-else-if="scope.row.appStoreType===8">运维服务</div> -->
        </template>
      </el-table-column>
      <el-table-column label="标签" align="center" prop="appLabel" />
      <el-table-column label="简介" align="center" prop="appStoreIntroduction" />
      <el-table-column label="详情" align="center" prop="appStoreContent" />
      <el-table-column label="审核状态" align="center" prop="auditStatus" >
        <template slot-scope="scope">
          <div v-if="scope.row.auditStatus==='3'">审批通过</div>
          <div v-else-if="scope.row.auditStatus==='2'">审批不通过</div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="封面" align="center" prop="appStoreImg" /> -->
      <el-table-column label="创建时间" align="center" prop="createTime" width="200"/>
      <el-table-column label="联系人" align="center" prop="appStoreContactsName" />
      <el-table-column label="联系方式" align="center" prop="appStoreContactsPhone" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:AppStore:edit']"
          >修改</el-button> -->
          <el-button
            size="mini"
            type="text"
            @click="handleDetail(scope.row)"
            v-hasPermi="['portalconsole:AppStore:edit']"
          >详情</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:AppStore:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改应用商店对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公司ID" prop="companyId">
          <el-input v-model="form.companyId" placeholder="请输入公司ID" />
        </el-form-item>
        <el-form-item label="应用名称" prop="appStoreName">
          <el-input v-model="form.appStoreName" placeholder="请输入应用名称" />
        </el-form-item>
        <el-form-item label="简介" prop="appStoreIntroduction">
          <el-input v-model="form.appStoreIntroduction" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="详情">
          <editor v-model="form.appStoreContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="封面" prop="appStoreImg">
          <el-input v-model="form.appStoreImg" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="联系人" prop="appStoreContactsName">
          <el-input v-model="form.appStoreContactsName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="联系方式" prop="appStoreContactsPhone">
          <el-input v-model="form.appStoreContactsPhone" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="售价" prop="appStorePrice">
          <el-input v-model="form.appStorePrice" placeholder="请输入售价" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <detailDialog ref="detailDialog" @submit="submit"></detailDialog>
  </div>
</template>

<script>
import { listAppStore, getAppStore, delAppStore, addAppStore, updateAppStore } from "@/api/portalconsole/AppStore";
import detailDialog from "./components/detailDialog"
export default {
  name: "AppStore",
  dicts:['app_store_type'],
  components: {
    detailDialog
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 应用商店表格数据
      AppStoreList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyId: null,
        appStoreName: null,
        appStoreType: null,
        appStoreIntroduction: null,
        appStoreContent: null,
        appStoreImg: null,
        appStoreContactsName: null,
        appStoreContactsPhone: null,
        appStorePrice: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询应用商店列表 */
    getList() {
      this.loading = true;
      listAppStore(this.queryParams).then(response => {
        this.AppStoreList = response.rows;
        this.AppStoreList.forEach(item=>{
          item.appStoreContent=this.removePTags(item.appStoreContent)
        })
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        appStoreId: null,
        companyId: null,
        appStoreName: null,
        appStoreType: null,
        appStoreIntroduction: null,
        appStoreContent: null,
        appStoreImg: null,
        appStoreContactsName: null,
        appStoreContactsPhone: null,
        appStorePrice: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.appStoreId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加应用商店";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const appStoreId = row.appStoreId || this.ids
      getAppStore(appStoreId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改应用商店";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.appStoreId != null) {
            updateAppStore(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAppStore(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const appStoreIds = row.appStoreId || this.ids;
      this.$modal.confirm('是否确认删除应用商店编号为"' + appStoreIds + '"的数据项？').then(function() {
        return delAppStore(appStoreIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/AppStore/export', {
        ...this.queryParams
      }, `AppStore_${new Date().getTime()}.xlsx`)
    },
    //详情
    handleDetail(row){
      getAppStore(row.appStoreId).then(response => {
        this.$refs.detailDialog.show(response.data)
      });
      
    },
    removePTags(str) {
      return str.replace(/<p[^>]*>|<\/p>/gi, '');
    },
    submit(updata){
        if(updata){
          this.getList()
        }
      }
  }
};
</script>
<style scoped>
/deep/.el-table .cell{
  height: 26px;
}
</style>
