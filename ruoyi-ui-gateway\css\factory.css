@charset "utf-8";
/* CSS Document */
li.nav-item:hover>a,
li.nav-item.active>a {
    color: #034fcc!important;
    transition: all 1s ease;
}
.jrgzt, .jrgzt:hover{background-color: #034fcc!important;}
.but2 {
    background-color: #034fcc!important;
    background-image: linear-gradient(to right, #034fcc, #034fcc)!important;

}
ul.news_list2 li a:hover { color: #034fcc!important;
}.denglu {
    color: #034fcc;
}
.fangan1_r img{ width:100%}
.fangan1_t{
	background-image: url(../images/blue.png);
	background-repeat: no-repeat;
	background-position: left center;
	padding-top: 0px;
	padding-right: 0px;
	padding-bottom: 0px;
	padding-left: 20px;
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 1px;
	border-left-width: 0px;
	border-top-style: solid;
	border-right-style: solid;
	border-bottom-style: solid;
	border-left-style: solid;
	border-top-color: #eee;
	border-right-color: #eee;
	border-bottom-color: #eee;
	border-left-color: #eee;
	font-size: 16px;
	line-height: 40px;
	margin-top: 10px;
}
.fangan_bg {
    background-color: #fff!important;
    padding-bottom: 50px;
}
.fangan1_bg {
    width: 1200px!important;

}
.fangan2_bg {
    width: 1200px!important;

}
ul#page li a:hover {
    color: #fff;
    background-color: #034fcc;
    border: 1px solid #034fcc;
}
.main1200 {
	width: 1200px;
	margin-top: 0px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
	padding-top: 50px;
	padding-right: 0px;
	padding-bottom: 50px;
	padding-left: 0px;
}
.main_t11 {
	text-align: center;
	line-height: 30px;
	font-size: 26px;
	color: #fff;
	padding-top: 50px;
	margin-bottom: 30px;
	width: 100%;
}
.case_chuangxin {
	background-color: #f1f2f6;
	display: flex;
	border-radius: 5px;
	flex-flow: row wrap;
	justify-content: space-between;
	box-shadow: 0px 2px 5px #fff;
	width: 1100px;
	padding-top: 3%;
	padding-right: 50px;
	padding-bottom: 3%;
	padding-left: 50px;
	margin-right: auto;
	margin-left: auto;
	margin-top: 50px;
}
.case_chuangxin_pic {
	width: 30%
}
.case_chuangxin_txt {
	width: 65%;
}
.case_chuangxin_txt strong {
	font-size: 18px
}
.main_t12 {
	text-align: center;
	line-height: 30px;
	font-size: 26px;
	color: #000;
	margin-top: 0px;
	margin-bottom: 30px;
	width: 100%;
}
.more_factory {
	text-align: right;
}
.more_factory a {
	color: #034fcc;
}
ul.fuwushang_list {
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	padding-top: 20px;
}
ul.fuwushang_list li {
	width: 31%;
	padding-bottom: 15px;
	padding-top: 15px;
}
ul.fuwushang_list li a {
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
}
ul.fuwushang_list li a:hover{color:#034fcc}
.fuwushang_pic {
	width: 22%;
}
.fuwushang_pic img {
	width: 100%;
}
.fuwushang_txt {
	width: 72%; 
}
.fuwushang_txt p {
	padding-bottom: 5px;
}
.fuwushang_txt p strong {
	font-size: 16px
}
.fuwushang_txt span {
	color: #034fcc;
	border: 1px solid #034fcc;
	padding: 8px;
	margin-top: 5px;
}
ul.factory_fuwu_list {
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	padding-top: 20px;
}
ul.factory_fuwu_list li {
	width: 31%;
	margin-bottom: 25px;
	box-shadow: 0 3px 5px #fff
}
ul.factory_fuwu_list li  img{ display:none;}
ul.factory_fuwu_list li a {
	background-image: url(../images/fuwu_bg.png);
	background-repeat: no-repeat;
	background-position: right top;
	background-size: cover;
	padding: 20px;
	  display:block; height:155px;
}
ul.factory_fuwu_list li a:hover{color:#034fcc}
.factory_fuwu_list p {
	padding-bottom: 5px;
}
.factory_fuwu_list p strong {
	font-size: 16px
}
.factory_fuwu_list span {
	color: #dd312d;
	border: 1px solid #dd312d;
	padding: 8px;
	margin-top: 0px;
}
ul.factory_xuqiu_list {
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	padding-top: 20px;
}
ul.factory_xuqiu_list li {
	width: 22%;
	margin-bottom: 25px;
	box-shadow: 0 3px 5px #fff;
	position: relative;
}
.zhuangtai {
	position: absolute;
	right: 25px;
	color: #fff;
	line-height: 43px;
}
ul.factory_xuqiu_list li a {

	background-image: url(../images/factory_xuqiu_bg.png);
	background-repeat: no-repeat;
	background-position: right top;
	background-size: cover;
	padding: 20px;
	color: #fff;
	border-radius: 5px;
	border: 2px solid #fff; display:block;
}
.factory_xuqiu_list p {
	padding-bottom: 5px;
}
.factory_xuqiu_list p strong {
	font-size: 16px
}
.factory_xuqiu_list span {
	color: #ffd9b0;
	display: block-inline;
	float: none;
	padding: 0px;
	margin-top: 0px;
	font-size: 18px;
}
.ziyuan_tab {
	text-align: center;
}
.ziyuan_tab a {
	display: inline-block;
	font-size: 14px;
	margin-top: 0px;
	margin-right: 15px;
	margin-bottom: 0px;
	margin-left: 15px;
}
.ziyuan_tab a:hover{color: #034fcc;}
.selected100 {
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 3px;
	border-left-width: 0px;
	border-top-style: solid;
	border-right-style: solid;
	border-bottom-style: solid;
	border-left-style: solid;
	border-top-color: #034fcc;
	border-right-color: #034fcc;
	border-bottom-color: #034fcc;
	border-left-color: #034fcc;
	color: #034fcc;
}
ul.shejishi_list {
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	padding-top: 20px;
}
ul.shejishi_list li {
	width: 30%;
	padding-top: 15px;
	padding-bottom: 15px;
}
ul.shejishi_list li a {
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	width: 100%
}
.shejishi_l {
	width: 30%;
}
.shejishi_r {
	width: 65%;
	color: #666;
}
.shejishi_r strong {
	color: #333;
}
.sjsjs {
	padding-top: 5px;
	margin-top: 10px;
	border-top-width: 1px;
	border-right-width: 0px;
	border-bottom-width: 0px;
	border-left-width: 0px;
	border-top-style: solid;
	border-right-style: solid;
	border-bottom-style: solid;
	border-left-style: solid;
	border-top-color: #ccc;
	border-right-color: #ccc;
	border-bottom-color: #ccc;
	border-left-color: #ccc;
}
.yundou_new {
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	padding-top: 20px;
}
.yundou_new_l {
	width: 45%;
}
.yundou_new_r {
	width: 50%;
}
ul.yundou_news_list {
	margin: 0px;
	padding: 0px;
}
ul.yundou_news_list li {
	margin: 0px;
	padding-top: 0px;
	padding-right: 0px;
	padding-bottom: 20px;
	padding-left: 0px;
	line-height: 30px;
}
ul.yundou_news_list li a {
	color: #666;
}
ul.yundou_news_list li:hover a{ color:#034fcc
}
ul.yundou_news_list li:hover a strong { color:#034fcc
}
ul.yundou_news_list li a strong {
	color: #333;
}
.ljgd, .ljgd:hover {
	display: block;
	width: 100px;
	text-align: center;
	color: #fff;
	background-color: #034fcc;
	line-height: 40px;
	border-radius: 25px;
}
ul.yundou_news_list2 {
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	padding-top: 0px;
}
ul.yundou_news_list2 li {
	width: 48%;
	position: relative;
	margin-bottom: 15px;
}
ul.yundou_news_list2 li a img {
	height: 180px; width:100%;
}
.news_factory_t {
	position: absolute;
	line-height: 35px;
	text-align: center;
	color: #fff;
	background: rgba(0,0,0,0.5);
	bottom: 0;
	width: 90%;
	overflow: hidden;
	height: 35px;
	padding-top: 0px;
	padding-right: 5%;
	padding-bottom: 0px;
	padding-left: 5%;
}
.search_bg {
	line-height: 50px;
	height: 50px;
	border: 1px solid #eee;
	border-radius: 35px;
	box-shadow: 0 3px 5px #ccc;
	background-color: #fff;
	margin-bottom: 20px;
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	padding-top: 0px;
	padding-right: 2%;
	padding-bottom: 0px;
	padding-left: 2%;
	margin-top: 30px;
}
.search_l {
	width: 40%;
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	padding-top: 0px;
}
.search_r {
	line-height: 40px;
	height: 40px;
	margin-top: 5px;
	width: 35%;
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	padding-top: 0px;
}
.search_l a {
	display: inline-block;
	text-align: center;
	line-height: 40px;
	height: 40px;
	margin-top: 5px;
	text-align: center;
	padding-right: 30px;
	padding-bottom: 0px;
	padding-left: 30px;
	border-radius: 35px;
	padding-top: 0px;
}
.sel13, .search_l a:hover {
	color: #fff;
	background-color: #034fcc;
}
.search_txt4{
	width: 75%;
	line-height: 40px;
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 0px;
	border-left-width: 0px;
	text-align: right;
	font-size: 16px;
}
.search_but4{
	width: 20%;
	background-image: url(../images/search_but.png);
	background-repeat: no-repeat;
	background-position: center center;
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 0px;
	border-left-width: 0px; background-color:transparent;
}
.fangan_factory_t{
	font-size: 18px;
	color: #333;
	text-align: center;
	padding-top: 15px;
	padding-bottom: 15px;
}
ul.factory_case_list{

	margin-top: 5px;
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	padding-top: 0px;}
	ul.factory_case_list li{ width:22%; text-align:center;}
	ul.factory_case_list li img{ width:100%; height:160px;}
	ul.factory_rongyu_list{

	margin-top: 5px;
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	padding-top: 0px;}
	ul.factory_rongyu_list li{
	width: 22%;
	text-align: center;
	border: 1px solid #eee;
	margin-bottom: 30px;
	padding-bottom: 10px;
}
.factory_detail{
	margin-top: 20px;
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
	padding-top: 0px;
	padding-bottom: 20px;
}
	.factory_detail_l{ width:40%}
		.factory_detail_r{ width:55%}
			.factory_detail_l2{ width:22%}
		.factory_detail_r2{ width:75%}
		.factory_detail_l img,.factory_detail_l2 img{ width:100%;}
		.factory_logo{ padding-top:20px; font-size:18px;padding-bottom:20px; }
		.factory_logo img{ padding-right:30px; max-width:280px;}
		.factory_banner{padding-bottom:20px;}
		.factory_banner img{ width:100%; height:400px;}