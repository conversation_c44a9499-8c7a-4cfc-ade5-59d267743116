<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.PolicyLabelMapper">
    
    <resultMap type="PolicyLabel" id="PolicyLabelResult">
        <result property="policyLabelId"    column="policy_label_id"    />
        <result property="policyLabelValue"    column="policy_label_value"    />
        <result property="policyLabelGroup"    column="policy_label_group"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPolicyLabelVo">
        select policy_label_id, policy_label_value, policy_label_group, create_by, create_time, update_by, update_time, remark from policy_label
    </sql>

    <select id="selectPolicyLabelList" parameterType="PolicyLabel" resultMap="PolicyLabelResult">
        <include refid="selectPolicyLabelVo"/>
        <where>  
            <if test="policyLabelValue != null  and policyLabelValue != ''"> and policy_label_value = #{policyLabelValue}</if>
            <if test="policyLabelGroup != null  and policyLabelGroup != ''"> and policy_label_group = #{policyLabelGroup}</if>
        </where>
    </select>
    
    <select id="selectPolicyLabelByPolicyLabelId" parameterType="Long" resultMap="PolicyLabelResult">
        <include refid="selectPolicyLabelVo"/>
        where policy_label_id = #{policyLabelId}
    </select>

    <insert id="insertPolicyLabel" parameterType="PolicyLabel" useGeneratedKeys="true" keyProperty="policyLabelId">
        insert into policy_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="policyLabelValue != null and policyLabelValue != ''">policy_label_value,</if>
            <if test="policyLabelGroup != null and policyLabelGroup != ''">policy_label_group,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="policyLabelValue != null and policyLabelValue != ''">#{policyLabelValue},</if>
            <if test="policyLabelGroup != null and policyLabelGroup != ''">#{policyLabelGroup},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePolicyLabel" parameterType="PolicyLabel">
        update policy_label
        <trim prefix="SET" suffixOverrides=",">
            <if test="policyLabelValue != null and policyLabelValue != ''">policy_label_value = #{policyLabelValue},</if>
            <if test="policyLabelGroup != null and policyLabelGroup != ''">policy_label_group = #{policyLabelGroup},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where policy_label_id = #{policyLabelId}
    </update>

    <delete id="deletePolicyLabelByPolicyLabelId" parameterType="Long">
        delete from policy_label where policy_label_id = #{policyLabelId}
    </delete>

    <delete id="deletePolicyLabelByPolicyLabelIds" parameterType="String">
        delete from policy_label where policy_label_id in 
        <foreach item="policyLabelId" collection="array" open="(" separator="," close=")">
            #{policyLabelId}
        </foreach>
    </delete>
</mapper>