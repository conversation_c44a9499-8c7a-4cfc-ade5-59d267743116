<template>
  <div class="app-container">
    <el-form ref="platformForm" :model="form" label-width="80px">
            <el-row>
              <el-form-item v-for="item in rPlatform" :key='item.id' :label="item.name">
                <el-select v-model='item.status'>
                  <el-option label='上线' :value='1'></el-option>
                  <el-option label='下线' :value='0'></el-option>
                  <el-option label='移除' :value='-1'></el-option>
                </el-select>
              </el-form-item>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitPlatform">确 定</el-button>
          </div>
  </div>
</template>
<style>
</style>

<script>
  // import {
  //   platformRequire,
  //   getPlatform
  // } from "@/api/sso/service";
  export default {
    props: {
      form: Object
    },
    name: "PlatformContect",
    data() {
      return {
        rPlatform:[]
      };
    },
    created() {
        // getPlatform(this.form.id).then(res => {
        //   this.rPlatform = res.data;
        // })
    },
    methods: {
      /* 平台操作-提交*/
      submitPlatform() {
        // let data = this.rPlatform.filter(item => {
        //   return item.status>=0;
        // }).map(item => ({
        //   'appCode': item.platform,
        //   'status': item.status
        // }))
        // let params = {
        //   id: this.form.id,
        //   platforms: JSON.stringify(data)
        // }
        // platformRequire(params).then(res => {
        //   this.$modal.msgSuccess("提交成功");
        // })
      },
    }
  };
</script>
