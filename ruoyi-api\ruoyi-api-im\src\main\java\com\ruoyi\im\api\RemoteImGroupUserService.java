package com.ruoyi.im.api;

import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.im.api.domain.ImGroup;
import com.ruoyi.im.api.domain.ImGroupUser;
import com.ruoyi.im.api.domain.ImUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.api
 * @ClassName: ImGroupUserController
 * @Author: ${maguojun}
 * @Description: ${description}
 * @Date: 2022/3/16 14:10
 * @Version: 1.0
 */
@FeignClient(contextId = "remoteImGroupUserService", value = ServiceNameConstants.IM_SERVICE)
public interface RemoteImGroupUserService {
    /**
    * @Description:
    * @Param:
    * @return:
    * @Author: 根据群组id查询群组成员
    * @Date:
    */
    @PostMapping(value = "/im/groupuser/list" )
    R<List<ImUser>> UserList(@RequestBody ImGroupUser imGroupUser);

    /**
    * @Description:
    * @Param:
    * @return:
    * @Author: 根据群组id查询群组详情
    * @Date:
    */
    @PostMapping(value = "/im/groupuser/detail" )
    R<ImGroup> groupUser(@RequestBody  ImGroupUser imGroupUser);
    /**
    * @Description:
    * @Param:
    * @return:
    * @Author: 退出群聊
    * @Date:
    */
    @PostMapping(value="/im/groupuser/signout")
    R<Long> signOutGroup(@RequestBody ImGroupUser imGroupUser);


    /***
     * 新增ImGroup数据
     * @param imGroupUser 用户加入指定群组
     * @return
     */
    @PostMapping(value="/im/groupuser/signin")
    R<Long> add(@RequestBody ImGroupUser imGroupUser);



    @PostMapping(value = "/im/groupuser/queryList" )
    R<List<ImGroup>> queryList(@RequestBody ImGroupUser imGroupUser);



    @PostMapping(value = "/im/groupuser/removeList" )
    R<Boolean> removeList(@RequestParam(value = "userIds") String userIds, @RequestParam(value = "groupId") String groupId);
}
