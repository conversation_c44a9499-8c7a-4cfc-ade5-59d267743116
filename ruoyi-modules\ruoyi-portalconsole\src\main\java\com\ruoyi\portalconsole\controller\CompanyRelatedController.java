package com.ruoyi.portalconsole.controller;

import java.io.InputStream;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.CompanyRelated;
import com.ruoyi.portalconsole.service.ICompanyRelatedService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 关联企业信息Controller
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
@RestController
@RequestMapping("/companyRelated")
public class CompanyRelatedController extends BaseController
{
    @Autowired
    private ICompanyRelatedService companyRelatedService;

    /**
     * 查询关联企业信息列表
     */
    @RequiresPermissions("portalconsole:portalconsole:list")
    @GetMapping("/list")
    public TableDataInfo list(CompanyRelated companyRelated)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<CompanyRelated> list = companyRelatedService.selectCompanyRelatedList(companyRelated);
        return getDataTable(list);
    }

    /**
     * 导出关联企业信息列表
     */
    @RequiresPermissions("portalconsole:portalconsole:export")
    @Log(title = "关联企业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CompanyRelated companyRelated)
    {
        List<CompanyRelated> list = companyRelatedService.selectCompanyRelatedList(companyRelated);
        ExcelUtil<CompanyRelated> util = new ExcelUtil<CompanyRelated>(CompanyRelated.class);
        util.exportExcel(response, list, "关联企业信息数据");
    }

    /**
     * 获取关联企业信息详细信息
     */
    @RequiresPermissions("portalconsole:portalconsole:query")
    @GetMapping(value = "/{companyRelatedId}")
    public AjaxResult getInfo(@PathVariable("companyRelatedId") Long companyRelatedId)
    {
        return success(companyRelatedService.selectCompanyRelatedByCompanyRelatedId(companyRelatedId));
    }

    /**
     * 新增关联企业信息
     */
    @RequiresPermissions("portalconsole:portalconsole:add")
    @Log(title = "关联企业信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CompanyRelated companyRelated)
    {
        return toAjax(companyRelatedService.insertCompanyRelated(companyRelated));
    }

    /**
     * 新增关联企业信息
     */
    @Log(title = "关联企业信息", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    public AjaxResult addByImport(@RequestBody CompanyRelated companyRelated)
    {
        return toAjax(companyRelatedService.importCompanyRelated(companyRelated));
    }

    /**
     * 修改关联企业信息
     */
    @RequiresPermissions("portalconsole:portalconsole:edit")
    @Log(title = "关联企业信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CompanyRelated companyRelated)
    {
        return toAjax(companyRelatedService.updateCompanyRelated(companyRelated));
    }

    /**
     * 删除关联企业信息
     */
    @RequiresPermissions("portalconsole:portalconsole:remove")
    @Log(title = "关联企业信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{companyRelatedIds}")
    public AjaxResult remove(@PathVariable Long[] companyRelatedIds)
    {
        return toAjax(companyRelatedService.deleteCompanyRelatedByCompanyRelatedIds(companyRelatedIds));
    }

    /**
     * 文件导入数据库
     */
    @PostMapping("/importBatch")
    public AjaxResult importBatch(@RequestParam("file") MultipartFile file) {
        String fileName = file.getOriginalFilename(); // 获取文件名
        InputStream is = null;
        try {
            is = file.getInputStream();
            List<CompanyRelated> list = companyRelatedService.getListByExcel(is, fileName);// 获取解析后的List集合
            Boolean result = companyRelatedService.batchImportSlFormulasInfo(list); // 把数据插入数据库
            if (result) {
                return AjaxResult.success("文件上传成功！");
            } else {
                return AjaxResult.success("文件上传失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 获取导入模板
     * @param response
     */
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<CompanyRelated> util = new ExcelUtil<CompanyRelated>(CompanyRelated.class);
        util.importTemplateExcel(response, "用户数据");
    }
}
