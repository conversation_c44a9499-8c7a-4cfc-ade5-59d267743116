package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.portalweb.api.domain.SolutionType;
import com.ruoyi.portalweb.mapper.SolutionTypeMapper;
import com.ruoyi.portalweb.service.ISolutionTypeService;
import com.ruoyi.portalweb.vo.SolutionTypeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 解决方案类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class SolutionTypeServiceImpl implements ISolutionTypeService
{
    @Autowired
    private SolutionTypeMapper solutionTypeMapper;

    /**
     * 查询解决方案类型
     * 
     * @param solutionTypeId 解决方案类型主键
     * @return 解决方案类型
     */
    @Override
    public SolutionTypeVO selectSolutionTypeBySolutionTypeId(Long solutionTypeId)
    {
        return solutionTypeMapper.selectSolutionTypeBySolutionTypeId(solutionTypeId);
    }

    /**
     * 查询解决方案类型列表
     * 
     * @param solutionType 解决方案类型
     * @return 解决方案类型
     */
    @Override
    public List<SolutionTypeVO> selectSolutionTypeList(SolutionType solutionType)
    {
        return solutionTypeMapper.selectSolutionTypeList(solutionType);
    }

    /**
     * 父级列表
     */
    public List<SolutionTypeVO> parentList(SolutionType solutionType){
        return solutionTypeMapper.parentList(solutionType);
    }

    /**
     * 新增解决方案类型
     * 
     * @param solutionType 解决方案类型
     * @return 结果
     */
    @Override
    public int insertSolutionType(SolutionType solutionType)
    {
        solutionType.setCreateTime(DateUtils.getNowDate());
        return solutionTypeMapper.insertSolutionType(solutionType);
    }

    /**
     * 修改解决方案类型
     * 
     * @param solutionType 解决方案类型
     * @return 结果
     */
    @Override
    public int updateSolutionType(SolutionType solutionType)
    {
        solutionType.setUpdateTime(DateUtils.getNowDate());
        return solutionTypeMapper.updateSolutionType(solutionType);
    }

    /**
     * 批量删除解决方案类型
     * 
     * @param solutionTypeIds 需要删除的解决方案类型主键
     * @return 结果
     */
    @Override
    public int deleteSolutionTypeBySolutionTypeIds(Long[] solutionTypeIds)
    {
        return solutionTypeMapper.deleteSolutionTypeBySolutionTypeIds(solutionTypeIds);
    }

    /**
     * 删除解决方案类型信息
     * 
     * @param solutionTypeId 解决方案类型主键
     * @return 结果
     */
    @Override
    public int deleteSolutionTypeBySolutionTypeId(Long solutionTypeId)
    {
        return solutionTypeMapper.deleteSolutionTypeBySolutionTypeId(solutionTypeId);
    }

    /**
     * 典型案例分类列表
     */
    public List<SolutionTypeVO> classicCaseList(SolutionType solutionType){
        return solutionTypeMapper.classicCaseList(solutionType);
    }

    /**
     * 解决方案分类列表
     */
    public List<SolutionTypeVO> solutionList(SolutionType solutionType){
        return solutionTypeMapper.solutionList(solutionType);
    }

}
