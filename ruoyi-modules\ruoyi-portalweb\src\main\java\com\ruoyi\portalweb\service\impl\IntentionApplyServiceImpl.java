package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.IntentionApply;
import com.ruoyi.portalweb.api.enums.IntentionApplyStatus;
import com.ruoyi.portalweb.api.enums.IntentionApplyType;
import com.ruoyi.portalweb.mapper.CompanyMapper;
import com.ruoyi.portalweb.mapper.DemandMapper;
import com.ruoyi.portalweb.mapper.IntentionApplyMapper;
import com.ruoyi.portalweb.mapper.SupplyMapper;
import com.ruoyi.portalweb.service.IIntentionApplyService;
import com.ruoyi.portalweb.service.IMemberService;
import com.ruoyi.portalweb.vo.DemandVO;
import com.ruoyi.portalweb.vo.IntentionApplyVO;
import com.ruoyi.portalweb.vo.MemberVO;
import com.ruoyi.portalweb.vo.SupplyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 意向申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-24
 */
@Service
public class IntentionApplyServiceImpl implements IIntentionApplyService {
    @Autowired
    private IntentionApplyMapper intentionApplyMapper;

    @Autowired
    private SupplyMapper supplyMapper;

    @Autowired
    private DemandMapper demandMapper;

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private IMemberService memberService;

    /**
     * 查询意向申请
     *
     * @param id 意向申请主键
     * @return 意向申请
     */
    @Override
    public IntentionApplyVO selectIntentionApplyById(Long id) {
        return intentionApplyMapper.selectIntentionApplyById(id);
    }

    /**
     * 查询意向申请列表
     *
     * @param intentionApply 意向申请
     * @return 意向申请
     */
    @Override
    public List<IntentionApplyVO> selectIntentionApplyList(IntentionApplyVO intentionApply) {
        if (StringUtils.isNotEmpty(intentionApply.getQueryType()) && "my".equals(intentionApply.getQueryType())) {
            intentionApply.setMemberId(SecurityUtils.getUserId());
        }else {
            intentionApply.setIntentionMemberId(SecurityUtils.getUserId());
        }
        return intentionApplyMapper.selectIntentionApplyList(intentionApply);
    }

    /**
     * 新增意向申请
     *
     * @param intentionApply 意向申请
     * @return 结果
     */
    @Override
    public int insertIntentionApply(IntentionApply intentionApply) {
        IntentionApplyVO wrapper = new IntentionApplyVO();
        wrapper.setMemberId(SecurityUtils.getUserId());
        wrapper.setIntentionId(intentionApply.getIntentionId());
        wrapper.setIntentionType(intentionApply.getIntentionType());
        wrapper.setIntentionStatus(IntentionApplyStatus.PENDING.getValue());
        List<IntentionApplyVO> intentionApplyVOS = intentionApplyMapper.selectIntentionApplyList(wrapper);
        if (intentionApplyVOS != null && intentionApplyVOS.size() > 0) {
            throw new ServiceException("请勿重复申请");
        }

        if (Objects.equals(intentionApply.getIntentionType(), IntentionApplyType.DEMAND.getValue())){
            DemandVO demandVO = demandMapper.selectDemandById(intentionApply.getIntentionId());
            intentionApply.setIntentionMemberId(demandVO.getMemberId());
            intentionApply.setIntentionCompanyName(demandVO.getCompanyName());
        }else if (Objects.equals(intentionApply.getIntentionType(), IntentionApplyType.SUPPLY.getValue())){
            SupplyVO supplyVO = supplyMapper.selectSupplyById(intentionApply.getIntentionId());
            intentionApply.setIntentionCompanyName(supplyVO.getOrganization());
            intentionApply.setIntentionMemberId(supplyVO.getMemberId());
        }

        MemberVO memberVO = memberService.selectMemberByMemberId(SecurityUtils.getUserId());

        intentionApply.setCompanyName(memberVO.getCompanyName());
        intentionApply.setIntentionStatus(IntentionApplyStatus.PENDING.getValue());
        intentionApply.setCreateTime(DateUtils.getNowDate());
        intentionApply.setMemberId(SecurityUtils.getUserId());
        return intentionApplyMapper.insertIntentionApply(intentionApply);
    }

    /**
     * 修改意向申请
     *
     * @param intentionApply 意向申请
     * @return 结果
     */
    @Override
    public int updateIntentionApply(IntentionApply intentionApply) {
        intentionApply.setUpdateTime(DateUtils.getNowDate());
        return intentionApplyMapper.updateIntentionApply(intentionApply);
    }

    /**
     * 批量删除意向申请
     *
     * @param ids 需要删除的意向申请主键
     * @return 结果
     */
    @Override
    public int deleteIntentionApplyByIds(Long[] ids) {
        return intentionApplyMapper.deleteIntentionApplyByIds(ids);
    }

    /**
     * 删除意向申请信息
     *
     * @param id 意向申请主键
     * @return 结果
     */
    @Override
    public int deleteIntentionApplyById(Long id) {
        return intentionApplyMapper.deleteIntentionApplyById(id);
    }
}
