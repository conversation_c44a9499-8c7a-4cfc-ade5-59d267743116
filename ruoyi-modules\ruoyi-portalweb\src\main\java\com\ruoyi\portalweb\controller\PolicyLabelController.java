package com.ruoyi.portalweb.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalweb.api.domain.PolicyLabel;
import com.ruoyi.portalweb.service.IPolicyLabelService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 政策标签Controller
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
@Api(value = "9.政策标签(政策画像)",tags = "9.政策标签(政策画像)")
@RestController
@RequestMapping("/policyLabel")
public class PolicyLabelController extends BaseController
{
    @Autowired
    private IPolicyLabelService policyLabelService;

    /**
     * 查询政策标签列表
     */
    @ApiOperation("查询政策标签列表")
    @RequiresPermissions("portalweb:label:list")
    @GetMapping("/list")
    public TableDataInfo list(PolicyLabel policyLabel)
    {
        startPage();
        List<PolicyLabel> list = policyLabelService.selectPolicyLabelList(policyLabel);
        return getDataTable(list);
    }

    /**
     * 查询全部政策标签列表
     */
    @ApiOperation("查询全部政策标签列表")
    @RequiresPermissions("portalweb:label:all")
    @GetMapping("/all")
    public TableDataInfo all(PolicyLabel policyLabel)
    {
        List<List<PolicyLabel>> list = policyLabelService.selectPolicyLabelAll(policyLabel);
        return getDataTable(list);
    }

    /**
     * 查询全部政策标签列表
     */
    @ApiOperation("查询全部政策标签列表")
    @GetMapping("/allDesk")
    public TableDataInfo allDesk(PolicyLabel policyLabel)
    {
        List<List<PolicyLabel>> list = policyLabelService.selectPolicyLabelAll(policyLabel);
        return getDataTable(list);
    }

    /**
     * 导出政策标签列表
     */
    @ApiOperation("导出政策标签列表")
    @RequiresPermissions("portalweb:label:export")
    @Log(title = "政策标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PolicyLabel policyLabel)
    {
        List<PolicyLabel> list = policyLabelService.selectPolicyLabelList(policyLabel);
        ExcelUtil<PolicyLabel> util = new ExcelUtil<PolicyLabel>(PolicyLabel.class);
        util.exportExcel(response, list, "政策标签数据");
    }

    /**
     * 获取政策标签详细信息
     */
    @ApiOperation("获取政策标签详细信息")
    @RequiresPermissions("portalweb:label:query")
    @GetMapping(value = "/{policyLabelId}")
    public AjaxResult getInfo(@PathVariable("policyLabelId") Long policyLabelId)
    {
        return success(policyLabelService.selectPolicyLabelByPolicyLabelId(policyLabelId));
    }

    /**
     * 新增政策标签
     */
    @ApiOperation("新增政策标签")
    @RequiresPermissions("portalweb:label:add")
    @Log(title = "政策标签", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PolicyLabel policyLabel)
    {
        return toAjax(policyLabelService.insertPolicyLabel(policyLabel));
    }

    /**
     * 修改政策标签
     */
    @ApiOperation("修改政策标签")
    @RequiresPermissions("portalweb:label:edit")
    @Log(title = "政策标签", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PolicyLabel policyLabel)
    {
        return toAjax(policyLabelService.updatePolicyLabel(policyLabel));
    }

    /**
     * 删除政策标签
     */
    @ApiOperation("删除政策标签")
    @RequiresPermissions("portalweb:label:remove")
    @Log(title = "政策标签", businessType = BusinessType.DELETE)
	@DeleteMapping("/{policyLabelIds}")
    public AjaxResult remove(@PathVariable Long[] policyLabelIds)
    {
        return toAjax(policyLabelService.deletePolicyLabelByPolicyLabelIds(policyLabelIds));
    }
}
