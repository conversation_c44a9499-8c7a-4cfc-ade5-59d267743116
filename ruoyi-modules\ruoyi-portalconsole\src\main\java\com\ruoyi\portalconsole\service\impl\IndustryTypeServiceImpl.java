package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.IndustryTypeMapper;
import com.ruoyi.portalconsole.domain.IndustryType;
import com.ruoyi.portalconsole.service.IIndustryTypeService;

/**
 * 行业类别Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Service
public class IndustryTypeServiceImpl implements IIndustryTypeService 
{
    @Autowired
    private IndustryTypeMapper industryTypeMapper;

    /**
     * 查询行业类别
     * 
     * @param industryTypeId 行业类别主键
     * @return 行业类别
     */
    @Override
    public IndustryType selectIndustryTypeByIndustryTypeId(Long industryTypeId)
    {
        return industryTypeMapper.selectIndustryTypeByIndustryTypeId(industryTypeId);
    }

    /**
     * 查询行业类别列表
     * 
     * @param industryType 行业类别
     * @return 行业类别
     */
    @Override
    public List<IndustryType> selectIndustryTypeList(IndustryType industryType)
    {
        return industryTypeMapper.selectIndustryTypeList(industryType);
    }

    /**
     * 新增行业类别
     * 
     * @param industryType 行业类别
     * @return 结果
     */
    @Override
    public int insertIndustryType(IndustryType industryType)
    {
        industryType.setCreateTime(DateUtils.getNowDate());
        return industryTypeMapper.insertIndustryType(industryType);
    }

    /**
     * 修改行业类别
     * 
     * @param industryType 行业类别
     * @return 结果
     */
    @Override
    public int updateIndustryType(IndustryType industryType)
    {
        industryType.setUpdateTime(DateUtils.getNowDate());
        return industryTypeMapper.updateIndustryType(industryType);
    }

    /**
     * 批量删除行业类别
     * 
     * @param industryTypeIds 需要删除的行业类别主键
     * @return 结果
     */
    @Override
    public int deleteIndustryTypeByIndustryTypeIds(Long[] industryTypeIds)
    {
        return industryTypeMapper.deleteIndustryTypeByIndustryTypeIds(industryTypeIds);
    }

    /**
     * 删除行业类别信息
     * 
     * @param industryTypeId 行业类别主键
     * @return 结果
     */
    @Override
    public int deleteIndustryTypeByIndustryTypeId(Long industryTypeId)
    {
        return industryTypeMapper.deleteIndustryTypeByIndustryTypeId(industryTypeId);
    }
}
