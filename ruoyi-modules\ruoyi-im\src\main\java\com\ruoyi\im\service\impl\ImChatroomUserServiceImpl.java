package com.ruoyi.im.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.ruoyi.im.api.domain.ImChatroomUser;
import com.ruoyi.im.api.dto.ChatroomPojo;
import com.ruoyi.im.mapper.ImChatroomUserMapper;
import com.ruoyi.im.service.ImChatroomUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class ImChatroomUserServiceImpl extends ServiceImpl<ImChatroomUserMapper, ImChatroomUser> implements ImChatroomUserService
{
    @Resource
    private ImChatroomUserMapper imChatroomUserMapper;

    @Override
    public Map<String, Integer> findUser(String ids) {
        Map<String, Integer> map = Maps.newHashMap();
        List<ChatroomPojo> pojos = imChatroomUserMapper.findUser(ids);
        if(pojos==null || pojos.size()==0){
            return map;
        }
        pojos.forEach(p->{
            map.put(p.getChatroomId(),p.getNumber());
        });

        return map;
    }
}