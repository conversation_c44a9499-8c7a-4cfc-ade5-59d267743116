Vue.component('pc-suspension', {
    data: function() {
        return {

        }
    },
    methods: {
    },
    mounted() {
        // <script type="text/javascript">
            $(document).ready(function() {

            /* ----- 侧边悬浮 ---- */
            $(document).on("mouseenter", ".suspension .a", function() {
                var _this = $(this);
                var s = $(".suspension");
                var isService = _this.hasClass("a-service");
                var isServiceFabu = _this.hasClass("a-service-fabu");
                var isServicePhone = _this.hasClass("a-service-wechat");
                var isQrcode = _this.hasClass("a-qrcode");
                if (isService) {
                    s.find(".d-service").show().siblings(".d").hide();
                }
                if (isServiceFabu) {
                    s.find(".d-service-fabu").show().siblings(".d").hide();
                }
                if (isServicePhone) {
                    s.find(".d-service-wechat").show().siblings(".d").hide();
                }
                if (isQrcode) {
                    s.find(".d-qrcode").show().siblings(".d").hide();
                }
            });
            $(document).on("mouseleave", ".suspension, .suspension .a-top", function() {
            $(".suspension").find(".d").hide();
        });
            $(document).on("mouseenter", ".suspension .a-top", function() {
            $(".suspension").find(".d").hide();
        });
            $(document).on("click", ".suspension .a-top", function() {
            $("html,body").animate({
            scrollTop: 0
        });
        });
            $(window).scroll(function() {
            var st = $(document).scrollTop();
            var $top = $(".suspension .a-top");
            if (st > 400) {
            $top.css({
            display: 'block'
        });
        } else {
            if ($top.is(":visible")) {
            $top.hide();
        }
        }
        });

        });
        {/*</script>*/}
    },
    template: `  <div class="suspension">
        <div class="suspension-box">
            <!--<a href="#" class="a a-service "><i class="i"></i></a>-->
            <a href="#" class="a a-service-fabu "><img src="/images/icon/fb.svg"><div>发布</div></a>
           <a href="#" class="a a-service-wechat "><img src="/images/icon/kf.svg"><div>客服</div></a>
        <a href="javascript:;" class="a a-top"><img src="/images/icon/top.svg"><div>TOP</div></a>
          <!--  <a href="javascript:;" class="a a-qrcode"><i class="i"></i></a>-->
            <div class="d d-service"> <i class="arrow"></i>
                <div class="inner-box">
                    <div class="d-service-item clearfix">
                        <a href="https://095742.kefu.easemob.com/webim/im.html?configId=0429ddb7-b08d-405a-abaa-8066e92af279" class="clearfix" target="_blank"><span class="circle"><i class="i-qq"></i></span>
                             <h3>咨询在线客服</h3>
                        </a>
                    </div>
                </div>
            </div>
            <div class="d d-service-fabu "> <i class="arrow"></i>
                <div class="inner-box">
                    <div class="d-service-item clearfix"> <a href='/newPages/issueIndex.html?type=demand' class="clearfix"><span class="circle"><img src="/images/icon/fb_xq.svg"></span>
          <h3>发布需求</h3>
          </a> </div>
                    <div class="clear"></div>
                    <div class="d-service-item clearfix" style="padding-bottom: 0"> <a href="/newPages/issueIndex.html?type=supply" class="clearfix"><span class="circle"><img src="/images/icon/fb_gj.svg"></span>
          <h3>发布供给</h3>
          </a> </div>
                    <div class="clear"></div>
                </div>
            </div>
            <div class="d d-service-wechat"> <i class="arrow"></i>
                <div class="inner-box" style="padding: 10px">
                    <div class="d-service-item clearfix" style="padding: 0">
                        <div class="text">
                            <p style="font-size: 12px;color: #B3B3B3;">服务热线</p>
                            <p class="number2">400-8939-365</p>
                        </div>
                    </div>

               </div>
            </div>
<!--            <div class="d d-qrcode"> <i class="arrow"></i>-->
<!--                <div class="inner-box">-->
<!--                    <div class="qrcode-img"><img src="images/erweima.png" alt=""></div>-->
<!--                    <p>微信服务号</p>-->
<!--                </div>-->
<!--            </div>-->
        </div>
    </div>`

});
