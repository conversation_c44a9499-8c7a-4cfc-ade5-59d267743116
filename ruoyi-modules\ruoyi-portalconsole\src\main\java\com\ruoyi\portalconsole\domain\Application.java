package com.ruoyi.portalconsole.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 系统对接对象 application
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
public class Application extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 系统ID */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long applicationId;

    /** 系统图标 */
    @Excel(name = "系统图标")
    private String applicationIcon;

    /** 系统名称 */
    @Excel(name = "系统名称")
    private String applicationName;

    /** 系统分配id */
    @Excel(name = "系统分配id")
    private String applicationAppId;

    /** 系统地址 */
    @Excel(name = "系统地址")
    private String applicationAppUrl;

    /** 状态，业务字典 */
    @Excel(name = "状态，业务字典")
    private String applicationStatus;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setApplicationId(Long applicationId) 
    {
        this.applicationId = applicationId;
    }

    public Long getApplicationId() 
    {
        return applicationId;
    }
    public void setApplicationIcon(String applicationIcon) 
    {
        this.applicationIcon = applicationIcon;
    }

    public String getApplicationIcon() 
    {
        return applicationIcon;
    }
    public void setApplicationName(String applicationName) 
    {
        this.applicationName = applicationName;
    }

    public String getApplicationName() 
    {
        return applicationName;
    }
    public void setApplicationAppId(String applicationAppId) 
    {
        this.applicationAppId = applicationAppId;
    }

    public String getApplicationAppId() 
    {
        return applicationAppId;
    }
    public void setApplicationAppUrl(String applicationAppUrl) 
    {
        this.applicationAppUrl = applicationAppUrl;
    }

    public String getApplicationAppUrl() 
    {
        return applicationAppUrl;
    }
    public void setApplicationStatus(String applicationStatus) 
    {
        this.applicationStatus = applicationStatus;
    }

    public String getApplicationStatus() 
    {
        return applicationStatus;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("applicationId", getApplicationId())
            .append("applicationIcon", getApplicationIcon())
            .append("applicationName", getApplicationName())
            .append("applicationAppId", getApplicationAppId())
            .append("applicationAppUrl", getApplicationAppUrl())
            .append("applicationStatus", getApplicationStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
