package com.ruoyi.im.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.im.api.domain.ImUser;
import com.ruoyi.im.api.domain.ImUserApply;
import com.ruoyi.im.api.domain.ImUserFriend;
import com.ruoyi.im.api.dto.ImUserVO;
import com.ruoyi.im.api.dto.UserDto;
import com.ruoyi.im.api.util.ImUtil;
import com.ruoyi.im.api.util.PinyinUtils;
import com.ruoyi.im.service.ImUserApplyService;
import com.ruoyi.im.service.ImUserFriendService;
import com.ruoyi.im.service.ImUserService;
import com.ruoyi.im.api.util.SqlUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/im/userfriend")
public class ImUserFriendController extends BaseController {

    @Resource
    private ImUserFriendService imUserFriendService;

    @Resource
    private ImUserService imUserService;

    @Resource
    private ImUserApplyService imUserApplyService;



    /***
     * ImUserFriend分页条件搜索实现
     * @param imUserFriend
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/search/{page}/{size}")
    public TableDataInfo findPage(@RequestBody(required = false) ImUserFriend imUserFriend, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields) {
        Page<ImUserFriend> pageSearch = new Page<>(page, size);
        QueryWrapper<ImUserFriend> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        imUserFriendService.page(pageSearch, wrapper);
        TableDataInfo tableDataInfo = new TableDataInfo(pageSearch.getRecords(), (int) pageSearch.getTotal());
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setMsg("success");
        return tableDataInfo;
    }

    /***
     * 根据手机号和昵称全局搜索用户
     * @param
     * @return
     */
    @PostMapping(value = "/search")
    public R<List<ImUserVO>> findList(@RequestBody ImUserFriend imUserFriend, @RequestParam(value = "fields") String fields) {
        QueryWrapper<ImUser> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(fields)) {
            //封装返回的数据
            ArrayList<ImUserVO> userList = new ArrayList<>();
            //根据好友的id，以及电话，昵称去查找
            wrapper.like("userId", fields).or().like("name", fields);

            List<ImUser> list = imUserService.list(wrapper);
            if (list.size()!=0 ) {
                for (ImUser user : list) {
                    //封装用户的信息
                    ImUserVO imUserVO = new ImUserVO();
                    imUserVO.setImUser(user);

                    ImUserFriend friend = imUserFriendService.getOne(new QueryWrapper<ImUserFriend>().eq("friendId", user.getUserId()).eq("userId", imUserFriend.getUserId()));


                    ImUserFriend userFriend=imUserFriendService.getOne(new QueryWrapper<ImUserFriend>().eq("userId", user.getUserId()).eq("friendId", imUserFriend.getUserId()));

                    if (ObjectUtils.isNotEmpty(friend) || ObjectUtils.isNotEmpty(userFriend)) {
                        imUserVO.setIdentification(1);  //默认为好友
                        if (ObjectUtils.isNotEmpty(friend)) {
                            imUserVO.setFriendsId(friend.getId());
                            imUserVO.setRemark(friend.getRemark());
                        }else {
                            imUserVO.setFriendsId(userFriend.getId());
                            imUserVO.setRemark(userFriend.getRemark());
                        }
                    }else {
                        imUserVO.setIdentification(0);    //默认为非好友

                    }
                    userList.add(imUserVO);  //封装用户的好友标识
                    return R.ok(userList);
                }
            }

        }
        return R.fail(400, "没有找到相应的结果");
    }


    /***
     * 新增ImUserFriend数据
     * @param imUserFriend
     * @return
     */
    @PostMapping
    public R<Long> add(@RequestBody ImUserFriend imUserFriend) {
        if (imUserFriendService.save(imUserFriend)) {
            return R.ok(imUserFriend.getId());
        }
        return R.ok(-1L);
    }

    /***
     * 根据ID查询ImUserFriend数据
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public R<ImUserFriend> findById(@PathVariable("id") Long id) {
        return R.ok(imUserFriendService.getById(id));
    }

    /***
     * 批量状态
     * @param opid
     * @return
     */
    @PostMapping("/batch/delete")
    public R<Boolean> batchDelete(@RequestParam("opid") String opid) {
        if (imUserFriendService.removeByIds(SqlUtils.str2LongList(opid, ","))) {
            return R.ok(true);
        }
        return R.ok(false);
    }

    /**
     * @Description:
     * @Param:
     * @return:
     * @Author:查看通讯录好友列表，根据首字母排序
     * @Date:
     */
    @PostMapping(value = "/list")
    public R<Map<String, List<JSONObject>>> userfriendList(@RequestBody ImUserFriend imUserFriend) {
        //根据用户ID查询好友id
        if (ObjectUtils.isNotEmpty(imUserFriend)) {

                List<ImUserFriend> imUserFriendLists= imUserFriendService.list(new QueryWrapper<ImUserFriend>().eq("userId",imUserFriend.getUserId()));

                List<ImUserFriend> imUserFriendList= imUserFriendService.list(new QueryWrapper<ImUserFriend>().eq("friendId",imUserFriend.getUserId()));
                imUserFriendLists.addAll(imUserFriendList);

            List<UserDto> userLists=new ArrayList<>();
            //1、根据userId集合查询索引的user信息
            //2、第一步的信息放到map里面
            //3、循环里面根据map去找
            if (imUserFriendLists.size()!=0) {
                for (ImUserFriend userFriend : imUserFriendLists) {
                    //根据好友id查询用户信息
                    ImUser imUser = null;
                    //A是本人
                    if(imUserFriend.getUserId().equals(userFriend.getUserId())){
                        imUser = imUserService.getOne(new QueryWrapper<ImUser>().eq("userId", userFriend.getFriendId()));
                    }
                    else{
                        imUser = imUserService.getOne(new QueryWrapper<ImUser>().eq("userId", userFriend.getUserId()));
                    }
                    if (ObjectUtils.isNotEmpty(imUser)) {
                        UserDto userDto = new UserDto();
                        userDto.setCustomer(imUser.getCustomer());
                        userDto.setName(imUser.getName());
                        userDto.setId(imUser.getId());
                        userDto.setUserId(imUser.getUserId());
                        userDto.setPortraitUri(imUser.getPortraitUri());
                        userDto.setRemark(userFriend.getRemark());
                        userLists.add(userDto);
                    }
                }
                //遍历获取的好友信息
                if (userLists != null && userLists.size() > 0) {
                    Map<String, List<JSONObject>> map = Maps.newHashMap();
                    for (UserDto model : userLists) {
                        String alpha = "#";
                        String str="#";
                        if (!PinyinUtils.startChar(model.getName())) {
                            alpha = PinyinUtils.getHeadChar(model.getName(), true);
                             str = numStr(alpha);
                        }
                        if (map.containsKey(str)) {
                            map.get(str).add(getMember(model));
                        } else {
                            map.put(str, Lists.newArrayList(getMember(model)));
                        }
                    }
                    return R.ok(ImUtil.sortMapByKey(map));
                }
            }else {
                return R.ok(null);
            }
        }
        return R.ok(null);
    }

    /**
     * @Description:
     * @Param:
     * @return:
     * @Author: 删除好友
     * @Date:
     */
    @PostMapping(value = "/delete")
    @Transactional
    public R<Boolean> delete(@RequestBody ImUserFriend imUserFriend) {
        if (ObjectUtils.isNotEmpty(imUserFriend)) {
            Boolean falg=imUserFriendService.remove(new QueryWrapper<ImUserFriend>().eq("id", imUserFriend.getId()));
            if (falg) {
                boolean removeq = imUserApplyService.remove(new QueryWrapper<ImUserApply>().eq("senderId", imUserFriend.getUserId()).eq("receiverId", imUserFriend.getFriendId()));
                boolean remove = imUserApplyService.remove(new QueryWrapper<ImUserApply>().eq("senderId", imUserFriend.getFriendId()).eq("receiverId", imUserFriend.getUserId()));
                if (remove==true || removeq==true) {
                    return R.ok(true);
                }

            }
        }
        return R.fail(400, "删除失败");
    }


    private JSONObject getMember(UserDto member) {
        JSONObject detail = new JSONObject();
        detail.put("id", member.getId());
        detail.put("customer", member.getCustomer());
        detail.put("userId", member.getUserId());
        detail.put("name", member.getName());
        detail.put("remark", member.getRemark());
        detail.put("portraitUri", member.getPortraitUri());
        return detail;
    }

    /***
     * 修改ImUserFriend数据
     * @param imUserFriend
     * @return
     */
    @PostMapping(value = "/update")
    public R<Boolean> update(@RequestBody ImUserFriend imUserFriend){
        if (imUserFriendService.update(imUserFriend,new QueryWrapper<ImUserFriend>().eq("id",imUserFriend.getId()))) {
            return R.ok(true);
        }
        return R.ok(false);
    }


    public String  numStr(String str) {
        String strs = "0123456789";
        boolean status = strs.contains(str);
        if(status){
            return "#";
        }else {
            return str;
        }
    }

}
