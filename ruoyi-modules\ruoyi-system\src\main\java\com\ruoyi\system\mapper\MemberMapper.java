package com.ruoyi.system.mapper;


import com.ruoyi.system.api.domain.Member;

import java.util.List;

/**
 * 会员Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface MemberMapper
{
    /**
     * 查询会员Id列表
     *
     * @return 会员id集合
     */
    public List<Long> selectMemberIdList();

    public Member selectMemberByMemberPhone(String memberPhone);

    public Member updateMemberRongYunTokenByMemberPhone(Member member);

    public List<Member> selectMemberListWithoutRongYunToken();

    public List<Member> selectByMemberPhoneOrNickname(String searchKey);

}
