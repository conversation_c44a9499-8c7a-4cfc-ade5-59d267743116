package com.ruoyi.portalconsole.service;

import java.util.List;
import com.ruoyi.portalconsole.domain.AppStoreOrder;
import com.ruoyi.portalconsole.domain.vo.AppStoreOrderVO;

/**
 * 应用商店订单Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface IAppStoreOrderService 
{
    /**
     * 查询应用商店订单
     * 
     * @param appStoreOrderId 应用商店订单主键
     * @return 应用商店订单
     */
    public AppStoreOrderVO selectAppStoreOrderByAppStoreOrderId(Long appStoreOrderId);

    /**
     * 查询应用商店订单列表
     * 
     * @param appStoreOrder 应用商店订单
     * @return 应用商店订单集合
     */
    public List<AppStoreOrderVO> selectAppStoreOrderList(AppStoreOrder appStoreOrder);

    /**
     * 新增应用商店订单
     * 
     * @param appStoreOrder 应用商店订单
     * @return 结果
     */
    public int insertAppStoreOrder(AppStoreOrder appStoreOrder);

    /**
     * 修改应用商店订单
     * 
     * @param appStoreOrder 应用商店订单
     * @return 结果
     */
    public int updateAppStoreOrder(AppStoreOrder appStoreOrder);

    /**
     * 批量删除应用商店订单
     * 
     * @param appStoreOrderIds 需要删除的应用商店订单主键集合
     * @return 结果
     */
    public int deleteAppStoreOrderByAppStoreOrderIds(Long[] appStoreOrderIds);

    /**
     * 删除应用商店订单信息
     * 
     * @param appStoreOrderId 应用商店订单主键
     * @return 结果
     */
    public int deleteAppStoreOrderByAppStoreOrderId(Long appStoreOrderId);
}
