<template>
  <!-- 政策资讯 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="发布单位" prop="policyInformationUnit">
        <el-input
          v-model="queryParams.policyInformationUnit"
          placeholder="请输入发布单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="资讯标题" prop="policyInformationTitle">
        <el-input
          v-model="queryParams.policyInformationTitle"
          placeholder="请输入资讯标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="浏览次数" prop="policyInformationFrequency">
        <el-input
          v-model="queryParams.policyInformationFrequency"
          placeholder="请输入浏览次数"
          clearable
          type="number"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:PolicyInformation:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:PolicyInformation:remove']"
        >删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:PolicyInformation:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="PolicyInformationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编码" align="center" prop="policyInformationId" />
      <el-table-column label="资讯标题" align="center" prop="policyInformationTitle" />
      <el-table-column label="发布日期" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发布单位" align="center" prop="policyInformationUnit" />
      <!-- <el-table-column label="简介内容" align="center" prop="policyInformationIntroduction" /> -->
      <!-- <el-table-column label="封面" align="center" prop="policyInformationImg" /> -->
      <el-table-column label="资讯内容" align="center" prop="policyInformationContent" />
      <el-table-column label="资讯板块" align="center" prop="policyInformationType" />
      <!-- <el-table-column label="浏览次数" align="center" prop="policyInformationFrequency" /> -->
      <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:PolicyInformation:edit']"
          >修改</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:PolicyInformation:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改政策资讯对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row :span="24">
          <el-col :span="8">
            <el-form-item label="发布单位" prop="policyInformationUnit">
              <el-input v-model="form.policyInformationUnit" placeholder="请输入发布单位" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资讯板块" prop="policyInformationType">
              <el-select v-model="form.policyInformationType" placeholder="请选择"  clearable style="width:200px">
                <el-option v-for="dict in labelList" :key="dict.policyLabelId"
                :label="dict.policyLabelValue"
                :value="dict.policyLabelValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="级别">
              <el-select v-model="form.policyInformationLevel" placeholder="请选择级别" clearable>
                <el-option v-for="dict in dict.type.sys_grade" 
                :key="dict.value"
                :label="dict.label"
                :value="dict.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-row>
              <el-col :span="12">
                <el-form-item label="资讯来源">
                  <el-radio-group v-model="form.origin">
                    <el-radio v-for="dict in originOptions" :key="dict.dictValue" :label="dict.dictValue">
                      {{dict.dictLabel}}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item>
                  <el-input v-model="form.publisher" placeholder="作者或转载媒体" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col> -->
        </el-row>
        <el-row :span="24">
          <el-col :span="18">
            <el-form-item label="资讯标题" prop="policyInformationTitle">
              <el-input v-model="form.policyInformationTitle" placeholder="请输入资讯标题" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否置顶" prop="top">
              <el-select v-model="form.top" placeholder="置顶选项">
                <el-option v-for="dict in dict.type.sys_yesno_option" :key="dict.value"
                :label="dict.label"
                :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="简介内容" prop="policyInformationIntroduction">
          <el-input v-model="form.policyInformationIntroduction" type="textarea" placeholder="请输入简介内容" />
        </el-form-item>
        <el-form-item label="封面图片" prop="policyInformationImg">
          <!-- <el-input v-model="form.policyInformationImg" type="textarea" placeholder="请输入" /> -->
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :http-request="uploadFun"
            :before-upload="beforeAvatarUpload">
            <el-image
              v-if="form.policyInformationImg"
              :src="form.policyInformationImg"
              class="avatar"
            >
              <div slot="error" class="image-slot">
                <span>暂无图片</span>
              </div>
            </el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">请上传 大小不超过 5MB 格式为 png/jpg/jpeg 的文件</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="资讯内容">
          <editor v-model="form.policyInformationContent" :min-height="192"/>
        </el-form-item>
        <!-- <el-row :span="24">
          <el-col :span="18">
            <el-form-item label="资讯行业">
              <el-select v-model="form.industryFirstLevelCode" placeholder="资讯行业大类" @change="getIndustrySecondLevel"
                clearable>
                <el-option v-for="industry in industryFirstLevel" :key="industry.id" :label="industry.name"
                  :value="industry.id"></el-option>
              </el-select>
              <el-select v-model="form.industrySecondLevelCode" placeholder="资讯行业二级类" @change="getIndustryThirdLevel"
                clearable>
                <el-option v-for="industry in industrySecondLevel" :key="industry.id" :label="industry.name"
                  :value="industry.id"></el-option>
              </el-select>
              <el-select v-model="form.industryThirdLevelCode" placeholder="资讯行业小类" clearable>
                <el-option v-for="industry in industryThirdLevel" :key="industry.id" :label="industry.name"
                  :value="industry.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPolicyInformation, getPolicyInformation, delPolicyInformation, addPolicyInformation, updatePolicyInformation } from "@/api/portalconsole/PolicyInformation";
import { listLabel} from "@/api/portalconsole/label";
import {comUpload} from "@/api/portalconsole/uploadApi";


export default {
  name: "PolicyInformation",
  dicts: ['policy_label_group','sys_grade','sys_yesno_option'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 政策资讯表格数据
      PolicyInformationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        policyInformationUnit: null,
        policyInformationType: null,
        policyInformationLevel: null,
        policyInformationTitle: null,
        policyInformationIntroduction: null,
        policyInformationImg: null,
        policyInformationContent: null,
        policyInformationFrequency: null,
        policyInformationDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        policyInformationUnit:[
          { required: true, message: "发布单位不能为空", trigger: "blur" }
        ],
        policyInformationType:[
          { required: true, message: "资讯板块不能为空", trigger: "change" }
        ],
        policyInformationTitle:[
          { required: true, message: "资讯标题不能为空", trigger: "blur" }
        ],
      },
      labelList:[],
    };

  },
  created() {
    this.getList();
    this.getLabel()
  },
  methods: {
    /** 查询政策资讯列表 */
    getList() {
      this.loading = true;
      listPolicyInformation(this.queryParams).then(response => {
        this.PolicyInformationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        policyInformationId: null,
        policyInformationUnit: null,
        policyInformationType: null,
        policyInformationLevel: null,
        policyInformationTitle: null,
        policyInformationIntroduction: null,
        policyInformationImg: null,
        policyInformationContent: null,
        policyInformationFrequency: null,
        policyInformationDate: null,
        delFlag: null,
        top:null,
        // createBy: null,
        // createTime: null,
        // updateBy: null,
        // updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.policyInformationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加政策资讯";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const policyInformationId = row.policyInformationId || this.ids
      getPolicyInformation(policyInformationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改政策资讯";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.policyInformationId != null) {
            updatePolicyInformation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPolicyInformation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const policyInformationIds = row.policyInformationId || this.ids;
      this.$modal.confirm('是否确认删除政策资讯编号为"' + policyInformationIds + '"的数据项？').then(function() {
        return delPolicyInformation(policyInformationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/PolicyInformation/export', {
        ...this.queryParams
      }, `PolicyInformation_${new Date().getTime()}.xlsx`)
    },
    // 图片上传
    uploadFun(params) {
        const file = params.file;
        let form = new FormData();
        form.append("file", file); // 文件对象
        comUpload(form).then(res => {
          let data = res.data;
          this.$set(this.form,'policyInformationImg',data.fileFullPath)  // 图片全路径
          // this.$set(this.form,'imageUrl',data.fileId) // 图片Id
        })
      },
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/jpeg' || 'image/png' || 'image/jpg';
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isJPG) {
          this.$message.error('上传图片只能是 jpg、jpeg、png 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传头像图片大小不能超过 2MB!');
        }
        return isJPG && isLt2M;
      },
      //资讯板块得列表查询
      getLabel(){
        const data={
          pageNum: 1,
          pageSize: 100,
        }
        listLabel(data).then(response => {
          this.labelList = response.rows;
      });
      },
  }
};
</script>
<style  scoped>
>>>.el-table .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
    margin-left: 5px;
}
.avatar-uploader >>> .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
/deep/.el-table .cell{
  height: 26px;
}
</style>