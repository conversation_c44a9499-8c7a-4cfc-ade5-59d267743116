package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.FactoryPerformance;
import com.ruoyi.system.service.IFactoryPerformanceService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 工厂业绩情况Controller
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@RestController
@RequestMapping("/performance")
public class FactoryPerformanceController extends BaseController
{
    @Autowired
    private IFactoryPerformanceService factoryPerformanceService;

    /**
     * 查询工厂业绩情况列表
     */
    @RequiresPermissions("system:performance:list")
    @GetMapping("/list")
    public TableDataInfo list(FactoryPerformance factoryPerformance)
    {
        startPage();
        List<FactoryPerformance> list = factoryPerformanceService.selectFactoryPerformanceList(factoryPerformance);
        return getDataTable(list);
    }

    /**
     * 导出工厂业绩情况列表
     */
    @RequiresPermissions("system:performance:export")
    @Log(title = "工厂业绩情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FactoryPerformance factoryPerformance)
    {
        List<FactoryPerformance> list = factoryPerformanceService.selectFactoryPerformanceList(factoryPerformance);
        ExcelUtil<FactoryPerformance> util = new ExcelUtil<FactoryPerformance>(FactoryPerformance.class);
        util.exportExcel(response, list, "工厂业绩情况数据");
    }

    /**
     * 获取工厂业绩情况详细信息
     */
    @RequiresPermissions("system:performance:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(factoryPerformanceService.selectFactoryPerformanceById(id));
    }

    /**
     * 新增工厂业绩情况
     */
    @RequiresPermissions("system:performance:add")
    @Log(title = "工厂业绩情况", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FactoryPerformance factoryPerformance)
    {
        return toAjax(factoryPerformanceService.insertFactoryPerformance(factoryPerformance));
    }

    /**
     * 修改工厂业绩情况
     */
    @RequiresPermissions("system:performance:edit")
    @Log(title = "工厂业绩情况", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FactoryPerformance factoryPerformance)
    {
        return toAjax(factoryPerformanceService.updateFactoryPerformance(factoryPerformance));
    }

    /**
     * 删除工厂业绩情况
     */
    @RequiresPermissions("system:performance:remove")
    @Log(title = "工厂业绩情况", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(factoryPerformanceService.deleteFactoryPerformanceByIds(ids));
    }
}
