package com.ruoyi.portalweb.api.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 生态类别对象 ecology_category
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public class EcologyCategory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 生态类别ID */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ecologyCategoryId;

    /** 编码 */
    @Excel(name = "编码")
    private String ecologyCategoryCode;

    /** 名称 */
    @Excel(name = "名称")
    private String ecologyCategoryName;

    /** 简介 */
    @Excel(name = "简介")
    private String ecologyCategoryIntroduction;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setEcologyCategoryId(Long ecologyCategoryId) 
    {
        this.ecologyCategoryId = ecologyCategoryId;
    }

    public Long getEcologyCategoryId() 
    {
        return ecologyCategoryId;
    }
    public void setEcologyCategoryCode(String ecologyCategoryCode) 
    {
        this.ecologyCategoryCode = ecologyCategoryCode;
    }

    public String getEcologyCategoryCode() 
    {
        return ecologyCategoryCode;
    }
    public void setEcologyCategoryName(String ecologyCategoryName) 
    {
        this.ecologyCategoryName = ecologyCategoryName;
    }

    public String getEcologyCategoryName() 
    {
        return ecologyCategoryName;
    }
    public void setEcologyCategoryIntroduction(String ecologyCategoryIntroduction) 
    {
        this.ecologyCategoryIntroduction = ecologyCategoryIntroduction;
    }

    public String getEcologyCategoryIntroduction() 
    {
        return ecologyCategoryIntroduction;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ecologyCategoryId", getEcologyCategoryId())
            .append("ecologyCategoryCode", getEcologyCategoryCode())
            .append("ecologyCategoryName", getEcologyCategoryName())
            .append("ecologyCategoryIntroduction", getEcologyCategoryIntroduction())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
