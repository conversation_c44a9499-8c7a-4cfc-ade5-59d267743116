<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.WorkshopInfoMapper">
    
    <resultMap type="WorkshopInfo" id="WorkshopInfoResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="company"    column="company"    />
        <result property="address"    column="address"    />
        <result property="area"    column="area"    />
        <result property="price"    column="price"    />
        <result property="description"    column="description"    />
        <result property="resources"    column="resources"    />
        <result property="capability"    column="capability"    />
        <result property="notes"    column="notes"    />
        <result property="images"    column="images"    />
        <result property="type"    column="type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="checkStatus"    column="check_status"    />
    </resultMap>

    <sql id="selectWorkshopInfoVo">
        select id,
               name,
               company,
               address,
               area,
               price,
               description,
               resources,
               capability,
               notes,
               images,
               type,
               create_time,
               update_time,
               create_by,
               update_by,
               check_status
        from workshop_info
    </sql>

    <select id="selectWorkshopInfoList" parameterType="WorkshopInfo" resultMap="WorkshopInfoResult">
        <include refid="selectWorkshopInfoVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="company != null  and company != ''"> and company = #{company}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="resources != null  and resources != ''"> and resources = #{resources}</if>
            <if test="capability != null  and capability != ''"> and capability = #{capability}</if>
            <if test="notes != null  and notes != ''"> and notes = #{notes}</if>
            <if test="images != null  and images != ''">and images = #{images}</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="createBy != null  and createBy != ''">and create_by = #{createBy}</if>
            <if test="updateBy != null  and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="checkStatus != null ">and check_status = #{checkStatus}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectWorkshopInfoById" parameterType="Long" resultMap="WorkshopInfoResult">
        <include refid="selectWorkshopInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertWorkshopInfo" parameterType="WorkshopInfo" useGeneratedKeys="true" keyProperty="id">
        insert into workshop_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="company != null and company != ''">company,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="area != null">area,</if>
            <if test="price != null">price,</if>
            <if test="description != null">description,</if>
            <if test="resources != null">resources,</if>
            <if test="capability != null">capability,</if>
            <if test="notes != null">notes,</if>
            <if test="images != null">images,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="checkStatus != null">check_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="company != null and company != ''">#{company},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="area != null">#{area},</if>
            <if test="price != null">#{price},</if>
            <if test="description != null">#{description},</if>
            <if test="resources != null">#{resources},</if>
            <if test="capability != null">#{capability},</if>
            <if test="notes != null">#{notes},</if>
            <if test="images != null">#{images},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
         </trim>
    </insert>

    <update id="updateWorkshopInfo" parameterType="WorkshopInfo">
        update workshop_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="company != null and company != ''">company = #{company},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="area != null">area = #{area},</if>
            <if test="price != null">price = #{price},</if>
            <if test="description != null">description = #{description},</if>
            <if test="resources != null">resources = #{resources},</if>
            <if test="capability != null">capability = #{capability},</if>
            <if test="notes != null">notes = #{notes},</if>
            <if test="images != null">images = #{images},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWorkshopInfoById" parameterType="Long">
        delete from workshop_info where id = #{id}
    </delete>

    <delete id="deleteWorkshopInfoByIds" parameterType="String">
        delete from workshop_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>