package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.WorkshopInfo;

/**
 * 车间信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface WorkshopInfoMapper 
{
    /**
     * 查询车间信息
     * 
     * @param id 车间信息主键
     * @return 车间信息
     */
    public WorkshopInfo selectWorkshopInfoById(Long id);

    /**
     * 查询车间信息列表
     * 
     * @param workshopInfo 车间信息
     * @return 车间信息集合
     */
    public List<WorkshopInfo> selectWorkshopInfoList(WorkshopInfo workshopInfo);

    /**
     * 新增车间信息
     * 
     * @param workshopInfo 车间信息
     * @return 结果
     */
    public int insertWorkshopInfo(WorkshopInfo workshopInfo);

    /**
     * 修改车间信息
     * 
     * @param workshopInfo 车间信息
     * @return 结果
     */
    public int updateWorkshopInfo(WorkshopInfo workshopInfo);

    /**
     * 删除车间信息
     * 
     * @param id 车间信息主键
     * @return 结果
     */
    public int deleteWorkshopInfoById(Long id);

    /**
     * 批量删除车间信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWorkshopInfoByIds(Long[] ids);
}
