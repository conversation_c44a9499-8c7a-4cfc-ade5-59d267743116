package com.ruoyi.portalweb.api.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 生态协作对象 ecology
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
public class Ecology extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 生态ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ecologyId;

    /**
     * 生态类别ID
     */
    @Excel(name = "生态类别ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ecologyCategoryId;

    /**
     * 公司id
     */
    @Excel(name = "公司id")
    private String companyId;

    /**
     * 合作思路
     */
    @Excel(name = "合作思路")
    private String ecologyIdeas;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String ecologyContact;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    private String ecologyPhone;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 会员id
     */
    @Excel(name = "会员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long memberId;

    /**
     * 合作领域
     */
    @Excel(name = "合作领域")
    private String ecologyField;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public String getEcologyField() {
        return ecologyField;
    }

    public void setEcologyField(String ecologyField) {
        this.ecologyField = ecologyField;
    }

    public void setEcologyId(Long ecologyId) {
        this.ecologyId = ecologyId;
    }

    public Long getEcologyId() {
        return ecologyId;
    }

    public void setEcologyCategoryId(Long ecologyCategoryId) {
        this.ecologyCategoryId = ecologyCategoryId;
    }

    public Long getEcologyCategoryId() {
        return ecologyCategoryId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setEcologyIdeas(String ecologyIdeas) {
        this.ecologyIdeas = ecologyIdeas;
    }

    public String getEcologyIdeas() {
        return ecologyIdeas;
    }

    public void setEcologyContact(String ecologyContact) {
        this.ecologyContact = ecologyContact;
    }

    public String getEcologyContact() {
        return ecologyContact;
    }

    public void setEcologyPhone(String ecologyPhone) {
        this.ecologyPhone = ecologyPhone;
    }

    public String getEcologyPhone() {
        return ecologyPhone;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getDelFlag() {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("ecologyId", getEcologyId())
                .append("ecologyCategoryId", getEcologyCategoryId())
                .append("companyId", getCompanyId())
                .append("ecologyIdeas", getEcologyIdeas())
                .append("ecologyContact", getEcologyContact())
                .append("ecologyPhone", getEcologyPhone())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
