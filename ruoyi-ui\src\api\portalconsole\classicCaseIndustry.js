import request from '@/utils/request'

// 查询典型案例行业列表
export function listClassicCaseIndustry(query) {
  return request({
    url: '/portalconsole/classicCaseIndustry/list',
    method: 'get',
    params: query
  })
}

// 查询典型案例行业详细
export function getClassicCaseIndustry(classicCaseIndustryId) {
  return request({
    url: '/portalconsole/classicCaseIndustry/' + classicCaseIndustryId,
    method: 'get'
  })
}

// 新增典型案例行业
export function addClassicCaseIndustry(data) {
  return request({
    url: '/portalconsole/classicCaseIndustry',
    method: 'post',
    data: data
  })
}

// 修改典型案例行业
export function updateClassicCaseIndustry(data) {
  return request({
    url: '/portalconsole/classicCaseIndustry',
    method: 'put',
    data: data
  })
}

// 删除典型案例行业
export function delClassicCaseIndustry(classicCaseIndustryId) {
  return request({
    url: '/portalconsole/classicCaseIndustry/' + classicCaseIndustryId,
    method: 'delete'
  })
}
