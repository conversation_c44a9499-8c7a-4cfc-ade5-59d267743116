package com.ruoyi.portalweb.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 行政区划表 region
 */
public class Region extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "区划编号")
    private String code;
    @ApiModelProperty(value = "父区划编号")
    private String parentCode;
    @ApiModelProperty(value = "祖区划编号")
    private String ancestors;
    @ApiModelProperty(value = "区划名称")
    private String name;
    @ApiModelProperty(value = "省级区划编号")
    private String provinceCode;
    @ApiModelProperty(value = "省级名称")
    private String provinceName;
    @ApiModelProperty(value = "市级区划编号")
    private String cityCode;
    @ApiModelProperty(value = "市级名称")
    private String cityName;
    @ApiModelProperty(value = "区级区划编号")
    private String districtCode;
    @ApiModelProperty(value = "区级名称")
    private String districtName;
    @ApiModelProperty(value = "镇级区划编号")
    private String townCode;
    @ApiModelProperty(value = "镇级名称")
    private String townName;
    @ApiModelProperty(value = "村级区划编号")
    private String villageCode;
    @ApiModelProperty(value = "村级名称")
    private String villageName;
    @ApiModelProperty(value = "层级")
    private String regionLevel;
    @ApiModelProperty(value = "排序")
    private String sort;
    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    private String delFlag;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public String getTownCode() {
        return townCode;
    }

    public void setTownCode(String townCode) {
        this.townCode = townCode;
    }

    public String getTownName() {
        return townName;
    }

    public void setTownName(String townName) {
        this.townName = townName;
    }

    public String getVillageCode() {
        return villageCode;
    }

    public void setVillageCode(String villageCode) {
        this.villageCode = villageCode;
    }

    public String getVillageName() {
        return villageName;
    }

    public void setVillageName(String villageName) {
        this.villageName = villageName;
    }

    public String getRegionLevel() {
        return regionLevel;
    }

    public void setRegionLevel(String regionLevel) {
        this.regionLevel = regionLevel;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
}
