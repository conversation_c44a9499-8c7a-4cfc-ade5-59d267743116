<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="280" height="170" viewBox="0 0 280 170">
  <defs>
    <linearGradient id="linear-gradient" x1="0.662" y1="0.316" x2="1" y2="0.995" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#428afa"/>
      <stop offset="1" stop-color="#51b9ff"/>
    </linearGradient>
    <clipPath id="clip-path">
      <rect id="矩形_21594" data-name="矩形 21594" width="280" height="170" rx="6" fill="url(#linear-gradient)" style="mix-blend-mode: multiply;isolation: isolate"/>
    </clipPath>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#3f97ff"/>
      <stop offset="1" stop-color="#0988f3"/>
    </linearGradient>
  </defs>
  <g id="组_87" data-name="组 87" clip-path="url(#clip-path)">
    <g id="组_18023" data-name="组 18023" transform="matrix(0.961, -0.276, 0.276, 0.961, 48.28, 28.13)">
      <g id="组_18021" data-name="组 18021" transform="translate(0 0)">
        <g id="组_21573" data-name="组 21573" transform="translate(0 0)" opacity="0">
          <path id="路径_8491" data-name="路径 8491" d="M0,0,231.616,14.372l13.417,221.309L13.417,221.309Z" fill="#fff"/>
          <path id="矩形_21573_-_轮廓" data-name="矩形 21573 - 轮廓" d="M5.08,4.888l12.8,211.145,220.889,13.711L225.969,18.6,5.08,4.888M0,0,230.493,14.307l13.352,220.325L13.352,220.325Z" transform="translate(0.594 0.524)" fill="#707070"/>
        </g>
        <path id="联合_361" data-name="联合 361" d="M102.493,193.358a96.762,96.762,0,0,1-48.735-17.187A30.818,30.818,0,0,0,60.542,170a85.84,85.84,0,0,0,41.394,14,71.422,71.422,0,0,0,39.912-8.792,44.8,44.8,0,0,0,7.576,7.109,78.46,78.46,0,0,1-41.334,11.216Q105.309,193.536,102.493,193.358Zm69.447-11.88a31.773,31.773,0,0,1-13.057-3.8l-.014-.008-.091-.049-.047-.025-.049-.027q-.651-.356-1.3-.746c-12.756-7.723-18.67-22.565-14.557-34.84l-17.192-10.589c-5.757,7.449-15.206,11.879-26.165,11.187a39.342,39.342,0,0,1-27.726-14.627L55.561,136.12q.177.394.342.788l0,.009.029.07.008.019.024.057.013.032.017.042.017.042.013.032.021.052,0,.012a29.249,29.249,0,0,1,1.989,8.006v.007l.007.067,0,.018.006.059,0,.021.005.057,0,.029,0,.048,0,.047,0,.031c.855,9.937-3.738,19.354-12.86,24.068a26.262,26.262,0,0,1-10,2.813c-11.2.892-23.17-4.933-29.832-15.532-8.724-13.879-5.039-30.689,8.233-37.545,10.153-5.245,22.975-3.145,32.346,4.346l.025.02.058.047.039.032.051.041.051.041.039.032.083.068.01.008.1.082h0l.1.081.006.005.1.081h0a33.775,33.775,0,0,1,3.542,3.452l16.376-8.267a34.874,34.874,0,0,1-3.064-12.263c-.994-16.7,10.863-29.72,27.22-30.971l-.986-19.441A31.068,31.068,0,0,1,86.6,56C72.935,53.836,62.479,42.281,62.456,28.36,62.43,12.913,75.26.4,91.113.419c14.777.015,26.965,10.908,28.572,24.9a28.237,28.237,0,0,1,1.2,6.615c.8,13.5-8.553,24.08-21.61,25.5l.99,19.461c16.69,3.284,30.259,17.935,31.256,34.681a30.434,30.434,0,0,1-1.636,12.009l17.269,10.634c8.156-9.746,23.344-11.728,36.052-4.034a34.209,34.209,0,0,1,5.438,4.079c10.1,9.284,13.483,23.712,7.347,34.8-4.488,8.106-13,12.486-22.23,12.477Q172.856,181.536,171.939,181.478Zm11.807-61.257a42.746,42.746,0,0,0-9.589-3.324c-.02-.787-.054-1.673-.111-2.636-1.647-27.653-18.775-53.9-44.7-68.511a33.457,33.457,0,0,0,1.744-9.255,102.352,102.352,0,0,1,23.779,17.431c17.2,16.885,27.411,38.53,28.746,60.938.1,1.712.149,3.511.145,5.347v.013ZM11.887,109.511c-.237-1.815-.407-3.677-.518-5.527-1.333-22.391,6.375-42.9,21.707-57.751A72.57,72.57,0,0,1,54.952,31.7a39.732,39.732,0,0,0,2.862,9.529C33.427,52.649,19.3,76.921,20.943,104.589c.064,1.071.141,1.969.233,2.82a34.668,34.668,0,0,0-9.283,2.1l0,.01Z" transform="translate(25.064 9.445)" fill="url(#linear-gradient-2)"/>
      </g>
    </g>
  </g>
</svg>
