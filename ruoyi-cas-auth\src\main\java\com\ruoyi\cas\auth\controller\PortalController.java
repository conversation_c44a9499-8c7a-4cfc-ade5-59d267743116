package com.ruoyi.cas.auth.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.cas.auth.domain.ApiR;
import com.ruoyi.cas.auth.model.ApiMember;
import com.ruoyi.cas.auth.service.PortalLoginService;

/**
 * token 控制
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
public class PortalController
{

    @Autowired
    private PortalLoginService portalLoginService;
    
    
    @GetMapping("/info")
    public ApiR<ApiMember> info(@RequestParam("appid") String appid,@RequestParam("ticket") String ticket)
    {
    	ApiR<ApiMember> apiR = portalLoginService.getLoginMember(appid,ticket);
        return apiR;
    }
    
}
