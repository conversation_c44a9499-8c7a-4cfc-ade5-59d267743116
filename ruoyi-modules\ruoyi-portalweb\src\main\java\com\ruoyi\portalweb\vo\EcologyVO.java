package com.ruoyi.portalweb.vo;

import com.ruoyi.portalweb.api.domain.Ecology;
import io.swagger.annotations.ApiModelProperty;

/**
 * 服务供给对象 supply
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public class EcologyVO extends Ecology {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "查询方式,我的供给queryType='my'")
    private String queryType;

    @ApiModelProperty(value = "生态类别名称")
    private String ecologyCategoryName;
    @ApiModelProperty(value = "合作领域名称")
    private String ecologyFieldName;
    @ApiModelProperty(value = "对接公司名称")
    private String memberCompanyName;

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    public String getEcologyCategoryName() {
        return ecologyCategoryName;
    }

    public void setEcologyCategoryName(String ecologyCategoryName) {
        this.ecologyCategoryName = ecologyCategoryName;
    }

    public String getEcologyFieldName() {
        return ecologyFieldName;
    }

    public void setEcologyFieldName(String ecologyFieldName) {
        this.ecologyFieldName = ecologyFieldName;
    }

    public String getMemberCompanyName() {
        return memberCompanyName;
    }

    public void setMemberCompanyName(String memberCompanyName) {
        this.memberCompanyName = memberCompanyName;
    }
}
