package com.ruoyi.im.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.im.api.domain.ImChatroom;
import com.ruoyi.im.api.domain.ImChatroomMsg;
import com.ruoyi.im.api.domain.ImChatroomUser;
import com.ruoyi.im.api.domain.ImUser;
import com.ruoyi.im.api.dto.ChatroomPojo;
import com.ruoyi.im.api.dto.ImChatroomMsgVO;
import com.ruoyi.im.service.ImChatroomMsgService;
import com.ruoyi.im.service.ImChatroomService;
import com.ruoyi.im.service.ImChatroomUserService;
import com.ruoyi.im.service.ImUserService;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/im/chatroom")
public class ImChatroomController {

    @Resource
    private ImChatroomService imChatroomService;

    @Resource
    private ImChatroomUserService imChatroomUserService;

    @Resource
    private ImUserService imUserService;

    @Resource
    private ImChatroomMsgService imChatroomMsgService;


    /***
     * ImChatroom分页条件搜索实现
     * @param
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/search/{page}/{size}" )
    public TableDataInfo findPage(@PathVariable("page")  int page, @PathVariable("size")  int size, @RequestParam(value = "fields",required = false) String fields){
        Page<ImChatroom> pageSearch = new Page<>(page,size);
        QueryWrapper<ImChatroom> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(fields)){
            wrapper.like("chatroomName",fields);
        }
        wrapper.orderByDesc("update_time","id");
        imChatroomService.page(pageSearch,wrapper);
        TableDataInfo tableDataInfo = new TableDataInfo(pageSearch.getRecords(), (int) pageSearch.getTotal());
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setMsg("success");
        return tableDataInfo;
    }

    /***
     * ImChatroom分页条件搜索实现
     * @param
     * @return
     */
    @PostMapping(value = "/list" )
    public R<List<ImChatroom>> findPage(@RequestBody(required = false)  ImChatroom imChatroom, @RequestParam(value = "fields",required = false) String fields){
        QueryWrapper<ImChatroom> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(fields)){
            wrapper.like("chatroomName",fields);
        }
        wrapper.orderByDesc("update_time","id");
        return R.ok(imChatroomService.list(wrapper));
    }

    /***
     * ImChatroom分页条件搜索实现
     * @param
     * @return
     */
    @PostMapping(value = "/ownlist" )
    public R<List<ChatroomPojo>> list(@RequestParam(value = "userId") String userId){
        List<ImChatroomUser> imChatroomUsers = imChatroomUserService.list(new QueryWrapper<ImChatroomUser>()
                .eq("userId",userId));
        if(imChatroomUsers!=null && imChatroomUsers.size()>0){
            List<ChatroomPojo> chatroomPojos = Lists.newArrayList();
            List<String> ids = imChatroomUsers.stream().map(ImChatroomUser::getChatroomId).collect(Collectors.toList());
            List<ImChatroom> imChatrooms = imChatroomService.list(new QueryWrapper<ImChatroom>().in("chatroomId",ids));
            List<ImChatroomMsg> imChatroomMsgs = imChatroomMsgService.findLatestMsg(ids);
            Map<String,ImChatroomMsg> map = Maps.newHashMap();
            if(imChatroomMsgs!=null && imChatroomMsgs.size()>0){
                imChatroomMsgs.forEach(m->{
                    map.put(m.getToUserId(),m);
                });
            }
            ChatroomPojo pojo = null;
            ImChatroomMsg msg = null;
            for(ImChatroom model:imChatrooms){
                msg = map.get(model.getChatroomId());
                pojo = new ChatroomPojo();
                pojo.setChatroomId(model.getChatroomId());
                pojo.setChatroomName(model.getChatroomName());
                pojo.setModule(model.getModule());
                pojo.setMsg(msg!=null? JSONObject.parseObject(msg.getMessage()):null);
                pojo.setMsgtype(msg!=null?msg.getObjectName():"");
                pojo.setCreate_time(msg!=null?getTime(msg.getMsgTimestamp()):"");
                chatroomPojos.add(pojo);
            }
            return R.ok(chatroomPojos);
        }
        return R.ok(null);
    }

    private String getTime(String timestamp){
        Date date = new Date(Long.valueOf(timestamp));
        String time = DateUtil.formatDateTime(date);
        return time.substring(5,16);
    }

    /***
     * ImChatroom分页条件搜索实现
     * @param
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/query/{page}/{size}" )
    public TableDataInfo queryPage(@RequestBody(required = false)  ImChatroom imChatroom,@PathVariable("page")  int page, @PathVariable("size")  int size, @RequestParam(value = "fields",required = false) String fields){
        Page<ImChatroom> pageSearch = new Page<>(page, size);
        pageSearch.setSearchCount(true);
        List<ImChatroom> list = imChatroomService.queryPage(pageSearch, imChatroom.getChatroomName());
        TableDataInfo tableDataInfo = new TableDataInfo(pageSearch.getRecords(), (int) pageSearch.getTotal());
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setMsg("success");
        return tableDataInfo;
    }

    /***
     * 多条件搜索数据
     * @param imChatroom
     * @return
     */
    @PostMapping(value = "/search" )
    public R<List<ImChatroom>> findList(@RequestBody(required = false)  ImChatroom imChatroom, @RequestParam(value = "fields",required = false) String fields){
        QueryWrapper<ImChatroom> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(imChatroom.getChatroomName())){
            wrapper.like("chatroomName",imChatroom.getChatroomName());
        }
        if(StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("update_time","id");
        return R.ok(imChatroomService.list(wrapper)) ;
    }


    /***
     * 新增ImChatroom数据 聊天室加入
     * @param imChatroom
     * @return
     */
    /***
     * 新增ImChatroom数据 聊天室加入
     * @param imChatroom
     * @return
     */
    @PostMapping(value="/signin")
    public R<String> add(@RequestBody ImChatroom imChatroom,@RequestParam("userId") String userId){
        //设置聊天室的id代码规则没有
        if (ObjectUtils.isNotEmpty(imChatroom)) {
            imChatroom.setUpdate_time(new Date());
            //根据聊天室id去查询是否已经存在
            ImChatroom imChatroomvo = imChatroomService.getOne(new QueryWrapper<ImChatroom>().eq("chatroomId", imChatroom.getChatroomId()));
            //不存在的情况下去创建聊天室
            if (ObjectUtils.isEmpty(imChatroomvo) && !imChatroomService.save(imChatroom)){
                return R.fail(400,"创建失败") ;
            }
            //添加聊天室成员
            ImChatroomUser imChatroomUser = imChatroomUserService.getOne(new QueryWrapper<ImChatroomUser>().eq("chatroomId", imChatroom.getChatroomId()).eq("userId", userId));
            if (Objects.isNull(imChatroomUser)) {
                imChatroomUser = new ImChatroomUser();
                imChatroomUser.setChatroomId(imChatroom.getChatroomId());
                imChatroomUser.setUserId(userId);
                if (imChatroomUserService.save(imChatroomUser)) {
                    return R.ok(imChatroom.getChatroomId()) ;
                }
            }else {
                return R.ok(imChatroom.getChatroomId());
            }
        }
        return R.fail(400,"创建失败") ;
    }

    /***
     * 聊天室退出
     * @param
     * @return
     */
    @PostMapping(value="/signout")
    public R<Boolean> delete(@RequestBody ImChatroomUser imChatroomUser){
        if(imChatroomUserService.remove(new QueryWrapper<ImChatroomUser>().eq("chatroomId",imChatroomUser.getChatroomId()).eq("userId",imChatroomUser.getUserId()))){
            return R.ok(true) ;
        }
        return R.ok(false) ;
    }


    /***
     * 修改ImChatroom数据
     * @param imChatroom
     * @return
     */
    @PostMapping(value="/update")
    public R<Boolean> update(@RequestBody ImChatroom imChatroom){
        if(imChatroomService.update(imChatroom,new QueryWrapper<ImChatroom>().eq("chatroomId",imChatroom.getChatroomId()))){
            return R.ok(true) ;
        }
        return R.ok(false) ;
    }

    @PostMapping(value = "/getchatroomUserList" )
    public R<List<ImUser>> getchatroomUserList(@RequestBody ImChatroomUser imChatroomUser){
        List<ImChatroomUser> chatroomList = imChatroomUserService.list(new QueryWrapper<ImChatroomUser>().eq("chatroomId", imChatroomUser.getChatroomId()));
        ArrayList<String> list = new ArrayList<>();
        chatroomList.forEach(item->{
            list.add(item.getUserId());
        });
        if (list.size()!=0) {
            List<ImUser> userList = imUserService.list(new QueryWrapper<ImUser>().in("userId", list));
            return R.ok(userList);
        }
        return R.ok(null);
    }



    /**
     * @Description:
     * @Param:
     * @return:
     * @Author: 根据聊天室查询聊天室的消息内容
     * @Date:
     */
    @PostMapping(value = "/findList" )
    public R<List<ImChatroomMsgVO>> findList(@RequestBody ImChatroom imChatroom) {
        QueryWrapper<ImChatroomMsg> wrapper = new QueryWrapper<>();
        //封装返回的数据
        List<ImChatroomMsgVO> arrayList = new ArrayList<>();
        if (ObjectUtils.isEmpty(imChatroom)) {
            wrapper.eq("chatroomId", imChatroom.getChatroomId());
            wrapper.orderByDesc("id");
            List<ImChatroomMsg> list = imChatroomMsgService.list(wrapper);
            //遍历开始封装数据
            for (ImChatroomMsg imChatroomMsg : list) {
                ImUser imUser = imUserService.getOne(new QueryWrapper<ImUser>().eq("", imChatroomMsg.getFromUserId()));
                if (ObjectUtils.isNotEmpty(imUser)) {
                    ImChatroomMsgVO imChatroomMsgVO = new ImChatroomMsgVO();
                    imChatroomMsgVO.setName(imUser.getName());
                    imChatroomMsgVO.setPortraitUri(imUser.getPortraitUri());
                    imChatroomMsgVO.setImChatroomMsg(imChatroomMsg);
                    arrayList.add(imChatroomMsgVO);
                }
            }
            return R.ok(arrayList);
        }
        return R.ok(null);
    }
}
