<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.AppStoreOrderMapper">

    <resultMap id="AppStoreOrderResult" type="com.ruoyi.portalconsole.domain.vo.AppStoreOrderVO" >
        <result property="appStoreOrderId" column="app_store_order_id"/>
        <result property="appStoreOrderNo" column="app_store_order_no"/>
        <result property="saleCompanyId" column="sale_company_id"/>
        <result property="buyCompanyId" column="buy_company_id"/>
        <result property="buyCompanyName" column="buy_company_name"/>
        <result property="saleCompanyName" column="sale_company_name"/>
        <result property="orderTime" column="order_time"/>
        <result property="appStorePrice" column="app_store_price"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="appStoreId" column="app_store_id"/>
        <result property="saleMemberId" column="sale_member_id"/>
        <result property="buyMemberId" column="buy_member_id"/>
        <result property="appStoreName" column="app_store_name"/>
        <result property="orderStatus" column="order_status"/>
        <result property="erweima" column="erweima"/>
        <result property="downloadUrl" column="download_url"/>
        <result property="invoiceStatus" column="invoice_status"/>
        <result property="invoiceImageUrl" column="invoice_image_url"/>
        <result property="ip"    column="ip"    />
        <result property="appWebUrl"    column="app_web_url"    />
        <result property="appWebTrialUrl"    column="app_web_trial_url"    />
        <result property="healthInspectionUrl"    column="health_inspection_url"    />
        <result property="contact"    column="contact"    />
        <result property="testToken"    column="test_token"    />

    </resultMap>

    <sql id="selectAppStoreOrderVo">
        select app_store_order_id, app_store_order_no, sale_company_id, buy_company_id, order_time, app_store_price, app_store_order.create_by, app_store_order.create_time, app_store_order.app_store_name, app_store_order.order_status, app_store_order.erweima, app_store_order.download_url,
               app_store_order.update_by, app_store_order.update_time, app_store_order.remark ,app_store_id,sale_member_id,buy_member_id, c1.company_name as sale_company_name, c2.company_name as buy_company_name, app_store_order.invoice_status, app_store_order.invoice_image_url,
               app_store_order.ip, app_store_order.app_web_url, app_store_order.app_web_trial_url, app_store_order.health_inspection_url, app_store_order.contact, app_store_order.test_token
        from app_store_order left join company c1 on app_store_order.sale_company_id = c1.company_id left join company c2 on app_store_order.buy_company_id = c2.company_id
    </sql>

    <select id="selectAppStoreOrderList" parameterType="AppStoreOrder" resultMap="AppStoreOrderResult">
        <include refid="selectAppStoreOrderVo"/>
        <where>
            <if test="appStoreOrderNo != null  and appStoreOrderNo != ''">and app_store_order_no = #{appStoreOrderNo}
            </if>
            <if test="saleCompanyId != null ">and sale_company_id = #{saleCompanyId}</if>
            <if test="buyCompanyId != null ">and buy_company_id = #{buyCompanyId}</if>
            <if test="orderTime != null ">and order_time = #{orderTime}</if>
            <if test="appStorePrice != null ">and app_store_price = #{appStorePrice}</if>
            <if test="appStoreId != null ">and app_store_id = #{appStoreId}</if>
            <if test="saleMemberId != null ">and sale_member_id = #{saleMemberId}</if>
            <if test="buyMemberId != null ">and buy_member_id = #{buyMemberId}</if>
            <if test="orderStatus != null ">and order_status = #{orderStatus}</if>
            <if test="invoiceStatus != null ">and invoice_status = #{invoiceStatus}</if>
            <if test="appStoreName != null ">and app_store_name like concat('%',#{appStoreName},'%') </if>
        </where>
    </select>

    <select id="selectAppStoreOrderByAppStoreOrderId" parameterType="Long" resultMap="AppStoreOrderResult">
        <include refid="selectAppStoreOrderVo"/>
        where app_store_order_id = #{appStoreOrderId}
    </select>

    <insert id="insertAppStoreOrder" parameterType="AppStoreOrder" useGeneratedKeys="true"
            keyProperty="appStoreOrderId">
        insert into app_store_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appStoreOrderNo != null">app_store_order_no,</if>
            <if test="saleCompanyId != null">sale_company_id,</if>
            <if test="buyCompanyId != null">buy_company_id,</if>
            <if test="orderTime != null">order_time,</if>
            <if test="appStorePrice != null">app_store_price,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="appStoreId != null">app_store_id,</if>
            <if test="saleMemberId != null">sale_member_id,</if>
            <if test="buyMemberId != null">buy_member_id,</if>
            <if test="appStoreName != null">app_store_name,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="erweima != null">erweima,</if>
            <if test="downloadUrl != null">download_url,</if>
            <if test="invoiceStatus != null">invoice_status,</if>
            <if test="invoiceImageUrl != null">invoice_image_url,</if>
            <if test="ip != null">ip,</if>
            <if test="appWebUrl != null">app_web_url,</if>
            <if test="appWebTrialUrl != null">app_web_trial_url,</if>
            <if test="healthInspectionUrl != null">health_inspection_url,</if>
            <if test="contact != null">contact,</if>
            <if test="testToken != null">test_token,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appStoreOrderNo != null">#{appStoreOrderNo},</if>
            <if test="saleCompanyId != null">#{saleCompanyId},</if>
            <if test="buyCompanyId != null">#{buyCompanyId},</if>
            <if test="orderTime != null">#{orderTime},</if>
            <if test="appStorePrice != null">#{appStorePrice},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="appStoreId != null">#{appStoreId},</if>
            <if test="saleMemberId != null">#{saleMemberId},</if>
            <if test="buyMemberId != null">#{buyMemberId},</if>
            <if test="appStoreName != null">#{appStoreName},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="erweima != null">#{erweima},</if>
            <if test="downloadUrl != null">#{downloadUrl},</if>
            <if test="invoiceStatus != null">#{invoiceStatus},</if>
            <if test="invoiceImageUrl != null">#{invoiceImageUrl},</if>
            <if test="ip != null">#{ip},</if>
            <if test="appWebUrl != null">#{appWebUrl},</if>
            <if test="appWebTrialUrl != null">#{appWebTrialUrl},</if>
            <if test="healthInspectionUrl != null">#{healthInspectionUrl},</if>
            <if test="contact != null">#{contact},</if>
            <if test="testToken != null">#{testToken},</if>
        </trim>
    </insert>

    <update id="updateAppStoreOrder" parameterType="AppStoreOrder">
        update app_store_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="appStoreOrderNo != null">app_store_order_no = #{appStoreOrderNo},</if>
            <if test="saleCompanyId != null">sale_company_id = #{saleCompanyId},</if>
            <if test="buyCompanyId != null">buy_company_id = #{buyCompanyId},</if>
            <if test="orderTime != null">order_time = #{orderTime},</if>
            <if test="appStorePrice != null">app_store_price = #{appStorePrice},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="appStoreId != null">app_store_id = #{appStoreId},</if>
            <if test="saleMemberId != null">sale_member_id = #{saleMemberId},</if>
            <if test="buyMemberId != null">buy_member_id = #{buyMemberId},</if>
            <if test="appStoreName != null">app_store_name = #{appStoreName},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="erweima != null">erweima = #{erweima},</if>
            <if test="downloadUrl != null">download_url = #{downloadUrl},</if>
            <if test="invoiceStatus != null">invoice_status = #{invoiceStatus},</if>
            <if test="invoiceImageUrl != null">invoice_image_url = #{invoiceImageUrl},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="appWebUrl != null">app_web_url = #{appWebUrl},</if>
            <if test="appWebTrialUrl != null">app_web_trial_url = #{appWebTrialUrl},</if>
            <if test="healthInspectionUrl != null">health_inspection_url = #{healthInspectionUrl},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="testToken != null">test_token = #{testToken},</if>
        </trim>
        where app_store_order_id = #{appStoreOrderId}
    </update>

    <delete id="deleteAppStoreOrderByAppStoreOrderId" parameterType="Long">
        delete from app_store_order where app_store_order_id = #{appStoreOrderId}
    </delete>

    <delete id="deleteAppStoreOrderByAppStoreOrderIds" parameterType="String">
        delete from app_store_order where app_store_order_id in
        <foreach item="appStoreOrderId" collection="array" open="(" separator="," close=")">
            #{appStoreOrderId}
        </foreach>
    </delete>
</mapper>