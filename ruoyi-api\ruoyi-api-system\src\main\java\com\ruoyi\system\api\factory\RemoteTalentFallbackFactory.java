package com.ruoyi.system.api.factory;

import com.ruoyi.system.api.domain.TalentInfoApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.RemoteTalentService;

/**
 * 人才服务降级处理
 */
@Component
public class RemoteTalentFallbackFactory implements FallbackFactory<RemoteTalentService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteTalentFallbackFactory.class);

    @Override
    public RemoteTalentService create(Throwable throwable)
    {
        log.error("人才服务调用失败:{}", throwable.getMessage());
        return new RemoteTalentService()
        {
            @Override
            public R<TalentInfoApi> getTalentByUserId(Long userId)
            {
                return R.fail("获取人才信息失败:" + throwable.getMessage());
            }
        };
    }
}
