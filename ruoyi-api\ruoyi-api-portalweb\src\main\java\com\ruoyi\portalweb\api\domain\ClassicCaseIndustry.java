package com.ruoyi.portalweb.api.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 典型案例行业对象 classic_case_industry
 * 
 * <AUTHOR>
 * @date 2024-06-07
 */
public class ClassicCaseIndustry extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 案例行业ID */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classicCaseIndustryId;

    /** 上级id */
    @Excel(name = "上级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /** 名称 */
    @Excel(name = "名称")
    private String classicCaseIndustryName;

    /** 服务范围 */
    @Excel(name = "服务范围")
    private String category;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setClassicCaseIndustryId(Long classicCaseIndustryId) 
    {
        this.classicCaseIndustryId = classicCaseIndustryId;
    }

    public Long getClassicCaseIndustryId() 
    {
        return classicCaseIndustryId;
    }
    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }
    public void setClassicCaseIndustryName(String classicCaseIndustryName) 
    {
        this.classicCaseIndustryName = classicCaseIndustryName;
    }

    public String getClassicCaseIndustryName() 
    {
        return classicCaseIndustryName;
    }
    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("classicCaseIndustryId", getClassicCaseIndustryId())
            .append("parentId", getParentId())
            .append("classicCaseIndustryName", getClassicCaseIndustryName())
            .append("category", getCategory())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
