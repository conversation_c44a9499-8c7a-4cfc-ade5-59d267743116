package com.ruoyi.system.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

public class WeiXinUtils {
    private static final Logger log = LoggerFactory.getLogger(WeiXinUtils.class);


//    private static final String access_token_url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=AppID&secret=AppSecret&code=00294221aeb06261d5966&grant_type=authorization_code";

    /**
     * 微信开放平台
     */
    private static final String APPID = "wx529aefcb6bd6d7b7";
    private static final String SECRET = "46ef8acfe657a84a23c670940b9132c3";

    /**
     * 玺品公众号（半岛科创）微信服务号
     */
    private static final String MP_APPID = "wx4c5bee17900a4b2d";
    private static final String MP_SECRET = "3ca9113ddb5cdfd4199db30c1ff06569";

    public static String getOpenid(String code) {

        String url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + APPID + "&secret=" + SECRET + "&code=" + code + "&grant_type=authorization_code";

        try {
            String result = HttpUtil.get(url);
            System.out.println(result);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    public static <T> T doGet(String url, Class<T> clazz) {

        T t = null;

        try {
            String result = HttpUtil.get(url);
            System.out.println(result);
            t = JSONObject.parseObject(result, clazz);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return t;
    }


    /**
     * 获取AccessToken
     *
     * @return
     */
    public static String getAccessToken() {
        String access_token = "";
        String grant_type = "client_credential";//获取access_token填写client_credential
        //这个url链接地址和参数皆不能变
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=" + grant_type + "&appid=" + MP_APPID + "&secret=" + MP_SECRET;  //访问链接

        try {
            URL urlGet = new URL(url);
            HttpURLConnection http = (HttpURLConnection) urlGet.openConnection();
            http.setRequestMethod("GET"); // 必须是get方式请求
            http.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            http.setDoOutput(true);
            http.setDoInput(true);
            System.setProperty("sun.net.client.defaultConnectTimeout", "30000");// 连接超时30秒
            System.setProperty("sun.net.client.defaultReadTimeout", "30000"); // 读取超时30秒
            http.connect();
            InputStream is = http.getInputStream();
            int size = is.available();
            byte[] jsonBytes = new byte[size];
            is.read(jsonBytes);
            String message = new String(jsonBytes, "UTF-8");
            JSONObject jsonObject = JSONObject.parseObject(message);
            access_token = jsonObject.getString("access_token");
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return access_token;
    }

    /**
     * 获取ticket
     *
     * @param access_token
     * @return
     */
    public static String getTicket(String access_token) {
        String ticket = null;
        String url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=" + access_token + "&type=jsapi";//这个url链接和参数不能变
        try {
            URL urlGet = new URL(url);
            HttpURLConnection http = (HttpURLConnection) urlGet.openConnection();
            http.setRequestMethod("GET"); // 必须是get方式请求
            http.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            http.setDoOutput(true);
            http.setDoInput(true);
            System.setProperty("sun.net.client.defaultConnectTimeout", "30000");// 连接超时30秒
            System.setProperty("sun.net.client.defaultReadTimeout", "30000"); // 读取超时30秒
            http.connect();
            InputStream is = http.getInputStream();
            int size = is.available();
            byte[] jsonBytes = new byte[size];
            is.read(jsonBytes);
            String message = new String(jsonBytes, "UTF-8");
            JSONObject result = JSONObject.parseObject(message);
            ticket = result.getString("ticket");
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ticket;
    }


    /**
     * 生成随机串
     *
     * @return
     */
    public static String genNonceStr() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成时间戳
     *
     * @return
     */
    public static String genTimestamp() {
        return Long.toString(System.currentTimeMillis() / 1000);
    }


    /**
     * 生成签名
     *
     * @param decript
     * @return
     */
    public static String genSing(String decript) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            digest.update(decript.getBytes());
            byte messageDigest[] = digest.digest();
            // Create Hex String
            StringBuffer hexString = new StringBuffer();
            // 字节数组转换为 十六进制 数
            for (int i = 0; i < messageDigest.length; i++) {
                String shaHex = Integer.toHexString(messageDigest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexString.append(0);
                }
                hexString.append(shaHex);
            }
            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 获取用户的openId
     * @param code
     * @param appId
     * @param appSecret
     * @return
     */
    public static JSONObject getOpenid(String code,String appId,String appSecret) {

        String url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + appId + "&secret=" + appSecret + "&code=" + code + "&grant_type=authorization_code";
        try {
            String result = HttpUtil.get(url);
            if(ObjectUtil.isNotEmpty(result)){
                return JSON.parseObject(result);
            }
        } catch (Exception e) {
            log.error("获取用户微信公众号openid失败{}",e);
        }
        return null;
    }
}
