package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.FileShare;
import com.ruoyi.system.service.IFileShareService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 文件共享Controller
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@RestController
@RequestMapping("/fileShare")
public class FileShareController extends BaseController
{
    @Autowired
    private IFileShareService fileShareService;

    /**
     * 查询文件共享列表
     */

    @GetMapping("/list")
    public TableDataInfo list(FileShare fileShare)
    {
        startPage();
        List<FileShare> list = fileShareService.selectFileShareList(fileShare);
        return getDataTable(list);
    }

    /**
     * 导出文件共享列表
     */

    @Log(title = "文件共享", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FileShare fileShare)
    {
        List<FileShare> list = fileShareService.selectFileShareList(fileShare);
        ExcelUtil<FileShare> util = new ExcelUtil<FileShare>(FileShare.class);
        util.exportExcel(response, list, "文件共享数据");
    }

    /**
     * 获取文件共享详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(fileShareService.selectFileShareById(id));
    }

    /**
     * 新增文件共享
     */

    @Log(title = "文件共享", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FileShare fileShare)
    {
        return toAjax(fileShareService.insertFileShare(fileShare));
    }

    /**
     * 修改文件共享
     */

    @Log(title = "文件共享", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FileShare fileShare)
    {
        return toAjax(fileShareService.updateFileShare(fileShare));
    }

    /**
     * 删除文件共享
     */

    @Log(title = "文件共享", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(fileShareService.deleteFileShareByIds(ids));
    }
}
