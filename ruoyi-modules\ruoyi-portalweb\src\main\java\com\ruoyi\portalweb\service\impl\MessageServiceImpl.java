package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.Message;
import com.ruoyi.portalweb.mapper.MessageMapper;
import com.ruoyi.portalweb.service.IMessageService;
import com.ruoyi.portalweb.vo.MessageVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 站内消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class MessageServiceImpl implements IMessageService
{
    @Autowired
    private MessageMapper messageMapper;

    /**
     * 查询站内消息
     * 
     * @param messageId 站内消息主键
     * @return 站内消息
     */
    @Override
    public Message selectMessageByMessageId(Long messageId)
    {
        Message message=messageMapper.selectMessageByMessageId(messageId);
        //是本人打开详情,设为已读
        if(SecurityUtils.getUserId().longValue()==message.getMemberId().longValue()){
            Message setData=new Message();
            setData.setMessageId(message.getMessageId());
            setData.setMessageStatus("1");
            messageMapper.updateMessage(setData);
        }
        return message;
    }

    /**
     * 查询站内消息列表
     * 
     * @param message 站内消息
     * @return 站内消息
     */
    @Override
    public List<Message> selectMessageList(MessageVO message)
    {
        if (StringUtils.isNotEmpty(message.getQueryType()) && "my".equals(message.getQueryType())) {
            message.setMemberId(SecurityUtils.getUserId());
        }
        return messageMapper.selectMessageList(message);
    }

    /**
     * 新增站内消息
     * 
     * @param message 站内消息
     * @return 结果
     */
    @Override
    public int insertMessage(Message message)
    {
        message.setCreateTime(DateUtils.getNowDate());
        return messageMapper.insertMessage(message);
    }

    /**
     * 修改站内消息
     * 
     * @param message 站内消息
     * @return 结果
     */
    @Override
    public int updateMessage(Message message)
    {
        message.setUpdateTime(DateUtils.getNowDate());
        return messageMapper.updateMessage(message);
    }

    /**
     * 批量删除站内消息
     * 
     * @param messageIds 需要删除的站内消息主键
     * @return 结果
     */
    @Override
    public int deleteMessageByMessageIds(Long[] messageIds)
    {
        return messageMapper.deleteMessageByMessageIds(messageIds);
    }

    /**
     * 删除站内消息信息
     * 
     * @param messageId 站内消息主键
     * @return 结果
     */
    @Override
    public int deleteMessageByMessageId(Long messageId)
    {
        return messageMapper.deleteMessageByMessageId(messageId);
    }
}
