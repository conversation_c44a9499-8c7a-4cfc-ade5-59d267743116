package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.EcologyCategory;
import com.ruoyi.portalweb.service.IEcologyCategoryService;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 生态类别Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/EcologyCategory")
public class EcologyCategoryController extends BaseController
{
    @Autowired
    private IEcologyCategoryService ecologyCategoryService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询生态类别列表
     */
    @GetMapping("/list")
    public TableDataInfo list(EcologyCategory ecologyCategory)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<EcologyCategory> list = ecologyCategoryService.selectEcologyCategoryList(ecologyCategory);
        return getDataTable(list);
    }

    /**
     * 查询生态类别列表
     */
    @GetMapping("/listDesk")
    public TableDataInfo listDesk(EcologyCategory ecologyCategory)
    {
        startPage();
        PageUtils.setOrderBy("create_time DESC");
        List<EcologyCategory> list = ecologyCategoryService.selectEcologyCategoryList(ecologyCategory);
        return getDataTable(list);
    }

    /**
     * 导出生态类别列表
     */
    @Log(title = "生态类别", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EcologyCategory ecologyCategory)
    {
        List<EcologyCategory> list = ecologyCategoryService.selectEcologyCategoryList(ecologyCategory);
        ExcelUtil<EcologyCategory> util = new ExcelUtil<EcologyCategory>(EcologyCategory.class);
        util.exportExcel(response, list, "生态类别数据");
    }

    /**
     * 获取生态类别详细信息
     */
    @GetMapping(value = "/{ecologyCategoryId}")
    public AjaxResult getInfo(@PathVariable("ecologyCategoryId") Long ecologyCategoryId)
    {
        return success(ecologyCategoryService.selectEcologyCategoryByEcologyCategoryId(ecologyCategoryId));
    }

    /**
     * 新增生态类别
     */
    @Log(title = "生态类别", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EcologyCategory ecologyCategory)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        ecologyCategory.setUpdateBy(userNickName.getData());
        ecologyCategory.setCreateBy(userNickName.getData());
        return toAjax(ecologyCategoryService.insertEcologyCategory(ecologyCategory));
    }

    /**
     * 修改生态类别
     */
    @Log(title = "生态类别", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EcologyCategory ecologyCategory)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        ecologyCategory.setUpdateBy(userNickName.getData());
        return toAjax(ecologyCategoryService.updateEcologyCategory(ecologyCategory));
    }

    /**
     * 删除生态类别
     */
    @Log(title = "生态类别", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ecologyCategoryIds}")
    public AjaxResult remove(@PathVariable Long[] ecologyCategoryIds)
    {
        return toAjax(ecologyCategoryService.deleteEcologyCategoryByEcologyCategoryIds(ecologyCategoryIds));
    }
}
