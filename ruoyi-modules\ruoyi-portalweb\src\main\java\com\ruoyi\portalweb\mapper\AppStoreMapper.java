package com.ruoyi.portalweb.mapper;


import com.ruoyi.portalweb.api.domain.AppStore;
import com.ruoyi.portalweb.vo.AppStoreVO;

import java.util.List;

/**
 * 应用商店Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface AppStoreMapper 
{
    /**
     * 查询应用商店
     * 
     * @param appStoreId 应用商店主键
     * @return 应用商店
     */
    public AppStoreVO selectAppStoreByAppStoreId(Long appStoreId);

    /**
     * 查询应用商店列表
     * 
     * @param appStore 应用商店
     * @return 应用商店集合
     */
    public List<AppStoreVO> selectAppStoreList(AppStore appStore);

    /**
     * 新增应用商店
     * 
     * @param appStore 应用商店
     * @return 结果
     */
    public int insertAppStore(AppStore appStore);

    /**
     * 修改应用商店
     * 
     * @param appStore 应用商店
     * @return 结果
     */
    public int updateAppStore(AppStore appStore);

    /**
     * 删除应用商店
     * 
     * @param appStoreId 应用商店主键
     * @return 结果
     */
    public int deleteAppStoreByAppStoreId(Long appStoreId);

    /**
     * 批量删除应用商店
     * 
     * @param appStoreIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppStoreByAppStoreIds(Long[] appStoreIds);
}
