package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.UserChatGroup;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface UserChatGroupMapper {

    int insertSelective(UserChatGroup record);

    int updateByPrimaryKeySelective(UserChatGroup record);

    int dismissChatGroup(@Param("groupId") Long groupId, @Param("userName") String userName, @Param("now") Date now);


    UserChatGroup selectById(@Param("groupId") Long groupId);
}