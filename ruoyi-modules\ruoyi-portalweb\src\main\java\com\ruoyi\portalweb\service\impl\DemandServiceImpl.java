package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.Demand;
import com.ruoyi.portalweb.api.domain.FileDetail;
import com.ruoyi.portalweb.api.domain.Member;
import com.ruoyi.portalweb.api.enums.AuditStatus;
import com.ruoyi.portalweb.api.enums.MyFavoriteStatus;
import com.ruoyi.portalweb.api.enums.MyFavoriteType;
import com.ruoyi.portalweb.mapper.DemandMapper;
import com.ruoyi.portalweb.mapper.MemberMapper;
import com.ruoyi.portalweb.mapper.MyFavoriteMapper;
import com.ruoyi.portalweb.service.IDemandService;
import com.ruoyi.portalweb.service.IFileDetailService;
import com.ruoyi.portalweb.vo.DemandVO;
import com.ruoyi.portalweb.vo.FileDetailVO;
import com.ruoyi.portalweb.vo.MemberVO;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.ruoyi.common.core.utils.PageUtils.startPage;

/**
 * 服务需求(NEW)Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class DemandServiceImpl implements IDemandService {
    @Autowired
    private DemandMapper demandMapper;
    @Autowired
    private MemberMapper memberMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private IFileDetailService fileDetailService;

    @Autowired
    private MyFavoriteMapper myFavoriteMapper;

    public static final String AUDIT_STATUS_PASS = "2";
    public static final String AUDIT_STATUS_TERMINAL = "4";
    public static final int ON_SHOW_Y = 0;


    /**
     * 查询服务需求(NEW)
     *
     * @param id 服务需求(NEW)主键
     * @return 服务需求(NEW)
     */
    @Override
    public DemandVO selectDemandById(Long id) {
        DemandVO demandVO = demandMapper.selectDemandById(id);
        if (demandVO != null) {
            List<FileDetailVO> alPictureVOs = fileDetailService.selectPictureList(demandVO.getId(),"demand", "");
            demandVO.setAlFileDetailVOs(alPictureVOs);
        }
        return demandVO;
    }

    @Override
    public DemandVO detailDesk(Long id) {
        // 更新阅读次数
        demandMapper.addDemandViewCount(id);
       return this.selectDemandById(id);
    }

    /**
     * 查询服务需求(NEW)列表
     *
     * @param demand 服务需求(NEW)
     * @return 服务需求(NEW)
     */
    @Override
    public List<DemandVO> selectDemandList(DemandVO demand) {
        if (StringUtils.isNotEmpty(demand.getQueryType()) && "my".equals(demand.getQueryType())) {
            demand.setMemberId(SecurityUtils.getUserId());
        }else {
            demand.setOnShow(ON_SHOW_Y);
            demand.setAuditStatus(AUDIT_STATUS_PASS);
        }
        List<DemandVO> list = demandMapper.selectDemandList(demand);
        for (DemandVO item : list) {
            List<FileDetailVO> alPictureVOs = fileDetailService.selectPictureList(item.getId(),"demand", "");
            item.setAlFileDetailVOs(alPictureVOs);
        }
        return list;
    }

    @Override
    public List<DemandVO> selectDemandListByDemandIds(List<Long> demandIds) {
        return demandMapper.selectDemandListByDemandIds(demandIds);
    }

    @Override
    public List<DemandVO> listDeskCompanyRelated(Long companyRelatedId) {
        Member m = new Member();
        m.setCompanyRelatedId(companyRelatedId);
        List<MemberVO> memberVOS = memberMapper.selectMemberList(m);
        List<Long> memberIds = new ArrayList<>();
        for (MemberVO memberVO : memberVOS) {
            memberIds.add(memberVO.getMemberId());
        }

        if(memberIds.isEmpty()){
            return new ArrayList<>();
        }
        startPage();
        PageUtils.setOrderBy("a.create_time DESC");
        List<DemandVO> list = demandMapper.selectDemandListByMemberIds(memberIds);
        if (list.isEmpty()){
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 新增服务需求(NEW)
     *
     * @param demand 服务需求(NEW)
     * @return 结果
     */
    @Override
    public int insertDemand(DemandVO demand) {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        demand.setUpdateBy(userNickName.getData());
        demand.setCreateBy(userNickName.getData());
        demand.setMemberId(SecurityUtils.getUserId());

        Integer res = demandMapper.insertDemand(demand);
        if (res == 1) {
            // 删除原先附件,重保存
            fileDetailService.removeBybillId(demand.getId(),"demand", "");
            // 保存图片
            if (demand.getAlFileDetailVOs() != null && demand.getAlFileDetailVOs().size() > 0) {
                for (FileDetailVO tmpPic : demand.getAlFileDetailVOs()) {

                    FileDetail saveFile = tmpPic;
                    tmpPic.setId(null);
                    tmpPic.setParentType("demand");
                    tmpPic.setParentId(demand.getId());
                    fileDetailService.insertFileDetail(saveFile);
                }
            }
        }
        return res;
    }

    /**
     * 修改服务需求(NEW)
     *
     * @param demand 服务需求(NEW)
     * @return 结果
     */
    @Override
    public int updateDemand(DemandVO demand) {
        // 获取用户信息存入修改需求数据
        DemandVO demandVO = demandMapper.selectDemandById(demand.getId());
        if (!AuditStatus.TERMINAL.getValue().equals(demandVO.getAuditStatus())&& !AuditStatus.TERMINAL.getValue().equals(demand.getAuditStatus())){
            demand.setAuditStatus(AuditStatus.PENDING.getValue());
        }
        // 获取用户信息存入数据
        MemberVO memberVO = memberMapper.selectMemberByMemberId(SecurityUtils.getUserId());
        demand.setUpdateBy(memberVO.getMemberRealName());
        return demandMapper.updateDemand(demand);
    }

    /**
     * 批量删除服务需求(NEW)
     *
     * @param ids 需要删除的服务需求(NEW)主键
     * @return 结果
     */
    @Override
    public int deleteDemandByIds(Long[] ids) {
        int i = demandMapper.deleteDemandByIds(ids);
        for (Long id : ids) {
            try {
              myFavoriteMapper.updateMyFavoriteStatus(id,MyFavoriteType.DEMAND.getType(), MyFavoriteStatus.INVALID.getValue());
            }catch(Exception e){
                e.printStackTrace();
            }
        }
        return i;
    }

    /**
     * 删除服务需求(NEW)信息
     *
     * @param id 服务需求(NEW)主键
     * @return 结果
     */
    @Override
    public int deleteDemandById(Long id) {
        int i = demandMapper.deleteDemandById(id);
        try {
            myFavoriteMapper.updateMyFavoriteStatus(id,MyFavoriteType.DEMAND.getType(), MyFavoriteStatus.INVALID.getValue());
        }catch(Exception e){
            e.printStackTrace();
        }
        return i;
    }
}
