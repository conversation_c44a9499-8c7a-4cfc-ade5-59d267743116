package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.TestingConsult;

/**
 * 检测咨询Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface TestingConsultMapper 
{
    /**
     * 查询检测咨询
     * 
     * @param id 检测咨询主键
     * @return 检测咨询
     */
    public TestingConsult selectTestingConsultById(Long id);

    /**
     * 查询检测咨询列表
     * 
     * @param testingConsult 检测咨询
     * @return 检测咨询集合
     */
    public List<TestingConsult> selectTestingConsultList(TestingConsult testingConsult);

    /**
     * 新增检测咨询
     * 
     * @param testingConsult 检测咨询
     * @return 结果
     */
    public int insertTestingConsult(TestingConsult testingConsult);

    /**
     * 修改检测咨询
     * 
     * @param testingConsult 检测咨询
     * @return 结果
     */
    public int updateTestingConsult(TestingConsult testingConsult);

    /**
     * 删除检测咨询
     * 
     * @param id 检测咨询主键
     * @return 结果
     */
    public int deleteTestingConsultById(Long id);

    /**
     * 批量删除检测咨询
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTestingConsultByIds(Long[] ids);
}
