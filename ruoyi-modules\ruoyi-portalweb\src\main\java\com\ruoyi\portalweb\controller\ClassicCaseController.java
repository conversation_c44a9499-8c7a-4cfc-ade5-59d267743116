package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.AppStoreOrder;
import com.ruoyi.portalweb.api.domain.ClassicCase;
import com.ruoyi.portalweb.service.IClassicCaseService;
import com.ruoyi.portalweb.vo.ClassicCaseVO;
import com.ruoyi.system.api.RemoteUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 典型案例Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/ClassicCase")
@Api(value = "2.典型案例", tags = "2.典型案例")
public class ClassicCaseController extends BaseController
{
    @Autowired
    private IClassicCaseService classicCaseService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询典型案例列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询典型案例列表", notes = "传入")
    public TableDataInfo list(ClassicCaseVO classicCase)
    {
        startPage();
        List<ClassicCaseVO> list = classicCaseService.selectClassicCaseList(classicCase);
        return getDataTable(list);
    }

    /**
     * 查询典型案例列表
     */
    @GetMapping("/listDesk")
    @ApiOperation(value = "查询典型案例列表", notes = "传入")
    public TableDataInfo listDesk(ClassicCaseVO classicCase)
    {
        startPage();
        PageUtils.setOrderBy("create_time DESC");
        List<ClassicCaseVO> list = classicCaseService.selectClassicCaseList(classicCase);
        return getDataTable(list);
    }

    /**
     * 导出典型案例列表
     */
    @Log(title = "典型案例", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出典型案例列表", notes = "传入")
    public void export(HttpServletResponse response, ClassicCaseVO classicCase)
    {
        List<ClassicCaseVO> list = classicCaseService.selectClassicCaseList(classicCase);
        ExcelUtil<ClassicCaseVO> util = new ExcelUtil<>(ClassicCaseVO.class);
        util.exportExcel(response, list, "典型案例数据");
    }

//    /**
//     * 获取典型案例详细信息
//     */
//    @GetMapping(value = "/{classicCaseId}")
//    @ApiOperation(value = "获取典型案例详细信息", notes = "传入")
//    public AjaxResult getInfo(@ApiParam(value = "主键", required = true) @PathVariable("classicCaseId") Long classicCaseId)
//    {
//        return success(classicCaseService.selectClassicCaseByClassicCaseId(classicCaseId));
//    }
    
    /**
     * 获取典型案例详细信息
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取服务需求(NEW)详细信息", notes = "传入")
    public AjaxResult detail(ClassicCase classicCase) {
    	return success(classicCaseService.selectClassicCaseByClassicCaseId(classicCase.getClassicCaseId()));
    }
    
    /**
     * 获取典型案例详细信息
     */
    @GetMapping("/detailDesk")
    @ApiOperation(value = "获取服务需求(NEW)详细信息", notes = "传入")
    public AjaxResult detailDesk(ClassicCase classicCase) {
    	return success(classicCaseService.selectClassicCaseByClassicCaseId(classicCase.getClassicCaseId()));
    }

    /**
     * 新增典型案例
     */
    @Log(title = "典型案例", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增典型案例", notes = "传入")
    public AjaxResult add(@RequestBody ClassicCase classicCase)
    {
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        classicCase.setUpdateBy(userNickName.getData());
        classicCase.setCreateBy(userNickName.getData());
        return toAjax(classicCaseService.insertClassicCase(classicCase));
    }

    /**
     * 修改典型案例
     */
    @Log(title = "典型案例", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改典型案例", notes = "传入")
    public AjaxResult edit(@RequestBody ClassicCase classicCase)
    {
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        classicCase.setUpdateBy(userNickName.getData());
        return toAjax(classicCaseService.updateClassicCase(classicCase));
    }

    /**
     * 删除典型案例
     */
    @Log(title = "典型案例", businessType = BusinessType.DELETE)
	@DeleteMapping("/{classicCaseIds}")
    @ApiOperation(value = "删除典型案例", notes = "传入")
    public AjaxResult remove(@PathVariable Long[] classicCaseIds)
    {
        return toAjax(classicCaseService.deleteClassicCaseByClassicCaseIds(classicCaseIds));
    }
}
