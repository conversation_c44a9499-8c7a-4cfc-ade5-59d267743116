package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.FileDetail;
import com.ruoyi.portalweb.service.IFileDetailService;
import com.ruoyi.portalweb.vo.FileDetailVO;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 附件子表Controller
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/fileDetail")
public class FileDetailController extends BaseController
{
    @Autowired
    private IFileDetailService fileDetailService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询附件子表列表
     */
    @GetMapping("/list")
    public TableDataInfo list(FileDetailVO fileDetail)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<FileDetailVO> list = fileDetailService.selectFileDetailList(fileDetail);
        return getDataTable(list);
    }

    /**
     * 查询附件子表列表
     */
    @GetMapping("/listDesk")
    public TableDataInfo listDesk(FileDetailVO fileDetail)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<FileDetailVO> list = fileDetailService.selectFileDetailList(fileDetail);
        return getDataTable(list);
    }

    /**
     * 导出附件子表列表
     */
    @Log(title = "附件子表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FileDetailVO fileDetail)
    {
        List<FileDetailVO> list = fileDetailService.selectFileDetailList(fileDetail);
        ExcelUtil<FileDetailVO> util = new ExcelUtil<FileDetailVO>(FileDetailVO.class);
        util.exportExcel(response, list, "附件子表数据");
    }

    /**
     * 获取附件子表详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(fileDetailService.selectFileDetailById(id));
    }

    /**
     * 新增附件子表
     */
    @Log(title = "附件子表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FileDetail fileDetail)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        fileDetail.setCreateBy(userNickName.getData());
        fileDetail.setUpdateBy(userNickName.getData());
        return toAjax(fileDetailService.insertFileDetail(fileDetail));
    }

    /**
     * 修改附件子表
     */
    @Log(title = "附件子表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FileDetail fileDetail)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        fileDetail.setUpdateBy(userNickName.getData());
        return toAjax(fileDetailService.updateFileDetail(fileDetail));
    }

    /**
     * 删除附件子表
     */
    @Log(title = "附件子表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(fileDetailService.deleteFileDetailByIds(ids));
    }
}
