package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.PolicySubmitConsultMapper;
import com.ruoyi.portalconsole.domain.PolicySubmitConsult;
import com.ruoyi.portalconsole.service.IPolicySubmitConsultService;

/**
 * 政策辅助申报Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-08
 */
@Service
public class PolicySubmitConsultServiceImpl implements IPolicySubmitConsultService 
{
    @Autowired
    private PolicySubmitConsultMapper policySubmitConsultMapper;

    /**
     * 查询政策辅助申报
     * 
     * @param policySubmitConsultId 政策辅助申报主键
     * @return 政策辅助申报
     */
    @Override
    public PolicySubmitConsult selectPolicySubmitConsultByPolicySubmitConsultId(Long policySubmitConsultId)
    {
        return policySubmitConsultMapper.selectPolicySubmitConsultByPolicySubmitConsultId(policySubmitConsultId);
    }

    /**
     * 查询政策辅助申报列表
     * 
     * @param policySubmitConsult 政策辅助申报
     * @return 政策辅助申报
     */
    @Override
    public List<PolicySubmitConsult> selectPolicySubmitConsultList(PolicySubmitConsult policySubmitConsult)
    {
        return policySubmitConsultMapper.selectPolicySubmitConsultList(policySubmitConsult);
    }

    /**
     * 新增政策辅助申报
     * 
     * @param policySubmitConsult 政策辅助申报
     * @return 结果
     */
    @Override
    public int insertPolicySubmitConsult(PolicySubmitConsult policySubmitConsult)
    {
        policySubmitConsult.setCreateTime(DateUtils.getNowDate());
        return policySubmitConsultMapper.insertPolicySubmitConsult(policySubmitConsult);
    }

    /**
     * 修改政策辅助申报
     * 
     * @param policySubmitConsult 政策辅助申报
     * @return 结果
     */
    @Override
    public int updatePolicySubmitConsult(PolicySubmitConsult policySubmitConsult)
    {
        policySubmitConsult.setUpdateTime(DateUtils.getNowDate());
        return policySubmitConsultMapper.updatePolicySubmitConsult(policySubmitConsult);
    }

    /**
     * 批量删除政策辅助申报
     * 
     * @param policySubmitConsultIds 需要删除的政策辅助申报主键
     * @return 结果
     */
    @Override
    public int deletePolicySubmitConsultByPolicySubmitConsultIds(Long[] policySubmitConsultIds)
    {
        return policySubmitConsultMapper.deletePolicySubmitConsultByPolicySubmitConsultIds(policySubmitConsultIds);
    }

    /**
     * 删除政策辅助申报信息
     * 
     * @param policySubmitConsultId 政策辅助申报主键
     * @return 结果
     */
    @Override
    public int deletePolicySubmitConsultByPolicySubmitConsultId(Long policySubmitConsultId)
    {
        return policySubmitConsultMapper.deletePolicySubmitConsultByPolicySubmitConsultId(policySubmitConsultId);
    }
}
