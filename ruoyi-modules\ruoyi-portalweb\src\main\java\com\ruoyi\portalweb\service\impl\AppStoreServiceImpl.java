package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.AppStore;
import com.ruoyi.portalweb.api.domain.Member;
import com.ruoyi.portalweb.api.enums.AuditStatus;
import com.ruoyi.portalweb.api.enums.OnShow;
import com.ruoyi.portalweb.mapper.AppStoreMapper;
import com.ruoyi.portalweb.service.IAppStoreService;
import com.ruoyi.portalweb.service.IMemberService;
import com.ruoyi.portalweb.vo.AppStoreVO;
import com.ruoyi.portalweb.vo.MemberVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.ruoyi.common.core.utils.PageUtils.startPage;

/**
 * 应用商店Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class AppStoreServiceImpl implements IAppStoreService
{
    @Autowired
    private AppStoreMapper appStoreMapper;

    @Autowired
    private IMemberService memberService;

    /**
     * 查询应用商店
     * 
     * @param appStoreId 应用商店主键
     * @return 应用商店
     */
    @Override
    public AppStoreVO selectAppStoreByAppStoreId(Long appStoreId)
    {
        return appStoreMapper.selectAppStoreByAppStoreId(appStoreId);
    }

    /**
     * 查询应用商店列表
     * 
     * @param appStore 应用商店
     * @return 应用商店
     */
    @Override
    public List<AppStoreVO> selectAppStoreList(AppStoreVO appStore)
    {
        if (StringUtils.isNotEmpty(appStore.getQueryType()) && "my".equals(appStore.getQueryType())) {
            appStore.setMemberId(SecurityUtils.getUserId());
        }
        return appStoreMapper.selectAppStoreList(appStore);
    }

    /**
     * 新增应用商店
     * 
     * @param appStore 应用商店
     * @return 结果
     */
    @Override
    public long insertAppStore(AppStore appStore)
    {
        Member member = memberService.selectMemberByMemberId(SecurityUtils.getUserId());
        appStore.setCreateTime(DateUtils.getNowDate());
        appStore.setMemberId(SecurityUtils.getUserId());
        appStore.setCreateBy(member.getMemberRealName());
        appStore.setUpdateBy(member.getMemberRealName());
        appStore.setAuditStatus(AuditStatus.APPROVED.getValue());
        appStore.setOnShow(OnShow.PENDING_CONFIG.getValue());
        appStore.setCompanyId(member.getMemberCompanyId());
        appStoreMapper.insertAppStore(appStore);
        return appStore.getAppStoreId();
    }

    /**
     * 修改应用商店
     * 
     * @param appStore 应用商店
     * @return 结果
     */
    @Override
    public long updateAppStore(AppStore appStore)
    {
        Member member = memberService.selectMemberByMemberId(SecurityUtils.getUserId());
        appStore.setUpdateBy(member.getMemberRealName());
        AppStoreVO appStoreVO = appStoreMapper.selectAppStoreByAppStoreId(appStore.getAppStoreId());
        if (Objects.equals(appStoreVO.getOnShow(), OnShow.PENDING_CONFIG.getValue())) {
            appStore.setOnShow(OnShow.PENDING.getValue());
        }else if (Objects.equals(appStoreVO.getOnShow(), OnShow.PENDING.getValue()) && appStore.getOnShow() == null) {
            appStore.setOnShow(OnShow._NO.getValue());
        }
        appStore.setAuditStatus(AuditStatus.PENDING.getValue());
        appStoreMapper.updateAppStore(appStore);
        return appStoreVO.getAppStoreId();
    }

    /**
     * 批量删除应用商店
     * 
     * @param appStoreIds 需要删除的应用商店主键
     * @return 结果
     */
    @Override
    public int deleteAppStoreByAppStoreIds(Long[] appStoreIds)
    {
        return appStoreMapper.deleteAppStoreByAppStoreIds(appStoreIds);
    }

    /**
     * 删除应用商店信息
     * 
     * @param appStoreId 应用商店主键
     * @return 结果
     */
    @Override
    public int deleteAppStoreByAppStoreId(Long appStoreId)
    {
        return appStoreMapper.deleteAppStoreByAppStoreId(appStoreId);
    }

    @Override
    public List<AppStoreVO> selectRecommendAppStoreList(AppStoreVO appStore) {
        MemberVO memberVO = memberService.selectMemberByMemberId(SecurityUtils.getUserId());
        if (memberVO.getSolutionTypeName() == null || Objects.equals(memberVO.getSolutionTypeName(), "")) {
            return null;
        }
        startPage();
        AppStoreVO wrapper = new AppStoreVO();
        wrapper.setKeyword(memberVO.getSolutionTypeName());

        // 通过审核并已上架
        wrapper.setOnShow(OnShow._YES.getValue());
        wrapper.setAuditStatus(AuditStatus.APPROVED.getValue());
        return appStoreMapper.selectAppStoreList(wrapper);
    }
}
