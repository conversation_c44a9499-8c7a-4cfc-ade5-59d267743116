<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysSupplierInfoMapper">
    
    <resultMap type="SysSupplierInfo" id="SysSupplierInfoResult">
        <result property="supplierId"    column="supplier_id"    />
        <result property="techRequirementId"    column="tech_requirement_id"    />
        <result property="requirementTitle"    column="requirement_title"    />
        <result property="companyName"    column="company_name"    />
        <result property="supplyType"    column="supply_type"    />
        <result property="supplyContent"    column="supply_content"    />
        <result property="supplyAmount"    column="supply_amount"    />
        <result property="technologyField"    column="technology_field"    />
        <result property="technologyDescription"    column="technology_description"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactEmail"    column="contact_email"    />
        <result property="companyAddress"    column="company_address"    />
        <result property="businessScope"    column="business_scope"    />
        <result property="cooperationMode"    column="cooperation_mode"    />
        <result property="supplierStatus"    column="supplier_status"    />
        <result property="certificationLevel"    column="certification_level"    />
        <result property="attachmentUrl"    column="attachment_url"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysSupplierInfoVo">
        select s.supplier_id, s.tech_requirement_id, t.requirement_title, s.company_name,
               s.supply_type, s.supply_content, s.supply_amount, s.technology_field,
               s.technology_description, s.contact_person, s.contact_phone, s.contact_email,
               s.company_address, s.business_scope, s.cooperation_mode, s.supplier_status,
               s.certification_level, s.attachment_url, s.create_by, s.create_time,
               s.update_by, s.update_time, s.remark
        from sys_supplier_info s
        left join sys_tech_requirement t on s.tech_requirement_id = t.requirement_id
    </sql>

    <select id="selectSysSupplierInfoList" parameterType="SysSupplierInfo" resultMap="SysSupplierInfoResult">
        <include refid="selectSysSupplierInfoVo"/>
        <where>
            <if test="companyName != null  and companyName != ''"> and s.company_name like concat('%', #{companyName}, '%')</if>
            <if test="techRequirementId != null "> and s.tech_requirement_id = #{techRequirementId}</if>
            <if test="requirementTitle != null and requirementTitle != ''"> and t.requirement_title like concat('%', #{requirementTitle}, '%')</if>
            <if test="supplyType != null  and supplyType != ''"> and s.supply_type = #{supplyType}</if>
            <if test="supplyContent != null  and supplyContent != ''"> and s.supply_content = #{supplyContent}</if>
            <if test="supplyAmount != null "> and s.supply_amount = #{supplyAmount}</if>
            <if test="technologyField != null  and technologyField != ''"> and s.technology_field = #{technologyField}</if>
            <if test="technologyDescription != null  and technologyDescription != ''"> and s.technology_description = #{technologyDescription}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and s.contact_person = #{contactPerson}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and s.contact_phone = #{contactPhone}</if>
            <if test="contactEmail != null  and contactEmail != ''"> and s.contact_email = #{contactEmail}</if>
            <if test="companyAddress != null  and companyAddress != ''"> and s.company_address = #{companyAddress}</if>
            <if test="businessScope != null  and businessScope != ''"> and s.business_scope = #{businessScope}</if>
            <if test="cooperationMode != null  and cooperationMode != ''"> and s.cooperation_mode = #{cooperationMode}</if>
            <if test="supplierStatus != null  and supplierStatus != ''"> and s.supplier_status = #{supplierStatus}</if>
            <if test="certificationLevel != null  and certificationLevel != ''"> and s.certification_level = #{certificationLevel}</if>
            <if test="attachmentUrl != null  and attachmentUrl != ''"> and s.attachment_url = #{attachmentUrl}</if>
        </where>
    </select>
    
    <select id="selectSysSupplierInfoBySupplierId" parameterType="Long" resultMap="SysSupplierInfoResult">
        <include refid="selectSysSupplierInfoVo"/>
        where s.supplier_id = #{supplierId}
    </select>
        
    <insert id="insertSysSupplierInfo" parameterType="SysSupplierInfo" useGeneratedKeys="true" keyProperty="supplierId">
        insert into sys_supplier_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="techRequirementId != null">tech_requirement_id,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="supplyType != null and supplyType != ''">supply_type,</if>
            <if test="supplyContent != null and supplyContent != ''">supply_content,</if>
            <if test="supplyAmount != null">supply_amount,</if>
            <if test="technologyField != null">technology_field,</if>
            <if test="technologyDescription != null">technology_description,</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="companyAddress != null">company_address,</if>
            <if test="businessScope != null">business_scope,</if>
            <if test="cooperationMode != null">cooperation_mode,</if>
            <if test="supplierStatus != null">supplier_status,</if>
            <if test="certificationLevel != null">certification_level,</if>
            <if test="attachmentUrl != null">attachment_url,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="techRequirementId != null">#{techRequirementId},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="supplyType != null and supplyType != ''">#{supplyType},</if>
            <if test="supplyContent != null and supplyContent != ''">#{supplyContent},</if>
            <if test="supplyAmount != null">#{supplyAmount},</if>
            <if test="technologyField != null">#{technologyField},</if>
            <if test="technologyDescription != null">#{technologyDescription},</if>
            <if test="contactPerson != null and contactPerson != ''">#{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="companyAddress != null">#{companyAddress},</if>
            <if test="businessScope != null">#{businessScope},</if>
            <if test="cooperationMode != null">#{cooperationMode},</if>
            <if test="supplierStatus != null">#{supplierStatus},</if>
            <if test="certificationLevel != null">#{certificationLevel},</if>
            <if test="attachmentUrl != null">#{attachmentUrl},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysSupplierInfo" parameterType="SysSupplierInfo">
        update sys_supplier_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="techRequirementId != null">tech_requirement_id = #{techRequirementId},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="supplyType != null and supplyType != ''">supply_type = #{supplyType},</if>
            <if test="supplyContent != null and supplyContent != ''">supply_content = #{supplyContent},</if>
            <if test="supplyAmount != null">supply_amount = #{supplyAmount},</if>
            <if test="technologyField != null">technology_field = #{technologyField},</if>
            <if test="technologyDescription != null">technology_description = #{technologyDescription},</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="companyAddress != null">company_address = #{companyAddress},</if>
            <if test="businessScope != null">business_scope = #{businessScope},</if>
            <if test="cooperationMode != null">cooperation_mode = #{cooperationMode},</if>
            <if test="supplierStatus != null">supplier_status = #{supplierStatus},</if>
            <if test="certificationLevel != null">certification_level = #{certificationLevel},</if>
            <if test="attachmentUrl != null">attachment_url = #{attachmentUrl},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where supplier_id = #{supplierId}
    </update>

    <delete id="deleteSysSupplierInfoBySupplierId" parameterType="Long">
        delete from sys_supplier_info where supplier_id = #{supplierId}
    </delete>

    <delete id="deleteSysSupplierInfoBySupplierIds" parameterType="String">
        delete from sys_supplier_info where supplier_id in 
        <foreach item="supplierId" collection="array" open="(" separator="," close=")">
            #{supplierId}
        </foreach>
    </delete>
</mapper>