package com.ruoyi.im.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName(value = "im_chatroom")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImChatroom extends Model<ImChatroom> {

        @TableId(value = "id", type = IdType.AUTO)
        private Long id;//自增

        @Excel(name = "聊天室ID")
        private String chatroomId;//聊天室ID

        @Excel(name = "聊天室名称")
        private String chatroomName;//聊天室名称

        @Excel(name = "模块")
        private String module;//模块

        @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
        private Date create_time;//创建时间

        private Date update_time;//更新时间
}
