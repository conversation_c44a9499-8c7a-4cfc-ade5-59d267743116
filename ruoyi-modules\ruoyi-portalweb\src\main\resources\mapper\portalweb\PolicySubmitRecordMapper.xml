<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.PolicySubmitRecordMapper">

    <resultMap type="PolicySubmitRecord" id="PolicySubmitRecordResult">
        <result property="policySubmitRecordId" column="policy_submit_record_id"/>
        <result property="policySubmitId" column="policy_submit_id"/>
        <result property="companyId" column="company_id"/>
        <result property="policySubmitRecordTime" column="policy_submit_record_time"/>
        <result property="policySubmitRecordStatus" column="policy_submit_record_status"/>
        <result property="policySubmitRecordAuditor" column="policy_submit_record_auditor"/>
        <result property="policySubmitRecordAudittime" column="policy_submit_record_audittime"/>
        <result property="policySubmitRecordFileUrl" column="policy_submit_record_file_url"/>
        <result property="contact" column="contact"/>
        <result property="phone" column="phone"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberId" column="member_id"/>
    </resultMap>

    <sql id="selectPolicySubmitRecordVo">
        select policy_submit_record_id, policy_submit_id, company_id, policy_submit_record_time,
        policy_submit_record_status, policy_submit_record_auditor, policy_submit_record_audittime, policy_submit_record_file_url, contact, phone, del_flag, create_by,
        create_time, update_by, update_time, remark ,member_id
        from policy_submit_record
    </sql>

    <select id="selectPolicySubmitRecordList" parameterType="PolicySubmitRecord" resultMap="PolicySubmitRecordResult">
        <include refid="selectPolicySubmitRecordVo"/>
        <where>
            <if test="policySubmitId != null ">and policy_submit_id = #{policySubmitId}</if>
            <if test="companyId != null  and companyId != ''">and company_id = #{companyId}</if>
            <if test="policySubmitRecordTime != null ">and policy_submit_record_time = #{policySubmitRecordTime}</if>
            <if test="policySubmitRecordStatus != null  and policySubmitRecordStatus != ''">and
                policy_submit_record_status = #{policySubmitRecordStatus}
            </if>
            <if test="policySubmitRecordAuditor != null  and policySubmitRecordAuditor != ''">and
                policy_submit_record_auditor = #{policySubmitRecordAuditor}
            </if>
            <if test="policySubmitRecordAudittime != null  and policySubmitRecordAudittime != ''">and
                policy_submit_record_audittime = #{policySubmitRecordAudittime}
            </if>
            <if test="memberId != null ">and member_id = #{memberId}</if>
        </where>
    </select>

    <select id="selectPolicySubmitRecordByPolicySubmitRecordId" parameterType="Long"
            resultMap="PolicySubmitRecordResult">
        <include refid="selectPolicySubmitRecordVo"/>
        where policy_submit_record_id = #{policySubmitRecordId}
    </select>

    <insert id="insertPolicySubmitRecord" parameterType="PolicySubmitRecord" useGeneratedKeys="true"
            keyProperty="policySubmitRecordId">
        insert into policy_submit_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="policySubmitId != null">policy_submit_id,</if>
            <if test="companyId != null">company_id,</if>
            <if test="policySubmitRecordTime != null">policy_submit_record_time,</if>
            <if test="policySubmitRecordStatus != null">policy_submit_record_status,</if>
            <if test="policySubmitRecordAuditor != null">policy_submit_record_auditor,</if>
            <if test="policySubmitRecordAudittime != null">policy_submit_record_audittime,</if>
            <if test="policySubmitRecordFileUrl != null">policy_submit_record_file_url,</if>
            <if test="contact != null">contact,</if>
            <if test="phone != null">phone,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="memberId != null">member_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="policySubmitId != null">#{policySubmitId},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="policySubmitRecordTime != null">#{policySubmitRecordTime},</if>
            <if test="policySubmitRecordStatus != null">#{policySubmitRecordStatus},</if>
            <if test="policySubmitRecordAuditor != null">#{policySubmitRecordAuditor},</if>
            <if test="policySubmitRecordAudittime != null">#{policySubmitRecordAudittime},</if>
            <if test="policySubmitRecordFileUrl != null">#{policySubmitRecordFileUrl},</if>
            <if test="contact != null">#{contact},</if>
            <if test="phone != null">#{phone},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="memberId != null">#{memberId},</if>
        </trim>
    </insert>

    <update id="updatePolicySubmitRecord" parameterType="PolicySubmitRecord">
        update policy_submit_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="policySubmitId != null">policy_submit_id = #{policySubmitId},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="policySubmitRecordTime != null">policy_submit_record_time = #{policySubmitRecordTime},</if>
            <if test="policySubmitRecordStatus != null">policy_submit_record_status = #{policySubmitRecordStatus},</if>
            <if test="policySubmitRecordAuditor != null">policy_submit_record_auditor = #{policySubmitRecordAuditor},
            </if>
            <if test="policySubmitRecordAudittime != null">policy_submit_record_audittime =
                #{policySubmitRecordAudittime},
            </if>
            <if test="policySubmitRecordFileUrl != null">policy_submit_record_file_url =
                #{policySubmitRecordFileUrl},
            </if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
        </trim>
        where policy_submit_record_id = #{policySubmitRecordId}
    </update>

    <delete id="deletePolicySubmitRecordByPolicySubmitRecordId" parameterType="Long">
        delete from policy_submit_record where policy_submit_record_id = #{policySubmitRecordId}
    </delete>

    <delete id="deletePolicySubmitRecordByPolicySubmitRecordIds" parameterType="String">
        delete from policy_submit_record where policy_submit_record_id in
        <foreach item="policySubmitRecordId" collection="array" open="(" separator="," close=")">
            #{policySubmitRecordId}
        </foreach>
    </delete>
</mapper>