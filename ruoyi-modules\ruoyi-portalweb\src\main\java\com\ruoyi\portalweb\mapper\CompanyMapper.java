package com.ruoyi.portalweb.mapper;


import com.ruoyi.portalweb.api.domain.Company;
import com.ruoyi.portalweb.vo.CompanyVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface CompanyMapper 
{
    /**
     * 查询企业信息
     * 
     * @param companyId 企业信息主键
     * @return 企业信息
     */
    public CompanyVO selectCompanyByCompanyId(Long companyId);

    /**
     * 查询企业信息
     *
     * @param companyId 企业信息主键
     * @return 企业信息
     */
    public CompanyVO selectCompanyByCompanyIdAndMemberId(@Param("companyId") Long companyId, @Param("memberId") Long memberId);

    /**
     * 查询企业信息列表
     * 
     * @param company 企业信息
     * @return 企业信息集合
     */
    public List<Company> selectCompanyList(Company company);

    /**
     * 新增企业信息
     * 
     * @param company 企业信息
     * @return 结果
     */
    public int insertCompany(Company company);

    /**
     * 修改企业信息
     * 
     * @param company 企业信息
     * @return 结果
     */
    public int updateCompany(Company company);

    /**
     * 删除企业信息
     * 
     * @param companyId 企业信息主键
     * @return 结果
     */
    public int deleteCompanyByCompanyId(Long companyId);

    /**
     * 批量删除企业信息
     * 
     * @param companyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCompanyByCompanyIds(Long[] companyIds);
}
