package com.ruoyi.system.service;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.domain.UserChatGroup;

/**
 * 用户交互相关
 * 群聊系统
 */
public interface ISysChatGroupService
{

    /**
     * 创建群组
     * @param userName 当前登录人手机号
     * @param groupUserNames 群组成员 多个逗号拼接
     * @param groupName 群组名称
     * @return
     */
    AjaxResult createChatGroup(String userName,String groupUserNames,String groupName);


    /**
     * 修改群组
     * @param userName
     * @param userChatGroup
     *          id  群组id
     *          groupName 群组名称
     *          avatar 群头像
     *
     * @return
     */
    AjaxResult updateChatGroup(String userName, UserChatGroup userChatGroup);

    /**
     * 解散群组
     * @param userName
     * @param groupId
     * @return
     */
    AjaxResult dismissChatGroup(String userName,Long groupId);


    /**
     * 用户加入群组
     * @param userName 登录用户的手机号
     * @param groupId 群组id
     * @param groupUserNames 要加入的用户手机号 多个逗号拼接
     * @return
     */
    AjaxResult joinChatGroup(String userName,Long groupId,String groupUserNames);

    /**
     * 用户退出群组
     * @param userName
     * @param groupId
     * @param groupUserNames
     * @return
     */
    AjaxResult quitChatGroup(String userName,Long groupId,String groupUserNames);

    /**
     * 查询用户群组列表
     * @param userName
     * @return
     */
    AjaxResult getUserGroupList(String userName);


    /**
     * 查询群用户
     * @param userName
     * @param groupId
     * @return
     */
    AjaxResult getGroupUserList(String userName,Long groupId);

}
