package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.portalweb.api.domain.EcologyCategory;
import com.ruoyi.portalweb.mapper.EcologyCategoryMapper;
import com.ruoyi.portalweb.service.IEcologyCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 生态类别Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class EcologyCategoryServiceImpl implements IEcologyCategoryService
{
    @Autowired
    private EcologyCategoryMapper ecologyCategoryMapper;

    /**
     * 查询生态类别
     * 
     * @param ecologyCategoryId 生态类别主键
     * @return 生态类别
     */
    @Override
    public EcologyCategory selectEcologyCategoryByEcologyCategoryId(Long ecologyCategoryId)
    {
        return ecologyCategoryMapper.selectEcologyCategoryByEcologyCategoryId(ecologyCategoryId);
    }

    /**
     * 查询生态类别列表
     * 
     * @param ecologyCategory 生态类别
     * @return 生态类别
     */
    @Override
    public List<EcologyCategory> selectEcologyCategoryList(EcologyCategory ecologyCategory)
    {
        return ecologyCategoryMapper.selectEcologyCategoryList(ecologyCategory);
    }

    /**
     * 新增生态类别
     * 
     * @param ecologyCategory 生态类别
     * @return 结果
     */
    @Override
    public int insertEcologyCategory(EcologyCategory ecologyCategory)
    {
        ecologyCategory.setCreateTime(DateUtils.getNowDate());
        return ecologyCategoryMapper.insertEcologyCategory(ecologyCategory);
    }

    /**
     * 修改生态类别
     * 
     * @param ecologyCategory 生态类别
     * @return 结果
     */
    @Override
    public int updateEcologyCategory(EcologyCategory ecologyCategory)
    {
        ecologyCategory.setUpdateTime(DateUtils.getNowDate());
        return ecologyCategoryMapper.updateEcologyCategory(ecologyCategory);
    }

    /**
     * 批量删除生态类别
     * 
     * @param ecologyCategoryIds 需要删除的生态类别主键
     * @return 结果
     */
    @Override
    public int deleteEcologyCategoryByEcologyCategoryIds(Long[] ecologyCategoryIds)
    {
        return ecologyCategoryMapper.deleteEcologyCategoryByEcologyCategoryIds(ecologyCategoryIds);
    }

    /**
     * 删除生态类别信息
     * 
     * @param ecologyCategoryId 生态类别主键
     * @return 结果
     */
    @Override
    public int deleteEcologyCategoryByEcologyCategoryId(Long ecologyCategoryId)
    {
        return ecologyCategoryMapper.deleteEcologyCategoryByEcologyCategoryId(ecologyCategoryId);
    }
}
