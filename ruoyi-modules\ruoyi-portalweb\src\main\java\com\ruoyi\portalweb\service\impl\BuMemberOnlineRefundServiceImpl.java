package com.ruoyi.portalweb.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalweb.mapper.BuMemberOnlineRefundMapper;
import com.ruoyi.portalweb.api.domain.BuMemberOnlineRefund;
import com.ruoyi.portalweb.service.IBuMemberOnlineRefundService;

/**
 * 商城用户线上退款Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@Service
public class BuMemberOnlineRefundServiceImpl implements IBuMemberOnlineRefundService 
{
    @Autowired
    private BuMemberOnlineRefundMapper buMemberOnlineRefundMapper;

    /**
     * 查询商城用户线上退款
     * 
     * @param id 商城用户线上退款主键
     * @return 商城用户线上退款
     */
    @Override
    public BuMemberOnlineRefund selectBuMemberOnlineRefundById(Long id)
    {
        return buMemberOnlineRefundMapper.selectBuMemberOnlineRefundById(id);
    }

    @Override
    public BuMemberOnlineRefund selectBuMemberOnlineRefundByRefundOrderNo(String refundOrderNo) {
        return buMemberOnlineRefundMapper.selectBuMemberOnlineRefundByRefundOrderNo(refundOrderNo);
    }

    /**
     * 查询商城用户线上退款列表
     * 
     * @param buMemberOnlineRefund 商城用户线上退款
     * @return 商城用户线上退款
     */
    @Override
    public List<BuMemberOnlineRefund> selectBuMemberOnlineRefundList(BuMemberOnlineRefund buMemberOnlineRefund)
    {
        return buMemberOnlineRefundMapper.selectBuMemberOnlineRefundList(buMemberOnlineRefund);
    }

    /**
     * 新增商城用户线上退款
     * 
     * @param buMemberOnlineRefund 商城用户线上退款
     * @return 结果
     */
    @Override
    public int insertBuMemberOnlineRefund(BuMemberOnlineRefund buMemberOnlineRefund)
    {
        buMemberOnlineRefund.setCreateTime(DateUtils.getNowDate());
        return buMemberOnlineRefundMapper.insertBuMemberOnlineRefund(buMemberOnlineRefund);
    }

    /**
     * 修改商城用户线上退款
     * 
     * @param buMemberOnlineRefund 商城用户线上退款
     * @return 结果
     */
    @Override
    public int updateBuMemberOnlineRefund(BuMemberOnlineRefund buMemberOnlineRefund)
    {
        buMemberOnlineRefund.setUpdateTime(DateUtils.getNowDate());
        return buMemberOnlineRefundMapper.updateBuMemberOnlineRefund(buMemberOnlineRefund);
    }

    @Override
    public int updateBuMemberOnlineRefundByRefundOrderNo(BuMemberOnlineRefund buMemberOnlineRefund) {
        return buMemberOnlineRefundMapper.updateBuMemberOnlineRefundByRefundOrderNo(buMemberOnlineRefund);
    }

    /**
     * 批量删除商城用户线上退款
     * 
     * @param ids 需要删除的商城用户线上退款主键
     * @return 结果
     */
    @Override
    public int deleteBuMemberOnlineRefundByIds(Long[] ids)
    {
        return buMemberOnlineRefundMapper.deleteBuMemberOnlineRefundByIds(ids);
    }

    /**
     * 删除商城用户线上退款信息
     * 
     * @param id 商城用户线上退款主键
     * @return 结果
     */
    @Override
    public int deleteBuMemberOnlineRefundById(Long id)
    {
        return buMemberOnlineRefundMapper.deleteBuMemberOnlineRefundById(id);
    }
}
