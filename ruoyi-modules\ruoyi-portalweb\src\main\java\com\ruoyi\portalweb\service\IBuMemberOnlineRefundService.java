package com.ruoyi.portalweb.service;

import java.util.List;
import com.ruoyi.portalweb.api.domain.BuMemberOnlineRefund;

/**
 * 商城用户线上退款Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface IBuMemberOnlineRefundService 
{
    /**
     * 查询商城用户线上退款
     * 
     * @param id 商城用户线上退款主键
     * @return 商城用户线上退款
     */
    public BuMemberOnlineRefund selectBuMemberOnlineRefundById(Long id);

    /**
     * 查询商城用户线上退款
     *
     * @param refundOrderNo 退款记录refundOrderNo
     * @return 商城用户线上退款
     */
    public BuMemberOnlineRefund selectBuMemberOnlineRefundByRefundOrderNo(String  refundOrderNo);

    /**
     * 查询商城用户线上退款列表
     * 
     * @param buMemberOnlineRefund 商城用户线上退款
     * @return 商城用户线上退款集合
     */
    public List<BuMemberOnlineRefund> selectBuMemberOnlineRefundList(BuMemberOnlineRefund buMemberOnlineRefund);

    /**
     * 新增商城用户线上退款
     * 
     * @param buMemberOnlineRefund 商城用户线上退款
     * @return 结果
     */
    public int insertBuMemberOnlineRefund(BuMemberOnlineRefund buMemberOnlineRefund);

    /**
     * 修改商城用户线上退款
     * 
     * @param buMemberOnlineRefund 商城用户线上退款
     * @return 结果
     */
    public int updateBuMemberOnlineRefund(BuMemberOnlineRefund buMemberOnlineRefund);

    /**
     * 修改商城用户线上退款
     *
     * @param buMemberOnlineRefund 商城用户线上退款
     * @return 结果
     */
    public int updateBuMemberOnlineRefundByRefundOrderNo(BuMemberOnlineRefund buMemberOnlineRefund);

    /**
     * 批量删除商城用户线上退款
     * 
     * @param ids 需要删除的商城用户线上退款主键集合
     * @return 结果
     */
    public int deleteBuMemberOnlineRefundByIds(Long[] ids);

    /**
     * 删除商城用户线上退款信息
     * 
     * @param id 商城用户线上退款主键
     * @return 结果
     */
    public int deleteBuMemberOnlineRefundById(Long id);
}
