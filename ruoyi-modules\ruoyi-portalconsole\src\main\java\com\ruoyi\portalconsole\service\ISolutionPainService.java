package com.ruoyi.portalconsole.service;

import java.util.List;
import com.ruoyi.portalconsole.domain.SolutionPain;

/**
 * 解决方案行业痛点Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface ISolutionPainService 
{
    /**
     * 查询解决方案行业痛点
     * 
     * @param solutionPainId 解决方案行业痛点主键
     * @return 解决方案行业痛点
     */
    public SolutionPain selectSolutionPainBySolutionPainId(Long solutionPainId);

    /**
     * 查询解决方案行业痛点列表
     * 
     * @param solutionPain 解决方案行业痛点
     * @return 解决方案行业痛点集合
     */
    public List<SolutionPain> selectSolutionPainList(SolutionPain solutionPain);

    /**
     * 新增解决方案行业痛点
     * 
     * @param solutionPain 解决方案行业痛点
     * @return 结果
     */
    public int insertSolutionPain(SolutionPain solutionPain);

    /**
     * 修改解决方案行业痛点
     * 
     * @param solutionPain 解决方案行业痛点
     * @return 结果
     */
    public int updateSolutionPain(SolutionPain solutionPain);

    /**
     * 批量删除解决方案行业痛点
     * 
     * @param solutionPainIds 需要删除的解决方案行业痛点主键集合
     * @return 结果
     */
    public int deleteSolutionPainBySolutionPainIds(Long[] solutionPainIds);

    /**
     * 删除解决方案行业痛点信息
     * 
     * @param solutionPainId 解决方案行业痛点主键
     * @return 结果
     */
    public int deleteSolutionPainBySolutionPainId(Long solutionPainId);
}
