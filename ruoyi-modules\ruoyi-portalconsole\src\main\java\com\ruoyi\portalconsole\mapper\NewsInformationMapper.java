package com.ruoyi.portalconsole.mapper;

import java.util.List;
import com.ruoyi.portalconsole.domain.NewsInformation;

/**
 * 动态资讯Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface NewsInformationMapper 
{
    /**
     * 查询动态资讯
     * 
     * @param newsInformationId 动态资讯主键
     * @return 动态资讯
     */
    public NewsInformation selectNewsInformationByNewsInformationId(Long newsInformationId);

    /**
     * 查询动态资讯列表
     * 
     * @param newsInformation 动态资讯
     * @return 动态资讯集合
     */
    public List<NewsInformation> selectNewsInformationList(NewsInformation newsInformation);

    /**
     * 新增动态资讯
     * 
     * @param newsInformation 动态资讯
     * @return 结果
     */
    public int insertNewsInformation(NewsInformation newsInformation);

    /**
     * 修改动态资讯
     * 
     * @param newsInformation 动态资讯
     * @return 结果
     */
    public int updateNewsInformation(NewsInformation newsInformation);

    /**
     * 删除动态资讯
     * 
     * @param newsInformationId 动态资讯主键
     * @return 结果
     */
    public int deleteNewsInformationByNewsInformationId(Long newsInformationId);

    /**
     * 批量删除动态资讯
     * 
     * @param newsInformationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewsInformationByNewsInformationIds(Long[] newsInformationIds);
}
