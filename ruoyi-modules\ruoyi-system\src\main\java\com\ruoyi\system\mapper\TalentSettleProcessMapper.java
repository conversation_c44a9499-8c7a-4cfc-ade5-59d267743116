package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.TalentSettleProcess;

/**
 * 人才入驻流程Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface TalentSettleProcessMapper 
{
    /**
     * 查询人才入驻流程
     * 
     * @param id 人才入驻流程主键
     * @return 人才入驻流程
     */
    public TalentSettleProcess selectTalentSettleProcessById(Long id);

    /**
     * 查询人才入驻流程列表
     * 
     * @param talentSettleProcess 人才入驻流程
     * @return 人才入驻流程集合
     */
    public List<TalentSettleProcess> selectTalentSettleProcessList(TalentSettleProcess talentSettleProcess);

    /**
     * 新增人才入驻流程
     * 
     * @param talentSettleProcess 人才入驻流程
     * @return 结果
     */
    public int insertTalentSettleProcess(TalentSettleProcess talentSettleProcess);

    /**
     * 修改人才入驻流程
     * 
     * @param talentSettleProcess 人才入驻流程
     * @return 结果
     */
    public int updateTalentSettleProcess(TalentSettleProcess talentSettleProcess);

    /**
     * 删除人才入驻流程
     * 
     * @param id 人才入驻流程主键
     * @return 结果
     */
    public int deleteTalentSettleProcessById(Long id);

    /**
     * 批量删除人才入驻流程
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTalentSettleProcessByIds(Long[] ids);
}
