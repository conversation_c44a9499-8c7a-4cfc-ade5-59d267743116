package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.mapper.SettledFactoryMapper;
import com.ruoyi.system.mapper.FactoryEquipmentMapper;
import com.ruoyi.system.mapper.FactoryQualificationMapper;
import com.ruoyi.system.mapper.FactoryPersonnelMapper;
import com.ruoyi.system.mapper.FactoryPerformanceMapper;
import com.ruoyi.system.domain.SettledFactory;
import com.ruoyi.system.domain.FactoryEquipment;
import com.ruoyi.system.domain.FactoryQualification;
import com.ruoyi.system.domain.FactoryPersonnel;
import com.ruoyi.system.domain.FactoryPerformance;
import com.ruoyi.system.domain.dto.SettledFactoryDetailDTO;
import com.ruoyi.system.domain.dto.SettledFactoryAddDTO;
import com.ruoyi.system.service.ISettledFactoryService;
import com.ruoyi.system.service.IFactoryEquipmentService;
import com.ruoyi.system.service.IFactoryQualificationService;
import com.ruoyi.system.service.IFactoryPersonnelService;
import com.ruoyi.system.service.IFactoryPerformanceService;

/**
 * 入驻工厂Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Service
public class SettledFactoryServiceImpl implements ISettledFactoryService
{
    @Autowired
    private SettledFactoryMapper settledFactoryMapper;

    @Autowired
    private FactoryEquipmentMapper factoryEquipmentMapper;

    @Autowired
    private FactoryQualificationMapper factoryQualificationMapper;

    @Autowired
    private FactoryPersonnelMapper factoryPersonnelMapper;

    @Autowired
    private FactoryPerformanceMapper factoryPerformanceMapper;

    @Autowired
    private IFactoryEquipmentService factoryEquipmentService;

    @Autowired
    private IFactoryQualificationService factoryQualificationService;

    @Autowired
    private IFactoryPersonnelService factoryPersonnelService;

    @Autowired
    private IFactoryPerformanceService factoryPerformanceService;

    /**
     * 查询入驻工厂
     *
     * @param id 入驻工厂主键
     * @return 入驻工厂
     */
    @Override
    public SettledFactory selectSettledFactoryById(Long id)
    {
        return settledFactoryMapper.selectSettledFactoryById(id);
    }

    /**
     * 查询入驻工厂详情（包含关联数据）
     *
     * @param id 入驻工厂主键
     * @return 入驻工厂详情
     */
    @Override
    public SettledFactoryDetailDTO selectSettledFactoryDetailById(Long id)
    {
        // 查询基本信息
        SettledFactory settledFactory = settledFactoryMapper.selectSettledFactoryById(id);
        if (settledFactory == null)
        {
            return null;
        }

        // 创建返回对象
        SettledFactoryDetailDTO detailDTO = new SettledFactoryDetailDTO();
        BeanUtils.copyProperties(settledFactory, detailDTO);

        // 查询设备信息
        FactoryEquipment equipment = new FactoryEquipment();
        equipment.setFactoryId(id);
        detailDTO.setEquipmentList(factoryEquipmentService.selectFactoryEquipmentList(equipment));

        // 查询资质信息
        FactoryQualification qualification = new FactoryQualification();
        qualification.setFactoryId(id);
        detailDTO.setQualificationList(factoryQualificationService.selectFactoryQualificationList(qualification));

        // 查询人员信息
        FactoryPersonnel personnel = new FactoryPersonnel();
        personnel.setFactoryId(id);
        detailDTO.setPersonnelList(factoryPersonnelService.selectFactoryPersonnelList(personnel));

        // 查询业绩信息
        FactoryPerformance performance = new FactoryPerformance();
        performance.setFactoryId(id);
        detailDTO.setPerformanceList(factoryPerformanceService.selectFactoryPerformanceList(performance));

        return detailDTO;
    }

    /**
     * 查询入驻工厂列表
     *
     * @param settledFactory 入驻工厂
     * @return 入驻工厂
     */
    @Override
    public List<SettledFactory> selectSettledFactoryList(SettledFactory settledFactory)
    {
        return settledFactoryMapper.selectSettledFactoryList(settledFactory);
    }

    /**
     * 根据产品ID查询关联的工厂列表
     *
     * @param productId 产品ID
     * @return 入驻工厂集合
     */
    @Override
    public List<SettledFactory> selectSettledFactoryListByProductId(Long productId)
    {
        return settledFactoryMapper.selectSettledFactoryListByProductId(productId);
    }

    /**
     * 新增入驻工厂（包含关联数据）
     *
     * @param factoryAdd 入驻工厂及关联数据
     * @return 结果
     */
    @Override
    @Transactional
    public int insertSettledFactoryWithRelated(SettledFactoryAddDTO factoryAdd)
    {
        // 1. 插入入驻工厂基本信息
        SettledFactory factory = new SettledFactory();
        BeanUtils.copyProperties(factoryAdd, factory);
        factory.setCreateTime(DateUtils.getNowDate());
        int rows = settledFactoryMapper.insertSettledFactory(factory);
        Long factoryId = factory.getId();

        // 2. 插入设备信息
        if (factoryAdd.getEquipmentList() != null) {
            for (FactoryEquipment equipment : factoryAdd.getEquipmentList()) {
                equipment.setFactoryId(factoryId);
                equipment.setCreateTime(DateUtils.getNowDate());
                factoryEquipmentMapper.insertFactoryEquipment(equipment);
            }
        }

        // 3. 插入资质信息
        if (factoryAdd.getQualificationList() != null) {
            for (FactoryQualification qualification : factoryAdd.getQualificationList()) {
                qualification.setFactoryId(factoryId);
                qualification.setCreateTime(DateUtils.getNowDate());
                factoryQualificationMapper.insertFactoryQualification(qualification);
            }
        }

        // 4. 插入人员信息
        if (factoryAdd.getPersonnelList() != null) {
            for (FactoryPersonnel personnel : factoryAdd.getPersonnelList()) {
                personnel.setFactoryId(factoryId);
                personnel.setCreateTime(DateUtils.getNowDate());
                factoryPersonnelMapper.insertFactoryPersonnel(personnel);
            }
        }

        // 5. 插入业绩信息
        if (factoryAdd.getPerformanceList() != null) {
            for (FactoryPerformance performance : factoryAdd.getPerformanceList()) {
                performance.setFactoryId(factoryId);
                performance.setCreateTime(DateUtils.getNowDate());
                factoryPerformanceMapper.insertFactoryPerformance(performance);
            }
        }

        return rows;
    }

    /**
     * 修改入驻工厂
     *
     * @param settledFactory 入驻工厂
     * @return 结果
     */
    @Override
    public int updateSettledFactory(SettledFactory settledFactory)
    {
        settledFactory.setUpdateTime(DateUtils.getNowDate());
        return settledFactoryMapper.updateSettledFactory(settledFactory);
    }

    /**
     * 批量删除入驻工厂
     *
     * @param ids 需要删除的入驻工厂主键
     * @return 结果
     */
    @Override
    public int deleteSettledFactoryByIds(Long[] ids)
    {
        return settledFactoryMapper.deleteSettledFactoryByIds(ids);
    }

    /**
     * 删除入驻工厂信息
     *
     * @param id 入驻工厂主键
     * @return 结果
     */
    @Override
    public int deleteSettledFactoryById(Long id)
    {
        return settledFactoryMapper.deleteSettledFactoryById(id);
    }

    /**
     * 新增入驻工厂
     *
     * @param settledFactory 入驻工厂
     * @return 结果
     */
    @Override
    public int insertSettledFactory(SettledFactory settledFactory)
    {
        settledFactory.setCreateTime(DateUtils.getNowDate());
        return settledFactoryMapper.insertSettledFactory(settledFactory);
    }
}
