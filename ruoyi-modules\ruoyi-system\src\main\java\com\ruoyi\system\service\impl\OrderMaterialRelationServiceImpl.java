package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.OrderMaterialRelationMapper;
import com.ruoyi.system.domain.OrderMaterialRelation;
import com.ruoyi.system.service.IOrderMaterialRelationService;

/**
 * 订单物料关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
@Service
public class OrderMaterialRelationServiceImpl implements IOrderMaterialRelationService 
{
    @Autowired
    private OrderMaterialRelationMapper orderMaterialRelationMapper;

    /**
     * 查询订单物料关联
     * 
     * @param id 订单物料关联主键
     * @return 订单物料关联
     */
    @Override
    public OrderMaterialRelation selectOrderMaterialRelationById(Long id)
    {
        return orderMaterialRelationMapper.selectOrderMaterialRelationById(id);
    }

    /**
     * 查询订单物料关联列表
     * 
     * @param orderMaterialRelation 订单物料关联
     * @return 订单物料关联
     */
    @Override
    public List<OrderMaterialRelation> selectOrderMaterialRelationList(OrderMaterialRelation orderMaterialRelation)
    {
        return orderMaterialRelationMapper.selectOrderMaterialRelationList(orderMaterialRelation);
    }

    /**
     * 查询订单关联的物料列表
     * 
     * @param orderId 订单ID
     * @return 订单物料关联集合
     */
    @Override
    public List<OrderMaterialRelation> selectOrderMaterialRelationByOrderId(Long orderId)
    {
        return orderMaterialRelationMapper.selectOrderMaterialRelationByOrderId(orderId);
    }

    /**
     * 查询物料关联的订单列表
     * 
     * @param materialId 物料ID
     * @return 订单物料关联集合
     */
    @Override
    public List<OrderMaterialRelation> selectOrderMaterialRelationByMaterialId(Long materialId)
    {
        return orderMaterialRelationMapper.selectOrderMaterialRelationByMaterialId(materialId);
    }

    /**
     * 新增订单物料关联
     * 
     * @param orderMaterialRelation 订单物料关联
     * @return 结果
     */
    @Override
    public int insertOrderMaterialRelation(OrderMaterialRelation orderMaterialRelation)
    {
        return orderMaterialRelationMapper.insertOrderMaterialRelation(orderMaterialRelation);
    }

    /**
     * 修改订单物料关联
     * 
     * @param orderMaterialRelation 订单物料关联
     * @return 结果
     */
    @Override
    public int updateOrderMaterialRelation(OrderMaterialRelation orderMaterialRelation)
    {
        return orderMaterialRelationMapper.updateOrderMaterialRelation(orderMaterialRelation);
    }

    /**
     * 批量删除订单物料关联
     * 
     * @param ids 需要删除的订单物料关联主键
     * @return 结果
     */
    @Override
    public int deleteOrderMaterialRelationByIds(Long[] ids)
    {
        return orderMaterialRelationMapper.deleteOrderMaterialRelationByIds(ids);
    }

    /**
     * 删除订单物料关联信息
     * 
     * @param id 订单物料关联主键
     * @return 结果
     */
    @Override
    public int deleteOrderMaterialRelationById(Long id)
    {
        return orderMaterialRelationMapper.deleteOrderMaterialRelationById(id);
    }

    /**
     * 删除订单的所有物料关联
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    @Override
    public int deleteOrderMaterialRelationByOrderId(Long orderId)
    {
        return orderMaterialRelationMapper.deleteOrderMaterialRelationByOrderId(orderId);
    }

    /**
     * 删除物料的所有订单关联
     * 
     * @param materialId 物料ID
     * @return 结果
     */
    @Override
    public int deleteOrderMaterialRelationByMaterialId(Long materialId)
    {
        return orderMaterialRelationMapper.deleteOrderMaterialRelationByMaterialId(materialId);
    }
}
