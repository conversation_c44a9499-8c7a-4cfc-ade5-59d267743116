package com.ruoyi.portalweb.mapper;


import com.ruoyi.portalweb.api.domain.PolicySubmitRecord;

import java.util.List;

/**
 * 政策申报记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface PolicySubmitRecordMapper 
{
    /**
     * 查询政策申报记录
     * 
     * @param policySubmitRecordId 政策申报记录主键
     * @return 政策申报记录
     */
    public PolicySubmitRecord selectPolicySubmitRecordByPolicySubmitRecordId(Long policySubmitRecordId);

    /**
     * 查询政策申报记录列表
     * 
     * @param policySubmitRecord 政策申报记录
     * @return 政策申报记录集合
     */
    public List<PolicySubmitRecord> selectPolicySubmitRecordList(PolicySubmitRecord policySubmitRecord);

    /**
     * 新增政策申报记录
     * 
     * @param policySubmitRecord 政策申报记录
     * @return 结果
     */
    public int insertPolicySubmitRecord(PolicySubmitRecord policySubmitRecord);

    /**
     * 修改政策申报记录
     * 
     * @param policySubmitRecord 政策申报记录
     * @return 结果
     */
    public int updatePolicySubmitRecord(PolicySubmitRecord policySubmitRecord);

    /**
     * 删除政策申报记录
     * 
     * @param policySubmitRecordId 政策申报记录主键
     * @return 结果
     */
    public int deletePolicySubmitRecordByPolicySubmitRecordId(Long policySubmitRecordId);

    /**
     * 批量删除政策申报记录
     * 
     * @param policySubmitRecordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePolicySubmitRecordByPolicySubmitRecordIds(Long[] policySubmitRecordIds);
}
