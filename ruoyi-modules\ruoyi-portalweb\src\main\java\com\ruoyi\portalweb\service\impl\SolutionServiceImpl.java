package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.RemoteMemberService;
import com.ruoyi.portalweb.api.domain.*;
import com.ruoyi.portalweb.mapper.SolutionAdvantageMapper;
import com.ruoyi.portalweb.mapper.SolutionCaseMapper;
import com.ruoyi.portalweb.mapper.SolutionMapper;
import com.ruoyi.portalweb.mapper.SolutionPainMapper;
import com.ruoyi.portalweb.service.IMemberService;
import com.ruoyi.portalweb.service.ISolutionService;
import com.ruoyi.portalweb.vo.MemberVO;
import com.ruoyi.portalweb.vo.SolutionVO;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.ruoyi.common.core.utils.PageUtils.startPage;

/**
 * 解决方案Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class SolutionServiceImpl implements ISolutionService {
    @Autowired
    private SolutionMapper solutionMapper;
    @Autowired
    private SolutionAdvantageMapper solutionAdvantageMapper;
    @Autowired
    private SolutionCaseMapper solutionCaseMapper;
    @Autowired
    private SolutionPainMapper solutionPainMapper;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询解决方案
     *
     * @param solutionId 解决方案主键
     * @return 解决方案
     */
    @Override
    public SolutionVO selectSolutionBySolutionId(Long solutionId) {
        SolutionVO solutionVO = solutionMapper.selectSolutionBySolutionId(solutionId);
        if (solutionVO != null && solutionVO.getSolutionId() != null) {
            List<SolutionAdvantage> lstSolutionAdvantage = solutionAdvantageMapper.selectListBySolutionId(solutionId);
            solutionVO.setAlSolutionAdvantageVOs(lstSolutionAdvantage);
            List<SolutionCase> lstSolutionCase = solutionCaseMapper.selectListBySolutionId(solutionId);
            solutionVO.setAlSolutionCaseVOs(lstSolutionCase);
            List<SolutionPain> lstSolutionPain = solutionPainMapper.selectListBySolutionId(solutionId);
            solutionVO.setAlSolutionPainVOs(lstSolutionPain);
        }
        return solutionVO;
    }

    /**
     * 查询解决方案列表
     *
     * @param solution 解决方案
     * @return 解决方案
     */
    @Override
    public List<SolutionVO> selectSolutionList(SolutionVO solution) {
        solution.setSolutionStatus("0");
        return solutionMapper.selectSolutionList(solution);
    }

    /**
     * 新增解决方案
     *
     * @param solution 解决方案
     * @return 结果
     */
    @Override
    public int insertSolution(Solution solution) {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        solution.setUpdateBy(userNickName.getData());
        solution.setCreateBy(userNickName.getData());
        solution.setMemberId(SecurityUtils.getUserId());
        solution.setCreateTime(DateUtils.getNowDate());
        return solutionMapper.insertSolution(solution);
    }

    /**
     * 修改解决方案
     *
     * @param solution 解决方案
     * @return 结果
     */
    @Override
    public int updateSolution(Solution solution) {
        solution.setUpdateTime(DateUtils.getNowDate());
        return solutionMapper.updateSolution(solution);
    }

    /**
     * 批量删除解决方案
     *
     * @param solutionIds 需要删除的解决方案主键
     * @return 结果
     */
    @Override
    public int deleteSolutionBySolutionIds(Long[] solutionIds) {
        return solutionMapper.deleteSolutionBySolutionIds(solutionIds);
    }

    /**
     * 删除解决方案信息
     *
     * @param solutionId 解决方案主键
     * @return 结果
     */
    @Override
    public int deleteSolutionBySolutionId(Long solutionId) {
        return solutionMapper.deleteSolutionBySolutionId(solutionId);
    }

    /**
     * 查询解决方案列表首页用
     */
    @Override
    public Map<String, Object> selectSolutionToDesk() {
        List<SolutionVO> list = solutionMapper.selectSolutionToDesk();

        Map<String, Object> resMap = new HashMap<>();
        Map<String, List<SolutionVO>> mapCategory1 = new HashMap<>();
        Map<String, List<SolutionVO>> mapCategory2 = new HashMap<>();
        for (SolutionVO item : list) {
            List<SolutionVO> thisList = new ArrayList<>();
            if ("1".equals(item.getCategory())) {
                if (mapCategory1.containsKey(item.getSolutionTypeName())) {
                    thisList = mapCategory1.get(item.getSolutionTypeName());
                    thisList.add(item);
                } else {
                    thisList.add(item);
                }
                mapCategory1.put(item.getSolutionTypeName(), thisList);
            } else if ("2".equals(item.getCategory())) {
                if (mapCategory2.containsKey(item.getSolutionTypeName())) {
                    thisList = mapCategory2.get(item.getSolutionTypeName());
                    thisList.add(item);
                } else {
                    thisList.add(item);
                }
                mapCategory2.put(item.getSolutionTypeName(), thisList);
            }
        }
        resMap.put("行业解决方案", mapCategory1);
        resMap.put("领域解决方案", mapCategory2);
        return resMap;
    }

    @Override
    public List<SolutionVO> listDeskCompanyRelated(Long companyRelatedId) {
        Member m = new Member();
        m.setCompanyRelatedId(companyRelatedId);
        List<MemberVO> memberVOS = memberService.selectMemberList(m);
        List<Long> memberIds = new ArrayList<>();
        for (MemberVO memberVO : memberVOS) {
            memberIds.add(memberVO.getMemberId());
        }

        if(memberIds.isEmpty()){
            return new ArrayList<>();
        }
        startPage();
        PageUtils.setOrderBy("a.create_time DESC");
        List<SolutionVO> list = solutionMapper.selectSolutionListByMemberIds(memberIds);
        if (list.isEmpty()) {
            return new ArrayList<>();
        };
        List <Long> solutionIds = new ArrayList<>();
        for (SolutionVO item : list) {
            solutionIds.add(item.getSolutionId());
        }
        List<SolutionPain> solutionPains = solutionPainMapper.selectSolutionPainListBySolutionIds(solutionIds);
        List<SolutionAdvantage> solutionAdvantages = solutionAdvantageMapper.selectSolutionAdvantageListBySolutionIds(solutionIds);
        List<SolutionCase> solutionCases = solutionCaseMapper.selectSolutionCaseListBySolutionIds(solutionIds);
        for (SolutionVO item : list) {
            List<SolutionPain> pains = new ArrayList<>();
            List<SolutionCase> cases = new ArrayList<>();
            List<SolutionAdvantage> advantages = new ArrayList<>();
            for (SolutionPain itemPain : solutionPains) {
                if (item.getSolutionId().equals(itemPain.getSolutionId())) {
                    pains.add(itemPain);
                }
            }
            for (SolutionCase itemCase : solutionCases) {
                if (item.getSolutionId().equals(itemCase.getSolutionId())) {
                    cases.add(itemCase);
                }
            }
            for (SolutionAdvantage itemAdv : solutionAdvantages) {
                if (item.getSolutionId().equals(itemAdv.getSolutionId())) {
                    advantages.add(itemAdv);
                }
            }
            item.setAlSolutionAdvantageVOs(advantages);
            item.setAlSolutionCaseVOs(cases);
            item.setAlSolutionPainVOs(pains);
        }
        return list;
    }

    @Override
    public List<SolutionVO> selectRecommendSolutionList(Solution solution) {
        // 获取用户关键信息
        MemberVO memberVO = memberService.selectMemberByMemberId(SecurityUtils.getUserId());
        if (memberVO.getSolutionTypeName() == null || Objects.equals(memberVO.getSolutionTypeName(), "")) {
            return null;
        }
        startPage();
        PageUtils.setOrderBy("a.create_time DESC");
        SolutionVO solutionVO = new SolutionVO();
        solutionVO.setSolutionTypeId(memberVO.getSolutionTypeId());
        return solutionMapper.selectSolutionList(solutionVO);
    }
}
