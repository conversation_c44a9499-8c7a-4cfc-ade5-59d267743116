package com.ruoyi.portalconsole.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 政策申报对象 policy_submit
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public class PolicySubmit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 政策申报ID */
    private Long policySubmitId;

    /** 发布单位：业务字典 */
    @Excel(name = "发布单位：业务字典")
    private String policySubmitUnit;

    /** 政策类型：业务字典 */
    @Excel(name = "政策类型：业务字典")
    private String policySubmitType;

    /** 政策标题 */
    @Excel(name = "政策标题")
    private String policySubmitTitle;

    /** 截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date policySubmitEndDate;

    /** 最高奖励 */
    @Excel(name = "最高奖励")
    private String policySubmitReward;

    /** 封面 */
    @Excel(name = "封面")
    private String policySubmitImg;

    /** 条款内容 */
    @Excel(name = "条款内容")
    private String policySubmitContent;

    /** 状态 */
    @Excel(name = "状态")
    private String policySubmitStatus;

    /** 政策标签 */
    @Excel(name = "政策标签")
    private String policyLabel;

    /** 企业标签 */
    @Excel(name = "企业标签")
    private String companyLabel;

    /** 推荐 */
    @Excel(name = "推荐")
    private Boolean recommend;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 会员id */
    @Excel(name = "会员id")
    private Long memberId;

    public void setPolicySubmitId(Long policySubmitId) 
    {
        this.policySubmitId = policySubmitId;
    }

    public Long getPolicySubmitId() 
    {
        return policySubmitId;
    }
    public void setPolicySubmitUnit(String policySubmitUnit) 
    {
        this.policySubmitUnit = policySubmitUnit;
    }

    public String getPolicySubmitUnit() 
    {
        return policySubmitUnit;
    }
    public void setPolicySubmitType(String policySubmitType) 
    {
        this.policySubmitType = policySubmitType;
    }

    public String getPolicySubmitType() 
    {
        return policySubmitType;
    }
    public void setPolicySubmitTitle(String policySubmitTitle) 
    {
        this.policySubmitTitle = policySubmitTitle;
    }

    public String getPolicySubmitTitle() 
    {
        return policySubmitTitle;
    }
    public void setPolicySubmitEndDate(Date policySubmitEndDate) 
    {
        this.policySubmitEndDate = policySubmitEndDate;
    }

    public Date getPolicySubmitEndDate() 
    {
        return policySubmitEndDate;
    }
    public void setPolicySubmitReward(String policySubmitReward) 
    {
        this.policySubmitReward = policySubmitReward;
    }

    public String getPolicySubmitReward() 
    {
        return policySubmitReward;
    }
    public void setPolicySubmitImg(String policySubmitImg) 
    {
        this.policySubmitImg = policySubmitImg;
    }

    public String getPolicySubmitImg() 
    {
        return policySubmitImg;
    }
    public void setPolicySubmitContent(String policySubmitContent) 
    {
        this.policySubmitContent = policySubmitContent;
    }

    public String getPolicySubmitContent() 
    {
        return policySubmitContent;
    }
    public void setPolicySubmitStatus(String policySubmitStatus) 
    {
        this.policySubmitStatus = policySubmitStatus;
    }

    public String getPolicySubmitStatus() 
    {
        return policySubmitStatus;
    }
    public void setPolicyLabel(String policyLabel)
    {
        this.policyLabel = policyLabel;
    }

    public String getPolicyLabel()
    {
        return policyLabel;
    }
    public void setCompanyLabel(String companyLabel)
    {
        this.companyLabel = companyLabel;
    }

    public String getCompanyLabel()
    {
        return companyLabel;
    }
    public void setRecommend(Boolean recommend)
    {
        this.recommend = recommend;
    }

    public Boolean getRecommend()
    {
        return recommend;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setMemberId(Long memberId)
    {
        this.memberId = memberId;
    }

    public Long getMemberId()
    {
        return memberId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("policySubmitId", getPolicySubmitId())
            .append("policySubmitUnit", getPolicySubmitUnit())
            .append("policySubmitType", getPolicySubmitType())
            .append("policySubmitTitle", getPolicySubmitTitle())
            .append("policySubmitEndDate", getPolicySubmitEndDate())
            .append("policySubmitReward", getPolicySubmitReward())
            .append("policySubmitImg", getPolicySubmitImg())
            .append("policySubmitContent", getPolicySubmitContent())
            .append("policySubmitStatus", getPolicySubmitStatus())
            .append("policyLabel", getPolicyLabel())
            .append("companyLabel", getCompanyLabel())
            .append("recommend", getRecommend())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("memberId", getMemberId())
            .toString();
    }
}
