package com.ruoyi.portalconsole.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 应用商店对象 app_store
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
public class AppStore extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 应用商店ID
     */
    @ApiModelProperty(value = "应用商店ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long appStoreId;

    /**
     * 公司ID
     */
    @Excel(name = "公司ID")
    @ApiModelProperty(value = "公司ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long companyId;

    /**
     * 应用名称
     */
    @Excel(name = "应用名称")
    @ApiModelProperty(value = "应用名称")
    private String appStoreName;

    /**
     * 应用类型：业务字典app_store_type
     */
    @Excel(name = "应用类型：业务字典app_store_type")
    @ApiModelProperty(value = "应用类型：业务字典app_store_type")
    private String appStoreType;

    /**
     * 简介
     */
    @Excel(name = "简介")
    @ApiModelProperty(value = "简介")
    private String appStoreIntroduction;

    /**
     * 详情
     */
    @Excel(name = "详情")
    @ApiModelProperty(value = "详情 ")
    private String appStoreContent;

    /**
     * 封面
     */
    @Excel(name = "封面")
    @ApiModelProperty(value = "封面")
    private String appStoreImg;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String appStoreContactsName;

    /**
     * 联系方式
     */
    @Excel(name = "联系方式")
    @ApiModelProperty(value = "联系方式")
    private String appStoreContactsPhone;

    /**
     * 售价
     */
    @Excel(name = "售价")
    @ApiModelProperty(value = "售价")
    private BigDecimal appStorePrice;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    private String delFlag;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long memberId;

    @ApiModelProperty(value = "便签")
    private String appLabel;
    @ApiModelProperty(value = "订单数量")
    private Integer appStoreOrderTotal;
    @ApiModelProperty(value = "推荐状态")
    private Integer recommend;
    @ApiModelProperty(value = "交付方式")
    private String deliveryMethod;
    @ApiModelProperty(value = "排序")
    private Integer sort;
//    @ApiModelProperty(value = "应用地址(交付方式-SaaS使用时填写)")
//    private String erweima;
//    @ApiModelProperty(value = "下载服务(交付方式-下载服务时填写)")
//    private String downloadUrl;
    @ApiModelProperty(value = "应用提供")
    private String supply;
    @ApiModelProperty(value = "审核状态")
    private String auditStatus;
    @ApiModelProperty(value = "上下架状态")
    private String onShow;
    @ApiModelProperty(value = "应用服务端")
    private String appServer;

    public String getAppServer() {return appServer;}

    public void setAppServer(String appServer) {this.appServer = appServer;}

    public String getAuditStatus() {return auditStatus;}

    public void setAuditStatus(String auditStatus) {this.auditStatus = auditStatus;}

    public String getOnShow() {return onShow;}

    public void setOnShow(String onShow) {this.onShow = onShow;}

//    public String getErweima() {
//        return erweima;
//    }
//
//    public void setErweima(String erweima) {
//        this.erweima = erweima;
//    }
//
//    public String getDownloadUrl() {
//        return downloadUrl;
//    }
//
//    public void setDownloadUrl(String downloadUrl) {
//        this.downloadUrl = downloadUrl;
//    }

    public String getAppLabel() {
        return appLabel;
    }

    public void setAppLabel(String appLabel) {
        this.appLabel = appLabel;
    }

    public Integer getAppStoreOrderTotal() {
        return appStoreOrderTotal;
    }

    public void setAppStoreOrderTotal(Integer appStoreOrderTotal) {
        this.appStoreOrderTotal = appStoreOrderTotal;
    }

    public Integer getRecommend() {
        return recommend;
    }

    public void setRecommend(Integer recommend) {
        this.recommend = recommend;
    }

    public String getDeliveryMethod() {
        return deliveryMethod;
    }

    public void setDeliveryMethod(String deliveryMethod) {
        this.deliveryMethod = deliveryMethod;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public void setAppStoreId(Long appStoreId) {
        this.appStoreId = appStoreId;
    }

    public Long getAppStoreId() {
        return appStoreId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setAppStoreName(String appStoreName) {
        this.appStoreName = appStoreName;
    }

    public String getAppStoreName() {
        return appStoreName;
    }

    public void setAppStoreType(String appStoreType) {
        this.appStoreType = appStoreType;
    }

    public String getAppStoreType() {
        return appStoreType;
    }

    public void setAppStoreIntroduction(String appStoreIntroduction) {
        this.appStoreIntroduction = appStoreIntroduction;
    }

    public String getAppStoreIntroduction() {
        return appStoreIntroduction;
    }

    public void setAppStoreContent(String appStoreContent) {
        this.appStoreContent = appStoreContent;
    }

    public String getAppStoreContent() {
        return appStoreContent;
    }

    public void setAppStoreImg(String appStoreImg) {
        this.appStoreImg = appStoreImg;
    }

    public String getAppStoreImg() {
        return appStoreImg;
    }

    public void setAppStoreContactsName(String appStoreContactsName) {
        this.appStoreContactsName = appStoreContactsName;
    }

    public String getAppStoreContactsName() {
        return appStoreContactsName;
    }

    public void setAppStoreContactsPhone(String appStoreContactsPhone) {
        this.appStoreContactsPhone = appStoreContactsPhone;
    }

    public String getAppStoreContactsPhone() {
        return appStoreContactsPhone;
    }

    public void setAppStorePrice(BigDecimal appStorePrice) {
        this.appStorePrice = appStorePrice;
    }

    public BigDecimal getAppStorePrice() {
        return appStorePrice;
    }

    public String getSupply() {return supply;}

    public void setSupply(String supply) {this.supply = supply;}

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getDelFlag() {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).
                append("appStoreId", appStoreId).
                append("companyId", companyId).
                append("appStoreName", appStoreName).
                append("appStoreType", appStoreType).
                append("appStoreIntroduction", appStoreIntroduction).
                append("appStoreContent", appStoreContent).
                append("appStoreImg", appStoreImg).
                append("appStoreContactsName", appStoreContactsName).
                append("appStoreContactsPhone", appStoreContactsPhone).
                append("appStorePrice", appStorePrice).
                append("delFlag", delFlag).
                append("memberId", memberId).
                append("appLabel", appLabel).
                append("appStoreOrderTotal", appStoreOrderTotal).
                append("recommend", recommend).
                append("deliveryMethod", deliveryMethod).
                append("sort", sort).
                append("supply", supply).toString();
    }
}
