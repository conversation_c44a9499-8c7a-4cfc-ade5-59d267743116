package com.ruoyi.portalconsole.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalconsole.domain.vo.PolicySubmitRecordVO;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.PolicySubmitRecord;
import com.ruoyi.portalconsole.service.IPolicySubmitRecordService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 政策申报记录Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/PolicySubmitRecord")
public class PolicySubmitRecordController extends BaseController
{
    @Autowired
    private IPolicySubmitRecordService policySubmitRecordService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询政策申报记录列表
     */
    @RequiresPermissions("portalconsole:PolicySubmitRecord:list")
    @GetMapping("/list")
    public TableDataInfo list(PolicySubmitRecord policySubmitRecord)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<PolicySubmitRecordVO> list = policySubmitRecordService.selectPolicySubmitRecordList(policySubmitRecord);
        return getDataTable(list);
    }

    /**
     * 导出政策申报记录列表
     */
    @RequiresPermissions("portalconsole:PolicySubmitRecord:export")
    @Log(title = "政策申报记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PolicySubmitRecord policySubmitRecord)
    {
        List<PolicySubmitRecordVO> list = policySubmitRecordService.selectPolicySubmitRecordList(policySubmitRecord);
        ExcelUtil<PolicySubmitRecordVO> util = new ExcelUtil<PolicySubmitRecordVO>(PolicySubmitRecordVO.class);
        util.exportExcel(response, list, "政策申报记录数据");
    }

    /**
     * 获取政策申报记录详细信息
     */
    @RequiresPermissions("portalconsole:PolicySubmitRecord:query")
    @GetMapping(value = "/{policySubmitRecordId}")
    public AjaxResult getInfo(@PathVariable("policySubmitRecordId") Long policySubmitRecordId)
    {
        return success(policySubmitRecordService.selectPolicySubmitRecordByPolicySubmitRecordId(policySubmitRecordId));
    }

    /**
     * 新增政策申报记录
     */
    @RequiresPermissions("portalconsole:PolicySubmitRecord:add")
    @Log(title = "政策申报记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PolicySubmitRecord policySubmitRecord)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        policySubmitRecord.setUpdateBy(userNickName.getData());
        policySubmitRecord.setCreateBy(userNickName.getData());
        return toAjax(policySubmitRecordService.insertPolicySubmitRecord(policySubmitRecord));
    }

    /**
     * 修改政策申报记录
     */
    @RequiresPermissions("portalconsole:PolicySubmitRecord:edit")
    @Log(title = "政策申报记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PolicySubmitRecord policySubmitRecord)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        policySubmitRecord.setUpdateBy(userNickName.getData());
        return toAjax(policySubmitRecordService.updatePolicySubmitRecord(policySubmitRecord));
    }

    /**
     * 删除政策申报记录
     */
    @RequiresPermissions("portalconsole:PolicySubmitRecord:remove")
    @Log(title = "政策申报记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{policySubmitRecordIds}")
    public AjaxResult remove(@PathVariable Long[] policySubmitRecordIds)
    {
        return toAjax(policySubmitRecordService.deletePolicySubmitRecordByPolicySubmitRecordIds(policySubmitRecordIds));
    }
}
