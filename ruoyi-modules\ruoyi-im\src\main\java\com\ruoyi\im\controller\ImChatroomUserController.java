package com.ruoyi.im.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.im.api.domain.ImChatroomUser;
import com.ruoyi.im.service.ImChatroomUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/im/chatroomuser")
public class ImChatroomUserController {

    @Resource
    private ImChatroomUserService imChatroomUserService;

    /***
     * ImChatroom分页条件搜索实现
     * @param imChatroomUser
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/search/{page}/{size}" )
    public TableDataInfo findPage(@RequestBody(required = false)  ImChatroomUser imChatroomUser, @PathVariable("page")  int page, @PathVariable("size")  int size, @RequestParam(value = "fields",required = false) String fields){
        Page<ImChatroomUser> pageSearch = new Page<>(page,size);
        QueryWrapper<ImChatroomUser> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(imChatroomUser.getChatroomId())){
            wrapper.eq("chatroomId",imChatroomUser.getChatroomId());
        }
        if(StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        imChatroomUserService.page(pageSearch,wrapper);
        TableDataInfo tableDataInfo = new TableDataInfo(pageSearch.getRecords(), (int) pageSearch.getTotal());
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setMsg("success");
        return tableDataInfo;
    }

    /**
     * 数量
     * @param imChatroomUser
     * @return
     */
    @GetMapping(value = "/count")
    public R<Integer> findCount(@RequestBody(required = false) ImChatroomUser imChatroomUser){
        QueryWrapper<ImChatroomUser> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(imChatroomUser.getChatroomId())){
            wrapper.eq("chatroomId",imChatroomUser.getChatroomId());
        }
        return R.ok(imChatroomUserService.count(wrapper)) ;
    }


    /**
     * 用户统计
     * @param ids
     * @return
     */
    @GetMapping(value = "/number")
    public R<Map<String, Integer>> findUser(@RequestParam("ids") String ids){
        return R.ok(imChatroomUserService.findUser(ids)) ;
    }

    /***
     * 多条件搜索数据
     * @param imChatroomUser
     * @return
     */
    @PostMapping(value = "/search" )
    public R<List<ImChatroomUser>> findList(@RequestBody(required = false) ImChatroomUser imChatroomUser, @RequestParam(value = "fields",required = false) String fields){
        QueryWrapper<ImChatroomUser> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(imChatroomUser.getChatroomId())){
            wrapper.eq("chatroomId",imChatroomUser.getChatroomId());
        }
        if(StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        return R.ok(imChatroomUserService.list(wrapper)) ;
    }

    /***
     * 修改ImChatroom数据
     * @param imChatroomUser
     * @return
     */
    @PostMapping(value="/{id}")
    public R<Boolean> update(@RequestBody ImChatroomUser imChatroomUser, @PathVariable("id") Long id){
        if(imChatroomUserService.updateById(imChatroomUser)){
            return R.ok(true) ;
        }
        return R.ok(false) ;
    }

    /***
     * 新增ImChatroom数据
     * @param imChatroomUser
     * @return
     */
    @PostMapping
    public R<Long> add(@RequestBody ImChatroomUser imChatroomUser){
        if(imChatroomUserService.save(imChatroomUser)){
            return R.ok(imChatroomUser.getId()) ;
        }
        return R.ok(-1L) ;
    }
}
