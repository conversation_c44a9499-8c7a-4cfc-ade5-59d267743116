<template>
  <!-- 服务需求 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="需求名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入需求名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="需求类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择需求类型" clearable>
          <el-option
            v-for="dict in dict.type.demand_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="企业名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布人" prop="contact">
        <el-input
          v-model="queryParams.contact"
          placeholder="请输入发布人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布电话" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入发布电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="应用领域" prop="applicationArea">
        <el-select v-model="queryParams.applicationArea" placeholder="请选择应用领域">
          <el-option v-for="dict in applicationFieldList" :key="dict.applicationFieldId" :label="dict.applicationFieldName"
            :value="dict.applicationFieldId"></el-option>
        </el-select>
        
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="queryParams.auditStatus" placeholder="请选择审核状态" clearable>
          <el-option
            v-for="dict in dict.type.demand_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态" prop="processStatus">
        <el-select v-model="queryParams.processStatus" placeholder="请选择处理状态" clearable>
          <el-option
            v-for="dict in dict.type.process_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="对接人" prop="platformContact">
        <el-input
          v-model="queryParams.platformContact"
          placeholder="请输入对接人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="联系人" prop="contact">
        <el-input
          v-model="queryParams.contact"
          placeholder="请输入联系人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="联系电话" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="招标类型" prop="bidType">
        <el-select v-model="queryParams.bidType" placeholder="请选择招标类型" clearable>
          <el-option
            v-for="dict in dict.type.bid_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="招标开始时间" prop="bidStartTime">
        <el-date-picker clearable
          v-model="queryParams.bidStartTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择招标开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="招标结束时间" prop="bidEndTime">
        <el-date-picker clearable
          v-model="queryParams.bidEndTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择招标结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="截止时间" prop="bidDeadline">
        <el-date-picker clearable
          v-model="queryParams.bidDeadline"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择截止时间">
        </el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="显示状态" prop="onShow">
        <el-select v-model="queryParams.onShow" placeholder="请选择显示状态" clearable>
          <el-option
            v-for="dict in dict.type.demand_show"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="自动发布" prop="autoPublish">
        <el-select v-model="queryParams.autoPublish" placeholder="请选择自动发布" clearable>
          <el-option
            v-for="dict in dict.type.auto_publish"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:demand:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['portalconsole:demand:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:demand:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:demand:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="demandList" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="创建日期" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="需求类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.demand_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="应用领域" align="center" prop="applicationAreaName" >
      </el-table-column>
      <el-table-column label="需求描述" align="center" prop="description" />
      <el-table-column label="企业名称" align="center" prop="companyName" />
      <el-table-column label="发布人" align="center" prop="contact" />
      <el-table-column label="发布人电话" align="center" prop="phone" />
      <!-- <el-table-column label="联系人" align="center" prop="contact" />
      <el-table-column label="联系电话" align="center" prop="phone" /> -->
      <el-table-column label="平台对接人" align="center" prop="platformContact" />
      <el-table-column label="处理状态" align="center" prop="processStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.process_status" :value="scope.row.processStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="auditStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.demand_status" :value="scope.row.auditStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="上架状态" align="center" prop="onShow">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.demand_show" :value="scope.row.onShow"/>
        </template>
      </el-table-column>
      
      <!-- <el-table-column label="产品照片" align="center" prop="imageUrl" />
      <el-table-column label="招标类型" align="center" prop="bidType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.bid_type" :value="scope.row.bidType"/>
        </template>
      </el-table-column>
      <el-table-column label="定向企业" align="center" prop="targetCompany" />
      <el-table-column label="招标开始时间" align="center" prop="bidStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.bidStartTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="招标结束时间" align="center" prop="bidEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.bidEndTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="截止时间" align="center" prop="bidDeadline" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.bidDeadline, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="附件" align="center" prop="attchement" />
      <el-table-column label="展示限制" align="center" prop="visible" />
      <el-table-column label="推荐状态" align="center" prop="recommend">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.recommend_status" :value="scope.row.recommend"/>
        </template>
      </el-table-column>
      <el-table-column label="意向企业" align="center" prop="prospectiveCorp" />
      <el-table-column label="自动发布" align="center" prop="autoPublish">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.auto_publish" :value="scope.row.autoPublish"/>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createdBy" />
      <el-table-column label="更新人" align="center" prop="updatedBy" />
      <el-table-column label="更新时间" align="center" prop="updatedTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updatedTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> 
      <el-table-column label="${comment}" align="center" prop="remark" />-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <!-- <el-button size="mini" type="text" icon="el-icon-view" @click="handleDetail(scope.row)">详情</el-button> -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:demand:edit']"
          >修改</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleSetup(scope.row)"
            v-hasPermi="['portalconsole:demand:edit']"
          >设置需求方</el-button> -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:demand:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改服务需求对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="需求类型" prop="type">
          <el-select v-model="form.type"    clearable placeholder="请选择需求类型" style="width: 400px;">
            <el-option
              v-for="dict in dict.type.demand_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="企业名称"  prop="companyName">
          <el-input v-model="form.companyName" clearable placeholder="请输入企业名称" />
        </el-form-item>
        
        <el-form-item label="需求标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入需求标题" clearable/>
        </el-form-item>
        <el-form-item label="发布人" prop="contact">
          <el-input v-model="form.contact" placeholder="请输入发布人" clearable/>
        </el-form-item>
        <!-- <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话" clearable/>
        </el-form-item> -->
        <el-form-item label="需求概述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入需求概述" clearable/>
        </el-form-item>
        <el-form-item label="应用领域" prop="applicationAreas">
          <el-select v-model="applicationAreas" placeholder="请选择应用领域" multiple>
          <el-option 
            v-for="dict in applicationFieldList" 
            :key="dict.applicationFieldId" 
            :label="dict.applicationFieldName"
            :value="dict.applicationFieldId" clearable >
          </el-option>
        </el-select>
          <!-- <el-input v-model="form.applicationArea" placeholder="请输入应用领域" clearable/> -->
        </el-form-item>
        <el-form-item label="产品图片" prop="imageUrl">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :http-request="uploadFun"
            :before-upload="beforeAvatarUpload">
            <el-image
              v-if="form.imageUrl"
              :src="form.imageUrl"
              class="avatar"
            >
              <div slot="error" class="image-slot">
                <span>暂无图片</span>
              </div>
            </el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="附件" prop="attachment">
          <!-- <el-input v-model="form.attachment" type="textarea" placeholder="请输入内容" /> -->
          <el-upload class="upload-demo" drag :file-list='fileAddList' :limit='10'
              action=""  
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :http-request="uploadFile" >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
        </el-form-item>
        <!-- <el-form-item label="招标类型" prop="bidType">
          <el-select v-model="form.bidType" placeholder="请选择招标类型">
            <el-option
              v-for="dict in dict.type.bid_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item> 
        <el-form-item label="定向企业" prop="targetCompany">
          <el-input v-model="form.targetCompany" placeholder="请输入定向企业" />
        </el-form-item>
        <el-form-item label="招标开始时间" prop="bidStartTime">
          <el-date-picker clearable
            v-model="form.bidStartTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择招标开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="招标结束时间" prop="bidEndTime">
          <el-date-picker clearable
            v-model="form.bidEndTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择招标结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="截止时间" prop="bidDeadline">
          <el-date-picker clearable
            v-model="form.bidDeadline"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择截止时间">
          </el-date-picker>
        </el-form-item>-->
        <!-- <el-form-item label="发布人" prop="publisher">
          <el-input v-model="form.createdBy" :disabled="true" placeholder="请输入发布人" clearable/>
        </el-form-item> -->
        <el-form-item label="发布人电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入发布人电话" clearable/>
        </el-form-item>
        <el-form-item label="平台对接人" prop="platformContact">
          <el-input v-model="form.platformContact" placeholder="请输入平台对接人" clearable/>
        </el-form-item>
        <el-row :span="24">
          <el-col :span="6">
            <el-form-item label="审核状态" prop="auditStatus">
              <el-select v-model="form.auditStatus" placeholder="请选择审核状态" clearable>
                <el-option
                  v-for="dict in dict.type.demand_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="显示状态" prop="onShow">
              <el-select v-model="form.onShow" placeholder="请选择显示状态" clearable>
                <el-option
                  v-for="dict in dict.type.demand_show"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="推荐状态" prop="recommend">
              <el-select v-model="form.recommend" placeholder="请选择推荐状态" clearable>
                <el-option
                  v-for="dict in dict.type.recommend_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select> 
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="处理状态" prop="processStatus">
              <el-select v-model="form.processStatus" placeholder="请选择处理状态" clearable>
                <el-option
                  v-for="dict in dict.type.process_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="意向企业" prop="prospectiveCorp">
          <el-input v-model="form.prospectiveCorp" placeholder="请输入意向企业" />
        </el-form-item>
        <el-form-item label="自动发布" prop="autoPublish">
          <el-select v-model="form.autoPublish" placeholder="请选择自动发布">
            <el-option
              v-for="dict in dict.type.auto_publish"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建人" prop="createdBy">
          <el-input v-model="form.createdBy" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createdTime">
          <el-date-picker clearable
            v-model="form.createdTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="更新人" prop="updatedBy">
          <el-input v-model="form.updatedBy" placeholder="请输入更新人" />
        </el-form-item>
        <el-form-item label="更新时间" prop="updatedTime">
          <el-date-picker clearable
            v-model="form.updatedTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择更新时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="${comment}" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" v-loading="load">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 设置需求方弹窗 -->
    <setupDialog ref="setupDialog"></setupDialog>
    <!-- 详情弹窗 -->
    <!-- <detailDialog ref="detailDialog"></detailDialog> -->
  </div>

</template>

<script>
import { listDemand, getDemand, delDemand, addDemand, updateDemand } from "@/api/portalconsole/demand";
import setupDialog from "./components/setupDialog"
import {comUpload} from "@/api/portalconsole/uploadApi";
// import detailDialog from "./components/detailDialog.vue";
import { listApplicationField } from "@/api/portalconsole/applicationField";
export default {
  name: "Demand",
  components: {
    setupDialog,
    // detailDialog
},
  dicts: ['demand_type', 'bid_type', 'process_status', 'recommend_status', 'demand_show', 'auto_publish', 'demand_status','application_area'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 服务需求(NEW)表格数据
      demandList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null,
        contact: null,
        phone: null,
        title: null,
        type: null,
        applicationArea: null,
        bidType: null,
        bidStartTime: null,
        bidEndTime: null,
        bidDeadline: null,
        // publisher: null,
        // publisherPhone: null,
        platformContact: null,
        auditStatus: null,
        processStatus: null,
        onShow: null,
        autoPublish: null,
        
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        companyName: [
          { required: true, message: "企业名称不能为空", trigger: "blur" }
        ],
        contact: [
          { required: true, message: "联系人不能为空", trigger: "blur" }
        ],
        phone: [
        {
              required: true,
              message: "联系电话不能为空",
              trigger: "blur"
            },
            {
              pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
              message: "请输入正确的手机号码",
              trigger: "blur"
            }
        ],
        title: [
          { required: true, message: "需求标题不能为空", trigger: "blur" }
        ],
        visible: [
          { required: true, message: "展示限制不能为空", trigger: "blur" }
        ],
        // recommend: [
        //   { required: true, message: "推荐状态不能为空", trigger: "change" }
        // ],
        // processStatus: [
        //   { required: true, message: "处理状态不能为空", trigger: "change" }
        // ],
        auditStatus:[
          { required: true, message: "审核状态不能为空", trigger: "change" }
        ],
        onShow: [
          { required: true, message: "显示状态不能为空", trigger: "change" }
        ],
        autoPublish: [
          { required: true, message: "自动发布不能为空", trigger: "change" }
        ],
        // createdBy: [
        //   { required: true, message: "创建人不能为空", trigger: "blur" }
        // ],
        // createdTime: [
        //   { required: true, message: "创建时间不能为空", trigger: "blur" }
        // ],
        // updatedBy: [
        //   { required: true, message: "更新人不能为空", trigger: "blur" }
        // ],
        description:[
          { required: true, message: "需求概述不能为空", trigger: "blur" }
        ],
      },
      dialogVisible: false,
      dialogImageUrl: '',
      types:[],
      applicationAreas:[],
      //附件
      fileAddList:[],
      load:false,
      applicationFieldList:[]
    };
  },
  created() {
    this.getList();
    console.log(this.$store.state)
    this.getapp()
  },
  methods: {
    /** 查询服务需求(NEW)列表 */
    getList() {
      this.loading = true;
      listDemand(this.queryParams).then(response => {
        this.demandList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      console.log("用户",this.$store.state.user.name)
      this.form = {
        id: null,
        companyName: null,
        contact: null,
        phone: null,
        title: null,
        type: null,
        description: null,
        applicationArea: null,
        imageUrl: null,
        bidType: null,
        targetCompany: null,
        bidStartTime: null,
        bidEndTime: null,
        bidDeadline: null,
        attchement: null,
        // publisher: null,
        // publisherPhone: null,
        platformContact: null,
        auditStatus: null,
        visible: null,
        recommend: null,
        processStatus: null,
        onShow: null,
        prospectiveCorp: null,
        autoPublish: null,
        // createdBy: this.$store.state.user.name,
        // createdTime: null,
        // updatedBy: null,
        // updatedTime: null,
        remark: null,
        
      };
      this.types=[]
      this.applicationAreas=[]
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加需求";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      
      getDemand(id).then(response => {
        // this.types=response.data.type
        // this.types=this.types.split(",")
        // console.log("this.types",this.types)

        this.applicationAreas=response.data.applicationArea
        this.applicationAreas=this.applicationAreas.split(",")
        this.applicationAreas= this.applicationAreas.map(item => Number(item))
        console.log("this.applicationAreas1",this.applicationAreas)
        this.form = response.data;
        this.form.auditStatus= parseInt(this.form.auditStatus)
       // this.form.applicationArea=parseInt(this.form.applicationArea)
        this.open = true;
        this.title = "修改需求";
        this.getapp()
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.load=true
      this.$refs["form"].validate(valid => {
        
          // if(this.types.length>0){
          //   this.form.type = this.types.join(',')
          // }
          console.log("this.applicationAreas",this.applicationAreas)
          if(this.applicationAreas.length>0){
            this.form.applicationArea = this.applicationAreas.join(',')
          }
          
        if (valid) {
          if (this.form.id != null) {
            
            updateDemand(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.load=false
            });
          } else {
            addDemand(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.load=false
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除服务需求编号为"' + ids + '"的数据项？').then(function() {
        return delDemand(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/demand/export', {
        ...this.queryParams
      }, `demand_${new Date().getTime()}.xlsx`)
    },
    
    
      clearList() {
        this.fileList = []
        this.fileAddList = []
        this.applications = []
        this.types = []
        this.applicationAreas=[]
        this.keywords = []
      },
      // 设置需求方
      handleSetup(row){
        this.$refs.setupDialog.show()
      },
      // 详情
    handleDetail(row){
      this.$router.push('/portalconsole/DemandDetail');
    },
    // 图片上传
    uploadFun(params) {
        const file = params.file;
        let form = new FormData();
        form.append("file", file); // 文件对象
        comUpload(form).then(res => {
          let data = res.data;
          this.$set(this.form,'imageUrl',data.fileFullPath)  // 图片全路径
          // this.$set(this.form,'imageUrl',data.fileId) // 图片Id
        })
      },
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/jpeg' || 'image/png' || 'image/jpg';
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isJPG) {
          this.$message.error('上传图片只能是 jpg、jpeg、png 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传头像图片大小不能超过 2MB!');
        }
        return isJPG && isLt2M;
      },
      //附件上传
      handleRemove(file, fileList) {
        this.fileAddList.splice(this.fileAddList.findIndex(item => item.id == file.id), 1)
        this.$set(this.form,'attachment',JSON.stringify(this.fileAddList))  // 图片全路径
      },
      handlePreview(file) {
        console.log(file);
      },
      beforeRemove(file, fileList) {
        return this.$confirm(`确定移除 ${ file.name }？`);
      },
      uploadFile(params){
        const file = params.file;
        let form = new FormData();
        form.append("file", file); // 文件对象
        comUpload(form).then(res => {
          let data = res.data;
          console.log("data",data)
          this.fileAddList.push({
            name:data.fileName,
            id:data.fileId,
            fileUrl:data.fileFullPath
          })
          this.$set(this.form,'attachment',JSON.stringify(this.fileAddList))  // 图片全路径
        })
      },
      //应用领域
      getapp(){
        listApplicationField().then(response => {
          this.applicationFieldList = response.rows;
          
        });
      }
  }
};
</script>
<style  scoped>
>>>.el-table .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
    margin-left: 5px;
}
.avatar-uploader >>> .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
/deep/.el-table .cell{
  height: 26px;
}
</style>
