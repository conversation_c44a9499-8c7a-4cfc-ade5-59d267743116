package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.AppStoreOrder;
import com.ruoyi.portalweb.service.IAppStoreOrderService;
import com.ruoyi.portalweb.vo.AppStoreOrderVO;
import com.ruoyi.system.api.RemoteUserService;

import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * 应用商店订单Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/AppStoreOrder")
public class AppStoreOrderController extends BaseController
{
    @Autowired
    private IAppStoreOrderService appStoreOrderService;

    @Autowired
    private RemoteUserService remoteUserService;


    /**
     * 查询应用商店订单列表
     */
    @GetMapping("/list")
    public TableDataInfo list(AppStoreOrderVO appStoreOrder)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        if (Objects.equals(appStoreOrder.getType(), "sale")){
            appStoreOrder.setSaleMemberId(SecurityUtils.getUserId());
        }else if(Objects.equals(appStoreOrder.getType(), "buy")){
            appStoreOrder.setBuyMemberId(SecurityUtils.getUserId());
        }
        List<AppStoreOrderVO> list = appStoreOrderService.selectAppStoreOrderList(appStoreOrder);
        return getDataTable(list);
    }

    /**
     * 查询应用商店订单列表
     */
    @GetMapping("/count")
    public AjaxResult count(AppStoreOrderVO appStoreOrder)
    {
        return AjaxResult.success(appStoreOrderService.selectAppStoreOrderCount(appStoreOrder));
    }

    /**
     * 导出应用商店订单列表
     */
    @Log(title = "应用商店订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppStoreOrder appStoreOrder)
    {
        List<AppStoreOrderVO> list = appStoreOrderService.selectAppStoreOrderList(appStoreOrder);
        ExcelUtil<AppStoreOrderVO> util = new ExcelUtil<AppStoreOrderVO>(AppStoreOrderVO.class);
        util.exportExcel(response, list, "应用商店订单数据");
    }

    /**
     * 获取应用商店订单详细信息
     */
    @GetMapping(value = "/{appStoreOrderId}")
    public AjaxResult getInfo(@PathVariable("appStoreOrderId") Long appStoreOrderId)
    {
        return success(appStoreOrderService.selectAppStoreOrderByAppStoreOrderId(appStoreOrderId));
    }
    
    /**
     * 获取应用商店订单详细信息
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取服务需求(NEW)详细信息", notes = "传入")
    public AjaxResult detail(AppStoreOrder appStoreOrder) {
    	return success(appStoreOrderService.selectAppStoreOrderByAppStoreOrderId(appStoreOrder.getAppStoreOrderId()));
    }
    
    /**
     * 获取应用商店订单详细信息
     */
    @GetMapping("/detailDesk")
    @ApiOperation(value = "获取服务需求(NEW)详细信息", notes = "传入")
    public AjaxResult detailDesk(AppStoreOrder appStoreOrder) {
    	return success(appStoreOrderService.selectAppStoreOrderByAppStoreOrderId(appStoreOrder.getAppStoreOrderId()));
    }

    /**
     * 新增应用商店订单
     */
    @Log(title = "应用商店订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppStoreOrder appStoreOrder)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        appStoreOrder.setUpdateBy(userNickName.getData());
        appStoreOrder.setCreateBy(userNickName.getData());
        return AjaxResult.success(appStoreOrderService.insertAppStoreOrder(appStoreOrder));
    }

    /**
     * 修改应用商店订单
     */
    @Log(title = "应用商店订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppStoreOrder appStoreOrder)
    {
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        appStoreOrder.setUpdateBy(userNickName.getData());
        return toAjax(appStoreOrderService.updateAppStoreOrder(appStoreOrder));
    }

    /**
     * 修改应用商店订单发票状态
     */
    @Log(title = "应用商店订单", businessType = BusinessType.UPDATE)
    @PutMapping("/invoice")
    public AjaxResult invoice(@RequestBody AppStoreOrder appStoreOrder)
    {
        return toAjax(appStoreOrderService.updateAppStoreOrderInvoice(appStoreOrder));
    }

    /**
     * 删除应用商店订单
     */
    @Log(title = "应用商店订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{appStoreOrderIds}")
    public AjaxResult remove(@PathVariable Long[] appStoreOrderIds)
    {
        return toAjax(appStoreOrderService.deleteAppStoreOrderByAppStoreOrderIds(appStoreOrderIds));
    }
}
