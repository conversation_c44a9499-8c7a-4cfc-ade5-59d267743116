package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysSupplierInfoMapper;
import com.ruoyi.system.domain.SysSupplierInfo;
import com.ruoyi.system.service.ISysSupplierInfoService;

/**
 * 供方信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
public class SysSupplierInfoServiceImpl implements ISysSupplierInfoService 
{
    @Autowired
    private SysSupplierInfoMapper sysSupplierInfoMapper;

    /**
     * 查询供方信息
     * 
     * @param supplierId 供方信息主键
     * @return 供方信息
     */
    @Override
    public SysSupplierInfo selectSysSupplierInfoBySupplierId(Long supplierId)
    {
        return sysSupplierInfoMapper.selectSysSupplierInfoBySupplierId(supplierId);
    }

    /**
     * 查询供方信息列表
     * 
     * @param sysSupplierInfo 供方信息
     * @return 供方信息
     */
    @Override
    public List<SysSupplierInfo> selectSysSupplierInfoList(SysSupplierInfo sysSupplierInfo)
    {
        return sysSupplierInfoMapper.selectSysSupplierInfoList(sysSupplierInfo);
    }

    /**
     * 新增供方信息
     * 
     * @param sysSupplierInfo 供方信息
     * @return 结果
     */
    @Override
    public int insertSysSupplierInfo(SysSupplierInfo sysSupplierInfo)
    {
        sysSupplierInfo.setCreateTime(DateUtils.getNowDate());
        return sysSupplierInfoMapper.insertSysSupplierInfo(sysSupplierInfo);
    }

    /**
     * 修改供方信息
     * 
     * @param sysSupplierInfo 供方信息
     * @return 结果
     */
    @Override
    public int updateSysSupplierInfo(SysSupplierInfo sysSupplierInfo)
    {
        sysSupplierInfo.setUpdateTime(DateUtils.getNowDate());
        return sysSupplierInfoMapper.updateSysSupplierInfo(sysSupplierInfo);
    }

    /**
     * 批量删除供方信息
     * 
     * @param supplierIds 需要删除的供方信息主键
     * @return 结果
     */
    @Override
    public int deleteSysSupplierInfoBySupplierIds(Long[] supplierIds)
    {
        return sysSupplierInfoMapper.deleteSysSupplierInfoBySupplierIds(supplierIds);
    }

    /**
     * 删除供方信息信息
     * 
     * @param supplierId 供方信息主键
     * @return 结果
     */
    @Override
    public int deleteSysSupplierInfoBySupplierId(Long supplierId)
    {
        return sysSupplierInfoMapper.deleteSysSupplierInfoBySupplierId(supplierId);
    }
}
