<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TalentSettleProcessMapper">
    
    <resultMap type="TalentSettleProcess" id="TalentSettleProcessResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="birthDate"    column="birth_date"    />
        <result property="graduateSchool"    column="graduate_school"    />
        <result property="currentCompany"    column="current_company"    />
        <result property="position"    column="position"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="location"    column="location"    />
        <result property="education"    column="education"    />
        <result property="workStatus"    column="work_status"    />
        <result property="jobTitle"    column="job_title"    />
        <result property="positionType"    column="position_type"    />
        <result property="technicalField"    column="technical_field"    />
        <result property="photo"    column="photo"    />
        <result property="introduction"    column="introduction"    />
        <result property="attachments"    column="attachments"    />
        <result property="currentStep"    column="current_step"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditComment"    column="audit_comment"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditor"    column="auditor"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTalentSettleProcessVo">
        select id, name, birth_date, graduate_school, current_company, position, contact_phone, location, education, work_status, job_title, position_type, technical_field, photo, introduction, attachments, current_step, audit_status, audit_comment, audit_time, auditor, create_time, update_time from talent_settle_process
    </sql>

    <select id="selectTalentSettleProcessList" parameterType="TalentSettleProcess" resultMap="TalentSettleProcessResult">
        <include refid="selectTalentSettleProcessVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="birthDate != null "> and birth_date = #{birthDate}</if>
            <if test="graduateSchool != null  and graduateSchool != ''"> and graduate_school = #{graduateSchool}</if>
            <if test="currentCompany != null  and currentCompany != ''"> and current_company = #{currentCompany}</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="location != null  and location != ''"> and location = #{location}</if>
            <if test="education != null  and education != ''"> and education = #{education}</if>
            <if test="workStatus != null  and workStatus != ''"> and work_status = #{workStatus}</if>
            <if test="jobTitle != null  and jobTitle != ''"> and job_title = #{jobTitle}</if>
            <if test="positionType != null  and positionType != ''"> and position_type = #{positionType}</if>
            <if test="technicalField != null  and technicalField != ''"> and technical_field = #{technicalField}</if>
            <if test="photo != null  and photo != ''"> and photo = #{photo}</if>
            <if test="introduction != null  and introduction != ''"> and introduction = #{introduction}</if>
            <if test="attachments != null  and attachments != ''"> and attachments = #{attachments}</if>
            <if test="currentStep != null "> and current_step = #{currentStep}</if>
            <if test="auditStatus != null "> and audit_status = #{auditStatus}</if>
            <if test="auditComment != null  and auditComment != ''"> and audit_comment = #{auditComment}</if>
            <if test="auditTime != null "> and audit_time = #{auditTime}</if>
            <if test="auditor != null  and auditor != ''"> and auditor = #{auditor}</if>
        </where>
    </select>
    
    <select id="selectTalentSettleProcessById" parameterType="Long" resultMap="TalentSettleProcessResult">
        <include refid="selectTalentSettleProcessVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTalentSettleProcess" parameterType="TalentSettleProcess" useGeneratedKeys="true" keyProperty="id">
        insert into talent_settle_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="birthDate != null">birth_date,</if>
            <if test="graduateSchool != null">graduate_school,</if>
            <if test="currentCompany != null">current_company,</if>
            <if test="position != null">position,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="location != null">location,</if>
            <if test="education != null and education != ''">education,</if>
            <if test="workStatus != null and workStatus != ''">work_status,</if>
            <if test="jobTitle != null">job_title,</if>
            <if test="positionType != null and positionType != ''">position_type,</if>
            <if test="technicalField != null">technical_field,</if>
            <if test="photo != null">photo,</if>
            <if test="introduction != null">introduction,</if>
            <if test="attachments != null">attachments,</if>
            <if test="currentStep != null">current_step,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditComment != null">audit_comment,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditor != null">auditor,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="birthDate != null">#{birthDate},</if>
            <if test="graduateSchool != null">#{graduateSchool},</if>
            <if test="currentCompany != null">#{currentCompany},</if>
            <if test="position != null">#{position},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="location != null">#{location},</if>
            <if test="education != null and education != ''">#{education},</if>
            <if test="workStatus != null and workStatus != ''">#{workStatus},</if>
            <if test="jobTitle != null">#{jobTitle},</if>
            <if test="positionType != null and positionType != ''">#{positionType},</if>
            <if test="technicalField != null">#{technicalField},</if>
            <if test="photo != null">#{photo},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="currentStep != null">#{currentStep},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditComment != null">#{auditComment},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditor != null">#{auditor},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTalentSettleProcess" parameterType="TalentSettleProcess">
        update talent_settle_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="birthDate != null">birth_date = #{birthDate},</if>
            <if test="graduateSchool != null">graduate_school = #{graduateSchool},</if>
            <if test="currentCompany != null">current_company = #{currentCompany},</if>
            <if test="position != null">position = #{position},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="location != null">location = #{location},</if>
            <if test="education != null and education != ''">education = #{education},</if>
            <if test="workStatus != null and workStatus != ''">work_status = #{workStatus},</if>
            <if test="jobTitle != null">job_title = #{jobTitle},</if>
            <if test="positionType != null and positionType != ''">position_type = #{positionType},</if>
            <if test="technicalField != null">technical_field = #{technicalField},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditComment != null">audit_comment = #{auditComment},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditor != null">auditor = #{auditor},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTalentSettleProcessById" parameterType="Long">
        delete from talent_settle_process where id = #{id}
    </delete>

    <delete id="deleteTalentSettleProcessByIds" parameterType="String">
        delete from talent_settle_process where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>