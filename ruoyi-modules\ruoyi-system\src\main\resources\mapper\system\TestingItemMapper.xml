<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TestingItemMapper">
    
    <resultMap type="TestingItem" id="TestingItemResult">
        <result property="id"    column="id"    />
        <result property="itemName"    column="item_name"    />
        <result property="testingDetails"    column="testing_details"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
    
    <resultMap type="LaboratoryInfo" id="LaboratoryInfoResult">
        <result property="id"    column="lab_id"    />
        <result property="labName"    column="lab_name"    />
        <result property="labType"    column="lab_type"    />
        <result property="labAddress"    column="lab_address"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="labIntroduction"    column="lab_introduction"    />
        <result property="labImages"    column="lab_images"    />
        <result property="testingScope"    column="testing_scope"    />
        <result property="qualifications"    column="qualifications"    />
        <result property="cnasQualification"    column="cnas_qualification"    />
        <result property="status"    column="lab_status"    />
        <result property="createTime"    column="lab_create_time"    />
        <result property="updateTime"    column="lab_update_time"    />
    </resultMap>
    
    <resultMap type="LabTestingRelation" id="LabTestingRelationResult">
        <result property="id"    column="relation_id"    />
        <result property="labId"    column="lab_id"    />
        <result property="testingId"    column="testing_id"    />
        <result property="price"    column="price"    />
        <result property="cycle"    column="cycle"    />
        <result property="remark"    column="relation_remark"    />
        <result property="createTime"    column="relation_create_time"    />
        <result property="updateTime"    column="relation_update_time"    />
    </resultMap>

    <sql id="selectTestingItemVo">
        select id, item_name, testing_details, status, create_time, update_time from testing_item
    </sql>

    <select id="selectTestingItemList" parameterType="TestingItem" resultMap="TestingItemResult">
        <include refid="selectTestingItemVo"/>
        <where>  
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="testingDetails != null  and testingDetails != ''"> and testing_details = #{testingDetails}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectTestingItemById" parameterType="Long" resultMap="TestingItemResult">
        <include refid="selectTestingItemVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTestingItem" parameterType="TestingItem" useGeneratedKeys="true" keyProperty="id">
        insert into testing_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="testingDetails != null">testing_details,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="testingDetails != null">#{testingDetails},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTestingItem" parameterType="TestingItem">
        update testing_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="testingDetails != null">testing_details = #{testingDetails},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTestingItemById" parameterType="Long">
        delete from testing_item where id = #{id}
    </delete>

    <delete id="deleteTestingItemByIds" parameterType="String">
        delete from testing_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectLabsByTestingId" parameterType="Long" resultMap="LaboratoryInfoResult">
        select 
            l.id as lab_id, 
            l.lab_name, 
            l.lab_type, 
            l.lab_address, 
            l.contact_phone, 
            l.lab_introduction, 
            l.lab_images, 
            l.testing_scope, 
            l.qualifications, 
            l.cnas_qualification, 
            l.status as lab_status, 
            l.create_time as lab_create_time, 
            l.update_time as lab_update_time
        from laboratory_info l
        inner join lab_testing_relation r on l.id = r.lab_id
        where r.testing_id = #{testingId}
        and l.status = '0'
    </select>
    
    <select id="selectRelationsByTestingId" parameterType="Long" resultMap="LabTestingRelationResult">
        select 
            id as relation_id, 
            lab_id, 
            testing_id, 
            price, 
            cycle, 
            remark as relation_remark, 
            create_time as relation_create_time, 
            update_time as relation_update_time
        from lab_testing_relation
        where testing_id = #{testingId}
    </select>
    
    <select id="selectTestingItemLeftJoinLabs" parameterType="String" resultMap="TestingItemWithLabsResult">
        select 
            t.id, 
            t.item_name, 
            t.testing_details, 
            t.status, 
            t.create_time, 
            t.update_time,
            l.id as lab_id, 
            l.lab_name, 
            l.lab_type, 
            l.lab_address, 
            l.contact_phone, 
            l.lab_introduction, 
            l.lab_images, 
            l.testing_scope, 
            l.qualifications, 
            l.cnas_qualification, 
            l.status as lab_status, 
            l.create_time as lab_create_time, 
            l.update_time as lab_update_time,
            r.id as relation_id, 
            r.lab_id, 
            r.testing_id, 
            r.price, 
            r.cycle, 
            r.remark as relation_remark, 
            r.create_time as relation_create_time, 
            r.update_time as relation_update_time
        from testing_item t
        left join lab_testing_relation r on t.id = r.testing_id
        left join laboratory_info l on r.lab_id = l.id
        <where>
            <if test="labType != null and labType != ''">
                and l.lab_type = #{labType}
            </if>
            and t.status = '0'
        </where>
        order by t.create_time desc
    </select>
    
    <resultMap type="TestingItemWithLabsDTO" id="TestingItemWithLabsResult">
        <association property="testingItem" javaType="TestingItem">
            <id property="id" column="id" />
            <result property="itemName" column="item_name" />
            <result property="testingDetails" column="testing_details" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
        </association>
        <collection property="labs" ofType="LaboratoryInfo">
            <id property="id" column="lab_id" />
            <result property="labName" column="lab_name" />
            <result property="labType" column="lab_type" />
            <result property="labAddress" column="lab_address" />
            <result property="contactPhone" column="contact_phone" />
            <result property="labIntroduction" column="lab_introduction" />
            <result property="labImages" column="lab_images" />
            <result property="testingScope" column="testing_scope" />
            <result property="qualifications" column="qualifications" />
            <result property="cnasQualification" column="cnas_qualification" />
            <result property="status" column="lab_status" />
            <result property="createTime" column="lab_create_time" />
            <result property="updateTime" column="lab_update_time" />
        </collection>
        <collection property="labTestingRelations" ofType="LabTestingRelation">
            <id property="id" column="relation_id" />
            <result property="labId" column="lab_id" />
            <result property="testingId" column="testing_id" />
            <result property="price" column="price" />
            <result property="cycle" column="cycle" />
            <result property="remark" column="relation_remark" />
            <result property="createTime" column="relation_create_time" />
            <result property="updateTime" column="relation_update_time" />
        </collection>
    </resultMap>
</mapper>