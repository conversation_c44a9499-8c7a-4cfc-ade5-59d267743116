package com.ruoyi.portalweb.service;

import com.ruoyi.portalweb.api.domain.SolutionAdvantage;

import java.util.List;

/**
 * 解决方案方案优势Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface ISolutionAdvantageService 
{
    /**
     * 查询解决方案方案优势
     * 
     * @param solutionAdvantageId 解决方案方案优势主键
     * @return 解决方案方案优势
     */
    public SolutionAdvantage selectSolutionAdvantageBySolutionAdvantageId(Long solutionAdvantageId);

    /**
     * 查询解决方案方案优势列表
     * 
     * @param solutionAdvantage 解决方案方案优势
     * @return 解决方案方案优势集合
     */
    public List<SolutionAdvantage> selectSolutionAdvantageList(SolutionAdvantage solutionAdvantage);

    /**
     * 新增解决方案方案优势
     * 
     * @param solutionAdvantage 解决方案方案优势
     * @return 结果
     */
    public int insertSolutionAdvantage(SolutionAdvantage solutionAdvantage);

    /**
     * 修改解决方案方案优势
     * 
     * @param solutionAdvantage 解决方案方案优势
     * @return 结果
     */
    public int updateSolutionAdvantage(SolutionAdvantage solutionAdvantage);

    /**
     * 批量删除解决方案方案优势
     * 
     * @param solutionAdvantageIds 需要删除的解决方案方案优势主键集合
     * @return 结果
     */
    public int deleteSolutionAdvantageBySolutionAdvantageIds(Long[] solutionAdvantageIds);

    /**
     * 删除解决方案方案优势信息
     * 
     * @param solutionAdvantageId 解决方案方案优势主键
     * @return 结果
     */
    public int deleteSolutionAdvantageBySolutionAdvantageId(Long solutionAdvantageId);
}
