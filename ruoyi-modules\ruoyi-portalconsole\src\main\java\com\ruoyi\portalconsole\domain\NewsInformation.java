package com.ruoyi.portalconsole.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 动态资讯对象 news_information
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public class NewsInformation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 动态资讯ID */
    private Long newsInformationId;

    /** 咨询板块ID */
    @Excel(name = "咨询板块ID")
    private Long newsInformationPlateId;

    /** 方案类型ID */
    @Excel(name = "方案类型ID")
    private Long solutionTypeId;

    /** 资讯来源：业务字典 */
    @Excel(name = "资讯来源：业务字典")
    private String newsInformationSource;

    /** 作者 */
    @Excel(name = "作者")
    private String newsInformationAuthor;

    /** 名称 */
    @Excel(name = "名称")
    private String newsInformationName;

    /** 简介内容 */
    @Excel(name = "简介内容")
    private String newsInformationIntroduction;

    /** 封面 */
    @Excel(name = "封面")
    private String newsInformationImg;

    /** 资讯内容 */
    @Excel(name = "资讯内容")
    private String newsInformationContent;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Long newsInformationFrequency;

    /** 是否置顶 */
    @Excel(name = "是否置顶")
    private String top;

    /** 发布日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发布日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date newsInformationDate;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setNewsInformationId(Long newsInformationId) 
    {
        this.newsInformationId = newsInformationId;
    }

    public Long getNewsInformationId() 
    {
        return newsInformationId;
    }
    public void setNewsInformationPlateId(Long newsInformationPlateId)
    {
        this.newsInformationPlateId = newsInformationPlateId;
    }

    public Long getNewsInformationPlateId()
    {
        return newsInformationPlateId;
    }
    public void setSolutionTypeId(Long solutionTypeId)
    {
        this.solutionTypeId = solutionTypeId;
    }

    public Long getSolutionTypeId()
    {
        return solutionTypeId;
    }
    public void setNewsInformationSource(String newsInformationSource) 
    {
        this.newsInformationSource = newsInformationSource;
    }

    public String getNewsInformationSource() 
    {
        return newsInformationSource;
    }
    public void setNewsInformationAuthor(String newsInformationAuthor) 
    {
        this.newsInformationAuthor = newsInformationAuthor;
    }

    public String getNewsInformationAuthor() 
    {
        return newsInformationAuthor;
    }
    public void setNewsInformationName(String newsInformationName) 
    {
        this.newsInformationName = newsInformationName;
    }

    public String getNewsInformationName() 
    {
        return newsInformationName;
    }
    public void setNewsInformationIntroduction(String newsInformationIntroduction) 
    {
        this.newsInformationIntroduction = newsInformationIntroduction;
    }

    public String getNewsInformationIntroduction() 
    {
        return newsInformationIntroduction;
    }
    public void setNewsInformationImg(String newsInformationImg) 
    {
        this.newsInformationImg = newsInformationImg;
    }

    public String getNewsInformationImg() 
    {
        return newsInformationImg;
    }
    public void setNewsInformationContent(String newsInformationContent) 
    {
        this.newsInformationContent = newsInformationContent;
    }

    public String getNewsInformationContent() 
    {
        return newsInformationContent;
    }
    public void setNewsInformationFrequency(Long newsInformationFrequency) 
    {
        this.newsInformationFrequency = newsInformationFrequency;
    }

    public Long getNewsInformationFrequency() 
    {
        return newsInformationFrequency;
    }
    public void setNewsInformationDate(Date newsInformationDate) 
    {
        this.newsInformationDate = newsInformationDate;
    }

    public Date getNewsInformationDate() 
    {
        return newsInformationDate;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public String getTop() {
        return top;
    }

    public void setTop(String top) {
        this.top = top;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("newsInformationId", getNewsInformationId())
            .append("newsInformationPlateId", getNewsInformationPlateId())
            .append("solutionTypeId", getSolutionTypeId())
            .append("newsInformationSource", getNewsInformationSource())
            .append("newsInformationAuthor", getNewsInformationAuthor())
            .append("newsInformationName", getNewsInformationName())
            .append("newsInformationIntroduction", getNewsInformationIntroduction())
            .append("newsInformationImg", getNewsInformationImg())
            .append("newsInformationContent", getNewsInformationContent())
            .append("newsInformationFrequency", getNewsInformationFrequency())
            .append("newsInformationDate", getNewsInformationDate())
            .append("top", getTop())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
