<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.AppConfigMapper">
    
    <resultMap type="AppConfig" id="AppConfigResult">
        <result property="appConfigId"    column="app_config_id"    />
        <result property="appStoreId"    column="app_store_id"    />
        <result property="ip"    column="ip"    />
        <result property="appWebUrl"    column="app_web_url"    />
        <result property="appWebTrialUrl"    column="app_web_trial_url"    />
        <result property="healthInspectionUrl"    column="health_inspection_url"    />
        <result property="contact"    column="contact"    />
        <result property="testToken"    column="test_token"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="erweima" column="erweima"/>
        <result property="downloadUrl" column="download_url"/>
    </resultMap>

    <sql id="selectAppConfigVo">
        select app_config_id, app_store_id, ip, app_web_url, app_web_trial_url, health_inspection_url, contact, test_token, create_by, create_time, update_by, update_time, remark,
               erweima, download_url from app_config
    </sql>

    <select id="selectAppConfigList" parameterType="AppConfig" resultMap="AppConfigResult">
        <include refid="selectAppConfigVo"/>
        <where>  
            <if test="appStoreId != null "> and app_store_id = #{appStoreId}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="appWebUrl != null  and appWebUrl != ''"> and app_web_url like concat('%', #{appWebUrl}, '%')</if>
            <if test="appWebTrialUrl != null  and appWebTrialUrl != ''"> and app_web_trial_url like concat('%', #{appWebTrialUrl}, '%')</if>
            <if test="healthInspectionUrl != null  and healthInspectionUrl != ''"> and health_inspection_url like concat('%', #{healthInspectionUrl}, '%')</if>
            <if test="contact != null  and contact != ''"> and contact like concat('%', #{contact}, '%')</if>
            <if test="testToken != null  and testToken != ''"> and test_token = #{testToken}</if>
        </where>
    </select>
    
    <select id="selectAppConfigByAppConfigId" parameterType="Long" resultMap="AppConfigResult">
        <include refid="selectAppConfigVo"/>
        where app_config_id = #{appConfigId}
    </select>
    <select id="selectAppConfigByAppStoreId" parameterType="Long" resultMap="AppConfigResult">
        <include refid="selectAppConfigVo"/>
        where app_store_id = #{appStoreId}
    </select>

    <insert id="insertAppConfig" parameterType="AppConfig"  useGeneratedKeys="true" keyProperty="appConfigId">
        insert into app_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appConfigId != null">app_config_id,</if>
            <if test="appStoreId != null">app_store_id,</if>
            <if test="ip != null">ip,</if>
            <if test="appWebUrl != null">app_web_url,</if>
            <if test="appWebTrialUrl != null">app_web_trial_url,</if>
            <if test="healthInspectionUrl != null">health_inspection_url,</if>
            <if test="contact != null">contact,</if>
            <if test="testToken != null">test_token,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="erweima != null">erweima,</if>
            <if test="downloadUrl != null">download_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appConfigId != null">#{appConfigId},</if>
            <if test="appStoreId != null">#{appStoreId},</if>
            <if test="ip != null">#{ip},</if>
            <if test="appWebUrl != null">#{appWebUrl},</if>
            <if test="appWebTrialUrl != null">#{appWebTrialUrl},</if>
            <if test="healthInspectionUrl != null">#{healthInspectionUrl},</if>
            <if test="contact != null">#{contact},</if>
            <if test="testToken != null">#{testToken},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="erweima != null">#{erweima},</if>
            <if test="downloadUrl != null">#{downloadUrl},</if>
         </trim>
    </insert>

    <update id="updateAppConfig" parameterType="AppConfig">
        update app_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="appStoreId != null">app_store_id = #{appStoreId},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="appWebUrl != null">app_web_url = #{appWebUrl},</if>
            <if test="appWebTrialUrl != null">app_web_trial_url = #{appWebTrialUrl},</if>
            <if test="healthInspectionUrl != null">health_inspection_url = #{healthInspectionUrl},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="testToken != null">test_token = #{testToken},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="erweima != null">erweima = #{erweima},</if>
            <if test="downloadUrl != null">download_url = #{downloadUrl},</if>
        </trim>
        where app_config_id = #{appConfigId}
    </update>
    <update id="updateAppConfigByAppStoreId">
        update app_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="ip != null">ip = #{ip},</if>
            <if test="appWebUrl != null">app_web_url = #{appWebUrl},</if>
            <if test="appWebTrialUrl != null">app_web_trial_url = #{appWebTrialUrl},</if>
            <if test="healthInspectionUrl != null">health_inspection_url = #{healthInspectionUrl},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="testToken != null">test_token = #{testToken},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="erweima != null">erweima = #{erweima},</if>
            <if test="downloadUrl != null">download_url = #{downloadUrl},</if>
        </trim>
        where app_store_id = #{appStoreId}
    </update>

    <delete id="deleteAppConfigByAppConfigId" parameterType="Long">
        delete from app_config where app_config_id = #{appConfigId}
    </delete>

    <delete id="deleteAppConfigByAppConfigIds" parameterType="String">
        delete from app_config where app_config_id in
        <foreach item="appConfigId" collection="array" open="(" separator="," close=")">
            #{appConfigId}
        </foreach>
    </delete>
</mapper>