package com.ruoyi.portalweb.mapper;


import com.ruoyi.portalweb.api.domain.AppStoreOrder;
import com.ruoyi.portalweb.vo.AppStoreOrderCountVO;
import com.ruoyi.portalweb.vo.AppStoreOrderVO;

import java.util.List;

/**
 * 应用商店订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface AppStoreOrderMapper 
{
    /**
     * 查询应用商店订单
     * 
     * @param appStoreOrderId 应用商店订单主键
     * @return 应用商店订单
     */
    public AppStoreOrderVO selectAppStoreOrderByAppStoreOrderId(Long appStoreOrderId);

    /**
     * 查询应用商店订单列表
     * 
     * @param appStoreOrder 应用商店订单
     * @return 应用商店订单集合
     */
    public List<AppStoreOrderVO> selectAppStoreOrderList(AppStoreOrder appStoreOrder);

    /**
     * 新增应用商店订单
     * 
     * @param appStoreOrder 应用商店订单
     * @return 结果
     */
    public int insertAppStoreOrder(AppStoreOrder appStoreOrder);

    /**
     * 修改应用商店订单
     * 
     * @param appStoreOrder 应用商店订单
     * @return 结果
     */
    public int updateAppStoreOrder(AppStoreOrder appStoreOrder);

    public int updateAppStoreOrderByOrderNo(AppStoreOrder appStoreOrder);

    /**
     * 删除应用商店订单
     * 
     * @param appStoreOrderId 应用商店订单主键
     * @return 结果
     */
    public int deleteAppStoreOrderByAppStoreOrderId(Long appStoreOrderId);

    /**
     * 批量删除应用商店订单
     * 
     * @param appStoreOrderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppStoreOrderByAppStoreOrderIds(Long[] appStoreOrderIds);

    public AppStoreOrderCountVO selectAppStoreOrderCount(AppStoreOrderVO appStoreOrder);

    AppStoreOrderVO selectAppStoreOrderByAppStoreOrderNo(String appStoreOrderNo);
}
