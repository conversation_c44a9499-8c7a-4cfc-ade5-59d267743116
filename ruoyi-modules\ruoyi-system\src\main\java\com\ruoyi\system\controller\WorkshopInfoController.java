package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.WorkshopInfo;
import com.ruoyi.system.service.IWorkshopInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 车间信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@RestController
@RequestMapping("/workInfo")
public class WorkshopInfoController extends BaseController {
    @Autowired
    private IWorkshopInfoService workshopInfoService;

    /**
     * 查询车间信息列表
     */

    @GetMapping("/list")
    public TableDataInfo list(WorkshopInfo workshopInfo) {
        startPage();
        List<WorkshopInfo> list = workshopInfoService.selectWorkshopInfoList(workshopInfo);
        return getDataTable(list);
    }

    @GetMapping("/user/list")
    public TableDataInfo list2(WorkshopInfo workshopInfo) {
        startPage();
//        workshopInfo.setCreateBy(SecurityUtils.getLoginMember().getMemberphone());
        List<WorkshopInfo> list = workshopInfoService.selectWorkshopInfoList(workshopInfo);
        return getDataTable(list);
    }

    /**
     * 导出车间信息列表
     */

    @Log(title = "车间信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WorkshopInfo workshopInfo) {
        List<WorkshopInfo> list = workshopInfoService.selectWorkshopInfoList(workshopInfo);
        ExcelUtil<WorkshopInfo> util = new ExcelUtil<WorkshopInfo>(WorkshopInfo.class);
        util.exportExcel(response, list, "车间信息数据");
    }

    /**
     * 获取车间信息详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(workshopInfoService.selectWorkshopInfoById(id));
    }

    /**
     * 新增车间信息
     */

    @Log(title = "车间信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WorkshopInfo workshopInfo) {
        return toAjax(workshopInfoService.insertWorkshopInfo(workshopInfo));
    }

    /**
     * 修改车间信息
     */

    @Log(title = "车间信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WorkshopInfo workshopInfo) {
        return toAjax(workshopInfoService.updateWorkshopInfo(workshopInfo));
    }

    /**
     * 删除车间信息
     */

    @Log(title = "车间信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(workshopInfoService.deleteWorkshopInfoByIds(ids));
    }
}
