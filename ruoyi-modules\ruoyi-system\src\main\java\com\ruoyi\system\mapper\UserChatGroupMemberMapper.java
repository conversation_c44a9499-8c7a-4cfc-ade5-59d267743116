package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.UserChatGroupMember;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface UserChatGroupMemberMapper {
    int insertSelective(UserChatGroupMember record);

    int dismissChatGroupMember(@Param("groupId") Long groupId, @Param("userName") String userName, @Param("now") Date now);

    UserChatGroupMember selectByGroupIdAndUserName(@Param("groupId") Long groupId, @Param("userName") String userName);

    int quitChatGroup(UserChatGroupMember userChatGroupMember);

    List<UserChatGroupMember> getGroupUserList(@Param("groupId") Long groupId);

    List<UserChatGroupMember> getUserGroupList(@Param("userName") String userName);
}