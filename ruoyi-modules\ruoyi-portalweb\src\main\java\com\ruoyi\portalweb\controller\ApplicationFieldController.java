package com.ruoyi.portalweb.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalweb.api.domain.ApplicationField;
import com.ruoyi.portalweb.service.IApplicationFieldService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 应用领域Controller
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
@RestController
@RequestMapping("/applicationField")
public class ApplicationFieldController extends BaseController
{
    @Autowired
    private IApplicationFieldService applicationFieldService;

    /**
     * 查询应用领域列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ApplicationField applicationField)
    {
        startPage();
        List<ApplicationField> list = applicationFieldService.selectApplicationFieldList(applicationField);
        return getDataTable(list);
    }

    /**
     * 查询应用领域列表
     */
    @GetMapping("/listDesk")
    public TableDataInfo listDesk(ApplicationField applicationField)
    {
        List<ApplicationField> list = applicationFieldService.selectApplicationFieldList(applicationField);
        return getDataTable(list);
    }


    /**
     * 导出应用领域列表
     */
    @RequiresPermissions("portalweb:applicationField:export")
    @Log(title = "应用领域", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ApplicationField applicationField)
    {
        List<ApplicationField> list = applicationFieldService.selectApplicationFieldList(applicationField);
        ExcelUtil<ApplicationField> util = new ExcelUtil<ApplicationField>(ApplicationField.class);
        util.exportExcel(response, list, "应用领域数据");
    }

    /**
     * 获取应用领域详细信息
     */
    @RequiresPermissions("portalweb:applicationField:query")
    @GetMapping(value = "/{applicationFieldId}")
    public AjaxResult getInfo(@PathVariable("applicationFieldId") Long applicationFieldId)
    {
        return success(applicationFieldService.selectApplicationFieldByApplicationFieldId(applicationFieldId));
    }

    /**
     * 新增应用领域
     */
    @Log(title = "应用领域", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApplicationField applicationField)
    {
        return toAjax(applicationFieldService.insertApplicationField(applicationField));
    }

    /**
     * 修改应用领域
     */
    @RequiresPermissions("portalweb:applicationField:edit")
    @Log(title = "应用领域", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApplicationField applicationField)
    {
        return toAjax(applicationFieldService.updateApplicationField(applicationField));
    }

    /**
     * 删除应用领域
     */
    @RequiresPermissions("portalweb:applicationField:remove")
    @Log(title = "应用领域", businessType = BusinessType.DELETE)
	@DeleteMapping("/{applicationFieldIds}")
    public AjaxResult remove(@PathVariable Long[] applicationFieldIds)
    {
        return toAjax(applicationFieldService.deleteApplicationFieldByApplicationFieldIds(applicationFieldIds));
    }
}
