-- 工序外协需求表
CREATE TABLE `outsourcing_requirement` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '需求ID',
  `process_name` varchar(255) NOT NULL COMMENT '外协工序名称',
  `processing_quantity` int(11) DEFAULT NULL COMMENT '加工数量',
  `outsourcing_content` text COMMENT '外协加工内容',
  `required_completion_time` datetime DEFAULT NULL COMMENT '要求完成时间',
  `project_number` varchar(100) DEFAULT NULL COMMENT '工程号',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `company_name` varchar(255) DEFAULT NULL COMMENT '公司名称',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='工序外协需求表';

-- 插入测试数据
INSERT INTO `outsourcing_requirement` (`id`, `process_name`, `processing_quantity`, `outsourcing_content`, `required_completion_time`, `project_number`, `remarks`, `company_name`, `contact_person`, `contact_phone`, `status`, `create_time`, `update_time`) VALUES
(1, '碳纤维预浸料裁剪', 500, '按照图纸要求裁剪碳纤维预浸料，尺寸公差±1mm', '2025-04-15 00:00:00', 'HR-CF-2025-001', '需要提供裁剪后的材料质量证明', '恒润复合材料有限公司', '张工', '13812345678', '0', NOW(), NOW()),
(2, '模具制作', 10, '根据3D模型制作复合材料成型模具，材质为铝合金', '2025-05-01 00:00:00', 'HR-MD-2025-002', '模具表面需要抛光处理，确保脱模顺利', '先进复合材料科技有限公司', '李工', '13987654321', '0', NOW(), NOW()),
(3, '复合材料热压成型', 200, '使用提供的预浸料和模具进行热压成型，温度180℃，压力5MPa', '2025-04-20 00:00:00', 'HR-HP-2025-003', '成型后需进行超声波无损检测', '高分子材料研究所', '王研究员', '13567891234', '0', NOW(), NOW());
