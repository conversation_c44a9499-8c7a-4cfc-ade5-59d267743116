package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.LabTestingRelation;

/**
 * 实验室检测项目关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface LabTestingRelationMapper 
{
    /**
     * 查询实验室检测项目关联
     * 
     * @param id 实验室检测项目关联主键
     * @return 实验室检测项目关联
     */
    public LabTestingRelation selectLabTestingRelationById(Long id);

    /**
     * 查询实验室检测项目关联列表
     * 
     * @param labTestingRelation 实验室检测项目关联
     * @return 实验室检测项目关联集合
     */
    public List<LabTestingRelation> selectLabTestingRelationList(LabTestingRelation labTestingRelation);

    /**
     * 新增实验室检测项目关联
     * 
     * @param labTestingRelation 实验室检测项目关联
     * @return 结果
     */
    public int insertLabTestingRelation(LabTestingRelation labTestingRelation);

    /**
     * 修改实验室检测项目关联
     * 
     * @param labTestingRelation 实验室检测项目关联
     * @return 结果
     */
    public int updateLabTestingRelation(LabTestingRelation labTestingRelation);

    /**
     * 删除实验室检测项目关联
     * 
     * @param id 实验室检测项目关联主键
     * @return 结果
     */
    public int deleteLabTestingRelationById(Long id);

    /**
     * 批量删除实验室检测项目关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLabTestingRelationByIds(Long[] ids);
    
    /**
     * 根据实验室ID和检测项目ID查询关联关系
     * 
     * @param labId 实验室ID
     * @param testingId 检测项目ID
     * @return 关联关系
     */
    public LabTestingRelation selectLabTestingRelationByLabIdAndTestingId(Long labId, Long testingId);
    
    /**
     * 根据实验室ID查询关联关系
     * 
     * @param labId 实验室ID
     * @return 关联关系列表
     */
    public List<LabTestingRelation> selectLabTestingRelationByLabId(Long labId);
    
    /**
     * 根据检测项目ID查询关联关系
     * 
     * @param testingId 检测项目ID
     * @return 关联关系列表
     */
    public List<LabTestingRelation> selectLabTestingRelationByTestingId(Long testingId);
    
    /**
     * 批量新增实验室检测项目关联
     * 
     * @param labTestingRelations 实验室检测项目关联列表
     * @return 结果
     */
    public int batchInsertLabTestingRelation(List<LabTestingRelation> labTestingRelations);
    
    /**
     * 根据实验室ID删除关联关系
     * 
     * @param labId 实验室ID
     * @return 结果
     */
    public int deleteLabTestingRelationByLabId(Long labId);
    
    /**
     * 根据检测项目ID删除关联关系
     * 
     * @param testingId 检测项目ID
     * @return 结果
     */
    public int deleteLabTestingRelationByTestingId(Long testingId);
}
