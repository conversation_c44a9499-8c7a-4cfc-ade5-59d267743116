package com.ruoyi.portalweb.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalweb.mapper.PolicyInformationFeedbackMapper;
import com.ruoyi.portalweb.api.domain.PolicyInformationFeedback;
import com.ruoyi.portalweb.service.IPolicyInformationFeedbackService;

/**
 * 政策意见反馈Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-26
 */
@Service
public class PolicyInformationFeedbackServiceImpl implements IPolicyInformationFeedbackService 
{
    @Autowired
    private PolicyInformationFeedbackMapper policyInformationFeedbackMapper;

    /**
     * 查询政策意见反馈
     * 
     * @param policyInformationFeedbackId 政策意见反馈主键
     * @return 政策意见反馈
     */
    @Override
    public PolicyInformationFeedback selectPolicyInformationFeedbackByPolicyInformationFeedbackId(Long policyInformationFeedbackId)
    {
        return policyInformationFeedbackMapper.selectPolicyInformationFeedbackByPolicyInformationFeedbackId(policyInformationFeedbackId);
    }

    /**
     * 查询政策意见反馈列表
     * 
     * @param policyInformationFeedback 政策意见反馈
     * @return 政策意见反馈
     */
    @Override
    public List<PolicyInformationFeedback> selectPolicyInformationFeedbackList(PolicyInformationFeedback policyInformationFeedback)
    {
        return policyInformationFeedbackMapper.selectPolicyInformationFeedbackList(policyInformationFeedback);
    }

    /**
     * 新增政策意见反馈
     * 
     * @param policyInformationFeedback 政策意见反馈
     * @return 结果
     */
    @Override
    public int insertPolicyInformationFeedback(PolicyInformationFeedback policyInformationFeedback)
    {
        policyInformationFeedback.setCreateTime(DateUtils.getNowDate());
        return policyInformationFeedbackMapper.insertPolicyInformationFeedback(policyInformationFeedback);
    }

    /**
     * 修改政策意见反馈
     * 
     * @param policyInformationFeedback 政策意见反馈
     * @return 结果
     */
    @Override
    public int updatePolicyInformationFeedback(PolicyInformationFeedback policyInformationFeedback)
    {
        policyInformationFeedback.setUpdateTime(DateUtils.getNowDate());
        return policyInformationFeedbackMapper.updatePolicyInformationFeedback(policyInformationFeedback);
    }

    /**
     * 批量删除政策意见反馈
     * 
     * @param policyInformationFeedbackIds 需要删除的政策意见反馈主键
     * @return 结果
     */
    @Override
    public int deletePolicyInformationFeedbackByPolicyInformationFeedbackIds(Long[] policyInformationFeedbackIds)
    {
        return policyInformationFeedbackMapper.deletePolicyInformationFeedbackByPolicyInformationFeedbackIds(policyInformationFeedbackIds);
    }

    /**
     * 删除政策意见反馈信息
     * 
     * @param policyInformationFeedbackId 政策意见反馈主键
     * @return 结果
     */
    @Override
    public int deletePolicyInformationFeedbackByPolicyInformationFeedbackId(Long policyInformationFeedbackId)
    {
        return policyInformationFeedbackMapper.deletePolicyInformationFeedbackByPolicyInformationFeedbackId(policyInformationFeedbackId);
    }
}
