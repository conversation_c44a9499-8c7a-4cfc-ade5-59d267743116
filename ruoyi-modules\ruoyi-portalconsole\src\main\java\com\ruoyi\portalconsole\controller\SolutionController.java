package com.ruoyi.portalconsole.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.portalconsole.domain.vo.SolutionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.Solution;
import com.ruoyi.portalconsole.service.ISolutionService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 解决方案Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/solution")
public class SolutionController extends BaseController
{
    @Autowired
    private ISolutionService solutionService;

    /**
     * 查询解决方案列表
     */
    @RequiresPermissions("portalconsole:solution:list")
    @GetMapping("/list")
    public TableDataInfo list(Solution solution)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<SolutionVO> list = solutionService.selectSolutionList(solution);
        return getDataTable(list);
    }

    /**
     * 导出解决方案列表
     */
    @RequiresPermissions("portalconsole:solution:export")
    @Log(title = "解决方案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Solution solution)
    {
        List<SolutionVO> list = solutionService.selectSolutionList(solution);
        ExcelUtil<SolutionVO> util = new ExcelUtil<SolutionVO>(SolutionVO.class);
        util.exportExcel(response, list, "解决方案数据");
    }

    /**
     * 获取解决方案详细信息
     */
    @RequiresPermissions("portalconsole:solution:query")
    @GetMapping(value = "/{solutionId}")
    public AjaxResult getInfo(@PathVariable("solutionId") Long solutionId)
    {
        return success(solutionService.selectSolutionBySolutionId(solutionId));
    }

    /**
     * 获取解决方案详细信息
     */
    @RequiresPermissions("portalconsole:solution:queryDetail")
    @GetMapping(value = "/detail/{solutionId}")
    public AjaxResult getDetail(@PathVariable("solutionId") Long solutionId)
    {
        return success(solutionService.selectSolutionDetailBySolutionId(solutionId));
    }

    /**
     * 新增解决方案
     */
    @RequiresPermissions("portalconsole:solution:add")
    @Log(title = "解决方案", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SolutionVO solutionVO)
    {
        return toAjax(solutionService.insertSolution(solutionVO));
    }

    /**
     * 新增解决方案
     */
    @Log(title = "解决方案", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    public AjaxResult addByImport(@RequestBody SolutionVO solutionVO)
    {
        return toAjax(solutionService.insertSolution(solutionVO));
    }

    /**
     * 修改解决方案详情
     */
    @RequiresPermissions("portalconsole:solution:edit")
    @Log(title = "解决方案", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult editDetail(@RequestBody SolutionVO solutionVO)
    {
        return toAjax(solutionService.updateSolutionDetail(solutionVO));
    }

    /**
     * 删除解决方案
     */
    @RequiresPermissions("portalconsole:solution:remove")
    @Log(title = "解决方案", businessType = BusinessType.DELETE)
	@DeleteMapping("/{solutionIds}")
    public AjaxResult remove(@PathVariable Long[] solutionIds)
    {
        return toAjax(solutionService.deleteSolutionBySolutionIds(solutionIds));
    }
}
