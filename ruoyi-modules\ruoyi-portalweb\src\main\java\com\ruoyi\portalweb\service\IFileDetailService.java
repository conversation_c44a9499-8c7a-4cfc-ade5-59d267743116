package com.ruoyi.portalweb.service;

import com.ruoyi.portalweb.api.domain.FileDetail;
import com.ruoyi.portalweb.vo.FileDetailVO;

import java.util.List;

/**
 * 附件子表Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface IFileDetailService 
{
    /**
     * 查询附件子表
     * 
     * @param id 附件子表主键
     * @return 附件子表
     */
    public FileDetailVO selectFileDetailById(Long id);

    /**
     * 查询附件子表列表
     * 
     * @param fileDetail 附件子表
     * @return 附件子表集合
     */
    public List<FileDetailVO> selectFileDetailList(FileDetailVO fileDetail);

    /**
     * 新增附件子表
     * 
     * @param fileDetail 附件子表
     * @return 结果
     */
    public int insertFileDetail(FileDetail fileDetail);

    /**
     * 修改附件子表
     * 
     * @param fileDetail 附件子表
     * @return 结果
     */
    public int updateFileDetail(FileDetail fileDetail);

    /**
     * 批量删除附件子表
     * 
     * @param ids 需要删除的附件子表主键集合
     * @return 结果
     */
    public int deleteFileDetailByIds(Long[] ids);

    /**
     * 删除附件子表信息
     * 
     * @param id 附件子表主键
     * @return 结果
     */
    public int deleteFileDetailById(Long id);

    /**
	 * 根据billID，物理删除
	 */
	int removeBybillId(Long billId,String parentType, String type);

	/**
	 * 根据billid获取附件列表
	 */
	List<FileDetailVO> selectPictureList(Long parentId,String parentType, String fileUrl);
}
