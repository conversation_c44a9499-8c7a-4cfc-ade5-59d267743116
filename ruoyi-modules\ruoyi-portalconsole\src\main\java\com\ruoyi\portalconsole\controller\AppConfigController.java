package com.ruoyi.portalconsole.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.AppConfig;
import com.ruoyi.portalconsole.service.IAppConfigService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 应用配置Controller
 * 
 * <AUTHOR>
 * @date 2024-07-22
 */
@RestController
@RequestMapping("/AppConfig")
public class AppConfigController extends BaseController
{
    @Autowired
    private IAppConfigService appConfigService;

    /**
     * 查询应用配置列表
     */
    @RequiresPermissions("portalconsole:AppConfig:list")
    @GetMapping("/list")
    public TableDataInfo list(AppConfig appConfig)
    {
        startPage();
        List<AppConfig> list = appConfigService.selectAppConfigList(appConfig);
        return getDataTable(list);
    }

    /**
     * 导出应用配置列表
     */
    @RequiresPermissions("portalconsole:AppConfig:export")
    @Log(title = "应用配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppConfig appConfig)
    {
        List<AppConfig> list = appConfigService.selectAppConfigList(appConfig);
        ExcelUtil<AppConfig> util = new ExcelUtil<AppConfig>(AppConfig.class);
        util.exportExcel(response, list, "应用配置数据");
    }

    /**
     * 获取应用配置详细信息
     */
    @RequiresPermissions("portalconsole:AppConfig:query")
    @GetMapping(value = "/{appConfigId}")
    public AjaxResult getInfo(@PathVariable("appConfigId") Long appConfigId)
    {
        return success(appConfigService.selectAppConfigByAppConfigId(appConfigId));
    }

    /**
     * 新增应用配置
     */
    @RequiresPermissions("portalconsole:AppConfig:add")
    @Log(title = "应用配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppConfig appConfig)
    {
        return toAjax(appConfigService.insertAppConfig(appConfig));
    }

    /**
     * 修改应用配置
     */
    @RequiresPermissions("portalconsole:AppConfig:edit")
    @Log(title = "应用配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppConfig appConfig)
    {
        return toAjax(appConfigService.updateAppConfig(appConfig));
    }

    /**
     * 删除应用配置
     */
    @RequiresPermissions("portalconsole:AppConfig:remove")
    @Log(title = "应用配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{appConfigIds}")
    public AjaxResult remove(@PathVariable Long[] appConfigIds)
    {
        return toAjax(appConfigService.deleteAppConfigByAppConfigIds(appConfigIds));
    }
}
