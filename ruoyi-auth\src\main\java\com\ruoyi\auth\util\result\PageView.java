package com.ruoyi.auth.util.result;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;

import java.util.List;

public class PageView<T> {
	/** 分页数据 **/
	private List<T> records;
	/** 页码开始索引和结束索引 **/
	private PageIndex pageindex;
	/** 总页数 **/
	private long totalpage = 1;
	/** 每页显示记录数 **/
	private int maxresult = 12;
	/** 当前页 **/
	private int currentpage = 1;
	/** 总记录数 **/
	private long totalrecord;
	/** 页码数量 **/
	private int pagecode = 10;

	public PageView(){

	}

	public PageView(int maxresult, int currentpage) {
		this.maxresult = maxresult;
		this.currentpage = currentpage;
	}

	public int getCurrentpage() {
		return currentpage;
	}

	/** 要获取记录的开始索引 **/
	public int getFirstResult() {
		return (this.currentpage - 1) * this.maxresult;
	}

	public int getMaxresult() {
		return maxresult;
	}

	public int getPagecode() {
		return pagecode;
	}

	public PageIndex getPageindex() {
		return pageindex;
	}

	public List<T> getRecords() {
		return records;
	}

	public long getTotalpage() {
		return totalpage;
	}

	public long getTotalrecord() {
		return totalrecord;
	}

	public void setPagecode(int pagecode) {
		this.pagecode = pagecode;
	}

	public void setQueryResult(QueryResult<T> qr) {
		if(qr==null){
			setTotalrecord(0);
			setRecords(Lists.newArrayList());
		}
		else{
			setTotalrecord(qr.getTotal());
			setRecords(qr.getRecords());
		}
	}

	public void setRecords(List<T> records) {
		this.records = records;
	}

	public void setTotalpage(long totalpage) {
		this.totalpage = totalpage;
		this.pageindex = PageIndex.getPageIndex(pagecode, currentpage,
				totalpage);
	}

	public void setTotalrecord(long totalrecord) {
		this.totalrecord = totalrecord;
		setTotalpage(this.totalrecord % this.maxresult == 0 ? this.totalrecord
				/ this.maxresult : this.totalrecord / this.maxresult + 1);
	}
}
