package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.TalentCertificationApplicationMapper;
import com.ruoyi.system.domain.TalentCertificationApplication;
import com.ruoyi.system.service.ITalentCertificationApplicationService;

/**
 * 衡水市职业技能鉴定中心申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
@Service
public class TalentCertificationApplicationServiceImpl implements ITalentCertificationApplicationService 
{
    @Autowired
    private TalentCertificationApplicationMapper talentCertificationApplicationMapper;

    /**
     * 查询衡水市职业技能鉴定中心申请
     * 
     * @param id 衡水市职业技能鉴定中心申请主键
     * @return 衡水市职业技能鉴定中心申请
     */
    @Override
    public TalentCertificationApplication selectTalentCertificationApplicationById(Long id)
    {
        return talentCertificationApplicationMapper.selectTalentCertificationApplicationById(id);
    }

    /**
     * 查询衡水市职业技能鉴定中心申请列表
     * 
     * @param talentCertificationApplication 衡水市职业技能鉴定中心申请
     * @return 衡水市职业技能鉴定中心申请
     */
    @Override
    public List<TalentCertificationApplication> selectTalentCertificationApplicationList(TalentCertificationApplication talentCertificationApplication)
    {
        return talentCertificationApplicationMapper.selectTalentCertificationApplicationList(talentCertificationApplication);
    }

    /**
     * 新增衡水市职业技能鉴定中心申请
     * 
     * @param talentCertificationApplication 衡水市职业技能鉴定中心申请
     * @return 结果
     */
    @Override
    public int insertTalentCertificationApplication(TalentCertificationApplication talentCertificationApplication)
    {
        talentCertificationApplication.setCreateTime(DateUtils.getNowDate());
        return talentCertificationApplicationMapper.insertTalentCertificationApplication(talentCertificationApplication);
    }

    /**
     * 修改衡水市职业技能鉴定中心申请
     * 
     * @param talentCertificationApplication 衡水市职业技能鉴定中心申请
     * @return 结果
     */
    @Override
    public int updateTalentCertificationApplication(TalentCertificationApplication talentCertificationApplication)
    {
        talentCertificationApplication.setUpdateTime(DateUtils.getNowDate());
        return talentCertificationApplicationMapper.updateTalentCertificationApplication(talentCertificationApplication);
    }

    /**
     * 批量删除衡水市职业技能鉴定中心申请
     * 
     * @param ids 需要删除的衡水市职业技能鉴定中心申请主键
     * @return 结果
     */
    @Override
    public int deleteTalentCertificationApplicationByIds(Long[] ids)
    {
        return talentCertificationApplicationMapper.deleteTalentCertificationApplicationByIds(ids);
    }

    /**
     * 删除衡水市职业技能鉴定中心申请信息
     * 
     * @param id 衡水市职业技能鉴定中心申请主键
     * @return 结果
     */
    @Override
    public int deleteTalentCertificationApplicationById(Long id)
    {
        return talentCertificationApplicationMapper.deleteTalentCertificationApplicationById(id);
    }
}
