package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.IntentionApplyMapper;
import com.ruoyi.portalconsole.domain.IntentionApply;
import com.ruoyi.portalconsole.service.IIntentionApplyService;

/**
 * 意向申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
@Service
public class IntentionApplyServiceImpl implements IIntentionApplyService 
{
    @Autowired
    private IntentionApplyMapper intentionApplyMapper;

    /**
     * 查询意向申请
     * 
     * @param id 意向申请主键
     * @return 意向申请
     */
    @Override
    public IntentionApply selectIntentionApplyById(Long id)
    {
        return intentionApplyMapper.selectIntentionApplyById(id);
    }

    /**
     * 查询意向申请列表
     * 
     * @param intentionApply 意向申请
     * @return 意向申请
     */
    @Override
    public List<IntentionApply> selectIntentionApplyList(IntentionApply intentionApply)
    {
        return intentionApplyMapper.selectIntentionApplyList(intentionApply);
    }

    /**
     * 新增意向申请
     * 
     * @param intentionApply 意向申请
     * @return 结果
     */
    @Override
    public int insertIntentionApply(IntentionApply intentionApply)
    {
        intentionApply.setCreateTime(DateUtils.getNowDate());
        return intentionApplyMapper.insertIntentionApply(intentionApply);
    }

    /**
     * 修改意向申请
     * 
     * @param intentionApply 意向申请
     * @return 结果
     */
    @Override
    public int updateIntentionApply(IntentionApply intentionApply)
    {
        intentionApply.setUpdateTime(DateUtils.getNowDate());
        return intentionApplyMapper.updateIntentionApply(intentionApply);
    }

    /**
     * 批量删除意向申请
     * 
     * @param ids 需要删除的意向申请主键
     * @return 结果
     */
    @Override
    public int deleteIntentionApplyByIds(Long[] ids)
    {
        return intentionApplyMapper.deleteIntentionApplyByIds(ids);
    }

    /**
     * 删除意向申请信息
     * 
     * @param id 意向申请主键
     * @return 结果
     */
    @Override
    public int deleteIntentionApplyById(Long id)
    {
        return intentionApplyMapper.deleteIntentionApplyById(id);
    }
}
