Vue.component('pc-header', {
	props: ['token', 'userinfo', 'sel'],
	data: function() {
		return {
			qyList: [],
			hyList: [],
            bannerShow: false,
            sonList:[]
		}
	},
	methods: {
		showLogin() {
			if (this.token) {
				//window.open('person/main.html?token='+this.token)
				window.location.href = '/newPages/person/main.html?token=' + this.token
			} else {
				window.location.href = '00zhucedenglu.html'
				// let url;
				// var str = window.location.protocol + '//' + window.location.hostname + window.location.pathname
				// var result = encodeURIComponent(str)
				// if (window.location.host == 'test.ningmengdou.com') {
				//     url = "https://ssotest.ningmengdou.com/single/login?returnUrl=" + result
				// } else if (window.location.host == 'www.ningmengdou.com') {
				//     url = "https://sso.ningmengdou.com/single/login?returnUrl=" + result
				// }
				// window.location.href = url
				// window.location.href = '00zhucedenglu.html'
				// let url;
				// var str = window.location.protocol + '//' + window.location.hostname + window.location.pathname
				// var result = encodeURIComponent(str)
				// if (window.location.host == 'test.ningmengdou.com') {
				// 	url = "https://ssotest.ningmengdou.com/single/login?returnUrl=" + result
				// } else if (window.location.host == 'www.ningmengdou.com') {
				// 	url = "https://sso.ningmengdou.com/single/login?returnUrl=" + result
				// }
				// window.location.href = url
			}
		},
		goXipin() {

			let url = 'https://xp-tech.ningmengdou.com/kczq/xipin_pc/01index.html'
			if (this.token) {
				url = 'https://xp-tech.ningmengdou.com/kczq/xipin_pc/01index.html?token=' + this.token + '&userName=' + this.userinfo
					.userName
			}
			window.location.href = url
			// var newWin = window.open('','_blank');
			// setTimeout(()=>{
			//     //这里使用setTimeout非常重要，没有将无法实现
			//     	//原因是window.open会中断正在执行的进程，这样能保证其它代码执行完成再执行这个。
			//     	newWin.location = url; //改变页面的location
			// }, 500)
		},
		goJicaiShop() {
			window.location.href = "https://mdy.ningmengdou.com";
		},
		loginout() {
			let url;
			// var str = window.location.href;
			var str = window.location.protocol + '//' + window.location.hostname + window.location.pathname
			var result = encodeURIComponent(str)
			if (window.location.host == 'test.ningmengdou.com') {
				url = "https://ssotest.ningmengdou.com/single/logout?returnUrl=" + result
			} else if (window.location.host == 'www.ningmengdou.com') {
				url = "https://sso.ningmengdou.com/single/logout?returnUrl=" + result
			}
			window.location.href = url
			window.sessionStorage.clear()
		},
		// getQy() {
		// 	let data = {
		// 		pageNum: 1,
		// 		pageSize: 50,
		// 		zoneType: 2,
		// 	}
		// 	axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
		// 	let result = axios({
		// 		method: 'get',
		// 		url: 'https://www.ningmengdou.com/prod-api/uuc/industryZone/list',
		// 		params: data,
		// 		transformRequest: [function(data) {
		// 			let ret = '';
		// 			for (let i in data) {
		// 				ret += encodeURIComponent(i) + '=' + encodeURIComponent(data[i]) + "&";
		// 			}
		// 			return ret;
		// 		}],
		// 	}).then(resp => {
		// 		if (resp.data.code == 200) {
		// 			this.qyList = resp.data.rows;
		// 		} else {
		// 			//	alert(resp.data.msg)
		// 		}
		//
		// 	}).catch(error => {
		// 		return "exception=" + error;
		// 	});
		// },
		// getHy() {
		// 	let data = {
		// 		pageNum: 1,
		// 		pageSize: 50,
		// 		zoneType: 1,
		// 	}
		// 	axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
		// 	let result = axios({
		// 		method: 'get',
		// 		url: 'https://www.ningmengdou.com/prod-api/uuc/industryZone/list',
		// 		params: data,
		// 		transformRequest: [function(data) {
		// 			let ret = '';
		// 			for (let i in data) {
		// 				ret += encodeURIComponent(i) + '=' + encodeURIComponent(data[i]) + "&";
		// 			}
		// 			return ret;
		// 		}],
		// 	}).then(resp => {
		// 		if (resp.data.code == 200) {
		// 			this.hyList = resp.data.rows;
		// 		} else {
		// 			//	alert(resp.data.msg)
		// 		}
		//
		// 	}).catch(error => {
		// 		return "exception=" + error;
		// 	});
		// },
        open(url){
			window.open(url)
        },
        getSonList(){
            YS.getList('uuc/child/website/list', {
                pageNum: 1,
                pageSize: 10,
            }).then(res => {
                this.sonList = res.rows
                console.log(this.sonList)
        })},
        sonBanner(){
            this.bannerShow = true
        },
        sonBanner1(){
             this.bannerShow = false
        },
	},
	mounted() {
		// 临时注释 wlj
		// this.getQy()
		// this.getHy()
        // this.getSonList()
	},
	template: `<header>
	<div class="son">
	<div style="float: right;margin-left: 40px;  line-height: 35px;cursor: pointer;" @mouseover="sonBanner()">檬豆子站<i class="el-icon-caret-bottom"></i></div> 
	 <div style="float: right;line-height: 35px;cursor: pointer;"><i class="el-icon-phone"></i>4008-939-365</div>
	</div>
	<div class="sonBanner" v-if="bannerShow"  @mouseleave="sonBanner1()">
	<div v-for="(item,index) in sonList" class="sonList" @click="open(item.platUrl)">
		<H5 style="font-size: 18px;">{{item.platName}}</H5>
	   <p style="font-size: 14px;">{{item.platDec}}</p> 
	</div>
	</div> 
	<div class="black"  v-if="bannerShow"></div>
    <div class="wrap">
      <nav id="nav">
        <div class="logo"><a href="01index.html"><img src="images/logo3.png"></a></div>
        <ul class="nav">
           <li :class="['nav-item',sel==1?' active':'']"><a href="01index.html">首页</a></li>
         <!--<li :class="['nav-item',sel==2?' active':'']"><a href="javascript:void(0)">产业集群</a> 
            
           <div class="subMenu">
             <ul class="wrap">
               <li><a href='javascript:void(0)'>区域</a>
              <ul class="sanji" style="display: flex;flex-flow: row wrap;justify-content:space-between;width:250px;">
               
				<li v-for="item in qyList" style="width:40%"><a :href='"021chanyejiqun_linyi.html?id="+item.id'>{{item.zoneName}}</a></li>
                
                </ul></li>
              <li><a href='javascript:void(0)'>行业</a>
               <ul class="sanji">
               
				 <li v-for="item in hyList"><a :href='"021chanyejiqun_suliao.html?id="+item.id'>{{item.zoneName}}</a></li>
				
               
               </ul></li>
				          
              </ul>
          </div>
            
        </li>-->
         <li :class="['nav-item',sel==3?' active':'']"> <a href="javascript:void(0)">解决方案</a> 
            <!-- 二级菜单 S-->
            <div class="subMenu">
			<ul class="newsanji">
			<li style="border-bottom:0">行业解决方案<a href='00301fangan_hangye1.html'>家电行业创新平台解决方案</a><a href='00301fangan_hangye2.html'>木业家具工业互联网平台整体解决方案</a><a href='00301fangan_hangye3.html'>机加工行业数据采集解决方案</a></li>
			
			<li style="padding-left:96px;"><a href='00301fangan_hangye4.html'>农牧企业数字化转型整体解决方案</a></li>
			
			<li style="border-bottom:0">领域解决方案<a href='00301fangan_lingyu1.html'>中小企业供应链管理解决方案</a><a href='00301fangan_lingyu1.html'>中小企业集采降本解决方案</a><a href='00301fangan_lingyu3.html'>供应链金融综合解决方案</a></li>
			
			<li style="padding-left:96px;"><a href='00301fangan_lingyu4.html'>企业云端研发中心建设整体解决方案</a><a href='00301fangan_lingyu5.html'>企业研发一体化全流程解决方案</a><a href='00301fangan_lingyu6.html'>协同研发设计解决方案</a><a href='00301fangan_lingyu7.html'>智慧工厂数据采集解决方案</a><a href='00301fangan_lingyu8.html'>工业互联网人才培训解决方案</a><a href='00301fangan_lingyu9.html'>工业互联网集采平台解决方案</a><a href='00301fangan_lingyu10.html'>科技成果转化解决方案</a></li>
			
			<li style="border-bottom:0;padding-left:63px;">其他<a href='021chanyejiqun_jinrong1.html'>平安数字贷</a><a href='021chanyejiqun_jinrong2.html'>兴业快易贷</a><a href='021chanyejiqun_jinrong3.html'>中银-企E贷</a></li>
			<li style="padding-left:96px;"><a href='065minimengdou.html'>小檬豆微官网</a></li>
			</ul>
              <!--<ul class="wrap">
			  
			   <li><a href='javascript:void(0)'>采购交易管理</a>
                <ul class="sanji">
                <li><a href='031chanpinfuwu1.html'>檬豆云·采购降本解决方案</a></li>
				<li><a href='031chanpinfuwu5.html'>供应链管理解决方案</a></li>
				<li><a href='031chanpinfuwu10.html'>担保交易</a></li>
                <li><a href='javascript:void(0)' @click="goJicaiShop">集采商城</a></li>
                <li><a href='065minimengdou.html'>小檬豆微官网</a></li>
                </ul></li>
				
				
				 <li><a href='javascript:void(0)'>物联网</a>
                <ul class="sanji">
                <li><a href='031chanpinfuwu2.html'>檬豆物联·智能制造解决方案</a></li>
                
                </ul></li>
				 <li><a href='javascript:void(0)'>科创研发</a>
                <ul class="sanji">
                <li><a href='031chanpinfuwu6.html'>线上研发中心</a></li>
				<li><a href='031chanpinfuwu7.html'>科研工作站解决方案</a></li>
				<li><a href='031chanpinfuwu8.html'>技术创新平台解决方案</a></li>
                
                </ul></li>
				      <li><a href='javascript:void(0)'>金融服务</a>
                <ul class="sanji">
               
				<li><a href='021chanyejiqun_jinrong1.html'>平安数字贷</a></li>
				<li><a href='021chanyejiqun_jinrong2.html'>兴业快易贷</a></li>
				<li><a href='021chanyejiqun_jinrong3.html'>中银-企E贷</a></li>
               
                </ul></li>
				 <li><a href='javascript:void(0)'>企业管理</a>
                <ul class="sanji">
               
				
				
				 <li><a href='031chanpinfuwu3.html'>质量检测解决方案</a></li>
                
                </ul></li>
				 <li><a href='javascript:void(0)'>培训服务</a>
                <ul class="sanji">
                <li><a href='031chanpinfuwu9.html'>工业互联网人才解决方案</a></li>
                
                </ul></li>
				
			 
              </ul>-->
            </div>
            <!-- 二级菜单 E--> 
          </li>
          <li class="nav-item"><a href="javascript:void(0)" @click="goXipin" >科技创新</a></li>
		  <li class="nav-item"><a href="https://mdy.ningmengdou.com/" target="_blank" >集采商城</a></li>
          <li :class="['nav-item',sel==4?'active':'']"><a href="023jiejuefangan.html">服务市场</a> </li>
          <li :class="['nav-item',sel==5?'active':'']"><a href="061yingyongshangdian.html">应用商店</a></li>
          <li :class="['nav-item',sel==6?'active':'']"><a href="071shengtaihuoban.html">生态伙伴</a></li>
		  <li class="nav-item"><a href="https://xp-tech.ningmengdou.com/kczq/ningmengdou_zhengfu/01index.html" target="_blank">政府专区</a></li><li :class="['nav-item',sel==9?' active':'']"><a href="090zjtx.html">专精特新</a></li><!--<li :class="['nav-item',sel==10?' active':'']"><a href="101factory_index.html">云豆工场</a></li>-->
          <li :class="['nav-item',sel==7?' active':'']"> <a href="javascript:void(0)">走进檬豆</a> 
            <!-- 二级菜单 S-->
            <div class="subMenu">
              <ul class="wrap">
                <li><a href="081gongsijieshao.html">公司简介</a></li>
                <li><a href="082fazhanlicheng.html">发展历程</a></li>
                <li><a href="083rongyuzizhi.html">荣誉资质</a></li>
                <li><a href="084lianxiwomen.html">联系我们</a></li>
                <li><a href="085mengdouzixun.html">檬豆资讯</a></li>
                <li><a href="086rencaizhaopin.html">人才招聘</a></li>
              </ul>
            </div>
            <!-- 二级菜单 E--> 
          </li>
        </ul>
        <div class="person">
		
		<a href="javascript:void(0)" @click="showLogin" v-if='!token' class="jrgzt">注册登录</a>
		<a href="javascript:void(0)" @click="showLogin" v-if='token' class="jrgzt">进入工作台</a>
		<a href="javascript:void(0)" @click="loginout" v-if='token'>退出</a></div>
        <div class="clear"></div>
      </nav>
    </div>
  </header>`

});
