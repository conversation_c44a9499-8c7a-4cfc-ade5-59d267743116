import request from '@/utils/request'

// 查询政策资讯列表
export function listPolicyInformation(query) {
  return request({
    url: '/portalconsole/PolicyInformation/list',
    method: 'get',
    params: query
  })
}

// 查询政策资讯详细
export function getPolicyInformation(policyInformationId) {
  return request({
    url: '/portalconsole/PolicyInformation/' + policyInformationId,
    method: 'get'
  })
}

// 新增政策资讯
export function addPolicyInformation(data) {
  return request({
    url: '/portalconsole/PolicyInformation',
    method: 'post',
    data: data
  })
}

// 修改政策资讯
export function updatePolicyInformation(data) {
  return request({
    url: '/portalconsole/PolicyInformation',
    method: 'put',
    data: data
  })
}

// 删除政策资讯
export function delPolicyInformation(policyInformationId) {
  return request({
    url: '/portalconsole/PolicyInformation/' + policyInformationId,
    method: 'delete'
  })
}
