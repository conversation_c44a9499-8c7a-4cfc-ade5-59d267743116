Vue.component('pc-newfoot', {
	data() {
		return {
			firstList: [{
				label: '首页',
				type: 'thin',
				url: 'index.html'
			},
			// {
			// 	label: '供需对接',
			// 	type: 'thin',
			// 	url: 'javascript:void(0)'
			// },
			{
				label: '需求大厅',
				type: 'thin',
				url: '/newPages/demandHall.html'
			},
			{
				label: '供给大厅',
				type: 'thin',
				url: '/newPages/supplyHall.html'
			},
			{
				label: '解决方案',
				type: 'thin',
				url: '/newPages/solutionIndex.html'
			},
			{
				label: '典型案例',
				type: 'thin',
				url: '/newPages/dianxinganli.html'
			},
			],
			secondList: [{
				label: '工业服务',
				type: 'thin',
				url: '/newPages/gongyefuwu.html'
			}, {
				label: '应用商店',
				type: 'thin',
				url: '/newPages/yingyongshangdian.html'
			},
			// {
			// 	label: '科技创新',
			// 	type: 'thin',
			// 	url: '/newPages/scienceTechnologyIndex.html'
			// },
			{
				label: '生态协作',
				type: 'thin',
				url: '/newPages/ecologicalCollaboration.html'
			},
			{
				label: '平台介绍',
				type: 'thin',
				url: '/newPages/introduction.html'
			},
			{
				label: '联系我们',
				type: 'thin',
				url: '084lianxiwomen.html'
			}
			],
			thirdList: [{
				label: '贵州工业投资集团',
				type: 'thin',
				url: ''
			},
			{
				label: '贵州省科技技术厅',
				type: 'thin',
				url: ''
			},
			{
				label: '贵州省科技技术局',
				type: 'thin',
				url: ''
			},
			{
				label: '贵州省人民政府',
				type: 'thin',
				url: ''
			},
			{
				label: '贵州省科学技术协会',
				type: 'thin',
				url: ''
			},
			{
				label: '黔东南州科技局',
				type: 'thin',
				url: ''
			}
			],
			list1: [
				{
					value: 'https://www.guizhou.gov.cn/',
					label: '贵州省人民政府网'
				},
				{
					value: ' https://www.qdn.gov.cn/',
					label: '黔东南州人民政府网'
				},
			],
			list2: [
				{
					value: 'https://gxt.guizhou.gov.cn/',
					label: '贵州省工业和信息化厅'
				},
				{
					value: 'https://gxj.qdn.gov.cn/',
					label: '黔东南州工业和信息化局'
				},
			],
			list3: [
				{
					value: 'https://fgw.qdn.gov.cn/',
					label: '黔东南州发展和改革委员会 '
				},
			],
			// thirdList: [{
			// 		label: '生态协作',
			// 		type: 'thin',
			// 		url: 'ecologicalCollaboration.html'
			// 	},
			// 	{
			// 		label: '数字应用',
			// 		type: 'thin',
			// 		url: 'digitalApplications.html'
			// 	},
			// 	{
			// 		label: '开发者平台',
			// 		type: 'thin',
			// 		url: 'exploitIndex.html'
			// 	},
			// 	{
			// 		label: '工业模型',
			// 		type: 'thin',
			// 		url: 'https://industry.ningmengdou.com:9443/ims-web/#/homepage/home'
			// 	},
			// 	{
			// 		label: '新闻资讯',
			// 		type: 'thin',
			// 		url: 'newsInformation.html'
			// 	},
			// 	{
			// 		label: '云豆工厂',
			// 		type: 'thin',
			// 		url: 'https://www.ningmengdou.com/website/101factory_index.html'
			// 	},
			// ],
			// fourthList: [{
			// 		label: '走进檬豆',
			// 		type: 'thin',
			// 		url: 'javascript:void(0)'
			// 	},
			// 	{
			// 		label: '公司简介',
			// 		type: 'thin',
			// 		url: '081gongsijieshao.html'
			// 	},
			// 	{
			// 		label: '发展历程',
			// 		type: 'thin',
			// 		url: '082fazhanlicheng.html'
			// 	},
			// 	{
			// 		label: '荣誉资质',
			// 		type: 'thin',
			// 		url: '083rongyuzizhi.html'
			// 	},
			// 	{
			// 		label: '联系我们',
			// 		type: 'thin',
			// 		url: '084lianxiwomen.html'
			// 	},
			// 	{
			// 		label: '人才招聘',
			// 		type: 'thin',
			// 		url: '086rencaizhaopin.html'
			// 	}
			// ],
		}

	},
	mounted() {
		// var qrcode = new QRCode("app-qrcode", {
		// 	text: "https://www.pgyer.com/nmdou", //要生成二维码的链接
		// 	width: 85, //二维码的宽度
		// 	height: 85, //二维码的高度
		// 	colorDark: "#000000", //前景色
		// 	colorLight: "#ffffff", //背景色
		// 	correctLevel: QRCode.CorrectLevel.H //纠错等级
		// });
	},
	template: `
				<div class="bottom_bg" xmlns="http://www.w3.org/1999/html">
				<div class="bottom_company">黔东南州工业互联网平台</div>
				<ul class="flexdiv">
					<li><span>友情链接：</span></li>
					<li>
						<el-select placeholder="--政府网站--" style="width: 100%">
							<el-option v-for="dict in list1" :key="dict.value" :label="dict.label"
							:value="dict.value">
							<a :href='dict.value' target="_blank">{{dict.label}}</a>
							</el-option>
						</el-select>
					</li>
					<li>
						<el-select placeholder="--各省市工信网站--" style="width: 100%">
							<el-option v-for="dict in list2" :key="dict.value" :label="dict.label"
							:value="dict.value">
							<a :href='dict.value' target="_blank">{{dict.label}}</a>
							</el-option>
						</el-select>
					</li>
					<li>
						<el-select placeholder="--州政府部门网站--" style="width: 100%">
							<el-option v-for="dict in list3" :key="dict.value" :label="dict.label"
							:value="dict.value">
							<a :href='dict.value' target="_blank">{{dict.label}}</a>
							</el-option>
						</el-select>
					</li>
				</ul>
				<div class="flexdiv">
				<div class="linkDiv">
				  <div class="listsub">
					  <div v-for="(item,index) in firstList" :key="index">
							<a :href="item.url"><div :class="[item.type=='blod'?'blodFont':'thinFont']">{{item.label}}</div></a>
						</div>
				  </div>
				  <div  class="listsub">
				  <div v-for="(item,index) in secondList" :key="index">
				  		<a :href="item.url"><div :class="[item.type=='blod'?'blodFont':'thinFont']">{{item.label}}</div></a>
						</div>
					</div>
				<!--<div>
            		<div v-for="(item,index) in thirdList" :key="index">
				  		<a :href="item.url"  target="_blank"><div :class="[item.type=='blod'?'blodFont':'thinFont']">{{item.label}}</div></a>
					</div>
				</div>-->
				  </div>
				  <!--<div class="imgDiv">
				  	 <div>
				  	 	<img src="../images/new/app.jpg">
				  	 </div>
				  	<div class="imgFont">扫描二维码<br/>关注平台公众号<br/>了解更多资讯</div>
				</div>-->
				</div>
			<div>
				<div class="footerDiv">
<!--				Copyright©上海檬豆网络科技有限公司版权所有 913101093507202199-->
					<div class="banquan"> <a href="https://beian.miit.gov.cn" target="_blank">沪ICP备15042831号-1</a></div>
				</div>
<!--				<div class="footerDiv">-->
<!--					<div class="youlian"><a href="http://gxt.shandong.gov.cn/"  target="_blank">山东省工业和信息化厅</a> | <a href="http://kjt.shandong.gov.cn/" target="_blank">山东省科学技术厅</a> | <a href="http://qdstc.qingdao.gov.cn/"  target="_blank">青岛市科学技术局</a> | <a href="http://gxj.qingdao.gov.cn/n28356049/index.html" target="_blank">青岛市工业和信息化局</a> | <a href="http://jx.ah.gov.cn/" target="_blank">安徽省经济和信息化厅</a></div>-->
<!--					<div style="margin-right: 200px"><div class="youlian"><a href="https://www.wandouclouds.com/" target="_blank">皖豆云工业互联网平台</a> | <a href="http://www.fxmuyeplat.com/" target="_blank">木业工业互联网平台</a></div></div>-->
<!--				</div>-->
			</div>
				</div>
`
});
