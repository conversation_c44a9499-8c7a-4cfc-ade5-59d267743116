module.exports =
/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "/dist/";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 117);
/******/ })
/************************************************************************/
/******/ ({

/***/ 0:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return normalizeComponent; });
/* globals __VUE_SSR_CONTEXT__ */

// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).
// This module is a runtime utility for cleaner component module output and will
// be included in the final webpack user bundle.

function normalizeComponent (
  scriptExports,
  render,
  staticRenderFns,
  functionalTemplate,
  injectStyles,
  scopeId,
  moduleIdentifier, /* server only */
  shadowMode /* vue-cli only */
) {
  // Vue.extend constructor export interop
  var options = typeof scriptExports === 'function'
    ? scriptExports.options
    : scriptExports

  // render functions
  if (render) {
    options.render = render
    options.staticRenderFns = staticRenderFns
    options._compiled = true
  }

  // functional template
  if (functionalTemplate) {
    options.functional = true
  }

  // scopedId
  if (scopeId) {
    options._scopeId = 'data-v-' + scopeId
  }

  var hook
  if (moduleIdentifier) { // server build
    hook = function (context) {
      // 2.3 injection
      context =
        context || // cached call
        (this.$vnode && this.$vnode.ssrContext) || // stateful
        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional
      // 2.2 with runInNewContext: true
      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {
        context = __VUE_SSR_CONTEXT__
      }
      // inject component styles
      if (injectStyles) {
        injectStyles.call(this, context)
      }
      // register component module identifier for async chunk inferrence
      if (context && context._registeredComponents) {
        context._registeredComponents.add(moduleIdentifier)
      }
    }
    // used by ssr in case component is cached and beforeCreate
    // never gets called
    options._ssrRegister = hook
  } else if (injectStyles) {
    hook = shadowMode
      ? function () { injectStyles.call(this, this.$root.$options.shadowRoot) }
      : injectStyles
  }

  if (hook) {
    if (options.functional) {
      // for template-only hot-reload because in that case the render fn doesn't
      // go through the normalizer
      options._injectStyles = hook
      // register for functioal component in vue file
      var originalRender = options.render
      options.render = function renderWithStyleInjection (h, context) {
        hook.call(context)
        return originalRender(h, context)
      }
    } else {
      // inject component registration as beforeCreate hook
      var existing = options.beforeCreate
      options.beforeCreate = existing
        ? [].concat(existing, hook)
        : [hook]
    }
  }

  return {
    exports: scriptExports,
    options: options
  }
}


/***/ }),

/***/ 117:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./packages/carousel/src/main.vue?vue&type=template&id=5d5d1482&
var render = function() {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  return _c(
    "div",
    {
      class: _vm.carouselClasses,
      on: {
        mouseenter: function($event) {
          $event.stopPropagation()
          return _vm.handleMouseEnter($event)
        },
        mouseleave: function($event) {
          $event.stopPropagation()
          return _vm.handleMouseLeave($event)
        }
      }
    },
    [
      _c(
        "div",
        {
          staticClass: "el-carousel__container",
          style: { height: _vm.height }
        },
        [
          _vm.arrowDisplay
            ? _c("transition", { attrs: { name: "carousel-arrow-left" } }, [
                _c(
                  "button",
                  {
                    directives: [
                      {
                        name: "show",
                        rawName: "v-show",
                        value:
                          (_vm.arrow === "always" || _vm.hover) &&
                          (_vm.loop || _vm.activeIndex > 0),
                        expression:
                          "(arrow === 'always' || hover) && (loop || activeIndex > 0)"
                      }
                    ],
                    staticClass: "el-carousel__arrow el-carousel__arrow--left",
                    attrs: { type: "button" },
                    on: {
                      mouseenter: function($event) {
                        _vm.handleButtonEnter("left")
                      },
                      mouseleave: _vm.handleButtonLeave,
                      click: function($event) {
                        $event.stopPropagation()
                        _vm.throttledArrowClick(_vm.activeIndex - 1)
                      }
                    }
                  },
                  [_c("i", { staticClass: "el-icon-arrow-left" })]
                )
              ])
            : _vm._e(),
          _vm.arrowDisplay
            ? _c("transition", { attrs: { name: "carousel-arrow-right" } }, [
                _c(
                  "button",
                  {
                    directives: [
                      {
                        name: "show",
                        rawName: "v-show",
                        value:
                          (_vm.arrow === "always" || _vm.hover) &&
                          (_vm.loop || _vm.activeIndex < _vm.items.length - 1),
                        expression:
                          "(arrow === 'always' || hover) && (loop || activeIndex < items.length - 1)"
                      }
                    ],
                    staticClass: "el-carousel__arrow el-carousel__arrow--right",
                    attrs: { type: "button" },
                    on: {
                      mouseenter: function($event) {
                        _vm.handleButtonEnter("right")
                      },
                      mouseleave: _vm.handleButtonLeave,
                      click: function($event) {
                        $event.stopPropagation()
                        _vm.throttledArrowClick(_vm.activeIndex + 1)
                      }
                    }
                  },
                  [_c("i", { staticClass: "el-icon-arrow-right" })]
                )
              ])
            : _vm._e(),
          _vm._t("default")
        ],
        2
      ),
      _vm.indicatorPosition !== "none"
        ? _c(
            "ul",
            { class: _vm.indicatorsClasses },
            _vm._l(_vm.items, function(item, index) {
              return _c(
                "li",
                {
                  key: index,
                  class: [
                    "el-carousel__indicator",
                    "el-carousel__indicator--" + _vm.direction,
                    { "is-active": index === _vm.activeIndex }
                  ],
                  on: {
                    mouseenter: function($event) {
                      _vm.throttledIndicatorHover(index)
                    },
                    click: function($event) {
                      $event.stopPropagation()
                      _vm.handleIndicatorClick(index)
                    }
                  }
                },
                [
                  _c("button", { staticClass: "el-carousel__button" }, [
                    _vm.hasLabel
                      ? _c("span", [_vm._v(_vm._s(item.label))])
                      : _vm._e()
                  ])
                ]
              )
            }),
            0
          )
        : _vm._e()
    ]
  )
}
var staticRenderFns = []
render._withStripped = true


// CONCATENATED MODULE: ./packages/carousel/src/main.vue?vue&type=template&id=5d5d1482&

// EXTERNAL MODULE: external "throttle-debounce/throttle"
var throttle_ = __webpack_require__(25);
var throttle_default = /*#__PURE__*/__webpack_require__.n(throttle_);

// EXTERNAL MODULE: external "element-ui/lib/utils/resize-event"
var resize_event_ = __webpack_require__(16);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/carousel/src/main.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var mainvue_type_script_lang_js_ = ({
  name: 'ElCarousel',

  props: {
    initialIndex: {
      type: Number,
      default: 0
    },
    height: String,
    trigger: {
      type: String,
      default: 'hover'
    },
    autoplay: {
      type: Boolean,
      default: true
    },
    interval: {
      type: Number,
      default: 3000
    },
    indicatorPosition: String,
    indicator: {
      type: Boolean,
      default: true
    },
    arrow: {
      type: String,
      default: 'hover'
    },
    type: String,
    loop: {
      type: Boolean,
      default: true
    },
    direction: {
      type: String,
      default: 'horizontal',
      validator: function validator(val) {
        return ['horizontal', 'vertical'].indexOf(val) !== -1;
      }
    }
  },

  data: function data() {
    return {
      items: [],
      activeIndex: -1,
      containerWidth: 0,
      timer: null,
      hover: false
    };
  },


  computed: {
    arrowDisplay: function arrowDisplay() {
      return this.arrow !== 'never' && this.direction !== 'vertical';
    },
    hasLabel: function hasLabel() {
      return this.items.some(function (item) {
        return item.label.toString().length > 0;
      });
    },
    carouselClasses: function carouselClasses() {
      var classes = ['el-carousel', 'el-carousel--' + this.direction];
      if (this.type === 'card') {
        classes.push('el-carousel--card');
      }
      return classes;
    },
    indicatorsClasses: function indicatorsClasses() {
      var classes = ['el-carousel__indicators', 'el-carousel__indicators--' + this.direction];
      if (this.hasLabel) {
        classes.push('el-carousel__indicators--labels');
      }
      if (this.indicatorPosition === 'outside' || this.type === 'card') {
        classes.push('el-carousel__indicators--outside');
      }
      return classes;
    }
  },

  watch: {
    items: function items(val) {
      if (val.length > 0) this.setActiveItem(this.initialIndex);
    },
    activeIndex: function activeIndex(val, oldVal) {
      this.resetItemPosition(oldVal);
      if (oldVal > -1) {
        this.$emit('change', val, oldVal);
      }
    },
    autoplay: function autoplay(val) {
      val ? this.startTimer() : this.pauseTimer();
    },
    loop: function loop() {
      this.setActiveItem(this.activeIndex);
    },
    interval: function interval() {
      this.pauseTimer();
      this.startTimer();
    }
  },

  methods: {
    handleMouseEnter: function handleMouseEnter() {
      this.hover = true;
      this.pauseTimer();
    },
    handleMouseLeave: function handleMouseLeave() {
      this.hover = false;
      this.startTimer();
    },
    itemInStage: function itemInStage(item, index) {
      var length = this.items.length;
      if (index === length - 1 && item.inStage && this.items[0].active || item.inStage && this.items[index + 1] && this.items[index + 1].active) {
        return 'left';
      } else if (index === 0 && item.inStage && this.items[length - 1].active || item.inStage && this.items[index - 1] && this.items[index - 1].active) {
        return 'right';
      }
      return false;
    },
    handleButtonEnter: function handleButtonEnter(arrow) {
      var _this = this;

      if (this.direction === 'vertical') return;
      this.items.forEach(function (item, index) {
        if (arrow === _this.itemInStage(item, index)) {
          item.hover = true;
        }
      });
    },
    handleButtonLeave: function handleButtonLeave() {
      if (this.direction === 'vertical') return;
      this.items.forEach(function (item) {
        item.hover = false;
      });
    },
    updateItems: function updateItems() {
      this.items = this.$children.filter(function (child) {
        return child.$options.name === 'ElCarouselItem';
      });
    },
    resetItemPosition: function resetItemPosition(oldIndex) {
      var _this2 = this;

      this.items.forEach(function (item, index) {
        item.translateItem(index, _this2.activeIndex, oldIndex);
      });
    },
    playSlides: function playSlides() {
      if (this.activeIndex < this.items.length - 1) {
        this.activeIndex++;
      } else if (this.loop) {
        this.activeIndex = 0;
      }
    },
    pauseTimer: function pauseTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    startTimer: function startTimer() {
      if (this.interval <= 0 || !this.autoplay || this.timer) return;
      this.timer = setInterval(this.playSlides, this.interval);
    },
    resetTimer: function resetTimer() {
      this.pauseTimer();
      this.startTimer();
    },
    setActiveItem: function setActiveItem(index) {
      if (typeof index === 'string') {
        var filteredItems = this.items.filter(function (item) {
          return item.name === index;
        });
        if (filteredItems.length > 0) {
          index = this.items.indexOf(filteredItems[0]);
        }
      }
      index = Number(index);
      if (isNaN(index) || index !== Math.floor(index)) {
        console.warn('[Element Warn][Carousel]index must be an integer.');
        return;
      }
      var length = this.items.length;
      var oldIndex = this.activeIndex;
      if (index < 0) {
        this.activeIndex = this.loop ? length - 1 : 0;
      } else if (index >= length) {
        this.activeIndex = this.loop ? 0 : length - 1;
      } else {
        this.activeIndex = index;
      }
      if (oldIndex === this.activeIndex) {
        this.resetItemPosition(oldIndex);
      }
      this.resetTimer();
    },
    prev: function prev() {
      this.setActiveItem(this.activeIndex - 1);
    },
    next: function next() {
      this.setActiveItem(this.activeIndex + 1);
    },
    handleIndicatorClick: function handleIndicatorClick(index) {
      this.activeIndex = index;
    },
    handleIndicatorHover: function handleIndicatorHover(index) {
      if (this.trigger === 'hover' && index !== this.activeIndex) {
        this.activeIndex = index;
      }
    }
  },

  created: function created() {
    var _this3 = this;

    this.throttledArrowClick = throttle_default()(300, true, function (index) {
      _this3.setActiveItem(index);
    });
    this.throttledIndicatorHover = throttle_default()(300, function (index) {
      _this3.handleIndicatorHover(index);
    });
  },
  mounted: function mounted() {
    var _this4 = this;

    this.updateItems();
    this.$nextTick(function () {
      Object(resize_event_["addResizeListener"])(_this4.$el, _this4.resetItemPosition);
      if (_this4.initialIndex < _this4.items.length && _this4.initialIndex >= 0) {
        _this4.activeIndex = _this4.initialIndex;
      }
      _this4.startTimer();
    });
  },
  beforeDestroy: function beforeDestroy() {
    if (this.$el) Object(resize_event_["removeResizeListener"])(this.$el, this.resetItemPosition);
    this.pauseTimer();
  }
});
// CONCATENATED MODULE: ./packages/carousel/src/main.vue?vue&type=script&lang=js&
 /* harmony default export */ var src_mainvue_type_script_lang_js_ = (mainvue_type_script_lang_js_);
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(0);

// CONCATENATED MODULE: ./packages/carousel/src/main.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  src_mainvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null

)

/* hot reload */
if (false) { var api; }
component.options.__file = "packages/carousel/src/main.vue"
/* harmony default export */ var main = (component.exports);
// CONCATENATED MODULE: ./packages/carousel/index.js


/* istanbul ignore next */
main.install = function (Vue) {
  Vue.component(main.name, main);
};

/* harmony default export */ var carousel = __webpack_exports__["default"] = (main);

/***/ }),

/***/ 16:
/***/ (function(module, exports) {

module.exports = require("element-ui/lib/utils/resize-event");

/***/ }),

/***/ 25:
/***/ (function(module, exports) {

module.exports = require("throttle-debounce/throttle");

/***/ })

/******/ });
