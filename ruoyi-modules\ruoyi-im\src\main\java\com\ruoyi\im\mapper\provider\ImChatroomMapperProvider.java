package com.ruoyi.im.mapper.provider;

import com.alibaba.nacos.common.utils.StringUtils;

import java.util.List;
import java.util.Map;

public class ImChatroomMapperProvider {

    public String queryPage(Map map) {
        String name = (String) map.get("name");
        StringBuilder sb = new StringBuilder("select c.* from im_chatroom c join" +
                " (select toUserId,count(id) from im_chatroom_msg group by toUserId having count(id)>0) m on m.toUserId=c.chatroomId where 1=1 ");
        if(StringUtils.isNotBlank(name)){
            sb.append(" and c.chatroomName like #{name}");
        }
        sb.append(" order by c.update_time desc,c.id desc");
        return sb.toString();
    }
}
