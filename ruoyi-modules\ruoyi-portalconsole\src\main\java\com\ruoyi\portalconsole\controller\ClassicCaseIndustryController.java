package com.ruoyi.portalconsole.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.ClassicCaseIndustry;
import com.ruoyi.portalconsole.service.IClassicCaseIndustryService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 典型案例行业Controller
 * 
 * <AUTHOR>
 * @date 2024-06-07
 */
@RestController
@RequestMapping("/classicCaseIndustry")
public class ClassicCaseIndustryController extends BaseController
{
    @Autowired
    private IClassicCaseIndustryService classicCaseIndustryService;

    /**
     * 查询典型案例行业列表
     */
    @RequiresPermissions("portalconsole:classicCaseIndustry:list")
    @GetMapping("/list")
    public TableDataInfo list(ClassicCaseIndustry classicCaseIndustry)
    {
        startPage();
        List<ClassicCaseIndustry> list = classicCaseIndustryService.selectClassicCaseIndustryList(classicCaseIndustry);
        return getDataTable(list);
    }

    /**
     * 导出典型案例行业列表
     */
    @RequiresPermissions("portalconsole:classicCaseIndustry:export")
    @Log(title = "典型案例行业", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ClassicCaseIndustry classicCaseIndustry)
    {
        List<ClassicCaseIndustry> list = classicCaseIndustryService.selectClassicCaseIndustryList(classicCaseIndustry);
        ExcelUtil<ClassicCaseIndustry> util = new ExcelUtil<ClassicCaseIndustry>(ClassicCaseIndustry.class);
        util.exportExcel(response, list, "典型案例行业数据");
    }

    /**
     * 获取典型案例行业详细信息
     */
    @RequiresPermissions("portalconsole:classicCaseIndustry:query")
    @GetMapping(value = "/{classicCaseIndustryId}")
    public AjaxResult getInfo(@PathVariable("classicCaseIndustryId") Long classicCaseIndustryId)
    {
        return success(classicCaseIndustryService.selectClassicCaseIndustryByClassicCaseIndustryId(classicCaseIndustryId));
    }

    /**
     * 新增典型案例行业
     */
    @RequiresPermissions("portalconsole:classicCaseIndustry:add")
    @Log(title = "典型案例行业", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ClassicCaseIndustry classicCaseIndustry)
    {
        return toAjax(classicCaseIndustryService.insertClassicCaseIndustry(classicCaseIndustry));
    }

    /**
     * 修改典型案例行业
     */
    @RequiresPermissions("portalconsole:classicCaseIndustry:edit")
    @Log(title = "典型案例行业", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ClassicCaseIndustry classicCaseIndustry)
    {
        return toAjax(classicCaseIndustryService.updateClassicCaseIndustry(classicCaseIndustry));
    }

    /**
     * 删除典型案例行业
     */
    @RequiresPermissions("portalconsole:classicCaseIndustry:remove")
    @Log(title = "典型案例行业", businessType = BusinessType.DELETE)
	@DeleteMapping("/{classicCaseIndustryIds}")
    public AjaxResult remove(@PathVariable Long[] classicCaseIndustryIds)
    {
        return toAjax(classicCaseIndustryService.deleteClassicCaseIndustryByClassicCaseIndustryIds(classicCaseIndustryIds));
    }
}
