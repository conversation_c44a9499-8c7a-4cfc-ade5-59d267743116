package com.ruoyi.portalweb.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalweb.api.domain.PolicySubmitConsult;
import com.ruoyi.portalweb.service.IPolicySubmitConsultService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 政策辅助申报Controller
 * 
 * <AUTHOR>
 * @date 2024-07-08
 */
@RestController
@RequestMapping("/consult")
public class PolicySubmitConsultController extends BaseController
{
    @Autowired
    private IPolicySubmitConsultService policySubmitConsultService;

    /**
     * 查询政策辅助申报列表
     */
    @RequiresPermissions("portalweb:consult:list")
    @GetMapping("/list")
    public TableDataInfo list(PolicySubmitConsult policySubmitConsult)
    {
        startPage();
        List<PolicySubmitConsult> list = policySubmitConsultService.selectPolicySubmitConsultList(policySubmitConsult);
        return getDataTable(list);
    }

    /**
     * 导出政策辅助申报列表
     */
    @RequiresPermissions("portalweb:consult:export")
    @Log(title = "政策辅助申报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PolicySubmitConsult policySubmitConsult)
    {
        List<PolicySubmitConsult> list = policySubmitConsultService.selectPolicySubmitConsultList(policySubmitConsult);
        ExcelUtil<PolicySubmitConsult> util = new ExcelUtil<PolicySubmitConsult>(PolicySubmitConsult.class);
        util.exportExcel(response, list, "政策辅助申报数据");
    }

    /**
     * 获取政策辅助申报详细信息
     */
    @RequiresPermissions("portalweb:consult:query")
    @GetMapping(value = "/{policySubmitConsultId}")
    public AjaxResult getInfo(@PathVariable("policySubmitConsultId") Long policySubmitConsultId)
    {
        return success(policySubmitConsultService.selectPolicySubmitConsultByPolicySubmitConsultId(policySubmitConsultId));
    }

    /**
     * 新增政策辅助申报
     */
    @Log(title = "政策辅助申报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PolicySubmitConsult policySubmitConsult)
    {
        return toAjax(policySubmitConsultService.insertPolicySubmitConsult(policySubmitConsult));
    }

    /**
     * 修改政策辅助申报
     */
    @RequiresPermissions("portalweb:consult:edit")
    @Log(title = "政策辅助申报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PolicySubmitConsult policySubmitConsult)
    {
        return toAjax(policySubmitConsultService.updatePolicySubmitConsult(policySubmitConsult));
    }

    /**
     * 删除政策辅助申报
     */
    @RequiresPermissions("portalweb:consult:remove")
    @Log(title = "政策辅助申报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{policySubmitConsultIds}")
    public AjaxResult remove(@PathVariable Long[] policySubmitConsultIds)
    {
        return toAjax(policySubmitConsultService.deletePolicySubmitConsultByPolicySubmitConsultIds(policySubmitConsultIds));
    }
}
