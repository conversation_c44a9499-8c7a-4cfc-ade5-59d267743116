package com.ruoyi.portalweb.vo;

import java.io.Serializable;

public class AppStoreOrderCountVO implements Serializable {

    private Long totalCount;

    private Long unpaidOrderCount;

    private Long paidOrderCount;

    private Long refundingOrderCount;

    private Long refundedOrderCount;

    private Long deliveryOrderCount;

    private Long confirmedOrderCount;

    private Long invoiceAppliedOrderCount;

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public Long getUnpaidOrderCount() {
        return unpaidOrderCount;
    }

    public void setUnpaidOrderCount(Long unpaidOrderCount) {
        this.unpaidOrderCount = unpaidOrderCount;
    }

    public Long getPaidOrderCount() {
        return paidOrderCount;
    }

    public void setPaidOrderCount(Long paidOrderCount) {
        this.paidOrderCount = paidOrderCount;
    }

    public Long getRefundingOrderCount() {
        return refundingOrderCount;
    }

    public void setRefundingOrderCount(Long refundingOrderCount) {
        this.refundingOrderCount = refundingOrderCount;
    }

    public Long getRefundedOrderCount() {
        return refundedOrderCount;
    }

    public void setRefundedOrderCount(Long refundedOrderCount) {
        this.refundedOrderCount = refundedOrderCount;
    }

    public Long getDeliveryOrderCount() {
        return deliveryOrderCount;
    }

    public void setDeliveryOrderCount(Long deliveryOrderCount) {
        this.deliveryOrderCount = deliveryOrderCount;
    }

    public Long getConfirmedOrderCount() {
        return confirmedOrderCount;
    }

    public void setConfirmedOrderCount(Long confirmedOrderCount) {
        this.confirmedOrderCount = confirmedOrderCount;
    }

    public Long getInvoiceAppliedOrderCount() {
        return invoiceAppliedOrderCount;
    }

    public void setInvoiceAppliedOrderCount(Long invoiceAppliedOrderCount) {
        this.invoiceAppliedOrderCount = invoiceAppliedOrderCount;
    }
}
