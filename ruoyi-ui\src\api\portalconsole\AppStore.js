import request from '@/utils/request'

// 查询应用商店列表
export function listAppStore(query) {
  return request({
    url: '/portalconsole/AppStore/list',
    method: 'get',
    params: query
  })
}

// 查询应用商店详细
export function getAppStore(appStoreId) {
  return request({
    url: '/portalconsole/AppStore/' + appStoreId,
    method: 'get'
  })
}

// 新增应用商店
export function addAppStore(data) {
  return request({
    url: '/portalconsole/AppStore',
    method: 'post',
    data: data
  })
}

// 修改应用商店
export function updateAppStore(data) {
  return request({
    url: '/portalconsole/AppStore',
    method: 'put',
    data: data
  })
}

// 删除应用商店
export function delAppStore(appStoreId) {
  return request({
    url: '/portalconsole/AppStore/' + appStoreId,
    method: 'delete'
  })
}
//审批
export function auditAppStore(data) {
  return request({
    url: '/portalconsole/AppStore/audit',
    method: 'put',
    data: data
  })
}
