<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.InviteCodeMapper">
    
    <resultMap type="InviteCode" id="InviteCodeResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="companyRelatedId"    column="company_related_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="isValid"    column="is_valid"    />
    </resultMap>

    <resultMap type="InviteCodeVO" id="InviteCodeVOResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="companyRelatedId"    column="company_related_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="isValid"    column="is_valid"    />
        <result property="companyName"    column="company_name"    />
    </resultMap>

    <sql id="selectInviteCodeVo">
        select id, code, company_related_id, create_time, is_valid from invite_code
    </sql>

    <select id="selectInviteCodeList" parameterType="InviteCode" resultMap="InviteCodeResult">
        <include refid="selectInviteCodeVo"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="companyRelatedId != null "> and company_related_id = #{companyRelatedId}</if>
            <if test="isValid != null  and isValid != ''"> and is_valid = #{isValid}</if>
        </where>
    </select>
    
    <select id="selectInviteCodeById" parameterType="Long" resultMap="InviteCodeResult">
        <include refid="selectInviteCodeVo"/>
        where id = #{id}
    </select>
    <select id="selectInviteCodeByCode" parameterType="InviteCode" resultMap="InviteCodeVOResult">
        select id, code, i.company_related_id, i.create_time, is_valid, c.company_name from invite_code i
        LEFT JOIN company_related c ON i.company_related_id = c.company_related_id
        where code = #{code}
    </select>

    <insert id="insertInviteCode" parameterType="InviteCode" useGeneratedKeys="true" keyProperty="id">
        insert into invite_code (code, company_related_id, create_time, is_valid)
            values
        <foreach collection="list"   separator="," item="item">
                <trim prefix="(" suffix=")" suffixOverrides=",">
            #{item.code},#{item.companyRelatedId},#{item.createTime},#{item.isValid}
                </trim>
        </foreach>

    </insert>

    <update id="updateInviteCode" parameterType="InviteCode">
        update invite_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="companyRelatedId != null">company_related_id = #{companyRelatedId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="isValid != null">is_valid = #{isValid},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInviteCodeById" parameterType="Long">
        delete from invite_code where id = #{id}
    </delete>

    <delete id="deleteInviteCodeByIds" parameterType="String">
        delete from invite_code where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteInvalidInviteCodes">
        delete from invite_code where company_related_id = #{companyRelatedId} and is_valid = #{isValid}
    </delete>
</mapper>