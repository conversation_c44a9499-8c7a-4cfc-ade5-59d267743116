package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.PolicyLabelMapper;
import com.ruoyi.portalconsole.domain.PolicyLabel;
import com.ruoyi.portalconsole.service.IPolicyLabelService;

/**
 * 政策标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
public class PolicyLabelServiceImpl implements IPolicyLabelService 
{
    @Autowired
    private PolicyLabelMapper policyLabelMapper;

    /**
     * 查询政策标签
     * 
     * @param policyLabelId 政策标签主键
     * @return 政策标签
     */
    @Override
    public PolicyLabel selectPolicyLabelByPolicyLabelId(Long policyLabelId)
    {
        return policyLabelMapper.selectPolicyLabelByPolicyLabelId(policyLabelId);
    }

    /**
     * 查询政策标签列表
     * 
     * @param policyLabel 政策标签
     * @return 政策标签
     */
    @Override
    public List<PolicyLabel> selectPolicyLabelList(PolicyLabel policyLabel)
    {
        return policyLabelMapper.selectPolicyLabelList(policyLabel);
    }

    /**
     * 新增政策标签
     * 
     * @param policyLabel 政策标签
     * @return 结果
     */
    @Override
    public int insertPolicyLabel(PolicyLabel policyLabel)
    {
        policyLabel.setCreateTime(DateUtils.getNowDate());
        return policyLabelMapper.insertPolicyLabel(policyLabel);
    }

    /**
     * 修改政策标签
     * 
     * @param policyLabel 政策标签
     * @return 结果
     */
    @Override
    public int updatePolicyLabel(PolicyLabel policyLabel)
    {
        policyLabel.setUpdateTime(DateUtils.getNowDate());
        return policyLabelMapper.updatePolicyLabel(policyLabel);
    }

    /**
     * 批量删除政策标签
     * 
     * @param policyLabelIds 需要删除的政策标签主键
     * @return 结果
     */
    @Override
    public int deletePolicyLabelByPolicyLabelIds(Long[] policyLabelIds)
    {
        return policyLabelMapper.deletePolicyLabelByPolicyLabelIds(policyLabelIds);
    }

    /**
     * 删除政策标签信息
     * 
     * @param policyLabelId 政策标签主键
     * @return 结果
     */
    @Override
    public int deletePolicyLabelByPolicyLabelId(Long policyLabelId)
    {
        return policyLabelMapper.deletePolicyLabelByPolicyLabelId(policyLabelId);
    }
}
