package com.ruoyi.portalweb.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.portalweb.api.domain.Member;
import com.ruoyi.portalweb.api.factory.RemoteMemberFallbackFactory;
import com.ruoyi.portalweb.api.model.LoginMember;

/**
 * 用户服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remotePortalMemberService", value = ServiceNameConstants.PORTALWEB_SERVICE, fallbackFactory = RemoteMemberFallbackFactory.class)
public interface RemoteMemberService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/Member/info/{memberphone}")
    public R<LoginMember> getMemberInfo(@PathVariable("memberphone") String memberphone, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/Member/smsCodeInfo/{memberphone}")
    public R<LoginMember> getMemberSms(@PathVariable("memberphone") String memberphone, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    

    @PostMapping("/Member/checkUserSmsCode/{username}/{smsCode}")
    public R<Boolean> checkUserSmsCode(@PathVariable("username") String username,@PathVariable("smsCode") String smsCode);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/user/register")
    public R<Boolean> registerMemberInfo(@RequestBody Member member, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 初始化用户密码
     *
     * @param member 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PutMapping("/Member/password")
    public R<Boolean> updateMemberPassword(@RequestBody Member member, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
