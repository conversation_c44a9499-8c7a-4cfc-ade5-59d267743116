<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.OrderMaterialRelationMapper">
    
    <resultMap type="com.ruoyi.system.domain.OrderMaterialRelation" id="OrderMaterialRelationResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="materialId"    column="material_id"    />
        <result property="quantity"    column="quantity"    />
        <result property="unit"    column="unit"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <association property="order" javaType="com.ruoyi.system.domain.ManufactureOrder">
            <id property="id" column="mo_id"/>
            <result property="orderType" column="order_type"/>
            <result property="status" column="status"/>
            <result property="demandCompany" column="demand_company"/>
        </association>
        <association property="material" javaType="com.ruoyi.system.domain.MaterialInfo">
            <id property="id" column="mi_id"/>
            <result property="name" column="name"/>
            <result property="modelNumber" column="model_number"/>
        </association>
    </resultMap>

    <sql id="selectOrderMaterialRelationVo">
        select id, order_id, material_id, quantity, unit, remark, create_time, update_time from order_material_relation
    </sql>

    <sql id="selectOrderMaterialRelationWithDetailVo">
        select r.id, r.order_id, r.material_id, r.quantity, r.unit, r.remark, r.create_time, r.update_time,
               o.id as mo_id, o.order_type, o.status, o.demand_company,
               m.id as mi_id, m.name, m.model_number
        from order_material_relation r
        left join manufacture_order o on r.order_id = o.id
        left join material_info m on r.material_id = m.id
    </sql>

    <select id="selectOrderMaterialRelationList" parameterType="com.ruoyi.system.domain.OrderMaterialRelation" resultMap="OrderMaterialRelationResult">
        <include refid="selectOrderMaterialRelationWithDetailVo"/>
        <where>  
            <if test="orderId != null "> and r.order_id = #{orderId}</if>
            <if test="materialId != null "> and r.material_id = #{materialId}</if>
            <if test="quantity != null "> and r.quantity = #{quantity}</if>
            <if test="unit != null  and unit != ''"> and r.unit = #{unit}</if>
        </where>
    </select>
    
    <select id="selectOrderMaterialRelationById" parameterType="Long" resultMap="OrderMaterialRelationResult">
        <include refid="selectOrderMaterialRelationWithDetailVo"/>
        where r.id = #{id}
    </select>
    
    <select id="selectOrderMaterialRelationByOrderId" parameterType="Long" resultMap="OrderMaterialRelationResult">
        <include refid="selectOrderMaterialRelationWithDetailVo"/>
        where r.order_id = #{orderId}
    </select>
    
    <select id="selectOrderMaterialRelationByMaterialId" parameterType="Long" resultMap="OrderMaterialRelationResult">
        <include refid="selectOrderMaterialRelationWithDetailVo"/>
        where r.material_id = #{materialId}
    </select>
        
    <insert id="insertOrderMaterialRelation" parameterType="com.ruoyi.system.domain.OrderMaterialRelation" useGeneratedKeys="true" keyProperty="id">
        insert into order_material_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="materialId != null">material_id,</if>
            <if test="quantity != null">quantity,</if>
            <if test="unit != null">unit,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="materialId != null">#{materialId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="unit != null">#{unit},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOrderMaterialRelation" parameterType="com.ruoyi.system.domain.OrderMaterialRelation">
        update order_material_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="materialId != null">material_id = #{materialId},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOrderMaterialRelationById" parameterType="Long">
        delete from order_material_relation where id = #{id}
    </delete>

    <delete id="deleteOrderMaterialRelationByIds" parameterType="String">
        delete from order_material_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteOrderMaterialRelationByOrderId" parameterType="Long">
        delete from order_material_relation where order_id = #{orderId}
    </delete>
    
    <delete id="deleteOrderMaterialRelationByMaterialId" parameterType="Long">
        delete from order_material_relation where material_id = #{materialId}
    </delete>
</mapper>
