package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.FactoryPerformance;

/**
 * 工厂业绩情况Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface IFactoryPerformanceService 
{
    /**
     * 查询工厂业绩情况
     * 
     * @param id 工厂业绩情况主键
     * @return 工厂业绩情况
     */
    public FactoryPerformance selectFactoryPerformanceById(Long id);

    /**
     * 查询工厂业绩情况列表
     * 
     * @param factoryPerformance 工厂业绩情况
     * @return 工厂业绩情况集合
     */
    public List<FactoryPerformance> selectFactoryPerformanceList(FactoryPerformance factoryPerformance);

    /**
     * 新增工厂业绩情况
     * 
     * @param factoryPerformance 工厂业绩情况
     * @return 结果
     */
    public int insertFactoryPerformance(FactoryPerformance factoryPerformance);

    /**
     * 修改工厂业绩情况
     * 
     * @param factoryPerformance 工厂业绩情况
     * @return 结果
     */
    public int updateFactoryPerformance(FactoryPerformance factoryPerformance);

    /**
     * 批量删除工厂业绩情况
     * 
     * @param ids 需要删除的工厂业绩情况主键集合
     * @return 结果
     */
    public int deleteFactoryPerformanceByIds(Long[] ids);

    /**
     * 删除工厂业绩情况信息
     * 
     * @param id 工厂业绩情况主键
     * @return 结果
     */
    public int deleteFactoryPerformanceById(Long id);
}
