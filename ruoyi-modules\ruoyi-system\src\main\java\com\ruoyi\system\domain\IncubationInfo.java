package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 创业孵化信息对象 incubation_info
 *
 * <AUTHOR>
 * @date 2025-03-11
 */
public class IncubationInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 类型（1：创业基地 2：合作平台 3：产业园） */
    @Excel(name = "类型", readConverterExp = "1=：创业基地,2=：合作平台,3=：产业园")
    private String type;

    /** 图片列表（JSON格式存储多张图片URL） */
    @Excel(name = "图片列表", readConverterExp = "J=SON格式存储多张图片URL")
    private String images;

    /** 详情（富文本） */
    @Excel(name = "详情", readConverterExp = "富=文本")
    private String content;

    /** 排序（数字越大越靠前） */
    @Excel(name = "排序", readConverterExp = "数=字越大越靠前")
    private Long sort;

    /** 状态（0：正常 1：停用） */
    @Excel(name = "状态", readConverterExp = "0=：正常,1=：停用")
    private String status;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setImages(String images)
    {
        this.images = images;
    }

    public String getImages()
    {
        return images;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setSort(Long sort)
    {
        this.sort = sort;
    }

    public Long getSort()
    {
        return sort;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("title", getTitle())
                .append("type", getType())
                .append("images", getImages())
                .append("content", getContent())
                .append("sort", getSort())
                .append("status", getStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
