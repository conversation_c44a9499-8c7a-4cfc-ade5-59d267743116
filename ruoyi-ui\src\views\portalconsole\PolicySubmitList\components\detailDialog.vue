<template>
    <!-- 政策申报详情 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" @close="handelClose">
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
            <el-tab-pane label="基本信息" name="first">
                <el-form ref="form" :model="form" label-width="140px">
                    <el-row>
                        <h1 style="width: 100%;text-align: center;">基本信息</h1>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="政策标题:" prop="">
                                {{  form.policySubmitTitle?form.policySubmitTitle:''}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="最高奖励:" prop="">
                                {{ form.policySubmitReward? form.policySubmitReward:''}}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="条款内容:" prop="">
                                {{ form.policySubmitContent? form.policySubmitContent:''}}
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="审核状态:" prop="">
                                <div v-if="form.policySubmitStatus==='0'">草稿</div>
                                <div v-else-if="form.policySubmitStatus==='1'">审核中</div>
                                <div v-else-if="form.policySubmitStatus==='2'">已通过</div>
                                <div v-else-if="form.policySubmitStatus==='3'">已驳回</div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="截止时间:">
                                <span>{{ parseTime(form.policySubmitEndDate, '{y}-{m}-{d}') }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="发布单位:">
                                {{ form.policySubmitUnit?form.policySubmitUnit:'' }}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <div class="btn">
                            <el-button type="success" plain @click="edit('2')">认证通过</el-button>
                            <el-button type="danger" plain @click="edit('3')">认证拒绝</el-button>
                        </div>
                    </el-row>
                </el-form>
        </el-tab-pane>
        <el-tab-pane label="提报信息" name="second">
            <div>
                <el-table v-loading="loading" :data="listData" >
                    <el-table-column label="公司名称" align="center" prop="companyName" />
                    <el-table-column label="联系人" align="center" prop="memberRealName" />
                    <el-table-column label="联系电话" align="center" prop="memberPhone" />
                </el-table>
            </div>
        </el-tab-pane> 
        </el-tabs>
    </el-dialog>
</template>
<script>
import {getInfo,updatePolicySubmit,getPolicySubmit} from "@/api/portalconsole/PolicySubmit";
export default {
    name: "detailDialog",
    props: {

    },
    data() {
        return {
            title: '政策申报详情',
            open: false,
            id: '',
            form: {},
            loading:false,
            listData:[],
            activeName: 'first',
        };
    },
    created() {

    },
    methods: {
        /**
          * 显示弹框
          */
        async show(id) {
            this.id = id
            this.getDetail(id)
            this.open = true; // 切换显示
        },
        //基本信息查询
        getDetail(policySubmitId){
            getPolicySubmit(policySubmitId).then(response => {
                this.form=response.data
            });
        },
        
        //审批
        edit(status){
            let data={
                policySubmitId:this.form.policySubmitId,
                policySubmitStatus:status
            }
            updatePolicySubmit(data).then(response => {
              this.$modal.msgSuccess("操作成功");
              this.open = false;
              this.$emit("submit", 'updata');
            })
        },
        //查询提报信息
        getList() {
            this.loading=true
            let data={
                policySubmitId:this.form.policySubmitId
            }
            getInfo(data).then(response => {
                console.log("res",response.rows)
                this.listData=response.rows
                this.loading=false
            });
        },
        //tab切换
        handleClick(tab, event) {
            console.log(tab, event);
            if(this.activeName==='first'){
                this.getDetail(this.id)
            }else{
                this.getList()
            }
        },
        handelClose(){
            this.activeName='first'
        },
    }
};
</script>
<style scoped>
h3 {
    color: black;
}
.btn{
    width: 26%;
    margin: 0 auto;
}
</style>