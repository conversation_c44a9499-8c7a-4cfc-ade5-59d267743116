module.exports =
/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "/dist/";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 139);
/******/ })
/************************************************************************/
/******/ ({

/***/ 139:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: external "throttle-debounce/debounce"
var debounce_ = __webpack_require__(18);
var debounce_default = /*#__PURE__*/__webpack_require__.n(debounce_);

// EXTERNAL MODULE: external "element-ui/lib/utils/types"
var types_ = __webpack_require__(17);

// EXTERNAL MODULE: external "element-ui/lib/utils/dom"
var dom_ = __webpack_require__(2);

// CONCATENATED MODULE: ./packages/infinite-scroll/src/main.js




var getStyleComputedProperty = function getStyleComputedProperty(element, property) {
  if (element === window) {
    element = document.documentElement;
  }

  if (element.nodeType !== 1) {
    return [];
  }
  // NOTE: 1 DOM access here
  var css = window.getComputedStyle(element, null);
  return property ? css[property] : css;
};

var entries = function entries(obj) {
  return Object.keys(obj || {}).map(function (key) {
    return [key, obj[key]];
  });
};

var getPositionSize = function getPositionSize(el, prop) {
  return el === window || el === document ? document.documentElement[prop] : el[prop];
};

var getOffsetHeight = function getOffsetHeight(el) {
  return getPositionSize(el, 'offsetHeight');
};

var getClientHeight = function getClientHeight(el) {
  return getPositionSize(el, 'clientHeight');
};

var scope = 'ElInfiniteScroll';
var attributes = {
  delay: {
    type: Number,
    default: 200
  },
  distance: {
    type: Number,
    default: 0
  },
  disabled: {
    type: Boolean,
    default: false
  },
  immediate: {
    type: Boolean,
    default: true
  }
};

var main_getScrollOptions = function getScrollOptions(el, vm) {
  if (!Object(types_["isHtmlElement"])(el)) return {};

  return entries(attributes).reduce(function (map, _ref) {
    var key = _ref[0],
        option = _ref[1];
    var type = option.type,
        defaultValue = option.default;

    var value = el.getAttribute('infinite-scroll-' + key);
    value = Object(types_["isUndefined"])(vm[value]) ? value : vm[value];
    switch (type) {
      case Number:
        value = Number(value);
        value = Number.isNaN(value) ? defaultValue : value;
        break;
      case Boolean:
        value = Object(types_["isDefined"])(value) ? value === 'false' ? false : Boolean(value) : defaultValue;
        break;
      default:
        value = type(value);
    }
    map[key] = value;
    return map;
  }, {});
};

var getElementTop = function getElementTop(el) {
  return el.getBoundingClientRect().top;
};

var main_handleScroll = function handleScroll(cb) {
  var _scope = this[scope],
      el = _scope.el,
      vm = _scope.vm,
      container = _scope.container,
      observer = _scope.observer;

  var _getScrollOptions = main_getScrollOptions(el, vm),
      distance = _getScrollOptions.distance,
      disabled = _getScrollOptions.disabled;

  if (disabled) return;

  var containerInfo = container.getBoundingClientRect();
  if (!containerInfo.width && !containerInfo.height) return;

  var shouldTrigger = false;

  if (container === el) {
    // be aware of difference between clientHeight & offsetHeight & window.getComputedStyle().height
    var scrollBottom = container.scrollTop + getClientHeight(container);
    shouldTrigger = container.scrollHeight - scrollBottom <= distance;
  } else {
    var heightBelowTop = getOffsetHeight(el) + getElementTop(el) - getElementTop(container);
    var offsetHeight = getOffsetHeight(container);
    var borderBottom = Number.parseFloat(getStyleComputedProperty(container, 'borderBottomWidth'));
    shouldTrigger = heightBelowTop - offsetHeight + borderBottom <= distance;
  }

  if (shouldTrigger && Object(types_["isFunction"])(cb)) {
    cb.call(vm);
  } else if (observer) {
    observer.disconnect();
    this[scope].observer = null;
  }
};

/* harmony default export */ var main = ({
  name: 'InfiniteScroll',
  inserted: function inserted(el, binding, vnode) {
    var cb = binding.value;

    var vm = vnode.context;
    // only include vertical scroll
    var container = Object(dom_["getScrollContainer"])(el, true);

    var _getScrollOptions2 = main_getScrollOptions(el, vm),
        delay = _getScrollOptions2.delay,
        immediate = _getScrollOptions2.immediate;

    var onScroll = debounce_default()(delay, main_handleScroll.bind(el, cb));

    el[scope] = { el: el, vm: vm, container: container, onScroll: onScroll };

    if (container) {
      container.addEventListener('scroll', onScroll);

      if (immediate) {
        var observer = el[scope].observer = new MutationObserver(onScroll);
        observer.observe(container, { childList: true, subtree: true });
        onScroll();
      }
    }
  },
  unbind: function unbind(el) {
    var _el$scope = el[scope],
        container = _el$scope.container,
        onScroll = _el$scope.onScroll;

    if (container) {
      container.removeEventListener('scroll', onScroll);
    }
  }
});
// CONCATENATED MODULE: ./packages/infinite-scroll/index.js


/* istanbul ignore next */
main.install = function (Vue) {
  Vue.directive(main.name, main);
};

/* harmony default export */ var infinite_scroll = __webpack_exports__["default"] = (main);

/***/ }),

/***/ 17:
/***/ (function(module, exports) {

module.exports = require("element-ui/lib/utils/types");

/***/ }),

/***/ 18:
/***/ (function(module, exports) {

module.exports = require("throttle-debounce/debounce");

/***/ }),

/***/ 2:
/***/ (function(module, exports) {

module.exports = require("element-ui/lib/utils/dom");

/***/ })

/******/ });
