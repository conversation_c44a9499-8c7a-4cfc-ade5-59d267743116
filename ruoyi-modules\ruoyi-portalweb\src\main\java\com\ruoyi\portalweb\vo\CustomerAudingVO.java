package com.ruoyi.portalweb.vo;

import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;

/**
 *
 */
public class CustomerAudingVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String compName;
    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String address;

    /**
     * 公司电话
     */
    @ApiModelProperty(value = "公司电话")
    private String compPhone;

    /**
     * 公司法人
     */
    @ApiModelProperty(value = "公司法人")
    private String compLegal;
    /**
     * 税号(统一社会信用代码)
     */
    @ApiModelProperty(value = "税号(统一社会信用代码)")
    private String taxNo;
    /**
     * 注册资本
     */
    @ApiModelProperty(value = "注册资本")
    private String regCapital;
    /**
     * 成立日期
     */
    @ApiModelProperty(value = "成立日期")
    private String estiblishTime;

    /**
     * 企业状态
     */
    @ApiModelProperty(value = "企业状态")
    private String regStatus;
    /**
     * 注册号
     */
    @ApiModelProperty(value = "注册号")
    private String regNumber;
    /**
     * 组织机构代码
     */
    @ApiModelProperty(value = "组织机构代码")
    private String orgNumber;
    /**
     * 企业类型
     */
    @ApiModelProperty(value = "企业类型")
    private String companyOrgType;
    /**
     * 经营开始时间--经营结束时间
     */
    @ApiModelProperty(value = "")
    private String fromTime;//fromTime==toTime
    /**
     * 行业
     */
    @ApiModelProperty(value = "行业")
    private String industry;
    /**
     * 核准时间
     */
    @ApiModelProperty(value = "核准时间")
    private String approvedTime;
    /**
     * 实际注册资金
     */
    @ApiModelProperty(value = "实际注册资金")
    private String actualCapital;
    /**
     * 人员规模
     */
    @ApiModelProperty(value = "人员规模")
    private String staffNumRange;
    /**
     * 参保人数
     */
    @ApiModelProperty(value = "参保人数")
    private String socialStaffNum;
    /**
     * 登记机关
     */
    @ApiModelProperty(value = "登记机关")
    private String regInstitute;
    /**
     * 曾用名
     */
    @ApiModelProperty(value = "曾用名")
    private String historyNameList;
    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    private String property3;

    public String getCompName() {
        return compName;
    }

    public void setCompName(String compName) {
        this.compName = compName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCompPhone() {
        return compPhone;
    }

    public void setCompPhone(String compPhone) {
        this.compPhone = compPhone;
    }

    public String getCompLegal() {
        return compLegal;
    }

    public void setCompLegal(String compLegal) {
        this.compLegal = compLegal;
    }

    public String getTaxNo() {
        return taxNo;
    }

    public void setTaxNo(String taxNo) {
        this.taxNo = taxNo;
    }

    public String getRegCapital() {
        return regCapital;
    }

    public void setRegCapital(String regCapital) {
        this.regCapital = regCapital;
    }

    public String getEstiblishTime() {
        return estiblishTime;
    }

    public void setEstiblishTime(String estiblishTime) {
        this.estiblishTime = estiblishTime;
    }

    public String getRegStatus() {
        return regStatus;
    }

    public void setRegStatus(String regStatus) {
        this.regStatus = regStatus;
    }

    public String getRegNumber() {
        return regNumber;
    }

    public void setRegNumber(String regNumber) {
        this.regNumber = regNumber;
    }

    public String getOrgNumber() {
        return orgNumber;
    }

    public void setOrgNumber(String orgNumber) {
        this.orgNumber = orgNumber;
    }

    public String getCompanyOrgType() {
        return companyOrgType;
    }

    public void setCompanyOrgType(String companyOrgType) {
        this.companyOrgType = companyOrgType;
    }

    public String getFromTime() {
        return fromTime;
    }

    public void setFromTime(String fromTime) {
        this.fromTime = fromTime;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getApprovedTime() {
        return approvedTime;
    }

    public void setApprovedTime(String approvedTime) {
        this.approvedTime = approvedTime;
    }

    public String getActualCapital() {
        return actualCapital;
    }

    public void setActualCapital(String actualCapital) {
        this.actualCapital = actualCapital;
    }

    public String getStaffNumRange() {
        return staffNumRange;
    }

    public void setStaffNumRange(String staffNumRange) {
        this.staffNumRange = staffNumRange;
    }

    public String getSocialStaffNum() {
        return socialStaffNum;
    }

    public void setSocialStaffNum(String socialStaffNum) {
        this.socialStaffNum = socialStaffNum;
    }

    public String getRegInstitute() {
        return regInstitute;
    }

    public void setRegInstitute(String regInstitute) {
        this.regInstitute = regInstitute;
    }

    public String getHistoryNameList() {
        return historyNameList;
    }

    public void setHistoryNameList(String historyNameList) {
        this.historyNameList = historyNameList;
    }

    public String getProperty3() {
        return property3;
    }

    public void setProperty3(String property3) {
        this.property3 = property3;
    }

    public String getRegLocation() {
        return regLocation;
    }

    public void setRegLocation(String regLocation) {
        this.regLocation = regLocation;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    private String regLocation;
    /**
     * 经营范围
     */
    @ApiModelProperty(value = "经营范围")
    private String businessScope;


}
