package com.ruoyi.portalconsole.service;

import java.util.List;
import com.ruoyi.portalconsole.domain.AppStore;
import com.ruoyi.portalconsole.domain.vo.AppStoreVO;

/**
 * 应用商店Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface IAppStoreService 
{
    /**
     * 查询应用商店
     * 
     * @param appStoreId 应用商店主键
     * @return 应用商店
     */
    public AppStoreVO selectAppStoreByAppStoreId(Long appStoreId);

    /**
     * 查询应用商店列表
     * 
     * @param appStore 应用商店
     * @return 应用商店集合
     */
    public List<AppStoreVO> selectAppStoreList(AppStore appStore);

    /**
     * 新增应用商店
     * 
     * @param appStore 应用商店
     * @return 结果
     */
    public int insertAppStore(AppStore appStore);

    /**
     * 修改应用商店
     * 
     * @param appStore 应用商店
     * @return 结果
     */
    public int updateAppStore(AppStore appStore);

    /**
     * 批量删除应用商店
     * 
     * @param appStoreIds 需要删除的应用商店主键集合
     * @return 结果
     */
    public int deleteAppStoreByAppStoreIds(Long[] appStoreIds);

    /**
     * 删除应用商店信息
     * 
     * @param appStoreId 应用商店主键
     * @return 结果
     */
    public int deleteAppStoreByAppStoreId(Long appStoreId);

    public int auditAppStore(AppStoreVO appStore);
}
