<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="10">
          <!-- <el-form-item label="机构名称" prop="business_no">
            <el-select style="width: 100%;" v-model="form.business_no" filterable remote reserve-keyword
              placeholder="请输入机构名称" :remote-method="searchFun" @change="handleEnterpriseSelect2" :loading="loading">
              <el-option v-for="item in enterpriseOptions" :key="item.business_no" :label="item.name" :value="item.business_no">
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="公司名称" prop="company">
            <el-input v-model="form.company" placeholder="请输入公司名称" />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="联系人" prop="linker">
            <el-input v-model="form.linker" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="联系电话" prop="linkphone">
            <el-input v-model="form.linkphone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="需求标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入需求标题" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="10">
            <el-form-item label="需求类型" prop="pubCode">
              <el-select style="width: 360px;" v-model="types" clearable collapse-tags multiple placeholder="请选择">
                  <el-option
                    v-for="item in typeOptions"
                    :key="item.dictLabel"
                    :label="item.dictLabel"
                    :value="item.dictLabel">
                  </el-option>
                </el-select>
            </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="预算" prop="budget">
            <el-input type="number" v-model="form.budget" placeholder="请输入预算" />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="交货地">
            <el-cascader v-model="ress" :options="address" @change="handleAddress" style='width: 100%;'>
            </el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item label="">
          <el-tag :key="tag" v-for="tag in keywords" closable :disable-transitions="false" @close="handleClose2(tag)">
            {{tag}}
          </el-tag>
          <el-button type="primary" class="button-new-tag" size="small" @click="extract">抽取关键字</el-button>
          <el-input class="input-new-tag" @change="handleInputConfirm3" v-model="inputValue3" ref="saveTagInput3"
                  size="small">
                </el-input>
                <el-button class="button-new-tag" size="small" @click="handleInputConfirm3">+ 新增</el-button>
        </el-form-item>
      </el-row>
      <el-form-item label="需求概述" prop="description">
        <el-input type="textarea" :rows="5" v-model="form.description" placeholder="请输入需求概述" />
      </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="产品分类">
              <treeselect filterable @select="selectMenu" :options="menuOptions" :normalizer="normalizer" :show-count="true"
                placeholder="选择" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
              <el-input class="input-new-tag" @change="handleInputConfirm2" v-model="inputValue2"
                  size="small" />
                </el-input>
                <el-button class="button-new-tag" size="small" @click="handleInputConfirm2">+ 手动新增</el-button>
          </el-col>
        </el-row>
      <el-row style="margin-left: 120px;margin-bottom: 10px;">
        <el-col :span="24">
          <el-tag :key="tag" v-for="tag in categorys" closable :disable-transitions="false" @close="handleClose3(tag)">
            {{tag}}
          </el-tag>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="应用领域">
            <el-tag :key="tag" v-for="tag in applications" closable :disable-transitions="false"
              @close="handleClose(tag)">
              {{tag}}
            </el-tag>
            <el-input class="input-new-tag" @change="handleInputConfirm" v-model="inputValue" ref="saveTagInput"
              size="small" />
            </el-input>
            <el-button class="button-new-tag" size="small" @click="handleInputConfirm">+ 新增</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="产品照片" prop="">
        <el-upload :action="actionUrl" :headers="headers" list-type="picture-card" :file-list="fileList"
          :on-preview="handlePicturePreview" :on-remove="handleImgRemove" :on-success="handleImgSuccess">
          <i class="el-icon-plus"></i>
        </el-upload>
        <el-dialog :visible.sync="dialogVisible">
          <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
      </el-form-item>
      <el-row>
        <el-form-item label="附件" prop="">
          <el-upload class="upload-demo" drag :file-list='fileAddList' :limit='10' :headers=headers :action="actionUrl"
            :on-success="handleFileSuccess" :on-remove="handleFileRemove" :on-preview="handleFilePreview">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          </el-upload>
        </el-form-item>
      </el-row>
      <el-divider content-position="left"></el-divider>
      <el-row>
        <el-col :span="8">
          <el-form-item label="发布人" prop="create_name">
            <el-input v-model="form.create_name" :disabled="true" placeholder="请输入发布人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发布人电话" prop="telphone">
            <el-input v-model="form.telphone" placeholder="请输入发布人电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="平台对接人" prop="broker">
            <el-input v-model="form.broker" placeholder="请输入平台对接人" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="审核状态" prop="verify">
            <el-select v-model="form.verify" placeholder="审核状态">
              <el-option v-for="dict in verify" :key="dict.key" :label="dict.value" :value="dict.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="显示状态" prop="state">
            <el-select v-model="form.state" placeholder="显示状态">
              <el-option v-for="dict in state" :key="dict.key" :label="dict.value" :value="dict.key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="推荐状态" prop="recommend">
            <el-select v-model="form.recommend" placeholder="推荐状态" clearable size="small">
              <el-option v-for="dict in recommend" :key="dict.key" :label="dict.value" :value="dict.key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="处理状态" prop="opstate">
            <el-select v-model="form.opstate" placeholder="处理状态" clearable size="small">
              <el-option v-for="dict in opstate" :key="dict.key" :label="dict.value" :value="dict.key" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align: right;">
      <el-button :loading="btnload" type="primary" @click="submitForm">保 存</el-button>
    </div>
  </div>
</template>
<style>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }

  .disabled .el-upload--picture-card {
    display: none !important;
  }

  .el-tag+.el-tag {
    margin-left: 10px;
  }

  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }

  .input-new-tag {
    width: 120px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>

<script>
  // import Treeselect from "@riophae/vue-treeselect";
  // import "@riophae/vue-treeselect/dist/vue-treeselect.css";
  // import {
  //   delRequire,
  //   updateRequire,
  //   getPlatform,
  //   extractKeyword
  // } from "@/api/sso/service";
  // import {
  //   searchCompany
  // } from "@/api/sso/enterprise";
  // import {
  //   getPmcCategory
  // } from "@/api/sso/pmc";
  // import {
  //   getEnums,
  //   getApps
  // } from '@/api/sso/tool';
  // import {
  //   uploadUrl
  // } from "@/api/xipin/oss";
  // import {
  //   getToken
  // } from '@/utils/auth'
  // import {
  //   address
  // } from '@/assets/address2';
  // import {
  //   cates
  // } from '@/assets/list';
  // import {
  //   get_address_label,
  //   get_address_values
  // } from "@/utils/index";
  // import _ from "lodash";
  // import {
  //   getDicts
  // } from "@/api/sso/system/dict/data";
  export default {
    components: {
      // Treeselect
    },
    props: {
      form: Object
    },
    name: "PubContect",
    data() {
      return {
        btnload: false,
        dialogImageUrl: '',
        dialogVisible: false,
        inputValue: '',
        inputValue2: '',
        inputValue3: '',
        applications: [],
        categorys: [],
        types:[],
        typeOptions:[],
        cates:cates,
        // 遮罩层
        loading: true,
        enterpriseOptions: [],
        menuOptions:[],
        searchFun: null,
        //产品照片
        fileList: [],
        fileAddList: [],
        actionUrl: uploadUrl(),
        headers: {
          'Authorization': 'Bearer ' + getToken()
        },
        // 平台列表
        platform: [],
        cate: null,
        keywords: [],
        address: address,
        ress: [],
        // 评级列表
        grade: [],
        state: [{
            "key": "1",
            "value": "上架"
          },
          {
            "key": "0",
            "value": "下架"
          }
        ],
        recommend: [{
            "key": "1",
            "value": "推荐"
          },
          {
            "key": "0",
            "value": "普通"
          }
        ],
        status: [],
        opstate: [],
        // 状态列表
        verify: [],
        // 展示类型列表
        displays: [],
        // 表单校验
        rules: {
          title: [{
            required: true,
            message: "需求标题不能为空",
            trigger: "blur"
          }],
          platform: [{
            required: true,
            message: "发布平台不能为空",
            trigger: "blur"
          }],
          company: [{
            required: true,
            message: "公司名称不能为空",
            trigger: "blur"
          }],
          // business_no: [{
          //   required: true,
          //   message: "公司名称不能为空",
          //   trigger: "blur"
          // }],
          description: [{
            required: true,
            message: "'需求描述不能为空",
            trigger: ["blur"]
          }],
          linker: [{
            required: true,
            message: "联系人不能为空",
            trigger: "blur"
          }],
          linkphone: [{
              required: true,
              message: "联系电话不能为空",
              trigger: "blur"
            },
            {
              pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
              message: "请输入正确的手机号码",
              trigger: "blur"
            }
          ],
          verify: [{
            required: true,
            message: "审核状态不能为空",
            trigger: "change"
          }],
          state: [{
            required: true,
            message: "上架状态不能为空",
            trigger: "change"
          }],
        },
      };
    },
    created() {
      this.form.state = this.form.state + ''
      this.form.recommend = this.form.recommend + ''
      this.getApps();
      this.getEnums();
      this.getDicts();
      this.getTreeselect()
      this.fileList = []
      this.fileAddList = []
      var pictures = this.form.pictures!=''?this.form.pictures.split('@'):[]
      var attaches = this.form.attaches
      if (pictures.length > 0) {
        pictures.forEach(e => {
          this.fileList.push({
            name: e,
            url: e
          });
        });
      }
      if (attaches && attaches.length > 0) {
        attaches.forEach(e => {
          this.fileAddList.push({
            name: e.name,
            fileOriName: e.name,
            url: e.url
          });
        });
      }
      if (this.form.application) {
        this.applications = this.form.application.split('/')
      }
      if (this.form.category) {
        this.categorys = this.form.category.split('/')
      }
      if (this.form.type) {
        this.types = this.form.type.split('/')
      }
      let province = this.form.province;
      let city = this.form.city;
      let values = get_address_values([province, city, '']);
      this.ress = values;
      this.keywords = this.form.keywords || []
      this.searchFun = _.debounce(this.remoteMethod, 1000, {
        leading: false,
        trailing: true,
      });
      if (this.form.business_no) {
        this.enterpriseOptions.push({
          "business_no": this.form.business_no,
          "name": this.form.company
        })
      }
    },
    methods: {
      /** 转换菜单数据结构 */
      normalizer(node) {
        if (node.children && !node.children.length) {
          delete node.children;
        }
        return {
          id: node.id,
          label: node.name,
          children: node.children
        };
      },
      getTreeselect() {
        // getPmcCategory().then(res => {
        //   this.menuOptions = [];
        //   const menu = {
        //     id: 0,
        //     name: '选择分类',
        //     children: []
        //   };
        //   menu.children = res.result
        //   this.menuOptions.push(menu);
        // })
        this.menuOptions = [];
        const menu = {
          id: 0,
          name: '选择分类',
          children: []
        };
        menu.children = this.cates
        this.menuOptions.push(menu);
      },
      selectMenu(node) {
        if (node.name != '选择分类') {
          this.categorys.push(node.name);
        }
      },
      /* 选择省市区*/
      handleAddress(e) {
        let data = get_address_label(e);
        this.form.province = data[0];
        this.form.city = data[1];
      },
      handleInputConfirm2() {
        let inputValue = this.inputValue2;
        if (inputValue) {
          this.categorys.push(inputValue);
        }
        this.inputValue2 = '';
      },
      extract() {
        var text = this.form.description.replace(/<.*?>/g, "")
        if (text && text.length >= 2) {
          extractKeyword({
            text: text,
            size: "5"
          }).then((response) => {
            this.msgSuccess("抽取成功");
            this.keywords = response.data
          }).catch((err) => {
            this.msgError("抽取失败");

          });
        }
      },
      /**
       *
       远程搜索企业名称
       */
      remoteMethod(query) {
        if (query.length >= 2) {
          searchCompany({
            name: query
          }).then((response) => {
            this.enterpriseOptions = response.data;
            this.loading = false;
          }).catch(() => {
            this.loading = false;
          });
        } else {
          this.enterpriseOptions = [];
          this.loading = false;
        }
      },
      /* 获取平台列表 */
      getApps() {
        getApps().then(res => {
          this.platform = res.data;
        })
      },
      /* 获取评级列表 */
      getEnums() {
        getEnums().then(res => {
          this.grade = res.data.requiregrade;
          this.status = res.data.requirestatus;
          this.opstate = res.data.requireopstate;
          this.verify = res.data.requireverify;
          this.displays = res.data.requiredisplay;
        })
      },
      handleEnterpriseSelect2(no) {
        const list = this.enterpriseOptions.filter((item) => {
          return item.business_no === no;
        });
        if (list.length > 0) {
          this.form.company = list[0].name;
          this.form.business_no = list[0].business_no;
          console.log(this.form.company)
        }
      },
      /* 获取评级列表 */
      getDicts() {
          getDicts("xp_require_category").then(res => {
            this.typeOptions = res.data;
          })
      },
      handleClose(tag) {
        this.applications.splice(this.applications.indexOf(tag), 1);
      },
      handleClose2(tag) {
        this.keywords.splice(this.keywords.indexOf(tag), 1);
      },
      handleClose3(tag) {
        this.categorys.splice(this.categorys.indexOf(tag), 1);
      },
      handleInputConfirm() {
        let inputValue = this.inputValue;
        if (inputValue) {
          this.applications.push(inputValue);
        }
        this.inputValue = '';
      },
      handleInputConfirm3() {
        let inputValue3 = this.inputValue3;
        if (inputValue3) {
          this.keywords.push(inputValue3);
        }
        this.inputValue3 = '';
      },
      handlePicturePreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
      },
      handleFilePreview(file) {
        window.open(file.url);
      },
      handleImgRemove(file, fileList) {
        this.fileList = fileList
      },
      handleFileRemove(file, fileList) {
        this.fileAddList = fileList
      },
      handleImgSuccess(res, file, fileList) {
        //此处写上传oss成功之后的逻辑
        // if (res.code == 200) {
        //   res.data.name = res.data.fileOriName
        //   this.fileList.push(res.data);
        // }
      },
      handleFileSuccess(res, file, fileList) {
        //此处写上传oss成功之后的逻辑
        // if (res.code == 200) {
        //   res.data.name = res.data.fileOriName
        //   this.fileAddList.push(res.data);
        // }
      },
      /** 提交按钮 */
      submitForm() {
        // this.$refs["form"].validate(valid => {
        //   this.form.cover = this.fileList.length > 0 ? this.fileList[0].url : null
        //   var urls = []
        //   var files = []
        //   for (var i in this.fileList) {
        //     urls.push(this.fileList[i].url)
        //   }
        //   for (var i in this.fileAddList) {
        //     files.push({
        //       "name": this.fileAddList[i].fileOriName,
        //       "url": this.fileAddList[i].url
        //     })
        //   }
        //   this.form.pictures = urls.join('@')
        //   this.form.attaches = files.length > 0 ? JSON.stringify(files) : null
        //   this.form.application = this.applications.join('/')
        //   this.form.category = this.categorys.join('/')
        //   this.form.keywords = this.keywords
        //   if(this.types){
        //     this.form.type = this.types.join('/')
        //   }
        //   if (valid) {
        //     this.btnload = true
        //     updateRequire(this.form).then(response => {
        //       if (response.code === 200) {
        //         this.btnload = false
        //         this.msgSuccess("修改成功");
        //       }
        //     });
        //   }
        // });
      }
    }
  };
</script>
