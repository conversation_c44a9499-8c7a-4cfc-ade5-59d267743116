package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysProduct;

/**
 * 产品信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface ISysProductService 
{
    /**
     * 查询产品信息
     * 
     * @param productId 产品信息主键
     * @return 产品信息
     */
    public SysProduct selectSysProductByProductId(Long productId);

    /**
     * 查询产品信息列表
     * 
     * @param sysProduct 产品信息
     * @return 产品信息集合
     */
    public List<SysProduct> selectSysProductList(SysProduct sysProduct);

    /**
     * 新增产品信息
     * 
     * @param sysProduct 产品信息
     * @return 结果
     */
    public int insertSysProduct(SysProduct sysProduct);

    /**
     * 修改产品信息
     * 
     * @param sysProduct 产品信息
     * @return 结果
     */
    public int updateSysProduct(SysProduct sysProduct);

    /**
     * 批量删除产品信息
     * 
     * @param productIds 需要删除的产品信息主键集合
     * @return 结果
     */
    public int deleteSysProductByProductIds(Long[] productIds);

    /**
     * 删除产品信息信息
     * 
     * @param productId 产品信息主键
     * @return 结果
     */
    public int deleteSysProductByProductId(Long productId);
}
