package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.system.domain.dto.LaboratoryWithTestingItemsDTO;
import com.ruoyi.system.service.ILaboratoryInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.LaboratoryInfo;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.system.domain.dto.LabTypeTestingItemsDTO;

/**
 * 实验室信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@RestController
@RequestMapping("/laboratoryInfo")
public class LaboratoryInfoController extends BaseController
{
    @Autowired
    private ILaboratoryInfoService laboratoryInfoService;

    /**
     * 查询实验室信息列表
     */

    @GetMapping("/list")
    public TableDataInfo list(LaboratoryInfo laboratoryInfo)
    {
        startPage();
        List<LaboratoryInfo> list = laboratoryInfoService.selectLaboratoryInfoList(laboratoryInfo);
        return getDataTable(list);
    }

    /**
     * 导出实验室信息列表
     */

    @Log(title = "实验室信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LaboratoryInfo laboratoryInfo)
    {
        List<LaboratoryInfo> list = laboratoryInfoService.selectLaboratoryInfoList(laboratoryInfo);
        ExcelUtil<LaboratoryInfo> util = new ExcelUtil<LaboratoryInfo>(LaboratoryInfo.class);
        util.exportExcel(response, list, "实验室信息数据");
    }

    /**
     * 获取实验室信息详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(laboratoryInfoService.selectLaboratoryInfoById(id));
    }

    /**
     * 获取实验室信息详细信息（包含关联检测项目）
     */
    @GetMapping(value = "/withTestingItems/{id}")
    public AjaxResult getInfoWithTestingItems(@PathVariable("id") Long id)
    {
        return success(laboratoryInfoService.selectLaboratoryWithTestingItemsById(id));
    }

    /**
     * 查询实验室信息列表（包含关联检测项目）
     */
    @GetMapping("/withTestingItems/list")
    public TableDataInfo listWithTestingItems(LaboratoryInfo laboratoryInfo)
    {
        startPage();
        List<LaboratoryWithTestingItemsDTO> list = laboratoryInfoService.selectLaboratoryWithTestingItemsList(laboratoryInfo);
        return getDataTable(list);
    }

    /**
     * 根据实验室类型查询所有检测项目
     */
    @GetMapping("/testingItemsByLabType/{labType}")
    public AjaxResult getTestingItemsByLabType(@PathVariable("labType") String labType)
    {
        LabTypeTestingItemsDTO dto = laboratoryInfoService.selectTestingItemsByLabType(labType);
        return success(dto);
    }

    /**
     * 新增实验室信息
     */

    @Log(title = "实验室信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LaboratoryInfo laboratoryInfo)
    {
        return toAjax(laboratoryInfoService.insertLaboratoryInfo(laboratoryInfo));
    }

    /**
     * 修改实验室信息
     */

    @Log(title = "实验室信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LaboratoryInfo laboratoryInfo)
    {
        return toAjax(laboratoryInfoService.updateLaboratoryInfo(laboratoryInfo));
    }

    /**
     * 删除实验室信息
     */

    @Log(title = "实验室信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(laboratoryInfoService.deleteLaboratoryInfoByIds(ids));
    }
}
