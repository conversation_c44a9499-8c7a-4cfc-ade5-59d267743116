package com.ruoyi.portalconsole.mapper;

import java.util.List;
import com.ruoyi.portalconsole.domain.IndustryType;

/**
 * 行业类别Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface IndustryTypeMapper 
{
    /**
     * 查询行业类别
     * 
     * @param industryTypeId 行业类别主键
     * @return 行业类别
     */
    public IndustryType selectIndustryTypeByIndustryTypeId(Long industryTypeId);

    /**
     * 查询行业类别列表
     * 
     * @param industryType 行业类别
     * @return 行业类别集合
     */
    public List<IndustryType> selectIndustryTypeList(IndustryType industryType);

    /**
     * 新增行业类别
     * 
     * @param industryType 行业类别
     * @return 结果
     */
    public int insertIndustryType(IndustryType industryType);

    /**
     * 修改行业类别
     * 
     * @param industryType 行业类别
     * @return 结果
     */
    public int updateIndustryType(IndustryType industryType);

    /**
     * 删除行业类别
     * 
     * @param industryTypeId 行业类别主键
     * @return 结果
     */
    public int deleteIndustryTypeByIndustryTypeId(Long industryTypeId);

    /**
     * 批量删除行业类别
     * 
     * @param industryTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIndustryTypeByIndustryTypeIds(Long[] industryTypeIds);
}
