package com.ruoyi.system.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
//import com.ruoyi.im.api.RemoteImChatroomMsgService;
//import com.ruoyi.im.api.RemoteImUserService;
//import com.ruoyi.im.api.domain.ImChatroomMsg;
import com.ruoyi.system.domain.SysUserFriend;
import com.ruoyi.system.domain.SysUserFriendHistory;
import com.ruoyi.system.domain.UserChatGroup;
import com.ruoyi.system.domain.UserChatMessage;
import com.ruoyi.system.service.ISysChatFriendService;
import com.ruoyi.system.service.ISysChatGroupService;
import com.ruoyi.system.service.ISysUserChatService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStreamReader;

@RestController
@RequestMapping(value = "/sysUserChat",produces = "application/json;charset=UTF-8")
@RefreshScope
public class SysUserChatController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(SysUserChatController.class);

    @Autowired
    private ISysUserChatService iSysUserChatService;
    @Autowired
    private ISysChatFriendService iSysChatFriendService;
    @Autowired
    private ISysChatGroupService iSysChatGroupService;
//    @Autowired
//    private RemoteImUserService remoteImUserService;
//    @Autowired
//    private RemoteImChatroomMsgService remoteImChatroomMsgService;

//    @Value("${wx.redirectUri}")
    public String redirectUri;
//    @Value("${wx.appId}")
    public String appId;
    /**
     * 用户绑定微信公众号用于消息提醒
     * @param state  用户userName
     */
    @GetMapping(value = "/boundWeiXin")
    public AjaxResult boundWeiXin(
            HttpServletResponse response,
            @RequestParam(value = "state")String state,
            @RequestParam(value = "code",required = false,defaultValue = "")String code) {
        if(ObjectUtil.isEmpty(code)){
            //TODO 重定向跳转到微信获取code
            try {
                response.sendRedirect("https://open.weixin.qq.com/connect/oauth2/authorize?appid="+appId +
                        "&redirect_uri="+redirectUri+"&response_type=code&scope=snsapi_base&state="+state+"#wechat_redirect");
            } catch (IOException e) {
                log.error("重定向获取微信code失败{}",e);
                return AjaxResult.error("获取微信code失败，请重新扫码重试");
            }
        }
        //TODO 如果有code通过code获取用户的openid 并绑定
        AjaxResult ajaxResult = iSysUserChatService.boundWeiXin(state,code);

        return ajaxResult;
    }

    /**
     * 解绑用户绑定微信用于离线通知
     * @return
     */
    @GetMapping(value = "/unboundWeiXin")
    public AjaxResult unboundWeiXin() {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajaxResult = iSysUserChatService.unboundWeiXin(userName);

        return ajaxResult;
    }



    /**
     * 初始化老注册用户未有融云token问题
     * @return
     */
    @GetMapping("/initializeRongToken")
    public AjaxResult initializeRongToken(
            @RequestParam(value = "page",required = false)Integer page,
            @RequestParam(value = "rows",required = false)Integer rows) {

        AjaxResult ajax = iSysUserChatService.initializeRongToken(page,rows);
        return ajax;
    }

    /**
     * 获取用户融云token
     */
    @GetMapping("/getRongToken")
    public AjaxResult getRongToken(@RequestParam(value = "userName",required = false,defaultValue = "")String userName) {
        if(ObjectUtil.isEmpty(userName)){
            userName=  SecurityUtils.getUsername();
        }
//        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajax = iSysUserChatService.getRongToken(userName);
        return ajax;
    }
    /**
     * 根据用户信息获取用户详情
     * @param searchUserName 查询的用户名称
     * @return
     */
    @RequestMapping("/getUserDetal")
    public AjaxResult getUserDetal(
            @RequestParam(value = "searchUserName") String searchUserName) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajax = iSysUserChatService.getUserDetal(userName,searchUserName);
        return ajax;
    }

    /**
     *
     * @param userChatMessage
     *         targetId :接收方手机号或群聊id
     *         content：消息内容（内容、多媒体二选一，多媒体只有一个）
     *         mediaUrl：多媒体链接（内容、多媒体二选一，图片只有一个）
     *         contentType 消息类型 0 文字消息 1图片消息 2视频 3音频 4 文件
     *         conversationType 所属会话类型 单聊	1 群聊	3 聊天室	4 系统	6
     *         fileName 文件名
     *
     * @return
     */
    @PostMapping("/sendMessage")
    public AjaxResult sendMessage(@RequestBody UserChatMessage userChatMessage) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajax = iSysUserChatService.sendMessage(userName,userChatMessage);
        return ajax;
    }
    /**
     * 同步用户状态
     * @param
     * @return
     */
    @PostMapping(value = "/reciveUserRongYunState")
    public Integer reciveUserRongYunState(HttpServletRequest request) {
        try {
            InputStreamReader insr = new InputStreamReader(request.getInputStream());
            //读取服务器的响应内容并显示
            String result = "";
            int respInt = insr.read();
            while(respInt != -1){
                result += (char)respInt;
                respInt = insr.read();
            }
            try {
                JSONArray jsonArray = JSONObject.parseArray(result);
                Integer code = iSysUserChatService.reciveUserRongYunState(jsonArray);
//                remoteImUserService.token(jsonArray);
                return code;
            }catch (Exception e){
                log.error("接收融云同步用户状态失败{}",e);
                return 200;
            }
        }catch (Exception e){
            log.error("解析融云推送用户在线状态失败{}",e);
            return 500;
        }
    }
    /**
     * 同步消息状态
     * @param
     * @return
     */
    @PostMapping(value = "/reciveUserMsg")
    public Integer reciveUserMsg(HttpServletRequest request) {
        try {
            String fromUserId = request.getParameter("fromUserId");
            String toUserId = request.getParameter("toUserId");
            String objectName = request.getParameter("objectName");
            String content = request.getParameter("content");
            String channelType = request.getParameter("channelType");
            String msgTimestamp	 = request.getParameter("msgTimestamp");
            String sensitiveType = request.getParameter("sensitiveType");
            if(channelType.equals("TEMPGROUP")){
//                remoteImChatroomMsgService.receive(ImChatroomMsg.builder().fromUserId(fromUserId).toUserId(toUserId)
//                        .objectName(objectName).message(content).msgTimestamp(msgTimestamp).sensitiveType(Integer.parseInt(sensitiveType)).build());

                return 200;
            }
            return  200;
        }catch (Exception e){
            log.error("解析融云推送用户在线状态失败{}",e);
            return 500;
        }
    }

    /**
     * 好友列表
     */
    @GetMapping("/getFriendList")
    public AjaxResult getFriendList() {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajax = iSysChatFriendService.getFriendList(userName);
        return ajax;
    }

    /**
     * 删除好友
     *
     * @param friendUseName 好友friendUseName
     */
    @RequestMapping("/deleteFriend")
    public AjaxResult deleteFriend(@RequestParam(value = "friendUseName") String friendUseName) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajax = iSysChatFriendService.deleteFriend(friendUseName, userName);
        return ajax;
    }

    /**
     * 修改好友信息
     *
     * @param sysUserFriend
     *                      friendUserName 好友的userName
     *                      remark 备注
     */
    @RequestMapping("/updateFriend")
    public AjaxResult updateFriend(@RequestBody SysUserFriend sysUserFriend) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        sysUserFriend.setUserName(userName);
        AjaxResult ajax = iSysChatFriendService.updateFriend(sysUserFriend);
        return ajax;
    }

    /**
     * 查询用户
     *
     * @param searchKey 查询关键词 手机号或用户昵称
     * @param pageNum   页数
     * @param pageSize  条数
     * @return
     */
    @RequestMapping("/searchUser")
    public AjaxResult searchUser(
            @RequestParam(value = "searchKey") String searchKey,
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        if(ObjectUtil.isEmpty(searchKey)){
            return AjaxResult.success();
        }
        AjaxResult ajax = iSysChatFriendService.searchUser(searchKey, pageNum, pageSize,userName);
        return ajax;
    }




    /**
     * 申请好友
     *
     * @param sysUserFriendHistory userName 被申请人手机号
     *                             remark 申请备注
     * @return
     */
    @RequestMapping("/applyFriend")
    public AjaxResult applyFriend(@RequestBody SysUserFriendHistory sysUserFriendHistory) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        sysUserFriendHistory.setAppyUserName(userName);
        AjaxResult ajax = iSysChatFriendService.applyFriend(sysUserFriendHistory);
        return ajax;
    }
    /**
     * 好友申请历史
     * @param pageNum
     * @param pageSize
     */
    @RequestMapping("/getApplyFriendList")
    public AjaxResult getApplyFriendList(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajax = iSysChatFriendService.getApplyFriendList(userName, pageNum, pageSize);
        return ajax;
    }
    /**
     * 更改申请状态
     * @param sysUserFriendHistory
     *         id 表主键id
     *         applyState 1 通过 2拒绝
     *
     */
    @RequestMapping("/changeApplyState")
    public AjaxResult changeApplyState(@RequestBody SysUserFriendHistory sysUserFriendHistory) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        sysUserFriendHistory.setUserName(userName);
        AjaxResult ajax = iSysChatFriendService.changeApplyState(sysUserFriendHistory);
        return ajax;
    }


    /**
     * 创建群组
     * @param groupUserNames 群组成员 多个逗号拼接
     * @param groupName 群组名称
     * @return
     */
    @RequestMapping("/createChatGroup")
    public AjaxResult createChatGroup(String groupUserNames,String groupName) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajax = iSysChatGroupService.createChatGroup(userName,groupUserNames,groupName);
        return ajax;
    }
    /**
     * 修改群组
     * @param userChatGroup
     *          id  群组id
     *          groupName 群组名称
     *          avatar 群头像
     *
     * @return
     */
    @RequestMapping("/updateChatGroup")
    public AjaxResult updateChatGroup(@RequestBody UserChatGroup userChatGroup) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajax = iSysChatGroupService.updateChatGroup(userName,userChatGroup);
        return ajax;
    }
    /**
     * 解散群组
     * @param groupId
     * @return
     */
    @RequestMapping("/dismissChatGroup")
    public AjaxResult dismissChatGroup(Long groupId) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajax = iSysChatGroupService.dismissChatGroup(userName,groupId);
        return ajax;
    }

    /**
     * 用户加入群组
     * @param groupId 群组id
     * @param groupUserNames 要加入的用户手机号 多个逗号拼接
     * @return
     */
    @RequestMapping("/joinChatGroup")
    public AjaxResult joinChatGroup(Long groupId,String groupUserNames) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajax = iSysChatGroupService.joinChatGroup(userName,groupId,groupUserNames);
        return ajax;
    }
    /**
     * 用户退出群组
     * @param groupId
     * @param groupUserNames
     * @return
     */
    @RequestMapping("/quitChatGroup")
    public AjaxResult quitChatGroup(Long groupId,String groupUserNames) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajax = iSysChatGroupService.quitChatGroup(userName,groupId,groupUserNames);
        return ajax;
    }
    /**
     * 查询用户群组列表
     * @return
     */
    @RequestMapping("/getUserGroupList")
    public AjaxResult getUserGroupList() {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajax = iSysChatGroupService.getUserGroupList(userName);
        return ajax;
    }

    /**
     * 查询群用户
     * @param groupId
     * @return
     */
    @RequestMapping("/getGroupUserList")
    public AjaxResult getGroupUserList(Long groupId) {
        String userName = SecurityUtils.getUsername();
        if (ObjectUtil.isEmpty(userName)) {
            return AjaxResult.error("未登录，请登录后重试！");
        }
        AjaxResult ajax = iSysChatGroupService.getGroupUserList(userName,groupId);
        return ajax;
    }

}
