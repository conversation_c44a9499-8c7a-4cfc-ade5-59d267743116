<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.BuMemberOnlinePayMapper">
    
    <resultMap type="BuMemberOnlinePay" id="BuMemberOnlinePayResult">
        <result property="id"    column="id"    />
        <result property="payOrderNo"    column="pay_order_no"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="memberId"    column="member_id"    />
        <result property="appStoreOrderNo"    column="app_store_order_no"    />
        <result property="onlinePayStyle"    column="online_pay_style"    />
        <result property="money"    column="money"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDept"    column="create_dept"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="version"    column="version"    />
    </resultMap>

    <sql id="selectBuMemberOnlinePayVo">
        select id, pay_order_no, tenant_id, customer_id, member_id, app_store_order_no, online_pay_style, money, create_by, create_dept, create_time, update_by, update_time, status, is_deleted, version from bu_member_online_pay
    </sql>

    <select id="selectBuMemberOnlinePayList" parameterType="BuMemberOnlinePay" resultMap="BuMemberOnlinePayResult">
        <include refid="selectBuMemberOnlinePayVo"/>
        <where>  
            <if test="payOrderNo != null  and payOrderNo != ''"> and pay_order_no = #{payOrderNo}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="memberId != null "> and member_id = #{memberId}</if>
            <if test="appStoreOrderNo != null  and appStoreOrderNo != ''"> and app_store_order_no = #{appStoreOrderNo}</if>
            <if test="onlinePayStyle != null  and onlinePayStyle != ''"> and online_pay_style = #{onlinePayStyle}</if>
            <if test="money != null "> and money = #{money}</if>
            <if test="createDept != null "> and create_dept = #{createDept}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
            <if test="version != null "> and version = #{version}</if>
        </where>
    </select>
    
    <select id="selectBuMemberOnlinePayById" parameterType="Long" resultMap="BuMemberOnlinePayResult">
        <include refid="selectBuMemberOnlinePayVo"/>
        where id = #{id}
    </select>
    <select id="selectBuMemberOnlinePayByAppOrderNo"  parameterType="String" resultMap="BuMemberOnlinePayResult">
        <include refid="selectBuMemberOnlinePayVo"/>
        where app_sotre_order_no = #{appStoreOrderNo}
    </select>

    <insert id="insertBuMemberOnlinePay" parameterType="BuMemberOnlinePay">
        insert into bu_member_online_pay
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="payOrderNo != null and payOrderNo != ''">pay_order_no,</if>
            <if test="tenantId != null and tenantId != ''">tenant_id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="memberId != null">member_id,</if>
            <if test="appStoreOrderNo != null and appStoreOrderNo != ''">app_store_order_no,</if>
            <if test="onlinePayStyle != null and onlinePayStyle != ''">online_pay_style,</if>
            <if test="money != null">money,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDept != null">create_dept,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="version != null">version,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="payOrderNo != null and payOrderNo != ''">#{payOrderNo},</if>
            <if test="tenantId != null and tenantId != ''">#{tenantId},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="appStoreOrderNo != null and appStoreOrderNo != ''">#{appStoreOrderNo},</if>
            <if test="onlinePayStyle != null and onlinePayStyle != ''">#{onlinePayStyle},</if>
            <if test="money != null">#{money},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDept != null">#{createDept},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="version != null">#{version},</if>
         </trim>
    </insert>

    <update id="updateBuMemberOnlinePay" parameterType="BuMemberOnlinePay">
        update bu_member_online_pay
        <trim prefix="SET" suffixOverrides=",">
            <if test="payOrderNo != null and payOrderNo != ''">pay_order_no = #{payOrderNo},</if>
            <if test="tenantId != null and tenantId != ''">tenant_id = #{tenantId},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="appStoreOrderNo != null and appStoreOrderNo != ''">app_store_order_no = #{appStoreOrderNo},</if>
            <if test="onlinePayStyle != null and onlinePayStyle != ''">online_pay_style = #{onlinePayStyle},</if>
            <if test="money != null">money = #{money},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDept != null">create_dept = #{createDept},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="version != null">version = #{version},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuMemberOnlinePayById" parameterType="Long">
        delete from bu_member_online_pay where id = #{id}
    </delete>

    <delete id="deleteBuMemberOnlinePayByIds" parameterType="String">
        delete from bu_member_online_pay where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>