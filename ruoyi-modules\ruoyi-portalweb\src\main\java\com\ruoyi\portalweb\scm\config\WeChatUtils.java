package com.ruoyi.portalweb.scm.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.portalweb.scm.utils.MD5Utils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.*;

public class WeChatUtils {

    //appid
    public static String weChatAppid = "APPID";

    //appsecret
    public static String weChatAppsecret ="Appsecret";

    //微信支付商户号
    public static String weChatPayMchId = "MchId";

    //支付key
    public static String weChatPayKey = "PayKey";

    public static String weChatRedirectUri = "http://www.baidu.com";

    //支付回调地址
    public static String weChatPayNotifyUrl = "http://www.baidu.com";

    public static String weChatAuthorizeUrl = "https://open.weixin.qq.com/connect/oauth2/authorize";

    public static String weChatAccessTokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token";

    public static String weChatUserinfoUrl = "https://api.weixin.qq.com/sns/userinfo";

    public static String weChatPayUrl = "https://api.mch.weixin.qq.com/pay/unifiedorder";

    public static String weChatPayOrderQueryUrl = "https://api.mch.weixin.qq.com/pay/orderquery";

    //private static final String SYMBOLS = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    private static final String SYMBOLS2 = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    private static final Random RANDOM = new SecureRandom();


    /**
     * 获取微信Jsapi的accessToken
     */
    public static String getAccessToken() throws IOException{
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET";
        url = url.replace("APPID",weChatAppid).replace("APPSECRET",weChatAppsecret);
        String result = HttpUtils.sendGet(url);
        JSONObject jsonObject = JSON.parseObject(result);
        String accessToken = jsonObject.getString("access_token");
        return accessToken;
    }

    /**
     *
     * @Title: getNonceStr
     * @Description: 生成随机字符串
     * @param @return
     * @return String    返回类型
     * @throws
     */
    public static String getNonceStr() {
        String currT = getCurrTime();
        String strT = currT.substring(8, currT.length());
        String strRandom = buildRandom(4) + "";
        return strT + strRandom;
    }



    /**
     *
     * @Title: buildRandom
     * @Description: 生成随机数
     * @param @param length
     * @param @return
     * @return int    返回类型
     * @throws
     */
    public static int buildRandom(int length) {
        int mm= 1;
        double random = Math.random();
        if (random < 0.1) {
            random = random + 0.1;
        }
        for (int i = 0; i < length; i++) {
            mm= mm* 10;
        }
        return (int) ((random * mm));
    }


    /**
     *
     * @Title: getCurrTime
     * @Description: 获取当前时间
     * @param @return
     * @return String    返回类型
     * @throws
     */
    public static String getCurrTime() {
        Date date = new Date();
        SimpleDateFormat of= new SimpleDateFormat("yyyyMMddHHmmss");
        String s = of.format(date);
        return s;
    }


    /**
     * 随机字符串
     * @return
     */
    public static String generateNonceStr() {
        return UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);
    }

    /**
     *
     * @Title: createSignBySha1
     * @Description: 生成签名
     * @param @param params
     * @param @return
     * @return String    返回类型
     * @throws
     */
    @SuppressWarnings("rawtypes")
    public static String createSignBySha1(SortedMap<Object, Object> params) {
        StringBuffer sb = new StringBuffer();
        Set es = params.entrySet();
        Iterator it = es.iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            String v = (String) entry.getValue();
            if (v != null && !v.equals("")) {
                sb.append(k + "=" + v + "&");
            }
        }
        String result = sb.toString().substring(0, sb.toString().length()-1);
        return getSHA1(result);
    }
    /**
     *
     * @Title: getTimestamp
     * @Description: 获取时间戳(秒)
     * @param @return    参数
     * @return String    返回类型
     * @throws
     */
    public static String getTimestamp() {
        return String.valueOf(System.currentTimeMillis() / 1000);
    }


    /**
     *
     * @Title: getSHA1
     * @Description: SHA1签名生成
     * @param @param str
     * @param @return    参数
     * @return String    返回类型
     * @throws
     */
    public static String getSHA1(String str){
        StringBuffer hexstr = new StringBuffer();
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            md.update(str.getBytes());
            byte[] digest = md.digest();
            String shaHex = "";
            for (int i = 0; i < digest.length; i++) {
                shaHex = Integer.toHexString(digest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexstr.append(0);
                }
                hexstr.append(shaHex);
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return hexstr.toString();
    }

    /**
     *
     * @Title: getJsapiTicket
     * @Description: 获取JsapiTicket
     * @param @param access_token
     * @param @return
     * @return String    返回类型
     * @throws
     */
    public static String getJsapiTicket(String access_token)  throws IOException{
        String url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=ACCESS_TOKEN&type=jsapi".replace("ACCESS_TOKEN",access_token);
        String result = HttpUtils.sendGet(url);
        JSONObject jsonObject = JSON.parseObject(result);
        String ticket= jsonObject.getString("ticket");
        return ticket;
    }


    public static String createLinkString(Map<String, String> params){
        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);
        String prestr = "";
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = params.get(key);
//          try {
//              value = URLEncoder.encode(value, "UTF-8");
//          }catch (Exception e){
//              e.printStackTrace();
//          }
            if (i == keys.size() - 1) {//拼接时，不包括最后一个&字符
                prestr = prestr + key + "=" + value;
            } else {
                prestr = prestr + key + "=" + value + "&";
            }
        }
        return prestr;
    }

    public static String generateSignature(final Map<String, String> data, String key, String signType) throws Exception {
        Set<String> keySet = data.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals("sign")) {
                continue;
            }
            if (data.get(k).trim().length() > 0) {
                //参数值为空，则不参与签名
                sb.append(k).append("=").append(data.get(k).trim()).append("&");
            }

        }
        sb.append("key=").append(key);
        if ("MD5".equals(signType)) {
            return MD5(sb.toString()).toUpperCase();
        }else if ("HMACSHA256".equals(signType)) {
            return HMACSHA256(sb.toString(), key);
        }else {
            throw new Exception(String.format("Invalid sign_type: %s", signType));
        }
    }

    public static String MD5(String data) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] array = md.digest(data.getBytes("UTF-8"));
        StringBuilder sb = new StringBuilder();
        for (byte item : array) {
            sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
        }
        return sb.toString().toUpperCase();
    }

    public static String HMACSHA256(String data, String key) throws Exception {
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secret_key = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");
        sha256_HMAC.init(secret_key);
        byte[] array = sha256_HMAC.doFinal(data.getBytes("UTF-8"));
        StringBuilder sb = new StringBuilder();
        for (byte item : array) {
            sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
        }
        return sb.toString().toUpperCase();
    }

    public static String mapToXml(Map<String, String> map) throws Exception {
        StringBuffer sb = new StringBuffer();
        sb.append("<xml>");
        Set<String> set = map.keySet();
        Iterator<String> it = set.iterator();
        while (it.hasNext()) {
            String key = it.next();
            sb.append("<" + key + ">").append(map.get(key)).append("</" + key + ">");
        }
        sb.append("</xml>");
        return sb.toString();

    }

    public static Map<String, String> xmlToMap(String strXML) throws Exception {
        try {
            Map<String, String> data = new HashMap<String, String>();
            DocumentBuilder documentBuilder = newDocumentBuilder();
            InputStream stream = new ByteArrayInputStream(strXML.getBytes("UTF-8"));
            Document doc = documentBuilder.parse(stream);
            doc.getDocumentElement().normalize();
            NodeList nodeList = doc.getDocumentElement().getChildNodes();
            for (int idx = 0; idx < nodeList.getLength(); ++idx) {
                Node node = nodeList.item(idx);
                if (node.getNodeType() == Node.ELEMENT_NODE) {
                    org.w3c.dom.Element element = (org.w3c.dom.Element) node;
                    data.put(element.getNodeName(), element.getTextContent());
                }
            }
            try {
                stream.close();
            } catch (Exception ex) {
                // do nothing
            }
            return data;
        } catch (Exception ex) {
            getLogger().warn("Invalid XML, can not convert to map. Error message: {}. XML content: {}", ex.getMessage(), strXML);
            throw ex;
        }

    }

    public static DocumentBuilder newDocumentBuilder() throws ParserConfigurationException {
        DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
        documentBuilderFactory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        documentBuilderFactory.setFeature("http://xml.org/sax/features/external-general-entities", false);
        documentBuilderFactory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
        documentBuilderFactory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
        documentBuilderFactory.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
        documentBuilderFactory.setXIncludeAware(false);
        documentBuilderFactory.setExpandEntityReferences(false);

        return documentBuilderFactory.newDocumentBuilder();
    }


    public static Document newDocument() throws ParserConfigurationException {
        return newDocumentBuilder().newDocument();
    }

    public static Logger getLogger() {
        Logger logger = LoggerFactory.getLogger("wxpay java sdk");
        return logger;
    }

    /**
     * @TODO :
     * @AUTH : linfeng
     * @DATE : 2020年10月19日 上午9:34:49
     * @return_type : String
     * @return
     */
    public static String generateNonceStr5() {
        char[] nonceChars = new char[6];
        for (int index = 0; index < nonceChars.length; ++index) {
            nonceChars[index] = SYMBOLS2.charAt(RANDOM.nextInt(SYMBOLS2.length()));
        }
        return new String(nonceChars);
    }

    public static byte[] readInput(InputStream in) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int len = 0;
        byte[] buffer = new byte[1024];
        while ((len = in.read(buffer)) > 0) {
            out.write(buffer, 0, len);
        }
        out.close();
        in.close();
        return out.toByteArray();
    }
    public static String createSign(SortedMap<String, String> params, String key){
        StringBuilder sb = new StringBuilder();
        Set<Map.Entry<String, String>> es =  params.entrySet();
        Iterator<Map.Entry<String,String>> it =  es.iterator();

        //生成 stringA="appid=wxd930ea5d5a258f4f&body=test&device_info=1000&mch_id=10000100&nonce_str=ibuaiVcKdpRxkhJA";
        while (it.hasNext()){
            Map.Entry<String,String> entry = (Map.Entry<String,String>)it.next();
            String k = (String)entry.getKey();
            String v = (String)entry.getValue();
            if(null != v && !"".equals(v) && !"sign".equals(k) && !"key".equals(k)){
                sb.append(k+"="+v+"&");
            }
        }

        sb.append("key=").append(key);
        String sign = WeChatUtils.md5(sb.toString()).toUpperCase();
        return sign;
    }
    public static String md5(String data){
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            byte[] array = md5.digest(data.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte item : array) {
                sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
            }
            return sb.toString().toUpperCase();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 生成预支付订单参数
     */
    public static String genPayParams(String orderCode, String money,String openid,String mchId,String secret,String notifyUrl,String clientIp) {

        List<NameValuePair> signParams = new LinkedList<NameValuePair>();
       
         //signParams.add(new BasicNameValuePair("appid", "wxe411837960491937")); // 柠檬豆小程序 应用ID
        //signParams.add(new BasicNameValuePair("appid", "wxe4602a0a1a3c4775")); // 云端研发小程序应用ID
        //signParams.add(new BasicNameValuePair("appid", "wx0d647b53d9ee9557")); // 海洋小程序应用ID
        signParams.add(new BasicNameValuePair("appid", "wx7087db0843201a88")); // 云研工作室小程序应用ID
        signParams.add(new BasicNameValuePair("body", "app"));// 商品描述
        signParams.add(new BasicNameValuePair("mch_id", mchId));// 商户号
        signParams.add(new BasicNameValuePair("nonce_str", WeChatUtils.getNonceStr()));// 随机字符串
        signParams.add(new BasicNameValuePair("notify_url", notifyUrl));// 通知地址
        signParams.add(new BasicNameValuePair("openid", openid));// openid
        signParams.add(new BasicNameValuePair("out_trade_no", orderCode));// 商户订单号
        signParams.add(new BasicNameValuePair("spbill_create_ip", clientIp));// 终端IP
        signParams.add(new BasicNameValuePair("total_fee", money));// 总金额
        signParams.add(new BasicNameValuePair("trade_type", "JSAPI"));// 交易类型
        String sign = genPackageSign(signParams,secret);
        signParams.add(new BasicNameValuePair("sign", sign));
        String xmlstring = toXml(signParams);

        return xmlstring;

    }

    /**
     * 参数转换为XML
     * @param params
     * @return
     */
    public static String toXml(List<NameValuePair> params) {
        StringBuilder sb = new StringBuilder();
        sb.append("<xml>");
        for (int i = 0; i < params.size(); i++) {
            sb.append("<" + params.get(i).getName() + ">");

            sb.append(params.get(i).getValue());
            sb.append("</" + params.get(i).getName() + ">");
        }
        sb.append("</xml>");

        //logger.debug("xml:{}", sb.toString());
        /**
         * 需要注意的 获取到的字符串要编码
         */
        try {
            return new String(sb.toString().getBytes(), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 生成签名
     */
    public static String genPackageSign(List<NameValuePair> params,String secret) {
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < params.size(); i++) {
            sb.append(params.get(i).getName());
            sb.append('=');
            sb.append(params.get(i).getValue());
            sb.append('&');
        }
        sb.append("key=");
        sb.append(secret);
        String packageSign = MD5Utils.getMessageDigest(sb.toString().getBytes()).toUpperCase();
        //logger.debug("订单签名:{}", packageSign);
        return packageSign;
    }
}

