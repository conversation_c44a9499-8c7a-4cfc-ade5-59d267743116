package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.FileShare;

/**
 * 文件共享Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-17
 */
public interface IFileShareService 
{
    /**
     * 查询文件共享
     * 
     * @param id 文件共享主键
     * @return 文件共享
     */
    public FileShare selectFileShareById(Long id);

    /**
     * 查询文件共享列表
     * 
     * @param fileShare 文件共享
     * @return 文件共享集合
     */
    public List<FileShare> selectFileShareList(FileShare fileShare);

    /**
     * 新增文件共享
     * 
     * @param fileShare 文件共享
     * @return 结果
     */
    public int insertFileShare(FileShare fileShare);

    /**
     * 修改文件共享
     * 
     * @param fileShare 文件共享
     * @return 结果
     */
    public int updateFileShare(FileShare fileShare);

    /**
     * 批量删除文件共享
     * 
     * @param ids 需要删除的文件共享主键集合
     * @return 结果
     */
    public int deleteFileShareByIds(Long[] ids);

    /**
     * 删除文件共享信息
     * 
     * @param id 文件共享主键
     * @return 结果
     */
    public int deleteFileShareById(Long id);
}
