package com.ruoyi.portalweb.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.portalweb.api.domain.Company;
import com.ruoyi.portalweb.api.domain.FileDetail;
import com.ruoyi.portalweb.mapper.CompanyMapper;
import com.ruoyi.portalweb.service.ICompanyService;
import com.ruoyi.portalweb.service.IFileDetailService;
import com.ruoyi.portalweb.vo.CompanyVO;
import com.ruoyi.portalweb.vo.CustomerAudingVO;
import com.ruoyi.portalweb.vo.FileDetailVO;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 企业信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@Service
public class CompanyServiceImpl implements ICompanyService {
    @Autowired
    private CompanyMapper companyMapper;
    @Autowired
    private IFileDetailService fileDetailService;

    @Value("${eye.token}")
    private String eyeToken;

    @Value("${eye.requestUrl}")
    private String eyeUrl;

    @Value("${eye.researchUrl}")
    private String eysResearchUrl;



    /**
     * 查询企业信息
     *
     * @param companyId 企业信息主键
     * @return 企业信息
     */
    @Override
    public CompanyVO selectCompanyByCompanyId(Long companyId) {
        CompanyVO companyVO = companyMapper.selectCompanyByCompanyId(companyId);
        if (companyVO != null) {
            List<FileDetailVO> alPictureVOs = fileDetailService.selectPictureList(companyVO.getCompanyId(),"company", "");
            List<FileDetailVO> list0601 = new ArrayList<>();
            List<FileDetailVO> list0602 = new ArrayList<>();
            for (FileDetailVO item : alPictureVOs) {
                if ("0601".equals(item.getFileType())) {
                    list0601.add(item);
                }
                if ("0602".equals(item.getFileType())) {
                    list0602.add(item);
                }
            }
            companyVO.setAlFile0601(list0601);
            companyVO.setAlFile0602(list0602);
        }
        return companyVO;
    }

    @Override
    public CompanyVO selectCompanyByCompanyIdAndMemberId(Long companyId, Long memberId) {
        CompanyVO companyVO = companyMapper.selectCompanyByCompanyIdAndMemberId(companyId, memberId);
        if (companyVO != null) {
            List<FileDetailVO> alPictureVOs = fileDetailService.selectPictureList(companyVO.getCompanyId(),"company", "");
            List<FileDetailVO> list0601 = new ArrayList<>();
            List<FileDetailVO> list0602 = new ArrayList<>();
            for (FileDetailVO item : alPictureVOs) {
                if ("0601".equals(item.getFileType())) {
                    list0601.add(item);
                }
                if ("0602".equals(item.getFileType())) {
                    list0602.add(item);
                }
            }
            companyVO.setAlFile0601(list0601);
            companyVO.setAlFile0602(list0602);
        }
        return companyVO;
    }

    /**
     * 查询企业信息列表
     *
     * @param company 企业信息
     * @return 企业信息
     */
    @Override
    public List<Company> selectCompanyList(Company company) {
        return companyMapper.selectCompanyList(company);
    }

    /**
     * 新增企业信息
     *
     * @param company 企业信息
     * @return 结果
     */
    @Override
    public int insertCompany(CompanyVO company) {
        company.setCreateTime(DateUtils.getNowDate());
        Integer res = companyMapper.insertCompany(company);
        if (res == 1) {
            // 删除原先附件,重保存
            fileDetailService.removeBybillId(company.getCompanyId(),"company", "");
            // 保存图片
            if (company.getAlFileDetailVOs() != null && company.getAlFileDetailVOs().size() > 0) {
                for (FileDetailVO tmpPic : company.getAlFileDetailVOs()) {
                    FileDetail saveFile = tmpPic;
                    tmpPic.setId(null);
                    tmpPic.setParentType("company");
                    tmpPic.setParentId(company.getCompanyId());
                    fileDetailService.insertFileDetail(saveFile);
                }
            }
        }
        return res;
    }

    /**
     * 修改企业信息
     *
     * @param company 企业信息
     * @return 结果
     */
    @Override
    public int updateCompany(CompanyVO company) {
        company.setUpdateTime(DateUtils.getNowDate());
        Integer res = companyMapper.updateCompany(company);
        if (res == 1) {
            // 删除原先附件,重保存
            fileDetailService.removeBybillId(company.getCompanyId(),"company", "");
            // 保存图片
            if (company.getAlFileDetailVOs() != null && company.getAlFileDetailVOs().size() > 0) {
                for (FileDetailVO tmpPic : company.getAlFileDetailVOs()) {
                    FileDetail saveFile = tmpPic;
                    tmpPic.setId(null);
                    tmpPic.setParentType("company");
                    tmpPic.setParentId(company.getCompanyId());
                    fileDetailService.insertFileDetail(saveFile);
                }
            }
        }
        return res;
    }

    @Override
    public CustomerAudingVO searchByCustomerName(String keywords) {
        CustomerAudingVO customerAuding = new CustomerAudingVO();
        if (StringUtils.isNotEmpty(keywords)) {
            String tycToken = eyeToken;
            String result = doGet(eyeUrl + "?keyword=" + keywords, tycToken);
            if (StringUtils.isNotEmpty(result)) {
                JSONObject jsob = JSONObject.parseObject(result);
                if (StringUtils.isNotEmpty(jsob.getString("error_code")) && Integer.parseInt(jsob.get("error_code").toString()) == 0) {
                    JSONObject jo = jsob.getJSONObject("result");
                    customerAuding.setCompName(jo.getString("name"));
                    customerAuding.setAddress(jo.getString("regLocation"));
                    customerAuding.setCompPhone(jo.getString("phoneNumber"));
                    customerAuding.setCompLegal(jo.getString("legalPersonName"));
                    customerAuding.setTaxNo(jo.getString("taxNumber"));
                    // 时间格式转化
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");// 要转换的时间格式
                    customerAuding.setRegCapital(jo.getString("regCapital"));
                    if (jo.containsKey("estiblishTime") && StringUtils.isNotEmpty(jo.getString("estiblishTime")))
                        customerAuding.setEstiblishTime(sdf.format(new Date(jo.getLong("estiblishTime"))));
                    else
                        customerAuding.setEstiblishTime("-");
                    customerAuding.setRegStatus(jo.getString("regStatus"));
                    customerAuding.setRegNumber(jo.getString("regNumber"));
                    customerAuding.setOrgNumber(jo.getString("orgNumber"));
                    customerAuding.setCompanyOrgType(jo.getString("companyOrgType"));
                    String fromTimeStr = "";
                    if (jo.containsKey("fromTime") && StringUtils.isNotEmpty(jo.getString("fromTime")))
                        fromTimeStr = sdf.format(new Date(jo.getLong("fromTime")));
                    if (jo.containsKey("toTime") && StringUtils.isNotEmpty(jo.getString("toTime")))
                        fromTimeStr += "至" + sdf.format(new Date(jo.getLong("toTime")));
                    customerAuding.setFromTime(fromTimeStr);
                    customerAuding.setIndustry(jo.getString("industry"));
                    if (jo.containsKey("approvedTime") && StringUtils.isNotEmpty(jo.getString("approvedTime")))
                        customerAuding.setApprovedTime(sdf.format(new Date(jo.getLong("approvedTime"))));
                    else
                        customerAuding.setApprovedTime("-");
                    customerAuding.setActualCapital(jo.getString("actualCapital"));
                    customerAuding.setStaffNumRange(jo.getString("staffNumRange"));
                    customerAuding.setSocialStaffNum(jo.getString("socialStaffNum"));
                    customerAuding.setRegInstitute(jo.getString("regInstitute"));
                    // 曾用名 ,取第一个
                    JSONArray historyArray = jo.getJSONArray("historyNameList");
                    String tmpHisName = "-";
                    if (historyArray != null && historyArray.size() > 0)
                        tmpHisName = historyArray.getString(0);
                    customerAuding.setHistoryNameList(tmpHisName);
                    customerAuding.setProperty3(jo.getString("property3"));
                    customerAuding.setRegLocation(jo.getString("regLocation"));
                    customerAuding.setBusinessScope(jo.getString("businessScope"));

                } else {
                    throw new RuntimeException("获取企业信息错误：" + jsob.get("reason"));
                }
            }
        }
        return customerAuding;
    }

    /**
     * 根据web输入的公司关键字，查询所有企业信息
     */
    public List<String> searchByKeywords(String keywords) {
        List<String> reList = new ArrayList<String>();
        if (StringUtils.isNotEmpty(keywords)) {
            String tycToken = eyeToken;
            String result = this.doGet(eysResearchUrl + "?word=" + keywords + "&pageSize=10&pageNum=1", tycToken);
            if (StringUtils.isNotEmpty(result)) {
                JSONObject jsob = JSONObject.parseObject(result);
                if (StringUtils.isNotEmpty(jsob.getString("error_code")) && Integer.parseInt(jsob.get("error_code").toString()) == 0) {
                    JSONArray jsarray = jsob.getJSONObject("result").getJSONArray("items");
                    for (Object item : jsarray) {
                        JSONObject jo = (JSONObject) item;
                        reList.add(jo.get("name").toString());
                    }
                }
            }
        }
        return reList;
    }

    /**
     * 批量删除企业信息
     *
     * @param companyIds 需要删除的企业信息主键
     * @return 结果
     */
    @Override
    public int deleteCompanyByCompanyIds(Long[] companyIds) {
        return companyMapper.deleteCompanyByCompanyIds(companyIds);
    }

    /**
     * 删除企业信息信息
     *
     * @param companyId 企业信息主键
     * @return 结果
     */
    @Override
    public int deleteCompanyByCompanyId(Long companyId) {
        return companyMapper.deleteCompanyByCompanyId(companyId);
    }

    public static String doGet(String url, String token) {
        BasicHttpParams httpParams = new BasicHttpParams();
        HttpConnectionParams.setConnectionTimeout(httpParams, 1000);
        HttpConnectionParams.setSoTimeout(httpParams, 1000);
        HttpClient httpClient = new DefaultHttpClient(httpParams);

        String result = null;
        try {

            HttpGet get = new HttpGet(url);
            // 设置header
            get.setHeader("Authorization", token);
            // 设置类型
            HttpResponse response = httpClient.execute(get);
            HttpEntity entity = response.getEntity();
            result = EntityUtils.toString(entity, "utf-8");

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            httpClient.getConnectionManager().shutdown();
        }
        return result;
    }
}
