package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ProcessOutsourcing;

/**
 * 工序外协Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface ProcessOutsourcingMapper 
{
    /**
     * 查询工序外协
     * 
     * @param id 工序外协主键
     * @return 工序外协
     */
    public ProcessOutsourcing selectProcessOutsourcingById(Long id);

    /**
     * 查询工序外协列表
     * 
     * @param processOutsourcing 工序外协
     * @return 工序外协集合
     */
    public List<ProcessOutsourcing> selectProcessOutsourcingList(ProcessOutsourcing processOutsourcing);

    /**
     * 新增工序外协
     * 
     * @param processOutsourcing 工序外协
     * @return 结果
     */
    public int insertProcessOutsourcing(ProcessOutsourcing processOutsourcing);

    /**
     * 修改工序外协
     * 
     * @param processOutsourcing 工序外协
     * @return 结果
     */
    public int updateProcessOutsourcing(ProcessOutsourcing processOutsourcing);

    /**
     * 删除工序外协
     * 
     * @param id 工序外协主键
     * @return 结果
     */
    public int deleteProcessOutsourcingById(Long id);

    /**
     * 批量删除工序外协
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProcessOutsourcingByIds(Long[] ids);
}
