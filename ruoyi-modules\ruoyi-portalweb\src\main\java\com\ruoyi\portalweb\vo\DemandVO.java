package com.ruoyi.portalweb.vo;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.portalweb.api.domain.Demand;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 服务需求对象
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public class DemandVO extends Demand {
    private static final long serialVersionUID = 1L;

    @JsonIgnore
    private Date startTime;
    @JsonIgnore
    private Date endTime;

    @JsonIgnore
    private String keyword;

    @ApiModelProperty(value = "需求类型")
    private String typeName;

    @ApiModelProperty(value = "链接")
    private String imageUrlPath;

    @ApiModelProperty(value = "查询方式,我的需求queryType='my'")
    private String queryType;

    @ApiModelProperty(value = "应用领域名称")
    private String applicationAreaName;

    @ApiModelProperty(value = "审核状态名称")
    private String auditStatusName;



    /**
     * 传入附件
     */
    @ApiModelProperty(value = "传入附件")
    private List<FileDetailVO> alFileDetailVOs = new ArrayList<FileDetailVO>();

    public List<FileDetailVO> getAlFileDetailVOs() {
        return alFileDetailVOs;
    }

    public void setAlFileDetailVOs(List<FileDetailVO> alFileDetailVOs) {
        this.alFileDetailVOs = alFileDetailVOs;
    }

    public String getAuditStatusName() {
        return auditStatusName;
    }

    public void setAuditStatusName(String auditStatusName) {
        this.auditStatusName = auditStatusName;
    }

    public String getApplicationAreaName() {
        return applicationAreaName;
    }

    public void setApplicationAreaName(String applicationAreaName) {
        this.applicationAreaName = applicationAreaName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    public String getImageUrlPath() {
        return imageUrlPath;
    }

    public void setImageUrlPath(String imageUrlPath) {
        this.imageUrlPath = imageUrlPath;
    }

    public Date getStartTime() {return startTime;}

    public void setStartTime(Date startTime) {this.startTime = startTime;}

    public Date getEndTime() {return endTime;}

    public void setEndTime(Date endTime) {this.endTime = endTime;}

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
}
