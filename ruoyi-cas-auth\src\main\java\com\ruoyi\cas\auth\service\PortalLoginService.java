package com.ruoyi.cas.auth.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.ruoyi.cas.auth.domain.ApiR;
import com.ruoyi.cas.auth.model.ApiMember;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.service.ProtalTokenService;
import com.ruoyi.portalweb.api.RemoteApplicationService;
import com.ruoyi.portalweb.api.domain.Application;
import com.ruoyi.portalweb.api.model.LoginMember;

/**
 * 登录校验方法
 * 
 * <AUTHOR>
 */
@Component
public class PortalLoginService
{

    @Autowired
    private SysRecordLogService recordLogService;
    
    @Autowired
    private ProtalTokenService protalTokenService;
    
    @Autowired
    private RemoteApplicationService remoteApplicationService;





	public ApiR<ApiMember> getLoginMember(String appid, String ticket) {
		recordLogService.recordLogininfor(appid, Constants.LOGIN_LOG, appid+"接口调用"+ticket);
		ApiR<ApiMember> apiR = new ApiR<ApiMember>();
		if(StringUtils.isEmpty(appid)){
			return apiR.fail("缺少appid");
		}
		//校验appid是否合法&是否有权限访问
		R<Application> r = remoteApplicationService.selectApplicationByAppId(appid);
		if(r.getData() == null){
			return apiR.fail("非法的appid");
		}
		//获取登录用户信息
		LoginMember memberInfo = protalTokenService.getLoginMemberByUserkey(ticket);
		if(memberInfo == null){
			return apiR.fail("非法的ticket");
		}
		return apiR.ok(new ApiMember(memberInfo));
	}

	
}
