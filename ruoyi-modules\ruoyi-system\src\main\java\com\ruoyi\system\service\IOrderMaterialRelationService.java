package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.OrderMaterialRelation;

/**
 * 订单物料关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface IOrderMaterialRelationService 
{
    /**
     * 查询订单物料关联
     * 
     * @param id 订单物料关联主键
     * @return 订单物料关联
     */
    public OrderMaterialRelation selectOrderMaterialRelationById(Long id);

    /**
     * 查询订单物料关联列表
     * 
     * @param orderMaterialRelation 订单物料关联
     * @return 订单物料关联集合
     */
    public List<OrderMaterialRelation> selectOrderMaterialRelationList(OrderMaterialRelation orderMaterialRelation);

    /**
     * 查询订单关联的物料列表
     * 
     * @param orderId 订单ID
     * @return 订单物料关联集合
     */
    public List<OrderMaterialRelation> selectOrderMaterialRelationByOrderId(Long orderId);

    /**
     * 查询物料关联的订单列表
     * 
     * @param materialId 物料ID
     * @return 订单物料关联集合
     */
    public List<OrderMaterialRelation> selectOrderMaterialRelationByMaterialId(Long materialId);

    /**
     * 新增订单物料关联
     * 
     * @param orderMaterialRelation 订单物料关联
     * @return 结果
     */
    public int insertOrderMaterialRelation(OrderMaterialRelation orderMaterialRelation);

    /**
     * 修改订单物料关联
     * 
     * @param orderMaterialRelation 订单物料关联
     * @return 结果
     */
    public int updateOrderMaterialRelation(OrderMaterialRelation orderMaterialRelation);

    /**
     * 批量删除订单物料关联
     * 
     * @param ids 需要删除的订单物料关联主键集合
     * @return 结果
     */
    public int deleteOrderMaterialRelationByIds(Long[] ids);

    /**
     * 删除订单物料关联信息
     * 
     * @param id 订单物料关联主键
     * @return 结果
     */
    public int deleteOrderMaterialRelationById(Long id);

    /**
     * 删除订单的所有物料关联
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    public int deleteOrderMaterialRelationByOrderId(Long orderId);

    /**
     * 删除物料的所有订单关联
     * 
     * @param materialId 物料ID
     * @return 结果
     */
    public int deleteOrderMaterialRelationByMaterialId(Long materialId);
}
