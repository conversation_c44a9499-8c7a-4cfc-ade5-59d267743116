package com.ruoyi.portalconsole.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 行业类别对象 industry_type
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
public class IndustryType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 行业类别ID */
    private Long industryTypeId;

    /** 上级id */
    @Excel(name = "上级id")
    private Long parentId;

    /** 行业名称 */
    @Excel(name = "行业名称")
    private String industryTypeName;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setIndustryTypeId(Long industryTypeId) 
    {
        this.industryTypeId = industryTypeId;
    }

    public Long getIndustryTypeId() 
    {
        return industryTypeId;
    }
    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }
    public void setIndustryTypeName(String industryTypeName) 
    {
        this.industryTypeName = industryTypeName;
    }

    public String getIndustryTypeName() 
    {
        return industryTypeName;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("industryTypeId", getIndustryTypeId())
            .append("parentId", getParentId())
            .append("industryTypeName", getIndustryTypeName())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
