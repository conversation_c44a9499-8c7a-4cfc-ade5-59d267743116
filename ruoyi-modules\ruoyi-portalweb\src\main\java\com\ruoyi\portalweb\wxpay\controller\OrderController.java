/*
 *  Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.ruoyi.portalweb.wxpay.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.service.IMemberService;
import com.ruoyi.portalweb.vo.MemberVO;
import com.ruoyi.portalweb.wxpay.service.IOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;

/**
 * 订单管理 控制器
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@RestController
@AllArgsConstructor
@RequestMapping( "/pay")
@Api(value = "1、订单管理", tags = "1、订单管理")
public class OrderController {

	@Autowired
	private  IOrderService orderService;
	@Autowired
	private  IMemberService memberService;


	/**
	 * 微信扫码支付
	 *
	 * @param appStoreOrderId
	 * @return
	 */
	@PostMapping("/wechatNativePay")
	@ApiOperation(value = "微信扫码支付", notes = "传入销售订单ID")
	public AjaxResult wechatNativePay(@ApiParam(value = "销售订单ID", required = true) @RequestParam Long appStoreOrderId) {
		// 获取会员信息
		MemberVO memberVO = memberService.selectMemberByMemberId(SecurityUtils.getUserId());
		// TODO
		String remoteAddr = "?????";
		String codeUrl = orderService.updateWechatNativePayInfo(appStoreOrderId, remoteAddr, memberVO);
		return AjaxResult.success(codeUrl);
	}


}
