package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 技术需求对象 sys_tech_requirement
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public class SysTechRequirement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 需求ID */
    private Long requirementId;

    /** 技术需求 */
    @Excel(name = "技术需求")
    private String techRequirement;

    /** 发布方出资（元） */
    @Excel(name = "发布方出资", readConverterExp = "元=")
    private BigDecimal publisherInvestment;

    /** 计划资金（元） */
    @Excel(name = "计划资金", readConverterExp = "元=")
    private BigDecimal plannedBudget;

    /** 发布企业 */
    @Excel(name = "发布企业")
    private String publisherCompany;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactPhone;

    /** 截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deadline;

    /** 需求标题 */
    @Excel(name = "需求标题")
    private String requirementTitle;

    /** 需求分类 */
    @Excel(name = "需求分类")
    private String requirementCategory;

    /** 优先级（1高 2中 3低） */
    @Excel(name = "优先级", readConverterExp = "1=高,2=中,3=低")
    private String priorityLevel;

    /** 需求状态（0待处理 1进行中 2已完成 3已取消） */
    @Excel(name = "需求状态", readConverterExp = "0=待处理,1=进行中,2=已完成,3=已取消")
    private String requirementStatus;

    /** 附件地址 */
    @Excel(name = "附件地址")
    private String attachmentUrl;

    public void setRequirementId(Long requirementId) 
    {
        this.requirementId = requirementId;
    }

    public Long getRequirementId() 
    {
        return requirementId;
    }
    public void setTechRequirement(String techRequirement) 
    {
        this.techRequirement = techRequirement;
    }

    public String getTechRequirement() 
    {
        return techRequirement;
    }
    public void setPublisherInvestment(BigDecimal publisherInvestment) 
    {
        this.publisherInvestment = publisherInvestment;
    }

    public BigDecimal getPublisherInvestment() 
    {
        return publisherInvestment;
    }
    public void setPlannedBudget(BigDecimal plannedBudget) 
    {
        this.plannedBudget = plannedBudget;
    }

    public BigDecimal getPlannedBudget() 
    {
        return plannedBudget;
    }
    public void setPublisherCompany(String publisherCompany) 
    {
        this.publisherCompany = publisherCompany;
    }

    public String getPublisherCompany() 
    {
        return publisherCompany;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setDeadline(Date deadline) 
    {
        this.deadline = deadline;
    }

    public Date getDeadline() 
    {
        return deadline;
    }
    public void setRequirementTitle(String requirementTitle) 
    {
        this.requirementTitle = requirementTitle;
    }

    public String getRequirementTitle() 
    {
        return requirementTitle;
    }
    public void setRequirementCategory(String requirementCategory) 
    {
        this.requirementCategory = requirementCategory;
    }

    public String getRequirementCategory() 
    {
        return requirementCategory;
    }
    public void setPriorityLevel(String priorityLevel) 
    {
        this.priorityLevel = priorityLevel;
    }

    public String getPriorityLevel() 
    {
        return priorityLevel;
    }
    public void setRequirementStatus(String requirementStatus) 
    {
        this.requirementStatus = requirementStatus;
    }

    public String getRequirementStatus() 
    {
        return requirementStatus;
    }
    public void setAttachmentUrl(String attachmentUrl) 
    {
        this.attachmentUrl = attachmentUrl;
    }

    public String getAttachmentUrl() 
    {
        return attachmentUrl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("requirementId", getRequirementId())
            .append("techRequirement", getTechRequirement())
            .append("publisherInvestment", getPublisherInvestment())
            .append("plannedBudget", getPlannedBudget())
            .append("publisherCompany", getPublisherCompany())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("deadline", getDeadline())
            .append("requirementTitle", getRequirementTitle())
            .append("requirementCategory", getRequirementCategory())
            .append("priorityLevel", getPriorityLevel())
            .append("requirementStatus", getRequirementStatus())
            .append("attachmentUrl", getAttachmentUrl())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
