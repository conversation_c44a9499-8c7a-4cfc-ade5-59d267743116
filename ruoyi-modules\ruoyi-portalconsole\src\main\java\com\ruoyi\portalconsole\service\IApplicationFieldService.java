package com.ruoyi.portalconsole.service;

import java.util.List;
import com.ruoyi.portalconsole.domain.ApplicationField;

/**
 * 应用领域Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
public interface IApplicationFieldService 
{
    /**
     * 查询应用领域
     * 
     * @param applicationFieldId 应用领域主键
     * @return 应用领域
     */
    public ApplicationField selectApplicationFieldByApplicationFieldId(Long applicationFieldId);

    /**
     * 查询应用领域列表
     * 
     * @param applicationField 应用领域
     * @return 应用领域集合
     */
    public List<ApplicationField> selectApplicationFieldList(ApplicationField applicationField);

    /**
     * 新增应用领域
     * 
     * @param applicationField 应用领域
     * @return 结果
     */
    public int insertApplicationField(ApplicationField applicationField);

    /**
     * 修改应用领域
     * 
     * @param applicationField 应用领域
     * @return 结果
     */
    public int updateApplicationField(ApplicationField applicationField);

    /**
     * 批量删除应用领域
     * 
     * @param applicationFieldIds 需要删除的应用领域主键集合
     * @return 结果
     */
    public int deleteApplicationFieldByApplicationFieldIds(Long[] applicationFieldIds);

    /**
     * 删除应用领域信息
     * 
     * @param applicationFieldId 应用领域主键
     * @return 结果
     */
    public int deleteApplicationFieldByApplicationFieldId(Long applicationFieldId);
}
