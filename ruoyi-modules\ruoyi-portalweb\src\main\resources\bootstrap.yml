# Tomcat
server:
  port: 9212

# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-portalweb
  profiles:
    # 环境配置
#    active: dev
    active: prod
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: 8fd725fc-adb3-4a7e-bb5b-7dc4e5e9a5d1
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: 8fd725fc-adb3-4a7e-bb5b-7dc4e5e9a5d1
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

##天眼查
#eye:
#  token: 043c7a93-703f-4a3a-a3a9-d99a9d895e47
#  requestUrl: http://open.api.tianyancha.com/services/open/ic/baseinfoV2/2.0
#  researchUrl: http://open.api.tianyancha.com/services/open/search/2.0
