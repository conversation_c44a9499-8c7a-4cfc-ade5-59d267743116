package com.ruoyi.portalweb.mapper;

import java.util.List;
import com.ruoyi.portalweb.api.domain.BuMemberOnlineRefund;

/**
 * 商城用户线上退款Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface BuMemberOnlineRefundMapper 
{
    /**
     * 查询商城用户线上退款
     * 
     * @param id 商城用户线上退款主键
     * @return 商城用户线上退款
     */
    public BuMemberOnlineRefund selectBuMemberOnlineRefundById(Long id);

    /**
     * 查询商城用户线上退款列表
     * 
     * @param buMemberOnlineRefund 商城用户线上退款
     * @return 商城用户线上退款集合
     */
    public List<BuMemberOnlineRefund> selectBuMemberOnlineRefundList(BuMemberOnlineRefund buMemberOnlineRefund);

    /**
     * 新增商城用户线上退款
     * 
     * @param buMemberOnlineRefund 商城用户线上退款
     * @return 结果
     */
    public int insertBuMemberOnlineRefund(BuMemberOnlineRefund buMemberOnlineRefund);

    /**
     * 修改商城用户线上退款
     * 
     * @param buMemberOnlineRefund 商城用户线上退款
     * @return 结果
     */
    public int updateBuMemberOnlineRefund(BuMemberOnlineRefund buMemberOnlineRefund);

    /**
     * 删除商城用户线上退款
     * 
     * @param id 商城用户线上退款主键
     * @return 结果
     */
    public int deleteBuMemberOnlineRefundById(Long id);

    /**
     * 批量删除商城用户线上退款
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuMemberOnlineRefundByIds(Long[] ids);

    BuMemberOnlineRefund selectBuMemberOnlineRefundByRefundOrderNo(String refundOrderNo);

    int updateBuMemberOnlineRefundByRefundOrderNo(BuMemberOnlineRefund buMemberOnlineRefund);
}
