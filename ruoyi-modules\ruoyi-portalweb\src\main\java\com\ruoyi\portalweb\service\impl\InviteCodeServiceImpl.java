package com.ruoyi.portalweb.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.uuid.UUID;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.Member;
import com.ruoyi.portalweb.api.enums.MemberStatus;
import com.ruoyi.portalweb.api.enums.YesOrNo;
import com.ruoyi.portalweb.service.IMemberService;
import com.ruoyi.portalweb.vo.InviteCodeAmount;
import com.ruoyi.portalweb.vo.InviteCodeVO;
import com.ruoyi.portalweb.vo.MemberVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalweb.mapper.InviteCodeMapper;
import com.ruoyi.portalweb.api.domain.InviteCode;
import com.ruoyi.portalweb.service.IInviteCodeService;

import static com.ruoyi.common.core.utils.PageUtils.startPage;

/**
 * 企业邀请码Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-06
 */
@Service
public class InviteCodeServiceImpl implements IInviteCodeService 
{
    @Autowired
    private InviteCodeMapper inviteCodeMapper;

    @Autowired
    private IMemberService memberService;

    /**
     * 查询企业邀请码
     * 
     * @param id 企业邀请码主键
     * @return 企业邀请码
     */
    @Override
    public InviteCode selectInviteCodeById(Long id)
    {
        return inviteCodeMapper.selectInviteCodeById(id);
    }

    /**
     * 查询企业邀请码列表
     * 
     * @param inviteCode 企业邀请码
     * @return 企业邀请码
     */
    @Override
    public List<InviteCode> selectInviteCodeList(InviteCode inviteCode)
    {
        MemberVO memberVO = memberService.selectMemberByMemberId(SecurityUtils.getUserId());
        inviteCode.setCompanyRelatedId(memberVO.getCompanyRelatedId());
        startPage();
        PageUtils.setOrderBy("is_valid DESC, id DESC");
        return inviteCodeMapper.selectInviteCodeList(inviteCode);
    }

    /**
     * 新增企业邀请码
     * 
     *
     * @return 结果
     */
    @Override
    public List<String> insertInviteCode(InviteCodeAmount vo)
    {
        MemberVO memberVO = memberService.selectMemberByMemberId(SecurityUtils.getUserId());
        if (memberVO.getCompanyRelatedId() == null) {
            throw new ServiceException("未找到关联企业");
        }

        int amount = vo.getAmount() != 0 ? vo.getAmount(): 1;
        List<InviteCode> list = generate(memberVO.getCompanyRelatedId(), amount);

        inviteCodeMapper.insertInviteCode(list);
        List<String> codes = new ArrayList<>();
        for (InviteCode inviteCode : list) {
            codes.add(inviteCode.getCode());
        }

        return codes;
    }

    @Override
    public InviteCodeVO selectInviteCodeByCode(String code) {
        return inviteCodeMapper.selectInviteCodeByCode(code);
    }

    /**
     * 修改企业邀请码
     * 
     * @param inviteCode 企业邀请码
     * @return 结果
     */
    @Override
    public int updateInviteCode(InviteCode inviteCode)
    {
        // 验证邀请码的有效性
        if (inviteCode.getCode() == null) {
            throw new ServiceException("请输入邀请码");
        }
        InviteCodeVO entity = inviteCodeMapper.selectInviteCodeByCode(inviteCode.getCode().toUpperCase());
        if (entity == null|| Objects.equals(entity.getIsValid(), YesOrNo.NO.getValue())){
            throw new ServiceException("邀请码不存在或已失效");
        }

        // 加入关联企业
        Member member = new Member();
        member.setMemberId(SecurityUtils.getUserId());
        member.setCompanyRelatedId(entity.getCompanyRelatedId());
        member.setMemberStatus(MemberStatus.OK.getCode());
        member.setMemberCompanyName(entity.getCompanyName());
        memberService.updateMember(member);


        //  邀请码置失效
        InviteCode updateCode = new InviteCode();
        updateCode.setId(entity.getId());
        updateCode.setIsValid(YesOrNo.NO.getValue());
        return inviteCodeMapper.updateInviteCode(updateCode);
    }

    /**
     * 批量删除企业邀请码
     * 
     * @param ids 需要删除的企业邀请码主键
     * @return 结果
     */
    @Override
    public int deleteInviteCodeByIds(Long[] ids)
    {
        return inviteCodeMapper.deleteInviteCodeByIds(ids);
    }

    /**
     * 批量删除失效的企业邀请码
     *
     * @return 结果
     */
    @Override
    public int deleteInvalidInviteCodes()
    {
        MemberVO memberVO = memberService.selectMemberByMemberId(SecurityUtils.getUserId());
        return inviteCodeMapper.deleteInvalidInviteCodes(memberVO.getCompanyRelatedId(), YesOrNo.NO.getValue());
    }

    /**
     * 删除企业邀请码信息
     * 
     * @param id 企业邀请码主键
     * @return 结果
     */
    @Override
    public int deleteInviteCodeById(Long id)
    {
        return inviteCodeMapper.deleteInviteCodeById(id);
    }


    private  List<InviteCode>  generate(Long companyRelatedId, int amount){
        List<InviteCode> inviteCodes = new ArrayList<InviteCode>();

        for (int i = 0; i < amount; i++) {
            InviteCode inviteCode = new InviteCode();
            inviteCode.setCreateTime(DateUtils.getNowDate());
            String code = UUID.randomUUID().toString().replace("-", "").toUpperCase();
            inviteCode.setCode(code);
            inviteCode.setCompanyRelatedId(companyRelatedId);
            inviteCode.setIsValid(YesOrNo.YES.getValue());
            inviteCodes.add(inviteCode);
        }

        return inviteCodes;
    }
}
