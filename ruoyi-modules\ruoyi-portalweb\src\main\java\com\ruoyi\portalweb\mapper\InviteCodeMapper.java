package com.ruoyi.portalweb.mapper;

import java.util.List;
import com.ruoyi.portalweb.api.domain.InviteCode;
import com.ruoyi.portalweb.vo.InviteCodeVO;
import org.apache.ibatis.annotations.Param;

/**
 * 企业邀请码Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-06
 */
public interface InviteCodeMapper 
{
    /**
     * 查询企业邀请码
     * 
     * @param id 企业邀请码主键
     * @return 企业邀请码
     */
    public InviteCode selectInviteCodeById(Long id);

    /**
     * 查询企业邀请码
     *
     * @param code 企业邀请码
     * @return 企业邀请码
     */
    public InviteCodeVO selectInviteCodeByCode(String code);

    /**
     * 查询企业邀请码列表
     * 
     * @param inviteCode 企业邀请码
     * @return 企业邀请码集合
     */
    public List<InviteCode> selectInviteCodeList(InviteCode inviteCode);

    /**
     * 新增企业邀请码
     *
     * @param inviteCodes 企业邀请码
     * @return 结果
     */
    public int insertInviteCode(List<InviteCode> inviteCodes);

    /**
     * 修改企业邀请码
     * 
     * @param inviteCode 企业邀请码
     * @return 结果
     */
    public int updateInviteCode(InviteCode inviteCode);

    /**
     * 删除企业邀请码
     * 
     * @param id 企业邀请码主键
     * @return 结果
     */
    public int deleteInviteCodeById(Long id);

    /**
     * 批量删除企业邀请码
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInviteCodeByIds(Long[] ids);

    int deleteInvalidInviteCodes(@Param("companyRelatedId") Long companyRelatedId, @Param("isValid") String isValid);
}
