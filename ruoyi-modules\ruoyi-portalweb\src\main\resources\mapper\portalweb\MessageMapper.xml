<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.MessageMapper">

    <resultMap id="MessageResult" type="com.ruoyi.portalweb.vo.MessageVO">
        <result property="messageId" column="message_id"/>
        <result property="messageTitle" column="message_title"/>
        <result property="messageBody" column="message_body"/>
        <result property="messageTime" column="message_time"/>
        <result property="messageStatus" column="message_status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberId" column="member_id"/>

        <result property="messageStatusName" column="message_status_name"/>
    </resultMap>

    <sql id="selectMessageVo">
        select message_id, message_title, message_body, message_time, message_status, del_flag, create_by, create_time,
        update_by, update_time,member_id, remark from message
    </sql>

    <sql id="Base_Column_List">
		a.*, b.dict_label AS message_status_name
	</sql>

	<sql id="Base_Table_List">
		FROM message a
        LEFT JOIN sys_dict_data b ON a.message_status = b.dict_value AND b.dict_type = 'message_status'
	</sql>

    <select id="selectMessageList" parameterType="Message" resultMap="MessageResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        <where>
            <if test="messageTitle != null  and messageTitle != ''">and message_title = #{messageTitle}</if>
            <if test="messageBody != null  and messageBody != ''">and message_body = #{messageBody}</if>
            <if test="messageTime != null ">and message_time = #{messageTime}</if>
            <if test="messageStatus != null  and messageStatus != ''">and message_status = #{messageStatus}</if>
            <if test="memberId != null ">and a.member_id = #{memberId}</if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <select id="selectMessageByMessageId" parameterType="Long" resultMap="MessageResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        where message_id = #{messageId}
    </select>

    <insert id="insertMessage" parameterType="Message" useGeneratedKeys="true" keyProperty="messageId">
        insert into message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="messageTitle != null">message_title,</if>
            <if test="messageBody != null">message_body,</if>
            <if test="messageTime != null">message_time,</if>
            <if test="messageStatus != null">message_status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="memberId != null">member_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="messageTitle != null">#{messageTitle},</if>
            <if test="messageBody != null">#{messageBody},</if>
            <if test="messageTime != null">#{messageTime},</if>
            <if test="messageStatus != null">#{messageStatus},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="memberId != null">#{memberId},</if>
        </trim>
    </insert>

    <update id="updateMessage" parameterType="Message">
        update message
        <trim prefix="SET" suffixOverrides=",">
            <if test="messageTitle != null">message_title = #{messageTitle},</if>
            <if test="messageBody != null">message_body = #{messageBody},</if>
            <if test="messageTime != null">message_time = #{messageTime},</if>
            <if test="messageStatus != null">message_status = #{messageStatus},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
        </trim>
        where message_id = #{messageId}
    </update>

    <delete id="deleteMessageByMessageId" parameterType="Long">
        delete from message where message_id = #{messageId}
    </delete>

    <delete id="deleteMessageByMessageIds" parameterType="String">
        delete from message where message_id in
        <foreach item="messageId" collection="array" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </delete>
</mapper>