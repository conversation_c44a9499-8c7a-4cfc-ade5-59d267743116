package com.ruoyi.system.api.domain.key;

/**
 * SSO键值
 */
public class Sso<PERSON>ey extends BasePrefix {

	private SsoKey(int expireSeconds, String prefix) {
		super(expireSeconds, prefix);
	}

	public static SsoKey getCode = new SsoKey(0, "code");
	public static SsoKey getTmpTicket = new SsoKey(0, "tmpTicket");
	public static SsoKey getUserTicket = new SsoKey(0, "userTicket");
	public static SsoKey getLoginUser = new SsoKey(0, "loginUser");
}
