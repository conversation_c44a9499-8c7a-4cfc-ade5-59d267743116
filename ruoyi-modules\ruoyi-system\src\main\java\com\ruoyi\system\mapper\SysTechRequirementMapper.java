package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysTechRequirement;

/**
 * 技术需求Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface SysTechRequirementMapper 
{
    /**
     * 查询技术需求
     * 
     * @param requirementId 技术需求主键
     * @return 技术需求
     */
    public SysTechRequirement selectSysTechRequirementByRequirementId(Long requirementId);

    /**
     * 查询技术需求列表
     * 
     * @param sysTechRequirement 技术需求
     * @return 技术需求集合
     */
    public List<SysTechRequirement> selectSysTechRequirementList(SysTechRequirement sysTechRequirement);

    /**
     * 新增技术需求
     * 
     * @param sysTechRequirement 技术需求
     * @return 结果
     */
    public int insertSysTechRequirement(SysTechRequirement sysTechRequirement);

    /**
     * 修改技术需求
     * 
     * @param sysTechRequirement 技术需求
     * @return 结果
     */
    public int updateSysTechRequirement(SysTechRequirement sysTechRequirement);

    /**
     * 删除技术需求
     * 
     * @param requirementId 技术需求主键
     * @return 结果
     */
    public int deleteSysTechRequirementByRequirementId(Long requirementId);

    /**
     * 批量删除技术需求
     * 
     * @param requirementIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysTechRequirementByRequirementIds(Long[] requirementIds);
}
