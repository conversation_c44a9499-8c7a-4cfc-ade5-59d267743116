package com.ruoyi.portalweb.api.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 解决方案实施案例对象 solution_case
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public class SolutionCase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    public SolutionCase(Long solutionId) {
        this.solutionId = solutionId;
    }

    public SolutionCase() {}

    /** 解决方案实施案例ID */
    private Long solutionCaseId;

    /** 方案ID */
    @Excel(name = "方案ID")
    private Long solutionId;

    /** 案例名称 */
    @Excel(name = "案例名称")
    private String solutionCaseName;

    /** 案例简介 */
    @Excel(name = "案例简介")
    private String solutionCaseIntroduction;

    /** 案例内容 */
    @Excel(name = "案例内容")
    private String solutionCaseContent;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setSolutionCaseId(Long solutionCaseId) 
    {
        this.solutionCaseId = solutionCaseId;
    }

    public Long getSolutionCaseId() 
    {
        return solutionCaseId;
    }
    public void setSolutionId(Long solutionId) 
    {
        this.solutionId = solutionId;
    }

    public Long getSolutionId() 
    {
        return solutionId;
    }
    public void setSolutionCaseName(String solutionCaseName) 
    {
        this.solutionCaseName = solutionCaseName;
    }

    public String getSolutionCaseName() 
    {
        return solutionCaseName;
    }
    public void setSolutionCaseIntroduction(String solutionCaseIntroduction) 
    {
        this.solutionCaseIntroduction = solutionCaseIntroduction;
    }

    public String getSolutionCaseIntroduction() 
    {
        return solutionCaseIntroduction;
    }
    public void setSolutionCaseContent(String solutionCaseContent) 
    {
        this.solutionCaseContent = solutionCaseContent;
    }

    public String getSolutionCaseContent() 
    {
        return solutionCaseContent;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("solutionCaseId", getSolutionCaseId())
            .append("solutionId", getSolutionId())
            .append("solutionCaseName", getSolutionCaseName())
            .append("solutionCaseIntroduction", getSolutionCaseIntroduction())
            .append("solutionCaseContent", getSolutionCaseContent())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
