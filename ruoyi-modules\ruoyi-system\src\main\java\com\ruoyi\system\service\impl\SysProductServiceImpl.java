package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysProductMapper;
import com.ruoyi.system.domain.SysProduct;
import com.ruoyi.system.service.ISysProductService;

/**
 * 产品信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
public class SysProductServiceImpl implements ISysProductService 
{
    @Autowired
    private SysProductMapper sysProductMapper;

    /**
     * 查询产品信息
     * 
     * @param productId 产品信息主键
     * @return 产品信息
     */
    @Override
    public SysProduct selectSysProductByProductId(Long productId)
    {
        return sysProductMapper.selectSysProductByProductId(productId);
    }

    /**
     * 查询产品信息列表
     * 
     * @param sysProduct 产品信息
     * @return 产品信息
     */
    @Override
    public List<SysProduct> selectSysProductList(SysProduct sysProduct)
    {
        return sysProductMapper.selectSysProductList(sysProduct);
    }

    /**
     * 新增产品信息
     * 
     * @param sysProduct 产品信息
     * @return 结果
     */
    @Override
    public int insertSysProduct(SysProduct sysProduct)
    {
        sysProduct.setCreateTime(DateUtils.getNowDate());
        return sysProductMapper.insertSysProduct(sysProduct);
    }

    /**
     * 修改产品信息
     * 
     * @param sysProduct 产品信息
     * @return 结果
     */
    @Override
    public int updateSysProduct(SysProduct sysProduct)
    {
        sysProduct.setUpdateTime(DateUtils.getNowDate());
        return sysProductMapper.updateSysProduct(sysProduct);
    }

    /**
     * 批量删除产品信息
     * 
     * @param productIds 需要删除的产品信息主键
     * @return 结果
     */
    @Override
    public int deleteSysProductByProductIds(Long[] productIds)
    {
        return sysProductMapper.deleteSysProductByProductIds(productIds);
    }

    /**
     * 删除产品信息信息
     *
     * @param productId 产品信息主键
     * @return 结果
     */
    @Override
    public int deleteSysProductByProductId(Long productId)
    {
        return sysProductMapper.deleteSysProductByProductId(productId);
    }

    /**
     * 将工厂ID数组转换为逗号分隔的字符串
     *
     * @param factoryIds 工厂ID数组
     * @return 逗号分隔的字符串
     */
    public static String convertFactoryIdsToString(Long[] factoryIds)
    {
        if (factoryIds == null || factoryIds.length == 0)
        {
            return null;
        }
        return Arrays.stream(factoryIds)
                .map(String::valueOf)
                .collect(Collectors.joining(","));
    }

    /**
     * 将逗号分隔的工厂ID字符串转换为数组
     *
     * @param factoryIdStr 逗号分隔的工厂ID字符串
     * @return 工厂ID数组
     */
    public static Long[] convertStringToFactoryIds(String factoryIdStr)
    {
        if (StringUtils.isEmpty(factoryIdStr))
        {
            return new Long[0];
        }
        return Arrays.stream(factoryIdStr.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotEmpty)
                .map(Long::valueOf)
                .toArray(Long[]::new);
    }

    /**
     * 检查产品是否关联了指定工厂
     *
     * @param product 产品信息
     * @param factoryId 工厂ID
     * @return 是否关联
     */
    public static boolean isProductLinkedToFactory(SysProduct product, Long factoryId)
    {
        if (product == null || StringUtils.isEmpty(product.getFactoryId()) || factoryId == null)
        {
            return false;
        }
        Long[] factoryIds = convertStringToFactoryIds(product.getFactoryId());
        return Arrays.asList(factoryIds).contains(factoryId);
    }
}
