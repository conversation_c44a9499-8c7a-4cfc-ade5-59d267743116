package com.ruoyi.common.security.service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;

import com.ruoyi.portalweb.api.enums.MemberStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.model.LoginMember;

/**
 * token验证处理
 * 
 * <AUTHOR>
 */
@Component
public class ProtalTokenService
{
    private static final Logger log = LoggerFactory.getLogger(ProtalTokenService.class);

    @Autowired
    private RedisService redisService;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private final static long expireTime = CacheConstants.EXPIRATION;

    private final static String ACCESS_TOKEN = CacheConstants.PORTAL_TOKEN_KEY;

    private final static Long MILLIS_MINUTE_TEN = CacheConstants.REFRESH_TIME * MILLIS_MINUTE;

    /**
     * 创建令牌
     */
    public Map<String, Object> createToken(LoginMember loginMember)
    {
        String token = IdUtils.fastUUID();
        Long memberId = loginMember.getMember().getMemberId();
        String memberPhone = loginMember.getMember().getMemberPhone();
        loginMember.setToken(token);
        loginMember.setMemberid(memberId);
        loginMember.setMemberphone(memberPhone);
        refreshToken(loginMember);
        Boolean disabled = false;
        if (Objects.equals(loginMember.getMember().getMemberStatus(), MemberStatus.DISABLED.getCode())){
            disabled = true;
        }


        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<String, Object>();
        claimsMap.put(SecurityConstants.USER_KEY, token);
        claimsMap.put(SecurityConstants.DETAILS_USER_ID, memberId);
        claimsMap.put(SecurityConstants.DETAILS_USERNAME, memberPhone);
        claimsMap.put(SecurityConstants.LOGIN_TYPE, SecurityConstants.LOGIN_MEMBER);

        // 接口返回信息
        Map<String, Object> rspMap = new HashMap<String, Object>();
        rspMap.put("access_token", JwtUtils.createToken(claimsMap));
        rspMap.put("expires_in", expireTime);
        rspMap.put("success", true);
        rspMap.put("disabled", disabled);
        return rspMap;
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginMember getLoginMember()
    {
        return getLoginMember(ServletUtils.getRequest());
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginMember getLoginMember(HttpServletRequest request)
    {
        // 获取请求携带的令牌
        String token = SecurityUtils.getToken(request);
        return getLoginMember(token);
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginMember getLoginMember(String token)
    {
        LoginMember member = null;
        try
        {
            if (StringUtils.isNotEmpty(token))
            {
                String userkey = JwtUtils.getUserKey(token);
                member = redisService.getCacheObject(getTokenKey(userkey));
                return member;
            }
        }
        catch (Exception e)
        {
            log.error("获取用户信息异常'{}'", e.getMessage());
        }
        return member;
    }
    
    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginMember getLoginMemberByUserkey(String userKey)
    {
        LoginMember member = null;
        try
        {
            if (StringUtils.isNotEmpty(userKey))
            {
                member = redisService.getCacheObject(getTokenKey(userKey));
                return member;
            }
        }
        catch (Exception e)
        {
            log.error("获取用户信息异常'{}'", e.getMessage());
        }
        return member;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginMember(LoginMember loginMember)
    {
        if (StringUtils.isNotNull(loginMember) && StringUtils.isNotEmpty(loginMember.getToken()))
        {
            refreshToken(loginMember);
        }
    }

    /**
     * 删除用户缓存信息
     */
    public void delLoginMember(String token)
    {
        if (StringUtils.isNotEmpty(token))
        {
            String userkey = JwtUtils.getUserKey(token);
            redisService.deleteObject(getTokenKey(userkey));
        }
    }

    /**
     * 验证令牌有效期，相差不足120分钟，自动刷新缓存
     *
     * @param loginUser
     */
    public void verifyToken(LoginMember loginMember)
    {
        long expireTime = loginMember.getExpireTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN)
        {
            refreshToken(loginMember);
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(LoginMember loginMember)
    {
        loginMember.setLastLoginTime(System.currentTimeMillis());
        loginMember.setExpireTime(loginMember.getLastLoginTime() + expireTime * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginMember.getToken());
        redisService.setCacheObject(userKey, loginMember, expireTime, TimeUnit.MINUTES);
    }

    private String getTokenKey(String token)
    {
        return ACCESS_TOKEN + token;
    }
}