package com.ruoyi.portalconsole.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

@Configuration
@RefreshScope
@EnableAsync
public class SmsConfig {
    @Value("${ali.sign}")
    private String sign;

    @Value("${ali.template}")
    private String template;

    @Value("${ali.product}")
    private String product;

    @Value("${ali.domain}")
    private String domain;

    @Value("${ali.ACCESS_KEY_ID}")
    private String ACCESS_KEY_ID;

    @Value("${ali.ACCESS_KEY_SECRET}")
    private String ACCESS_KEY_SECRET;

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getACCESS_KEY_ID() {
        return ACCESS_KEY_ID;
    }

    public void setACCESS_KEY_ID(String ACCESS_KEY_ID) {
        this.ACCESS_KEY_ID = ACCESS_KEY_ID;
    }

    public String getACCESS_KEY_SECRET() {
        return ACCESS_KEY_SECRET;
    }

    public void setACCESS_KEY_SECRET(String ACCESS_KEY_SECRET) {
        this.ACCESS_KEY_SECRET = ACCESS_KEY_SECRET;
    }
}
