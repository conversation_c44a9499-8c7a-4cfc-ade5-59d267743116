package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 用工信息对象 job_info
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
public class JobInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 岗位名称 */
    @Excel(name = "岗位名称")
    private String positionName;

    /** 岗位分类 */
    @Excel(name = "岗位分类", readConverterExp = "手糊工=手糊工,模压工=模压工,拉挤工=拉挤工,缠绕工=缠绕工,灌注工=灌注工,喷射工=喷射工,其他=其他")
    private String jobCategory;

    /** 薪资范围显示文本 */
    @Excel(name = "薪资范围显示文本")
    private String salaryRange;

    /** 最低薪资 */
    @Excel(name = "最低薪资")
    private Long salaryMin;

    /** 最高薪资 */
    @Excel(name = "最高薪资")
    private Long salaryMax;

    /** 年龄限制 */
    @Excel(name = "年龄限制")
    private String ageLimit;

    /** 用工单位 */
    @Excel(name = "用工单位")
    private String company;

    /** 用工地点 */
    @Excel(name = "用工地点")
    private String location;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactPhone;

    /** 岗位要求 */
    @Excel(name = "岗位要求")
    private String requirements;

    /** 岗位职责 */
    @Excel(name = "岗位职责")
    private String responsibilities;

    /** 其他限制 */
    @Excel(name = "其他限制")
    private String otherLimits;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setPositionName(String positionName)
    {
        this.positionName = positionName;
    }

    public String getPositionName()
    {
        return positionName;
    }
    public void setJobCategory(String jobCategory)
    {
        this.jobCategory = jobCategory;
    }

    public String getJobCategory()
    {
        return jobCategory;
    }
    public void setSalaryRange(String salaryRange)
    {
        this.salaryRange = salaryRange;
    }

    public String getSalaryRange()
    {
        return salaryRange;
    }
    public void setSalaryMin(Long salaryMin)
    {
        this.salaryMin = salaryMin;
    }

    public Long getSalaryMin()
    {
        return salaryMin;
    }
    public void setSalaryMax(Long salaryMax)
    {
        this.salaryMax = salaryMax;
    }

    public Long getSalaryMax()
    {
        return salaryMax;
    }
    public void setAgeLimit(String ageLimit)
    {
        this.ageLimit = ageLimit;
    }

    public String getAgeLimit()
    {
        return ageLimit;
    }
    public void setCompany(String company)
    {
        this.company = company;
    }

    public String getCompany()
    {
        return company;
    }
    public void setLocation(String location)
    {
        this.location = location;
    }

    public String getLocation()
    {
        return location;
    }
    public void setContactPhone(String contactPhone)
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone()
    {
        return contactPhone;
    }
    public void setRequirements(String requirements)
    {
        this.requirements = requirements;
    }

    public String getRequirements()
    {
        return requirements;
    }
    public void setResponsibilities(String responsibilities)
    {
        this.responsibilities = responsibilities;
    }

    public String getResponsibilities()
    {
        return responsibilities;
    }
    public void setOtherLimits(String otherLimits)
    {
        this.otherLimits = otherLimits;
    }

    public String getOtherLimits()
    {
        return otherLimits;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("positionName", getPositionName())
            .append("salaryRange", getSalaryRange())
            .append("salaryMin", getSalaryMin())
            .append("salaryMax", getSalaryMax())
            .append("ageLimit", getAgeLimit())
            .append("company", getCompany())
            .append("location", getLocation())
            .append("contactPhone", getContactPhone())
            .append("requirements", getRequirements())
            .append("responsibilities", getResponsibilities())
            .append("otherLimits", getOtherLimits())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
