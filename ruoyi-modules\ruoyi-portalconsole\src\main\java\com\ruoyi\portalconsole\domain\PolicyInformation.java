package com.ruoyi.portalconsole.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 政策资讯对象 policy_information
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public class PolicyInformation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 政策资讯ID */
    private Long policyInformationId;

    /** 发布单位 */
    @Excel(name = "发布单位")
    private String policyInformationUnit;

    /** 资讯板块：业务字典 */
    @Excel(name = "资讯板块：业务字典")
    private String policyInformationType;

    /** 级别：业务字典 */
    @Excel(name = "级别：业务字典")
    private String policyInformationLevel;

    /** 咨询标题 */
    @Excel(name = "咨询标题")
    private String policyInformationTitle;

    /** 简介内容 */
    @Excel(name = "简介内容")
    private String policyInformationIntroduction;

    /** 封面 */
    @Excel(name = "封面")
    private String policyInformationImg;

    /** 资讯内容 */
    @Excel(name = "资讯内容")
    private String policyInformationContent;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Long policyInformationFrequency;

    /** 发布日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发布日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date policyInformationDate;

    /** 是否置顶 */
    @Excel(name = "是否置顶")
    private String top;


    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setPolicyInformationId(Long policyInformationId) 
    {
        this.policyInformationId = policyInformationId;
    }

    public Long getPolicyInformationId() 
    {
        return policyInformationId;
    }
    public void setPolicyInformationUnit(String policyInformationUnit)
    {
        this.policyInformationUnit = policyInformationUnit;
    }

    public String getPolicyInformationUnit()
    {
        return policyInformationUnit;
    }
    public void setPolicyInformationType(String policyInformationType) 
    {
        this.policyInformationType = policyInformationType;
    }

    public String getPolicyInformationType() 
    {
        return policyInformationType;
    }
    public void setPolicyInformationLevel(String policyInformationLevel) 
    {
        this.policyInformationLevel = policyInformationLevel;
    }

    public String getPolicyInformationLevel() 
    {
        return policyInformationLevel;
    }
    public void setPolicyInformationTitle(String policyInformationTitle) 
    {
        this.policyInformationTitle = policyInformationTitle;
    }

    public String getPolicyInformationTitle() 
    {
        return policyInformationTitle;
    }
    public void setPolicyInformationIntroduction(String policyInformationIntroduction) 
    {
        this.policyInformationIntroduction = policyInformationIntroduction;
    }

    public String getPolicyInformationIntroduction() 
    {
        return policyInformationIntroduction;
    }
    public void setPolicyInformationImg(String policyInformationImg) 
    {
        this.policyInformationImg = policyInformationImg;
    }

    public String getPolicyInformationImg() 
    {
        return policyInformationImg;
    }
    public void setPolicyInformationContent(String policyInformationContent) 
    {
        this.policyInformationContent = policyInformationContent;
    }

    public String getPolicyInformationContent() 
    {
        return policyInformationContent;
    }
    public void setPolicyInformationFrequency(Long policyInformationFrequency) 
    {
        this.policyInformationFrequency = policyInformationFrequency;
    }

    public Long getPolicyInformationFrequency() 
    {
        return policyInformationFrequency;
    }
    public void setPolicyInformationDate(Date policyInformationDate) 
    {
        this.policyInformationDate = policyInformationDate;
    }

    public Date getPolicyInformationDate() 
    {
        return policyInformationDate;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public String getTop() {
        return top;
    }

    public void setTop(String top) {
        this.top = top;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("policyInformationId", getPolicyInformationId())
            .append("policyInformationUnit", getPolicyInformationUnit())
            .append("policyInformationType", getPolicyInformationType())
            .append("policyInformationLevel", getPolicyInformationLevel())
            .append("policyInformationTitle", getPolicyInformationTitle())
            .append("policyInformationIntroduction", getPolicyInformationIntroduction())
            .append("policyInformationImg", getPolicyInformationImg())
            .append("policyInformationContent", getPolicyInformationContent())
            .append("policyInformationFrequency", getPolicyInformationFrequency())
            .append("policyInformationDate", getPolicyInformationDate())
            .append("top", getTop())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
