package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.TestingRequirementMapper;
import com.ruoyi.system.domain.TestingRequirement;
import com.ruoyi.system.service.ITestingRequirementService;

/**
 * 检测需求Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-08
 */
@Service
public class TestingRequirementServiceImpl implements ITestingRequirementService 
{
    @Autowired
    private TestingRequirementMapper testingRequirementMapper;

    /**
     * 查询检测需求
     * 
     * @param id 检测需求主键
     * @return 检测需求
     */
    @Override
    public TestingRequirement selectTestingRequirementById(Long id)
    {
        return testingRequirementMapper.selectTestingRequirementById(id);
    }

    /**
     * 查询检测需求列表
     * 
     * @param testingRequirement 检测需求
     * @return 检测需求
     */
    @Override
    public List<TestingRequirement> selectTestingRequirementList(TestingRequirement testingRequirement)
    {
        return testingRequirementMapper.selectTestingRequirementList(testingRequirement);
    }

    /**
     * 新增检测需求
     * 
     * @param testingRequirement 检测需求
     * @return 结果
     */
    @Override
    public int insertTestingRequirement(TestingRequirement testingRequirement)
    {
        testingRequirement.setCreateTime(DateUtils.getNowDate());
        return testingRequirementMapper.insertTestingRequirement(testingRequirement);
    }

    /**
     * 修改检测需求
     * 
     * @param testingRequirement 检测需求
     * @return 结果
     */
    @Override
    public int updateTestingRequirement(TestingRequirement testingRequirement)
    {
        testingRequirement.setUpdateTime(DateUtils.getNowDate());
        return testingRequirementMapper.updateTestingRequirement(testingRequirement);
    }

    /**
     * 批量删除检测需求
     * 
     * @param ids 需要删除的检测需求主键
     * @return 结果
     */
    @Override
    public int deleteTestingRequirementByIds(Long[] ids)
    {
        return testingRequirementMapper.deleteTestingRequirementByIds(ids);
    }

    /**
     * 删除检测需求信息
     * 
     * @param id 检测需求主键
     * @return 结果
     */
    @Override
    public int deleteTestingRequirementById(Long id)
    {
        return testingRequirementMapper.deleteTestingRequirementById(id);
    }
}
