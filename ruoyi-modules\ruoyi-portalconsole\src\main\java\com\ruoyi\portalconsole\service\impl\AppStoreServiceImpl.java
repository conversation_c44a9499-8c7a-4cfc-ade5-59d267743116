package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalconsole.domain.AppStore;
import com.ruoyi.portalconsole.mapper.AppConfigMapper;
import com.ruoyi.portalconsole.mapper.AppStoreMapper;
import com.ruoyi.portalconsole.service.IAppStoreService;
import com.ruoyi.portalconsole.domain.vo.AppStoreVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 * 应用商店Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class AppStoreServiceImpl implements IAppStoreService
{
    @Autowired
    private AppStoreMapper appStoreMapper;

    @Autowired
    private AppConfigMapper appConfigMapper;

    /**
     * 查询应用商店
     * 
     * @param appStoreId 应用商店主键
     * @return 应用商店
     */
    @Override
    public AppStoreVO selectAppStoreByAppStoreId(Long appStoreId)
    {
        return appStoreMapper.selectAppStoreByAppStoreId(appStoreId);
    }

    /**
     * 查询应用商店列表
     * 
     * @param appStore 应用商店
     * @return 应用商店
     */
    @Override
    public List<AppStoreVO> selectAppStoreList(AppStore appStore)
    {
        return appStoreMapper.selectAppStoreList(appStore);
    }

    /**
     * 新增应用商店
     * 
     * @param appStore 应用商店
     * @return 结果
     */
    @Override
    public int insertAppStore(AppStore appStore)
    {
        appStore.setCreateTime(DateUtils.getNowDate());
        appStore.setMemberId(SecurityUtils.getUserId());
        return appStoreMapper.insertAppStore(appStore);
    }

    /**
     * 修改应用商店
     * 
     * @param appStore 应用商店
     * @return 结果
     */
    @Override
    public int updateAppStore(AppStore appStore)
    {
        appStore.setUpdateTime(DateUtils.getNowDate());
        return appStoreMapper.updateAppStore(appStore);
    }

    /**
     * 批量删除应用商店
     * 
     * @param appStoreIds 需要删除的应用商店主键
     * @return 结果
     */
    @Override
    public int deleteAppStoreByAppStoreIds(Long[] appStoreIds)
    {
        return appStoreMapper.deleteAppStoreByAppStoreIds(appStoreIds);
    }

    /**
     * 删除应用商店信息
     * 
     * @param appStoreId 应用商店主键
     * @return 结果
     */
    @Override
    public int deleteAppStoreByAppStoreId(Long appStoreId)
    {
        appConfigMapper.deleteAppConfigByAppConfigId(appStoreId);
        return appStoreMapper.deleteAppStoreByAppStoreId(appStoreId);
    }

    @Override
    public int auditAppStore(AppStoreVO appStore) {
        return appStoreMapper.auditAppStore(appStore.getAppStoreIds(),appStore.getAuditStatus());
    }
}
