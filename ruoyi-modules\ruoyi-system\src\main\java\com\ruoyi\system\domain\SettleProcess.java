package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 入驻申请对象 settle_process
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public class SettleProcess extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 企业名称 */
    @Excel(name = "企业名称")
    private String companyName;

    /** 社会信用代码 */
    @Excel(name = "社会信用代码")
    private String socialCreditCode;

    /** 注册资本 */
    @Excel(name = "注册资本")
    private String registeredCapital;

    /** 行业 */
    @Excel(name = "行业")
    private String industry;

    /** 地址 */
    @Excel(name = "地址")
    private String address;

    /** 经营范围 */
    @Excel(name = "经营范围")
    private String businessScope;

    /** 项目描述 */
    @Excel(name = "项目描述")
    private String projectDescription;

    /** 联系人姓名 */
    @Excel(name = "联系人姓名")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 附件 */
    @Excel(name = "附件")
    private String attachments;

    /** 当前步骤（1-提交流程，2-申请入驻，3-入驻审核，4-开始经营） */
    @Excel(name = "当前步骤", readConverterExp = "1=-提交流程，2-申请入驻，3-入驻审核，4-开始经营")
    private Long currentStep;

    /** 审核状态（0-待审核，1-审核通过，2-审核拒绝） */
    @Excel(name = "审核状态", readConverterExp = "0=-待审核，1-审核通过，2-审核拒绝")
    private Long auditStatus;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String auditComment;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date auditTime;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditor;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    public void setSocialCreditCode(String socialCreditCode) 
    {
        this.socialCreditCode = socialCreditCode;
    }

    public String getSocialCreditCode() 
    {
        return socialCreditCode;
    }
    public void setRegisteredCapital(String registeredCapital) 
    {
        this.registeredCapital = registeredCapital;
    }

    public String getRegisteredCapital() 
    {
        return registeredCapital;
    }
    public void setIndustry(String industry) 
    {
        this.industry = industry;
    }

    public String getIndustry() 
    {
        return industry;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setBusinessScope(String businessScope) 
    {
        this.businessScope = businessScope;
    }

    public String getBusinessScope() 
    {
        return businessScope;
    }
    public void setProjectDescription(String projectDescription) 
    {
        this.projectDescription = projectDescription;
    }

    public String getProjectDescription() 
    {
        return projectDescription;
    }
    public void setContactName(String contactName) 
    {
        this.contactName = contactName;
    }

    public String getContactName() 
    {
        return contactName;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setAttachments(String attachments) 
    {
        this.attachments = attachments;
    }

    public String getAttachments() 
    {
        return attachments;
    }
    public void setCurrentStep(Long currentStep) 
    {
        this.currentStep = currentStep;
    }

    public Long getCurrentStep() 
    {
        return currentStep;
    }
    public void setAuditStatus(Long auditStatus) 
    {
        this.auditStatus = auditStatus;
    }

    public Long getAuditStatus() 
    {
        return auditStatus;
    }
    public void setAuditComment(String auditComment) 
    {
        this.auditComment = auditComment;
    }

    public String getAuditComment() 
    {
        return auditComment;
    }
    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }
    public void setAuditor(String auditor) 
    {
        this.auditor = auditor;
    }

    public String getAuditor() 
    {
        return auditor;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("companyName", getCompanyName())
            .append("socialCreditCode", getSocialCreditCode())
            .append("registeredCapital", getRegisteredCapital())
            .append("industry", getIndustry())
            .append("address", getAddress())
            .append("businessScope", getBusinessScope())
            .append("projectDescription", getProjectDescription())
            .append("contactName", getContactName())
            .append("contactPhone", getContactPhone())
            .append("attachments", getAttachments())
            .append("currentStep", getCurrentStep())
            .append("auditStatus", getAuditStatus())
            .append("auditComment", getAuditComment())
            .append("auditTime", getAuditTime())
            .append("auditor", getAuditor())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
