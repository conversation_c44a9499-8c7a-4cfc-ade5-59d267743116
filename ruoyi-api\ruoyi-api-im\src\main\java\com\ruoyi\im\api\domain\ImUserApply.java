package com.ruoyi.im.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName(value = "im_user_apply")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImUserApply extends Model<ImUserApply> {

        @TableId(value = "id", type = IdType.AUTO)
        private Long id;//自增

        private String senderId;//发送者

        private String receiverId;//接受者

        private String remark;//申请理由

        private Integer status;//状态 0-待处理 1-已同意 2-已拒绝

        private Date create_time;//创建时间

        private Date update_time;//更新时间
}
