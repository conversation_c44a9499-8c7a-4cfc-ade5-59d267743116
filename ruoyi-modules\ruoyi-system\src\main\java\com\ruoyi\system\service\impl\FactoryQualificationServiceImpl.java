package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.FactoryQualificationMapper;
import com.ruoyi.system.domain.FactoryQualification;
import com.ruoyi.system.service.IFactoryQualificationService;

/**
 * 工厂资质证件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@Service
public class FactoryQualificationServiceImpl implements IFactoryQualificationService 
{
    @Autowired
    private FactoryQualificationMapper factoryQualificationMapper;

    /**
     * 查询工厂资质证件
     * 
     * @param id 工厂资质证件主键
     * @return 工厂资质证件
     */
    @Override
    public FactoryQualification selectFactoryQualificationById(Long id)
    {
        return factoryQualificationMapper.selectFactoryQualificationById(id);
    }

    /**
     * 查询工厂资质证件列表
     * 
     * @param factoryQualification 工厂资质证件
     * @return 工厂资质证件
     */
    @Override
    public List<FactoryQualification> selectFactoryQualificationList(FactoryQualification factoryQualification)
    {
        return factoryQualificationMapper.selectFactoryQualificationList(factoryQualification);
    }

    /**
     * 新增工厂资质证件
     * 
     * @param factoryQualification 工厂资质证件
     * @return 结果
     */
    @Override
    public int insertFactoryQualification(FactoryQualification factoryQualification)
    {
        factoryQualification.setCreateTime(DateUtils.getNowDate());
        return factoryQualificationMapper.insertFactoryQualification(factoryQualification);
    }

    /**
     * 修改工厂资质证件
     * 
     * @param factoryQualification 工厂资质证件
     * @return 结果
     */
    @Override
    public int updateFactoryQualification(FactoryQualification factoryQualification)
    {
        factoryQualification.setUpdateTime(DateUtils.getNowDate());
        return factoryQualificationMapper.updateFactoryQualification(factoryQualification);
    }

    /**
     * 批量删除工厂资质证件
     * 
     * @param ids 需要删除的工厂资质证件主键
     * @return 结果
     */
    @Override
    public int deleteFactoryQualificationByIds(Long[] ids)
    {
        return factoryQualificationMapper.deleteFactoryQualificationByIds(ids);
    }

    /**
     * 删除工厂资质证件信息
     * 
     * @param id 工厂资质证件主键
     * @return 结果
     */
    @Override
    public int deleteFactoryQualificationById(Long id)
    {
        return factoryQualificationMapper.deleteFactoryQualificationById(id);
    }
}
