<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.IntentionApplyMapper">

    <resultMap type="com.ruoyi.portalweb.vo.IntentionApplyVO" id="IntentionApplyResult">
        <result property="id" column="id"/>
        <result property="intentionType" column="intention_type"/>
        <result property="intentionContent" column="intention_content"/>
        <result property="intentionContent" column="intention_content"/>
        <result property="intentionId" column="intention_id"/>
        <result property="phone" column="phone"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberId" column="member_id"/>
        <result property="intentionMemberId" column="intention_member_id"/>
        <result property="intentionStatus" column="intention_status"/>
        <result property="intentionCompanyName" column="intention_company_name"/>
        <result property="companyName" column="company_name"/>
        <result property="completionDate" column="completion_date"/>
        <result property="quantity" column="quantity"/>
        <result property="price" column="price"/>
        <result property="rate" column="rate"/>
        <result property="shippingFee" column="shipping_fee"/>
        <result property="sum" column="sum"/>
        <result property="term" column="term"/>
        <result property="title" column="title"/>

        <result property="intentionTypeName" column="intention_type_name"/>
        <result property="resourceTitle" column="resource_title"/>
    </resultMap>

    <sql id="selectIntentionApplyVo">
        select id, intention_type, intention_content, contacts, phone, del_flag, create_by, create_time, update_by,
        update_time, intention_id, remark, member_id, intention_member_id, intention_status, intention_company_name, company_name,
        completion_date, quantity, price, rate, shipping_fee, sum, term, title from intention_apply
    </sql>

    <sql id="Base_Column_List">
		a.*,
        d.dict_label as intention_type_name,
        IFNULL(b.title, c.title) AS resource_title
	</sql>

	<sql id="Base_Table_List">
		FROM intention_apply a
        LEFT JOIN sys_dict_data d ON a.intention_type = d.dict_value AND d.dict_type = 'intention_type'
        LEFT JOIN demand b ON a.intention_id = b.id AND d.dict_label = '需求'
        LEFT JOIN supply c ON a.intention_id = c.id AND d.dict_label = '供给'
	</sql>

    <select id="selectIntentionApplyList" parameterType="IntentionApply" resultMap="IntentionApplyResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        <where>
            a.del_flag = 0
            <if test="intentionType != null  and intentionType != ''">and a.intention_type = #{intentionType}</if>
            <if test="intentionContent != null  and intentionContent != ''">
                and a.intention_content = #{intentionContent}
            </if>
            <if test="contacts != null  and contacts != ''">and a.contacts = #{contacts}</if>
            <if test="phone != null  and phone != ''">and a.phone = #{phone}</if>
            <if test="memberId != null ">and a.member_id = #{memberId}</if>
            <if test="intentionMemberId != null ">and a.intention_member_id = #{intentionMemberId}</if>
            <if test="intentionId != null ">and a.intention_id = #{intentionId}</if>
            <if test="intentionStatus != null and intentionStatus != ''">and a.intention_status = #{intentionStatus}</if>
            <if test="intentionCompanyName != null and intentionCompanyName != ''">and a.intention_company_name like concat('%', #{intentionCompanyName},'%') </if>
            <if test="companyName != null and companyName != ''">and a.company_name like concat('%', #{companyName},'%') </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <select id="selectIntentionApplyById" parameterType="Long" resultMap="IntentionApplyResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        where id = #{id}
    </select>

    <insert id="insertIntentionApply" parameterType="IntentionApply" useGeneratedKeys="true" keyProperty="id">
        insert into intention_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="intentionType != null">intention_type,</if>
            <if test="intentionContent != null">intention_content,</if>
            <if test="intentionId != null">intention_id,</if>
            <if test="contacts != null">contacts,</if>
            <if test="phone != null">phone,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="memberId != null">member_id,</if>
            <if test="intentionMemberId != null">intention_member_id,</if>
            <if test="intentionCompanyName != null">intention_company_name, </if>
            <if test="companyName != null">company_name,</if>
            <if test="intentionStatus != null">intention_status,</if>
            <if test="completionDate != null">completion_date,</if>
            <if test="quantity != null">quantity,</if>
            <if test="price != null">price,</if>
            <if test="rate != null">rate,</if>
            <if test="shippingFee != null">shipping_fee,</if>
            <if test="sum != null">sum,</if>
            <if test="term != null">term,</if>
            <if test="title != null">title</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="intentionType != null">#{intentionType},</if>
            <if test="intentionContent != null">#{intentionContent},</if>
            <if test="intentionId != null">#{intentionId},</if>
            <if test="contacts != null">#{contacts},</if>
            <if test="phone != null">#{phone},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="intentionMemberId != null">#{intentionMemberId},</if>
            <if test="intentionCompanyName != null">#{intentionCompanyName}, </if>
            <if test="companyName != null">#{companyName},</if>
            <if test="intentionStatus != null">#{intentionStatus},</if>
            <if test="completionDate != null">#{completionDate},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="price != null">#{price},</if>
            <if test="rate != null">#{rate},</if>
            <if test="shippingFee != null">#{shippingFee},</if>
            <if test="sum != null">#{sum},</if>
            <if test="term != null">#{term},</if>
            <if test="title != null">#{title}</if>
        </trim>
    </insert>

    <update id="updateIntentionApply" parameterType="IntentionApply">
        update intention_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="intentionType != null">intention_type = #{intentionType},</if>
            <if test="intentionContent != null">intention_content = #{intentionContent},</if>
            <if test="contacts != null">contacts = #{contacts},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="intentionMemberId != null">intention_member_id = #{intentionMemberId},</if>
            <if test="intentionStatus != null">intention_status = #{intentionStatus},</if>
            <if test="completionDate != null">completion_date = #{completionDate},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="price != null">price = #{price},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="shippingFee != null">shipping_fee = #{shippingFee},</if>
            <if test="sum != null">sum = #{sum},</if>
            <if test="term != null">term = #{term},</if>
            <if test="title != null">title = #{title},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIntentionApplyById" parameterType="Long">
        delete from intention_apply where id = #{id}
    </delete>

    <delete id="deleteIntentionApplyByIds" parameterType="String">
        delete from intention_apply where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>