package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.NewsInformationPlateMapper;
import com.ruoyi.portalconsole.domain.NewsInformationPlate;
import com.ruoyi.portalconsole.service.INewsInformationPlateService;

/**
 * 咨询板块Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
@Service
public class NewsInformationPlateServiceImpl implements INewsInformationPlateService 
{
    @Autowired
    private NewsInformationPlateMapper newsInformationPlateMapper;

    /**
     * 查询咨询板块
     * 
     * @param newsInformationPlateId 咨询板块主键
     * @return 咨询板块
     */
    @Override
    public NewsInformationPlate selectNewsInformationPlateByNewsInformationPlateId(Long newsInformationPlateId)
    {
        return newsInformationPlateMapper.selectNewsInformationPlateByNewsInformationPlateId(newsInformationPlateId);
    }

    /**
     * 查询咨询板块列表
     * 
     * @param newsInformationPlate 咨询板块
     * @return 咨询板块
     */
    @Override
    public List<NewsInformationPlate> selectNewsInformationPlateList(NewsInformationPlate newsInformationPlate)
    {
        return newsInformationPlateMapper.selectNewsInformationPlateList(newsInformationPlate);
    }

    /**
     * 新增咨询板块
     * 
     * @param newsInformationPlate 咨询板块
     * @return 结果
     */
    @Override
    public int insertNewsInformationPlate(NewsInformationPlate newsInformationPlate)
    {
        newsInformationPlate.setCreateTime(DateUtils.getNowDate());
        return newsInformationPlateMapper.insertNewsInformationPlate(newsInformationPlate);
    }

    /**
     * 修改咨询板块
     * 
     * @param newsInformationPlate 咨询板块
     * @return 结果
     */
    @Override
    public int updateNewsInformationPlate(NewsInformationPlate newsInformationPlate)
    {
        newsInformationPlate.setUpdateTime(DateUtils.getNowDate());
        return newsInformationPlateMapper.updateNewsInformationPlate(newsInformationPlate);
    }

    /**
     * 批量删除咨询板块
     * 
     * @param newsInformationPlateIds 需要删除的咨询板块主键
     * @return 结果
     */
    @Override
    public int deleteNewsInformationPlateByNewsInformationPlateIds(Long[] newsInformationPlateIds)
    {
        return newsInformationPlateMapper.deleteNewsInformationPlateByNewsInformationPlateIds(newsInformationPlateIds);
    }

    /**
     * 删除咨询板块信息
     * 
     * @param newsInformationPlateId 咨询板块主键
     * @return 结果
     */
    @Override
    public int deleteNewsInformationPlateByNewsInformationPlateId(Long newsInformationPlateId)
    {
        return newsInformationPlateMapper.deleteNewsInformationPlateByNewsInformationPlateId(newsInformationPlateId);
    }
}
