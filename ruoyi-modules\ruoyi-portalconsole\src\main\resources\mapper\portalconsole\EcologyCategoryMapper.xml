<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.EcologyCategoryMapper">
    
    <resultMap type="EcologyCategory" id="EcologyCategoryResult">
        <result property="ecologyCategoryId"    column="ecology_category_id"    />
        <result property="ecologyCategoryCode"    column="ecology_category_code"    />
        <result property="ecologyCategoryName"    column="ecology_category_name"    />
        <result property="ecologyCategoryIntroduction"    column="ecology_category_introduction"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEcologyCategoryVo">
        select ecology_category_id, ecology_category_code, ecology_category_name, ecology_category_introduction, del_flag, create_by, create_time, update_by, update_time, remark from ecology_category
    </sql>

    <select id="selectEcologyCategoryList" parameterType="EcologyCategory" resultMap="EcologyCategoryResult">
        <include refid="selectEcologyCategoryVo"/>
        <where>  
            <if test="ecologyCategoryCode != null  and ecologyCategoryCode != ''"> and ecology_category_code = #{ecologyCategoryCode}</if>
            <if test="ecologyCategoryName != null  and ecologyCategoryName != ''"> and ecology_category_name like concat('%', #{ecologyCategoryName}, '%')</if>
            <if test="ecologyCategoryIntroduction != null  and ecologyCategoryIntroduction != ''"> and ecology_category_introduction = #{ecologyCategoryIntroduction}</if>
        </where>
    </select>
    
    <select id="selectEcologyCategoryByEcologyCategoryId" parameterType="Long" resultMap="EcologyCategoryResult">
        <include refid="selectEcologyCategoryVo"/>
        where ecology_category_id = #{ecologyCategoryId}
    </select>
        
    <insert id="insertEcologyCategory" parameterType="EcologyCategory" useGeneratedKeys="true" keyProperty="ecologyCategoryId">
        insert into ecology_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ecologyCategoryCode != null">ecology_category_code,</if>
            <if test="ecologyCategoryName != null">ecology_category_name,</if>
            <if test="ecologyCategoryIntroduction != null">ecology_category_introduction,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ecologyCategoryCode != null">#{ecologyCategoryCode},</if>
            <if test="ecologyCategoryName != null">#{ecologyCategoryName},</if>
            <if test="ecologyCategoryIntroduction != null">#{ecologyCategoryIntroduction},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEcologyCategory" parameterType="EcologyCategory">
        update ecology_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="ecologyCategoryCode != null">ecology_category_code = #{ecologyCategoryCode},</if>
            <if test="ecologyCategoryName != null">ecology_category_name = #{ecologyCategoryName},</if>
            <if test="ecologyCategoryIntroduction != null">ecology_category_introduction = #{ecologyCategoryIntroduction},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where ecology_category_id = #{ecologyCategoryId}
    </update>

    <delete id="deleteEcologyCategoryByEcologyCategoryId" parameterType="Long">
        delete from ecology_category where ecology_category_id = #{ecologyCategoryId}
    </delete>

    <delete id="deleteEcologyCategoryByEcologyCategoryIds" parameterType="String">
        delete from ecology_category where ecology_category_id in 
        <foreach item="ecologyCategoryId" collection="array" open="(" separator="," close=")">
            #{ecologyCategoryId}
        </foreach>
    </delete>
</mapper>