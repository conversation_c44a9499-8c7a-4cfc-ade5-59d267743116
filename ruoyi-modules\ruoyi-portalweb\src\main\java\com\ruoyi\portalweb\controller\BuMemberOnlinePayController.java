package com.ruoyi.portalweb.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalweb.api.domain.BuMemberOnlinePay;
import com.ruoyi.portalweb.service.IBuMemberOnlinePayService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 商城线上支付Controller
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@RestController
@RequestMapping("/BuMemberOnlinePay")
public class BuMemberOnlinePayController extends BaseController
{
    @Autowired
    private IBuMemberOnlinePayService buMemberOnlinePayService;

    /**
     * 查询商城线上支付列表
     */
    @RequiresPermissions("portalweb:BuMemberOnlinePay:list")
    @GetMapping("/list")
    public TableDataInfo list(BuMemberOnlinePay buMemberOnlinePay)
    {
        startPage();
        List<BuMemberOnlinePay> list = buMemberOnlinePayService.selectBuMemberOnlinePayList(buMemberOnlinePay);
        return getDataTable(list);
    }

    /**
     * 导出商城线上支付列表
     */
    @RequiresPermissions("portalweb:BuMemberOnlinePay:export")
    @Log(title = "商城线上支付", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BuMemberOnlinePay buMemberOnlinePay)
    {
        List<BuMemberOnlinePay> list = buMemberOnlinePayService.selectBuMemberOnlinePayList(buMemberOnlinePay);
        ExcelUtil<BuMemberOnlinePay> util = new ExcelUtil<BuMemberOnlinePay>(BuMemberOnlinePay.class);
        util.exportExcel(response, list, "商城线上支付数据");
    }

    /**
     * 获取商城线上支付详细信息
     */
    @RequiresPermissions("portalweb:BuMemberOnlinePay:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(buMemberOnlinePayService.selectBuMemberOnlinePayById(id));
    }

    /**
     * 新增商城线上支付
     */
    @RequiresPermissions("portalweb:BuMemberOnlinePay:add")
    @Log(title = "商城线上支付", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuMemberOnlinePay buMemberOnlinePay)
    {
        return toAjax(buMemberOnlinePayService.insertBuMemberOnlinePay(buMemberOnlinePay));
    }

    /**
     * 修改商城线上支付
     */
    @RequiresPermissions("portalweb:BuMemberOnlinePay:edit")
    @Log(title = "商城线上支付", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuMemberOnlinePay buMemberOnlinePay)
    {
        return toAjax(buMemberOnlinePayService.updateBuMemberOnlinePay(buMemberOnlinePay));
    }

    /**
     * 删除商城线上支付
     */
    @RequiresPermissions("portalweb:BuMemberOnlinePay:remove")
    @Log(title = "商城线上支付", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(buMemberOnlinePayService.deleteBuMemberOnlinePayByIds(ids));
    }
}
