package com.ruoyi.system.domain;

import java.io.Serializable;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 人才信息对象 talent_info
 * 
 * <AUTHOR>
 * @date 2024-03-12
 */
public class TalentInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 用户ID */
    private Long userId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 出生年月 */
    @Excel(name = "出生年月")
    private String birthDate;

    /** 毕业院校 */
    @Excel(name = "毕业院校")
    private String graduateSchool;

    /** 所在单位 */
    @Excel(name = "所在单位")
    private String currentCompany;

    /** 职务 */
    @Excel(name = "职务")
    private String position;

    /** 所在地 */
    @Excel(name = "所在地")
    private String location;

    /** 入驻状态（0待审核 1已入驻 2未通过） */
    @Excel(name = "入驻状态", readConverterExp = "0=待审核,1=已入驻,2=未通过")
    private String settledStatus;

    /** 学历 */
    @Excel(name = "学历")
    private String education;

    /** 职位类型 */
    @Excel(name = "职位类型")
    private String positionType;

    /** 职称 */
    @Excel(name = "职称")
    private String jobTitle;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 工作状态（0在职 1离职 2退休） */
    @Excel(name = "工作状态", readConverterExp = "0=在职,1=离职,2=退休")
    private String workStatus;

    /** 技能特长 */
    @Excel(name = "技能特长")
    private String skills;

    /** 工作经验 */
    @Excel(name = "工作经验")
    private String workExperience;

    /** 照片 */
    private String photo;

    /** 简历文件 */
    private String resumeFile;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setBirthDate(String birthDate) 
    {
        this.birthDate = birthDate;
    }

    public String getBirthDate() 
    {
        return birthDate;
    }

    public void setGraduateSchool(String graduateSchool) 
    {
        this.graduateSchool = graduateSchool;
    }

    public String getGraduateSchool() 
    {
        return graduateSchool;
    }

    public void setCurrentCompany(String currentCompany) 
    {
        this.currentCompany = currentCompany;
    }

    public String getCurrentCompany() 
    {
        return currentCompany;
    }

    public void setPosition(String position) 
    {
        this.position = position;
    }

    public String getPosition() 
    {
        return position;
    }

    public void setLocation(String location) 
    {
        this.location = location;
    }

    public String getLocation() 
    {
        return location;
    }

    public void setSettledStatus(String settledStatus) 
    {
        this.settledStatus = settledStatus;
    }

    public String getSettledStatus() 
    {
        return settledStatus;
    }

    public void setEducation(String education) 
    {
        this.education = education;
    }

    public String getEducation() 
    {
        return education;
    }

    public void setPositionType(String positionType) 
    {
        this.positionType = positionType;
    }

    public String getPositionType() 
    {
        return positionType;
    }

    public void setJobTitle(String jobTitle) 
    {
        this.jobTitle = jobTitle;
    }

    public String getJobTitle() 
    {
        return jobTitle;
    }

    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }

    public void setWorkStatus(String workStatus) 
    {
        this.workStatus = workStatus;
    }

    public String getWorkStatus() 
    {
        return workStatus;
    }

    public void setSkills(String skills) 
    {
        this.skills = skills;
    }

    public String getSkills() 
    {
        return skills;
    }

    public void setWorkExperience(String workExperience) 
    {
        this.workExperience = workExperience;
    }

    public String getWorkExperience() 
    {
        return workExperience;
    }

    public void setPhoto(String photo) 
    {
        this.photo = photo;
    }

    public String getPhoto() 
    {
        return photo;
    }

    public void setResumeFile(String resumeFile) 
    {
        this.resumeFile = resumeFile;
    }

    public String getResumeFile() 
    {
        return resumeFile;
    }
} 