package com.ruoyi.auth.config;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 全网智能通讯平台短信配置
 */
@Configuration
@ConfigurationProperties(prefix = "qwt")
public class QWTSmsConfig {

    @Value("${qwt.login-name}")
    private String loginName;

    @Value("${qwt.password}")
    private String password;

    @Value("${qwt.sign-name}")
    private String signName;

    @Value("${qwt.api-url}")
    private String apiUrl;

    @Value("${qwt.fee-type}")
    private String feeType;

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSignName() {
        return signName;
    }

    public void setSignName(String signName) {
        this.signName = signName;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("loginName", loginName)
                .append("password", password)
                .append("signName", signName)
                .append("apiUrl", apiUrl)
                .append("feeType", feeType)
                .toString();
    }
}
