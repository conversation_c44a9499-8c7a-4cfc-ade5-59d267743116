package com.ruoyi.portalconsole.mapper;

import java.util.List;
import com.ruoyi.portalconsole.domain.Solution;
import com.ruoyi.portalconsole.domain.vo.SolutionVO;

/**
 * 解决方案Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface SolutionMapper 
{
    /**
     * 查询解决方案
     * 
     * @param solutionId 解决方案主键
     * @return 解决方案
     */
    public SolutionVO selectSolutionBySolutionId(Long solutionId);

    /**
     * 查询解决方案列表
     * 
     * @param solution 解决方案
     * @return 解决方案集合
     */
    public List<SolutionVO> selectSolutionList(Solution solution);

    /**
     * 新增解决方案
     * 
     * @param solution 解决方案
     * @return 结果
     */
    public int insertSolution(Solution solution);

    /**
     * 修改解决方案
     * 
     * @param solution 解决方案
     * @return 结果
     */
    public int updateSolution(Solution solution);

    /**
     * 删除解决方案
     * 
     * @param solutionId 解决方案主键
     * @return 结果
     */
    public int deleteSolutionBySolutionId(Long solutionId);

    /**
     * 批量删除解决方案
     * 
     * @param solutionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSolutionBySolutionIds(Long[] solutionIds);
}
