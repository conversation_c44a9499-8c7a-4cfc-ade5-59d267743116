package com.ruoyi.portalweb.api.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 解决方案类型对象 solution_type
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public class SolutionType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 方案类型ID */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long solutionTypeId;

    /** 上级id */
    @Excel(name = "上级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /** 名称 */
    @Excel(name = "名称")
    private String solutionTypeName;

    /** 服务范围 */
    @Excel(name = "服务范围")
    private String category;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setSolutionTypeId(Long solutionTypeId) 
    {
        this.solutionTypeId = solutionTypeId;
    }

    public Long getSolutionTypeId() 
    {
        return solutionTypeId;
    }
    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }
    public void setSolutionTypeName(String solutionTypeName) 
    {
        this.solutionTypeName = solutionTypeName;
    }

    public String getSolutionTypeName() 
    {
        return solutionTypeName;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public String getCategory() {return category;}

    public void setCategory(String category) {this.category = category;}

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("solutionTypeId", getSolutionTypeId())
            .append("parentId", getParentId())
            .append("solutionTypeName", getSolutionTypeName())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("category", getCategory())
            .toString();
    }
}
