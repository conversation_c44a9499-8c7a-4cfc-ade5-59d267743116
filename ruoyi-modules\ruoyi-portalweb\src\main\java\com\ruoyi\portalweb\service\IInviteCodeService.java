package com.ruoyi.portalweb.service;

import java.util.List;
import com.ruoyi.portalweb.api.domain.InviteCode;
import com.ruoyi.portalweb.vo.InviteCodeAmount;
import com.ruoyi.portalweb.vo.InviteCodeVO;

/**
 * 企业邀请码Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-06
 */
public interface IInviteCodeService 
{
    /**
     * 查询企业邀请码
     * 
     * @param id 企业邀请码主键
     * @return 企业邀请码
     */
    public InviteCode selectInviteCodeById(Long id);

    /**
     * 查询企业邀请码列表
     * 
     * @param inviteCode 企业邀请码
     * @return 企业邀请码集合
     */
    public List<InviteCode> selectInviteCodeList(InviteCode inviteCode);

    /**
     * 新增企业邀请码
     *
     * @param inviteCodeAmount 生成数量
     * @return 结果
     */
    public List<String> insertInviteCode(InviteCodeAmount inviteCodeAmount);

    public InviteCodeVO selectInviteCodeByCode(String code);

    /**
     * 修改企业邀请码
     * 
     * @param inviteCode 企业邀请码
     * @return 结果
     */
    public int updateInviteCode(InviteCode inviteCode);

    /**
     * 批量删除企业邀请码
     * 
     * @param ids 需要删除的企业邀请码主键集合
     * @return 结果
     */
    public int deleteInviteCodeByIds(Long[] ids);



    /**
     * 批量删除失效的企业邀请码
     *
     * @return 结果
     */
    public int deleteInvalidInviteCodes();

    /**
     * 删除企业邀请码信息
     * 
     * @param id 企业邀请码主键
     * @return 结果
     */
    public int deleteInviteCodeById(Long id);
}
