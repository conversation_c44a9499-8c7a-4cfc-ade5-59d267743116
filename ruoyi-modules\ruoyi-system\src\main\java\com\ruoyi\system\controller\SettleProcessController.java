package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SettleProcess;
import com.ruoyi.system.service.ISettleProcessService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 入驻申请Controller
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@RestController
@RequestMapping("/settleProcess")
public class SettleProcessController extends BaseController
{
    @Autowired
    private ISettleProcessService settleProcessService;

    /**
     * 查询入驻申请列表
     */
    @RequiresPermissions("system:settleProcess:list")
    @GetMapping("/list")
    public TableDataInfo list(SettleProcess settleProcess)
    {
        startPage();
        List<SettleProcess> list = settleProcessService.selectSettleProcessList(settleProcess);
        return getDataTable(list);
    }

    /**
     * 导出入驻申请列表
     */
    @RequiresPermissions("system:settleProcess:export")
    @Log(title = "入驻申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SettleProcess settleProcess)
    {
        List<SettleProcess> list = settleProcessService.selectSettleProcessList(settleProcess);
        ExcelUtil<SettleProcess> util = new ExcelUtil<SettleProcess>(SettleProcess.class);
        util.exportExcel(response, list, "入驻申请数据");
    }

    /**
     * 获取入驻申请详细信息
     */
    @RequiresPermissions("system:settleProcess:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(settleProcessService.selectSettleProcessById(id));
    }

    /**
     * 新增入驻申请
     */
    @RequiresPermissions("system:settleProcess:add")
    @Log(title = "入驻申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SettleProcess settleProcess)
    {
        return toAjax(settleProcessService.insertSettleProcess(settleProcess));
    }

    /**
     * 修改入驻申请
     */
    @RequiresPermissions("system:settleProcess:edit")
    @Log(title = "入驻申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SettleProcess settleProcess)
    {
        return toAjax(settleProcessService.updateSettleProcess(settleProcess));
    }

    /**
     * 删除入驻申请
     */
    @RequiresPermissions("system:settleProcess:remove")
    @Log(title = "入驻申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(settleProcessService.deleteSettleProcessByIds(ids));
    }
}
