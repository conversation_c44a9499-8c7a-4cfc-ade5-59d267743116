<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.OutsourcingRequirementMapper">
    
    <resultMap type="OutsourcingRequirement" id="OutsourcingRequirementResult">
        <result property="id"    column="id"    />
        <result property="processName"    column="process_name"    />
        <result property="processingQuantity"    column="processing_quantity"    />
        <result property="outsourcingContent"    column="outsourcing_content"    />
        <result property="requiredCompletionTime"    column="required_completion_time"    />
        <result property="projectNumber"    column="project_number"    />
        <result property="remarks"    column="remarks"    />
        <result property="companyName"    column="company_name"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOutsourcingRequirementVo">
        select id, process_name, processing_quantity, outsourcing_content, required_completion_time, project_number, remarks, company_name, contact_person, contact_phone, status, del_flag, create_by, create_time, update_by, update_time from outsourcing_requirement
    </sql>

    <select id="selectOutsourcingRequirementList" parameterType="OutsourcingRequirement" resultMap="OutsourcingRequirementResult">
        <include refid="selectOutsourcingRequirementVo"/>
        <where>  
            <if test="processName != null  and processName != ''"> and process_name like concat('%', #{processName}, '%')</if>
            <if test="processingQuantity != null "> and processing_quantity = #{processingQuantity}</if>
            <if test="outsourcingContent != null  and outsourcingContent != ''"> and outsourcing_content = #{outsourcingContent}</if>
            <if test="requiredCompletionTime != null "> and required_completion_time = #{requiredCompletionTime}</if>
            <if test="projectNumber != null  and projectNumber != ''"> and project_number = #{projectNumber}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person = #{contactPerson}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectOutsourcingRequirementById" parameterType="Long" resultMap="OutsourcingRequirementResult">
        <include refid="selectOutsourcingRequirementVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOutsourcingRequirement" parameterType="OutsourcingRequirement" useGeneratedKeys="true" keyProperty="id">
        insert into outsourcing_requirement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="processName != null and processName != ''">process_name,</if>
            <if test="processingQuantity != null">processing_quantity,</if>
            <if test="outsourcingContent != null">outsourcing_content,</if>
            <if test="requiredCompletionTime != null">required_completion_time,</if>
            <if test="projectNumber != null">project_number,</if>
            <if test="remarks != null">remarks,</if>
            <if test="companyName != null">company_name,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="processName != null and processName != ''">#{processName},</if>
            <if test="processingQuantity != null">#{processingQuantity},</if>
            <if test="outsourcingContent != null">#{outsourcingContent},</if>
            <if test="requiredCompletionTime != null">#{requiredCompletionTime},</if>
            <if test="projectNumber != null">#{projectNumber},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOutsourcingRequirement" parameterType="OutsourcingRequirement">
        update outsourcing_requirement
        <trim prefix="SET" suffixOverrides=",">
            <if test="processName != null and processName != ''">process_name = #{processName},</if>
            <if test="processingQuantity != null">processing_quantity = #{processingQuantity},</if>
            <if test="outsourcingContent != null">outsourcing_content = #{outsourcingContent},</if>
            <if test="requiredCompletionTime != null">required_completion_time = #{requiredCompletionTime},</if>
            <if test="projectNumber != null">project_number = #{projectNumber},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOutsourcingRequirementById" parameterType="Long">
        delete from outsourcing_requirement where id = #{id}
    </delete>

    <delete id="deleteOutsourcingRequirementByIds" parameterType="String">
        delete from outsourcing_requirement where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>