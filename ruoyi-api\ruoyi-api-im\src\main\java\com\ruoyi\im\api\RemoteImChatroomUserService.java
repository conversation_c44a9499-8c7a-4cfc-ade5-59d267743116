package com.ruoyi.im.api;

import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.im.api.domain.ImChatroomUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.api
 * @ClassName: RemoteImChatroomService
 * @Author: ${maguojun}
 * @Description: ${description}
 * @Date: 2022/3/16 13:51
 * @Version: 1.0
 */
@FeignClient(contextId = "remoteImChatroomUserService", value = ServiceNameConstants.IM_SERVICE)
public interface RemoteImChatroomUserService {

    /**
     * 列表
     * @return
     */
    @GetMapping(value = "/im/chatroomuser/search")
    R<List<ImChatroomUser>> findList(@RequestBody(required = false) ImChatroomUser imChatroomUser, @RequestParam(value = "fields", required = false) String fields);

    /**
     * 数量
     * @param imChatroomUser
     * @return
     */
    @GetMapping(value = "/im/chatroomuser/count")
    R<Integer> findCount(@RequestBody(required = false) ImChatroomUser imChatroomUser);

    /**
     * 用户统计
     * @param ids
     * @return
     */
    @GetMapping(value = "/im/chatroomuser/number")
    R<Map<String, Integer>> findUser(@RequestParam("ids") String ids);
}