package com.ruoyi.im.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.im.api.domain.ImGroup;
import com.ruoyi.im.api.domain.ImGroupUser;
import com.ruoyi.im.api.domain.ImUser;
import com.ruoyi.im.api.util.RongyunUtils;
import com.ruoyi.im.service.ImGroupService;
import com.ruoyi.im.service.ImGroupUserService;
import com.ruoyi.im.service.ImUserService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/im/groupuser")
public class ImGroupUserController {

    @Resource
    private ImGroupUserService imGroupUserService;

    @Resource
    private ImUserService imUserService;

    @Resource
    private ImGroupService imGroupService;

    @Resource
    private RongyunUtils rongyunUtils;

    /***
     * ImGroup分页条件搜索实现
     * @param imGroupUser
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/search/{page}/{size}")
    public TableDataInfo findPage(@RequestBody(required = false) ImGroupUser imGroupUser, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields) {
        Page<ImGroupUser> pageSearch = new Page<>(page, size);
        QueryWrapper<ImGroupUser> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(imGroupUser.getGroupId())) {
            wrapper.eq("groupId", imGroupUser.getGroupId());
        }
        if (StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        imGroupUserService.page(pageSearch, wrapper);
        TableDataInfo tableDataInfo = new TableDataInfo(pageSearch.getRecords(), (int) pageSearch.getTotal());
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setMsg("success");
        return tableDataInfo;
    }

    /***
     * 多条件搜索数据
     * @param imGroupUser
     * @return
     */
    @PostMapping(value = "/search")
    public R<List<ImGroupUser>> findList(@RequestBody(required = false) ImGroupUser imGroupUser, @RequestParam(value = "fields", required = false) String fields) {
        QueryWrapper<ImGroupUser> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(imGroupUser.getGroupId())) {
            wrapper.eq("groupId", imGroupUser.getGroupId());
        }
        if (StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        return R.ok(imGroupUserService.list(wrapper));
    }

    /***
     * 修改ImGroup数据
     * @param imGroupUser
     * @return
     */
    @PostMapping(value = "/{id}")
    public R<Boolean> update(@RequestBody ImGroupUser imGroupUser, @PathVariable("id") Long id) {
        if (imGroupUserService.updateById(imGroupUser)) {
            return R.ok(true);
        }
        return R.ok(false);
    }

    /**
     * API 文档: http://www.rongcloud.cn/docs/server_sdk_api/group/group.html#join
     * <p>
     * 邀请用户加入群组
     */
    @PostMapping(value = "/signin")
    public R<Long> add(@RequestBody ImGroupUser imGroupUser) {
        if (ObjectUtils.isNotEmpty(imGroupUser)) {
            ImGroup imGroup = imGroupService.getOne(new QueryWrapper<ImGroup>().eq("groupId", imGroupUser.getGroupId()));
            rongyunUtils.specifyGroup(imGroupUser.getGroupId(), imGroupUser.getUserId(), imGroup.getGroupName());
            //保存客户信息
            String[] split = imGroupUser.getUserId().split(",");
            ArrayList<ImGroupUser> list = new ArrayList<>();
            //遍历用户id  根据群组id以及用户id去查是否存在
            for (String userid : split) {
                //如若没有侧开始封装数据
                ImGroupUser imGroupUsers = new ImGroupUser();
                //成功便开始保存数据
                imGroupUsers.setUserId(userid);
                imGroupUsers.setGroupId(imGroupUser.getGroupId());
                list.add(imGroupUsers);
            }
            //批量插入数据
            if (imGroupUserService.saveBatch(list)) {
                return R.ok(imGroupUser.getId());
            }
        }
        return R.ok(null);
    }

    /**
     * @Description:
     * @Param:
     * @return:
     * @Author: 退出群聊
     * @Date:
     */
    @PostMapping(value = "/signout")
    public R<Long> signOutGroup(@RequestBody ImGroupUser imGroupUser) {
        ImGroup imGroup = imGroupService.getOne(new QueryWrapper<ImGroup>().eq("groupId", imGroupUser.getGroupId()));
        String createGroupChat = rongyunUtils.signOut(imGroupUser.getUserId(), imGroup.getGroupName(), imGroupUser.getGroupId());
        if (StringUtils.isNotBlank(createGroupChat)) {
            JSONObject jsonStr = JSONObject.parseObject(createGroupChat);
            //校验在融云是否成功
            if (jsonStr.getString("code").equals("200")) {
                //成功便开始保存数据
                if (imGroupUserService.remove(new QueryWrapper<ImGroupUser>().eq("groupId", imGroupUser.getGroupId()).eq("userId", imGroupUser.getUserId()))) {
                    return R.ok(imGroupUser.getId());
                }
            }
        }
        return R.fail(400, "退出退出群聊失败");
    }

    /***
     * 根据群组id查询群组成员
     * @param imGroupUser
     * @return
     */
    @PostMapping(value = "/list")
    public R<List<ImUser>> UserList(@RequestBody ImGroupUser imGroupUser) {
        System.err.println(imGroupUser.getGroupId() + "#############");
        List<ImGroupUser> userIdlist = imGroupUserService.list(new QueryWrapper<ImGroupUser>().eq("groupId", imGroupUser.getGroupId()).orderByDesc("id"));
        System.err.println(userIdlist + "@@@");
        //结果集
        List<String> resultList = new ArrayList<>();
        //遍历集合取值
        //封装返回的数据
        List<ImUser> userList = null;
        if (userIdlist != null && userIdlist.size() != 0) {
            userIdlist.forEach(item -> {
                resultList.add(item.getUserId());
            });
            //条件构造器in上手使用
            userList = imUserService.list(new QueryWrapper<ImUser>().in("userId", resultList));
            System.out.println(userList);
            return R.ok(userList);
        }
        return R.ok(null);
    }


    /**
     * @Description:
     * @Param:
     * @return:
     * @Author: 根据群组id查询群组详情
     * @Date:
     */
    @PostMapping(value = "/detail")
    public R<ImGroup> groupUser(@RequestBody ImGroupUser imGroupUser) {
        if (ObjectUtils.isNotEmpty(imGroupUser)) {
            ImGroup imGroup = imGroupService.getOne(new QueryWrapper<ImGroup>().eq("groupId", imGroupUser.getGroupId()));
            return R.ok(imGroup);
        }
        return R.ok(null);
    }

    /**
     * @Description:
     * @Param:
     * @return:
     * @Author: 根据群组id查询群组详情
     * @Date:
     */
    @PostMapping(value = "/queryList")
    public R<List<ImGroup>> queryList(@RequestBody ImGroupUser imGroupUser) {
        if (ObjectUtils.isNotEmpty(imGroupUser)) {
            List<ImGroupUser> imGroupList = imGroupUserService.list(new QueryWrapper<ImGroupUser>().eq("userId", imGroupUser.getUserId()));
            ArrayList<String> list = new ArrayList<>();
            if (imGroupList.size() != 0) {
                imGroupList.forEach(item -> {
                    list.add(item.getGroupId());
                });
                List<ImGroup> imGroups = imGroupService.list(new QueryWrapper<ImGroup>().in("groupId", list));
                return R.ok(imGroups);
            }
        }
        return R.ok(null);
    }


    /**
     * @Description:
     * @Param:
     * @return:
     * @Author: 移除群聊
     * @Date:
     */
    @PostMapping(value = "/removeList")
    public R<Boolean> removeList(@RequestParam(value = "userIds") String userIds, @RequestParam(value = "groupId") String groupId) {
        if (StringUtils.isNotBlank(userIds)) {
            String[] userList = userIds.split(",");
            if (imGroupUserService.remove(new QueryWrapper<ImGroupUser>().in("userId", userList).eq("groupId", groupId))) {
                return R.ok(true);
            }
        }
        return R.ok(false);
    }

}
