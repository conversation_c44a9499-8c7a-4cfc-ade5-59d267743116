package com.ruoyi.system.domain.dto;

import com.ruoyi.system.domain.FactoryEquipment;
import com.ruoyi.system.domain.FactoryQualification;
import com.ruoyi.system.domain.FactoryPersonnel;
import com.ruoyi.system.domain.FactoryPerformance;
import com.ruoyi.system.domain.SettledFactory;

import java.util.List;

/**
 * 入驻工厂详情DTO
 */
public class SettledFactoryDetailDTO extends SettledFactory {
    private static final long serialVersionUID = 1L;

    /** 工厂设备列表 */
    private List<FactoryEquipment> equipmentList;

    /** 工厂资质列表 */
    private List<FactoryQualification> qualificationList;

    /** 工厂人员列表 */
    private List<FactoryPersonnel> personnelList;

    /** 工厂业绩列表 */
    private List<FactoryPerformance> performanceList;

    public List<FactoryEquipment> getEquipmentList() {
        return equipmentList;
    }

    public void setEquipmentList(List<FactoryEquipment> equipmentList) {
        this.equipmentList = equipmentList;
    }

    public List<FactoryQualification> getQualificationList() {
        return qualificationList;
    }

    public void setQualificationList(List<FactoryQualification> qualificationList) {
        this.qualificationList = qualificationList;
    }

    public List<FactoryPersonnel> getPersonnelList() {
        return personnelList;
    }

    public void setPersonnelList(List<FactoryPersonnel> personnelList) {
        this.personnelList = personnelList;
    }

    public List<FactoryPerformance> getPerformanceList() {
        return performanceList;
    }

    public void setPerformanceList(List<FactoryPerformance> performanceList) {
        this.performanceList = performanceList;
    }
} 