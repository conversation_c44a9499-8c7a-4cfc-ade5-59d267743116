package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.Ecology;
import com.ruoyi.portalweb.mapper.EcologyMapper;
import com.ruoyi.portalweb.service.IEcologyService;
import com.ruoyi.portalweb.vo.EcologyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 生态协作Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class EcologyServiceImpl implements IEcologyService
{
    @Autowired
    private EcologyMapper ecologyMapper;

    /**
     * 查询生态协作
     * 
     * @param ecologyId 生态协作主键
     * @return 生态协作
     */
    @Override
    public EcologyVO selectEcologyByEcologyId(Long ecologyId)
    {
        return ecologyMapper.selectEcologyByEcologyId(ecologyId);
    }

    /**
     * 查询生态协作列表
     * 
     * @param ecology 生态协作
     * @return 生态协作
     */
    @Override
    public List<EcologyVO> selectEcologyList(EcologyVO ecology)
    {
        if (StringUtils.isNotEmpty(ecology.getQueryType()) && "my".equals(ecology.getQueryType())) {
            ecology.setMemberId(SecurityUtils.getUserId());
        }
        return ecologyMapper.selectEcologyList(ecology);
    }

    /**
     * 新增生态协作
     * 
     * @param ecology 生态协作
     * @return 结果
     */
    @Override
    public int insertEcology(Ecology ecology)
    {
        ecology.setCreateTime(DateUtils.getNowDate());
        ecology.setMemberId(SecurityUtils.getUserId());
        return ecologyMapper.insertEcology(ecology);
    }

    /**
     * 修改生态协作
     * 
     * @param ecology 生态协作
     * @return 结果
     */
    @Override
    public int updateEcology(Ecology ecology)
    {
        ecology.setUpdateTime(DateUtils.getNowDate());
        return ecologyMapper.updateEcology(ecology);
    }

    /**
     * 批量删除生态协作
     * 
     * @param ecologyIds 需要删除的生态协作主键
     * @return 结果
     */
    @Override
    public int deleteEcologyByEcologyIds(Long[] ecologyIds)
    {
        return ecologyMapper.deleteEcologyByEcologyIds(ecologyIds);
    }

    /**
     * 删除生态协作信息
     * 
     * @param ecologyId 生态协作主键
     * @return 结果
     */
    @Override
    public int deleteEcologyByEcologyId(Long ecologyId)
    {
        return ecologyMapper.deleteEcologyByEcologyId(ecologyId);
    }
}
