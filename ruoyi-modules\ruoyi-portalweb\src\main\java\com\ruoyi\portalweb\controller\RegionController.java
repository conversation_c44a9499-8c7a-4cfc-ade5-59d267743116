package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.portalweb.api.domain.TreeNode;
import com.ruoyi.portalweb.service.IRegionService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 行政区划表Controller
 */
@RestController
@RequestMapping("/region")
public class RegionController extends BaseController {
    @Autowired
    private IRegionService regionService;

    /**
	 * 加载列表
	 */
	@GetMapping("/tree")
	@ApiOperation(value = "加载列表", notes = "查询所有数据")
	public TableDataInfo tree() {
//	    startPage();
//        PageUtils.setDefaultOrderBy();
		List<TreeNode> alTreeNodes = regionService.tree();
		return getDataTable(alTreeNodes);
	}

}
