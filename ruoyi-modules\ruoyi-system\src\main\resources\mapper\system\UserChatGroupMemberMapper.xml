<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserChatGroupMemberMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.UserChatGroupMember">
    <!--@mbg.generated-->
    <!--@Table user_chat_group_member-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="admin_flag" jdbcType="INTEGER" property="adminFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, group_id, user_name, create_time, create_by, delete_flag,
    update_time, update_by, admin_flag
  </sql>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.system.domain.UserChatGroupMember" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into user_chat_group_member
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        group_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>

      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>

      <if test="adminFlag != null">
        admin_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>

      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="adminFlag != null">
        #{adminFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>


  <update id="dismissChatGroupMember">
    update user_chat_group_member
    set
    delete_flag = 0,
    update_time = #{now},
    update_by = #{userName}
    where delete_flag = 1
    and group_id = #{groupId}
  </update>

  <select id="selectByGroupIdAndUserName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from user_chat_group_member
    where delete_flag = 1
    and group_id=#{groupId}
    and user_name = #{userName}
  </select>

  <update id="quitChatGroup">
    update user_chat_group_member
    set
      delete_flag = 0,
      update_time = #{updateTime},
      update_by = #{updateBy}
      where delete_flag = 1
      and group_id = #{groupId}
    and user_name = #{userName}
  </update>

  <select id="getGroupUserList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from user_chat_group_member
    where delete_flag = 1
    and group_id = #{groupId}
    order by create_time asc
  </select>

  <select id="getUserGroupList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from user_chat_group_member
    where delete_flag = 1
    and user_name = #{userName}
    order by create_time desc
    </select>
</mapper>