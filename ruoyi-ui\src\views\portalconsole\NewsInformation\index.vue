<template>
  <!-- 动态资讯 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="资讯板块ID" prop="newsInformationPlateId">
        <el-input
          v-model="queryParams.newsInformationPlateId"
          placeholder="请输入资讯板块ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="方案类型ID" prop="solutionTypeId">
        <el-input
          v-model="queryParams.solutionTypeId"
          placeholder="请输入方案类型ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资讯来源：业务字典" prop="newsInformationSource">
        <el-input
          v-model="queryParams.newsInformationSource"
          placeholder="请输入资讯来源：业务字典"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="发布人" prop="newsInformationAuthor">
        <el-input
          v-model="queryParams.newsInformationAuthor"
          placeholder="请输入发布人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资讯标题" prop="newsInformationName">
        <el-input
          v-model="queryParams.newsInformationName"
          placeholder="请输入资讯标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="浏览次数" prop="newsInformationFrequency">
        <el-input
          v-model="queryParams.newsInformationFrequency"
          placeholder="请输入浏览次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:NewsInformation:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['portalconsole:NewsInformation:edit']"
        >修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:NewsInformation:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:NewsInformation:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="NewsInformationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编码" align="center" prop="newsInformationId" />
      <el-table-column label="资讯板块" align="center" prop="newsInformationPlateId" >
      <template slot-scope="scope">
        <dict-tag :options="dict.type.sys_information" :value="scope.row.newsInformationPlateId" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="方案类型ID" align="center" prop="solutionTypeId" /> -->
      <!-- <el-table-column label="资讯来源：业务字典" align="center" prop="newsInformationSource" /> -->
      <el-table-column label="发布人" align="center" prop="newsInformationAuthor" />
      <el-table-column label="资讯标题" align="center" prop="newsInformationName" />
      <el-table-column label="简介内容" align="center" prop="newsInformationIntroduction" />
      <!-- <el-table-column label="封面" align="center" prop="newsInformationImg" /> -->
      <!-- <el-table-column label="资讯内容" align="center" prop="" >
        <template slot-scope="scope">
          <span>{{ decodeURIComponent(scope.row.newsInformationContent) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="浏览次数" align="center" prop="newsInformationFrequency" />
      <el-table-column label="发布日期" align="center" prop="newsInformationDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.newsInformationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:NewsInformation:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:NewsInformation:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改动态资讯对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-divider content-position="left">基础信息</el-divider>
        <el-row :span="24">
          <el-col :span="10">
            <el-form-item label="资讯板块" prop="newsInformationPlateId">
              <!-- <el-input v-model="form.newsInformationPlateId" placeholder="请输入资讯板块" /> -->
              <!-- <el-cascader :options="newsInformationPlateList" 
              v-model="solutionType" 
              @change="handleChange" 
               clearable
               :show-all-levels="false"
               :props="propsCascader"
              >
              </el-cascader> -->
              <el-select v-model="form.newsInformationPlateId" placeholder="请选择">
                <el-option v-for="dict in dict.type.sys_information" :key="dict.value"
                :label="dict.label"
                :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="资讯来源" prop="newsInformationSource">
              <el-radio-group v-model="form.newsInformationSource">
                <el-radio v-for="dict in dict.type.news_information_source" :key="dict.value" :label="dict.value" >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <!-- 新加的 -->
            <el-form-item>
              <el-input v-model="form.newsInformationAuthor" placeholder="作者或转载媒体" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="资讯标题" prop="newsInformationName">
          <el-input v-model="form.newsInformationName" placeholder="请输入资讯标题" />
        </el-form-item>
        <el-row :span="24">
          <el-col :span="18">
            <el-form-item label="资讯行业">
              <el-select v-model="form.solutionTypeId" placeholder="请选择">
                <el-option v-for="dict in dict.type.news_information_industry" :key="dict.value"
                :label="dict.label"
                :value="dict.value"></el-option>
              </el-select>
              <!-- <el-cascader
                v-model="solutionTypeId"
                :options="solutionTypeList"
                :show-all-levels="false"
                clearable
                :props="propsCascader"
                @change="handleChange"></el-cascader> -->
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="置顶" prop="top">
              <el-select v-model="form.top" placeholder="置顶选项">
                <el-option v-for="dict in dict.type.sys_yesno_option" :key="dict.value"
                :label="dict.label"
                :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="简介内容" prop="newsInformationIntroduction">
          <el-input v-model="form.newsInformationIntroduction" type="textarea" placeholder="请输入简介内容" />
        </el-form-item>
        <el-form-item label="封面图片" prop="newsInformationImg">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :http-request="uploadFun"
            :before-upload="beforeAvatarUpload">
            <el-image
              v-if="form.newsInformationImg"
              :src="form.newsInformationImg"
              class="avatar"
            >
              <div slot="error" class="image-slot">
                <span>暂无图片</span>
              </div>
            </el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">请上传 大小不超过 5MB 格式为 png/jpg/jpeg 的文件</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="资讯内容">
          <editor v-model="form.newsInformationContent" :min-height="192"/>
        </el-form-item>
        <!-- <el-form-item label="方案类型ID" prop="solutionTypeId">
          <el-input v-model="form.solutionTypeId" placeholder="请输入方案类型ID" />
        </el-form-item> -->
        <!-- <el-form-item label="发布人" prop="newsInformationAuthor">
          <el-input v-model="form.newsInformationAuthor" placeholder="请输入发布人" />
        </el-form-item> -->
        <!-- <el-form-item label="浏览次数" prop="newsInformationFrequency">
          <el-input v-model="form.newsInformationFrequency" placeholder="请输入浏览次数" />
        </el-form-item>
        <el-form-item label="发布日期" prop="newsInformationDate">
          <el-date-picker clearable
            v-model="form.newsInformationDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择发布日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNewsInformation, getNewsInformation, delNewsInformation, addNewsInformation, updateNewsInformation ,listSolutionType} from "@/api/portalconsole/NewsInformation";
import { listNewsInformationPlate } from "@/api/portalconsole/NewsInformationPlate";
import {comUpload} from "@/api/portalconsole/uploadApi";
export default {
  name: "NewsInformation",
  dicts: ['sys_yesno_option','news_information_source','sys_information','news_information_industry'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 动态资讯表格数据
      NewsInformationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        newsInformationPlateId: null,
        solutionTypeId: null,
        newsInformationSource: null,
        newsInformationAuthor: null,
        newsInformationName: null,
        newsInformationIntroduction: null,
        newsInformationImg: null,
        newsInformationContent: null,
        newsInformationFrequency: null,
        newsInformationDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        newsInformationName:[
          { required: true, message: "资讯标题不能为空", trigger: "blur" }
        ]
      },
      solutionTypeId:[],
      solutionType: [],
      solutionTypeList: [],
      newsInformationPlateList:[],
      propsCascader:{ value: 'value',label: 'label',children: 'children'}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询动态资讯列表 */
    getList() {
      this.loading = true;
      listNewsInformation(this.queryParams).then(response => {
        this.NewsInformationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        newsInformationId: null,
        newsInformationPlateId: null,
        solutionTypeId: null,
        newsInformationSource: null,
        newsInformationAuthor: null,
        newsInformationName: null,
        newsInformationIntroduction: null,
        newsInformationImg: null,
        newsInformationContent: null,
        newsInformationFrequency: null,
        newsInformationDate: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.solutionTypeId=[]
      this.newsInformationPlateList=[]
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.newsInformationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加动态资讯";
      this.getListSolution()
      this.getListSolutionList()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const newsInformationId = row.newsInformationId || this.ids
      getNewsInformation(newsInformationId).then(response => {
        this.form = response.data;
        if(response.data.newsInformationContent){
          this.form.newsInformationContent=decodeURIComponent(response.data.newsInformationContent)
        }
        
        this.getListSolution()
        this.getListSolutionList()
        setTimeout(() => {
          //资讯行业
          if (response.data.solutionTypeId) {
            this.solutionTypeId=this.getParentsById(this.solutionTypeList,response.data.solutionTypeId)
            
          }
        // 在这里执行你想要延迟执行的函数或代码
        // if (response.data.newsInformationId) {
        //     this.solutionType=this.getParentsById(this.newsInformationPlateList,response.data.newsInformationPlateId)
        //   }
          this.open = true;
          this.title = "修改动态资讯";
      }, 1000); // 设置延迟时间，这里是1秒（1000毫秒）
        
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          //资讯板块
          // if(this.solutionType != null && this.solutionType.length > 0) {
          //   this.form.newsInformationPlateId = this.solutionType.slice(-1)[0]
          // }
          //资讯行业
          if(this.solutionTypeId != null && this.solutionTypeId.length > 0) {
            this.form.solutionTypeId = this.solutionTypeId.slice(-1)[0]
          }
          if(this.form.newsInformationContent){
            this.form.newsInformationContent=encodeURIComponent(this.form.newsInformationContent)
          }
          
          if (this.form.newsInformationId != null) {
            updateNewsInformation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addNewsInformation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const newsInformationIds = row.newsInformationId || this.ids;
      this.$modal.confirm('是否确认删除动态资讯编号为"' + newsInformationIds + '"的数据项？').then(function() {
        return delNewsInformation(newsInformationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/NewsInformation/export', {
        ...this.queryParams
      }, `NewsInformation_${new Date().getTime()}.xlsx`)
    },
    // 图片上传
    uploadFun(params) {
        const file = params.file;
        let form = new FormData();
        form.append("file", file); // 文件对象
        comUpload(form).then(res => {
          let data = res.data;
          this.$set(this.form,'newsInformationImg',data.fileFullPath)  // 图片全路径
          // this.$set(this.form,'imageUrl',data.fileId) // 图片Id
        })
      },
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/jpeg' || 'image/png' || 'image/jpg';
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isJPG) {
          this.$message.error('上传图片只能是 jpg、jpeg、png 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传头像图片大小不能超过 2MB!');
        }
        return isJPG && isLt2M;
      },
      
      //查询资讯行业数据
      getListSolution() {
        let that = this
        let queryParams={
          pageNum: 1,
          pageSize: 50,
        }
        listSolutionType(queryParams).then(response => {
          let arrList=response.rows
          let data= []
          arrList.forEach(item => {
            data.push({
              value:item.solutionTypeId,
              parentId:item.parentId,
              label:item.solutionTypeName
            })
          });
          that.solutionTypeList=that.tranListToTreeData(data,null)
          console.log("that.solutionTypeList",that.solutionTypeList)
        });
      },
      
      //把list整理成树形
      tranListToTreeData(list, parentId=null) {
        const arr = [];
        list.forEach((item) => {
          if (item.parentId === parentId) {
            // 递归调用
            const children =this.tranListToTreeData(list, item.value)
            if (children.length) {
              item.children = children;
            }
            arr.push(item);
          }
        });
        console.log("arr",arr)
        return arr;
      },
      handleChange(value) {
        console.log(value);
      },
      /*
    * el-cascader递归获取父级id
    * @param  list 数据列表
    * @param  id 后端返回的id
    * propsCascader 是el-cascader props属性
    **/
    getParentsById(list, id) {
      for (let i in list) {
        if (list[i][this.propsCascader.value || 'value'] == id) {
          return [list[i][this.propsCascader.value || 'value']]
        }
        if (list[i].children) {
          let node = this.getParentsById(list[i].children, id)
          if (node !== undefined) {
            // 追加父节点
            node.unshift(list[i][this.propsCascader.value || 'value'])
            return node
          }
        }
      }
    },
    /** 查询资讯板块列表 */
    getListSolutionList() {
      let that = this
      let queryParams = {
        pageNum: 1,
        pageSize: 100,
      }
      listNewsInformationPlate(queryParams).then(response => {
        console.log("response.rows",response.rows)
        let arrList = response.rows
        let data = []
        arrList.forEach(item => {
          data.push({
            value: item.newsInformationPlateId,
            parentId: item.parentId,
            label: item.newsInformationPlateName
          })
        });
        that.newsInformationPlateList = that.tranListToTreeData(data, null)
      });
    },
    
  }
};
</script>
<style  scoped>
>>>.el-table .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
    margin-left: 5px;
}
.avatar-uploader >>> .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
