package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.TestingRequirement;
import com.ruoyi.system.service.ITestingRequirementService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 检测需求Controller
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@RestController
@RequestMapping("/testingRequirement")
public class TestingRequirementController extends BaseController
{
    @Autowired
    private ITestingRequirementService testingRequirementService;

    /**
     * 查询检测需求列表
     */

    @GetMapping("/list")
    public TableDataInfo list(TestingRequirement testingRequirement)
    {
        startPage();
        List<TestingRequirement> list = testingRequirementService.selectTestingRequirementList(testingRequirement);
        return getDataTable(list);
    }

    /**
     * 导出检测需求列表
     */

    @Log(title = "检测需求", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TestingRequirement testingRequirement)
    {
        List<TestingRequirement> list = testingRequirementService.selectTestingRequirementList(testingRequirement);
        ExcelUtil<TestingRequirement> util = new ExcelUtil<TestingRequirement>(TestingRequirement.class);
        util.exportExcel(response, list, "检测需求数据");
    }

    /**
     * 获取检测需求详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(testingRequirementService.selectTestingRequirementById(id));
    }

    /**
     * 新增检测需求
     */

    @Log(title = "检测需求", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TestingRequirement testingRequirement)
    {
        return toAjax(testingRequirementService.insertTestingRequirement(testingRequirement));
    }

    /**
     * 修改检测需求
     */

    @Log(title = "检测需求", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TestingRequirement testingRequirement)
    {
        return toAjax(testingRequirementService.updateTestingRequirement(testingRequirement));
    }

    /**
     * 删除检测需求
     */

    @Log(title = "检测需求", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(testingRequirementService.deleteTestingRequirementByIds(ids));
    }
}
