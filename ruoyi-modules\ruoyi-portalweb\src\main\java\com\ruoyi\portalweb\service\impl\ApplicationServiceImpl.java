package com.ruoyi.portalweb.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.portalweb.api.domain.Application;
import com.ruoyi.portalweb.mapper.ApplicationMapper;
import com.ruoyi.portalweb.service.IApplicationService;

/**
 * 系统对接Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
public class ApplicationServiceImpl implements IApplicationService 
{
    @Autowired
    private ApplicationMapper applicationMapper;

    /**
     * 查询系统对接
     * 
     * @param applicationId 系统对接主键
     * @return 系统对接
     */
    @Override
    public Application selectApplicationByApplicationId(Long applicationId)
    {
        return applicationMapper.selectApplicationByApplicationId(applicationId);
    }

    /**
     * 查询系统对接列表
     * 
     * @param application 系统对接
     * @return 系统对接
     */
    @Override
    public List<Application> selectApplicationList(Application application)
    {
        return applicationMapper.selectApplicationList(application);
    }

    /**
     * 新增系统对接
     * 
     * @param application 系统对接
     * @return 结果
     */
    @Override
    public int insertApplication(Application application)
    {
        application.setCreateTime(DateUtils.getNowDate());
        return applicationMapper.insertApplication(application);
    }

    /**
     * 修改系统对接
     * 
     * @param application 系统对接
     * @return 结果
     */
    @Override
    public int updateApplication(Application application)
    {
        application.setUpdateTime(DateUtils.getNowDate());
        return applicationMapper.updateApplication(application);
    }

    /**
     * 批量删除系统对接
     * 
     * @param applicationIds 需要删除的系统对接主键
     * @return 结果
     */
    @Override
    public int deleteApplicationByApplicationIds(Long[] applicationIds)
    {
        return applicationMapper.deleteApplicationByApplicationIds(applicationIds);
    }

    /**
     * 删除系统对接信息
     * 
     * @param applicationId 系统对接主键
     * @return 结果
     */
    @Override
    public int deleteApplicationByApplicationId(Long applicationId)
    {
        return applicationMapper.deleteApplicationByApplicationId(applicationId);
    }

	@Override
	public Application selectApplicationByAppId(String appId) {
		return applicationMapper.selectApplicationByAppId(appId);
    }

	@Override
	public List<Application> selectAllApplicationList() {
		return applicationMapper.selectApplicationList(new Application());
	}
}
