<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SettleProcessMapper">
    
    <resultMap type="SettleProcess" id="SettleProcessResult">
        <result property="id"    column="id"    />
        <result property="companyName"    column="company_name"    />
        <result property="socialCreditCode"    column="social_credit_code"    />
        <result property="registeredCapital"    column="registered_capital"    />
        <result property="industry"    column="industry"    />
        <result property="address"    column="address"    />
        <result property="businessScope"    column="business_scope"    />
        <result property="projectDescription"    column="project_description"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="attachments"    column="attachments"    />
        <result property="currentStep"    column="current_step"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditComment"    column="audit_comment"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditor"    column="auditor"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSettleProcessVo">
        select id, company_name, social_credit_code, registered_capital, industry, address, business_scope, project_description, contact_name, contact_phone, attachments, current_step, audit_status, audit_comment, audit_time, auditor, create_time, update_time from settle_process
    </sql>

    <select id="selectSettleProcessList" parameterType="SettleProcess" resultMap="SettleProcessResult">
        <include refid="selectSettleProcessVo"/>
        <where>  
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="socialCreditCode != null  and socialCreditCode != ''"> and social_credit_code = #{socialCreditCode}</if>
            <if test="registeredCapital != null  and registeredCapital != ''"> and registered_capital = #{registeredCapital}</if>
            <if test="industry != null  and industry != ''"> and industry = #{industry}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="businessScope != null  and businessScope != ''"> and business_scope = #{businessScope}</if>
            <if test="projectDescription != null  and projectDescription != ''"> and project_description = #{projectDescription}</if>
            <if test="contactName != null  and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="attachments != null  and attachments != ''"> and attachments = #{attachments}</if>
            <if test="currentStep != null "> and current_step = #{currentStep}</if>
            <if test="auditStatus != null "> and audit_status = #{auditStatus}</if>
            <if test="auditComment != null  and auditComment != ''"> and audit_comment = #{auditComment}</if>
            <if test="auditTime != null "> and audit_time = #{auditTime}</if>
            <if test="auditor != null  and auditor != ''"> and auditor = #{auditor}</if>
        </where>
    </select>
    
    <select id="selectSettleProcessById" parameterType="Long" resultMap="SettleProcessResult">
        <include refid="selectSettleProcessVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSettleProcess" parameterType="SettleProcess" useGeneratedKeys="true" keyProperty="id">
        insert into settle_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="socialCreditCode != null and socialCreditCode != ''">social_credit_code,</if>
            <if test="registeredCapital != null">registered_capital,</if>
            <if test="industry != null and industry != ''">industry,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="businessScope != null">business_scope,</if>
            <if test="projectDescription != null">project_description,</if>
            <if test="contactName != null and contactName != ''">contact_name,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="attachments != null">attachments,</if>
            <if test="currentStep != null">current_step,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditComment != null">audit_comment,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditor != null">auditor,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="socialCreditCode != null and socialCreditCode != ''">#{socialCreditCode},</if>
            <if test="registeredCapital != null">#{registeredCapital},</if>
            <if test="industry != null and industry != ''">#{industry},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="businessScope != null">#{businessScope},</if>
            <if test="projectDescription != null">#{projectDescription},</if>
            <if test="contactName != null and contactName != ''">#{contactName},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="currentStep != null">#{currentStep},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditComment != null">#{auditComment},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditor != null">#{auditor},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSettleProcess" parameterType="SettleProcess">
        update settle_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="socialCreditCode != null and socialCreditCode != ''">social_credit_code = #{socialCreditCode},</if>
            <if test="registeredCapital != null">registered_capital = #{registeredCapital},</if>
            <if test="industry != null and industry != ''">industry = #{industry},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="businessScope != null">business_scope = #{businessScope},</if>
            <if test="projectDescription != null">project_description = #{projectDescription},</if>
            <if test="contactName != null and contactName != ''">contact_name = #{contactName},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditComment != null">audit_comment = #{auditComment},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditor != null">auditor = #{auditor},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSettleProcessById" parameterType="Long">
        delete from settle_process where id = #{id}
    </delete>

    <delete id="deleteSettleProcessByIds" parameterType="String">
        delete from settle_process where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>