package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.FactoryPersonnel;

/**
 * 工厂人员能力Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface FactoryPersonnelMapper 
{
    /**
     * 查询工厂人员能力
     * 
     * @param id 工厂人员能力主键
     * @return 工厂人员能力
     */
    public FactoryPersonnel selectFactoryPersonnelById(Long id);

    /**
     * 查询工厂人员能力列表
     * 
     * @param factoryPersonnel 工厂人员能力
     * @return 工厂人员能力集合
     */
    public List<FactoryPersonnel> selectFactoryPersonnelList(FactoryPersonnel factoryPersonnel);

    /**
     * 新增工厂人员能力
     * 
     * @param factoryPersonnel 工厂人员能力
     * @return 结果
     */
    public int insertFactoryPersonnel(FactoryPersonnel factoryPersonnel);

    /**
     * 修改工厂人员能力
     * 
     * @param factoryPersonnel 工厂人员能力
     * @return 结果
     */
    public int updateFactoryPersonnel(FactoryPersonnel factoryPersonnel);

    /**
     * 删除工厂人员能力
     * 
     * @param id 工厂人员能力主键
     * @return 结果
     */
    public int deleteFactoryPersonnelById(Long id);

    /**
     * 批量删除工厂人员能力
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFactoryPersonnelByIds(Long[] ids);
}
