package com.ruoyi.im.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName(value = "im_group_user")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImGroupUser extends Model<ImGroupUser> {

        @TableId(value = "id", type = IdType.AUTO)
        private Long id;//自增

        private String groupId;//群组ID

        private String userId;//用户ID

        private Date create_time;//创建时间
}
