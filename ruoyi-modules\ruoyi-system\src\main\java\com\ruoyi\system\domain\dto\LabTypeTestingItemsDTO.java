package com.ruoyi.system.domain.dto;

import com.ruoyi.system.domain.LaboratoryInfo;
import com.ruoyi.system.domain.TestingItem;

import java.util.List;

/**
 * 实验室类型与检测项目数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-03-08
 */
public class LabTypeTestingItemsDTO {
    
    /** 实验室类型 */
    private String labType;
    
    /** 实验室列表 */
    private List<LaboratoryInfo> laboratories;
    
    /** 检测项目列表 */
    private List<TestingItem> testingItems;

    public String getLabType() {
        return labType;
    }

    public void setLabType(String labType) {
        this.labType = labType;
    }

    public List<LaboratoryInfo> getLaboratories() {
        return laboratories;
    }

    public void setLaboratories(List<LaboratoryInfo> laboratories) {
        this.laboratories = laboratories;
    }

    public List<TestingItem> getTestingItems() {
        return testingItems;
    }

    public void setTestingItems(List<TestingItem> testingItems) {
        this.testingItems = testingItems;
    }
}
