<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysTechRequirementMapper">
    
    <resultMap type="SysTechRequirement" id="SysTechRequirementResult">
        <result property="requirementId"    column="requirement_id"    />
        <result property="techRequirement"    column="tech_requirement"    />
        <result property="publisherInvestment"    column="publisher_investment"    />
        <result property="plannedBudget"    column="planned_budget"    />
        <result property="publisherCompany"    column="publisher_company"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="deadline"    column="deadline"    />
        <result property="requirementTitle"    column="requirement_title"    />
        <result property="requirementCategory"    column="requirement_category"    />
        <result property="priorityLevel"    column="priority_level"    />
        <result property="requirementStatus"    column="requirement_status"    />
        <result property="attachmentUrl"    column="attachment_url"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysTechRequirementVo">
        select requirement_id, tech_requirement, publisher_investment, planned_budget, publisher_company, contact_person, contact_phone, deadline, requirement_title, requirement_category, priority_level, requirement_status, attachment_url, create_by, create_time, update_by, update_time, remark from sys_tech_requirement
    </sql>

    <select id="selectSysTechRequirementList" parameterType="SysTechRequirement" resultMap="SysTechRequirementResult">
        <include refid="selectSysTechRequirementVo"/>
        <where>  
            <if test="techRequirement != null  and techRequirement != ''"> and tech_requirement = #{techRequirement}</if>
            <if test="publisherInvestment != null "> and publisher_investment = #{publisherInvestment}</if>
            <if test="plannedBudget != null "> and planned_budget = #{plannedBudget}</if>
            <if test="publisherCompany != null  and publisherCompany != ''"> and publisher_company = #{publisherCompany}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person = #{contactPerson}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="deadline != null "> and deadline = #{deadline}</if>
            <if test="requirementTitle != null  and requirementTitle != ''"> and requirement_title = #{requirementTitle}</if>
            <if test="requirementCategory != null  and requirementCategory != ''"> and requirement_category = #{requirementCategory}</if>
            <if test="priorityLevel != null  and priorityLevel != ''"> and priority_level = #{priorityLevel}</if>
            <if test="requirementStatus != null  and requirementStatus != ''"> and requirement_status = #{requirementStatus}</if>
            <if test="attachmentUrl != null  and attachmentUrl != ''"> and attachment_url = #{attachmentUrl}</if>
        </where>
    </select>
    
    <select id="selectSysTechRequirementByRequirementId" parameterType="Long" resultMap="SysTechRequirementResult">
        <include refid="selectSysTechRequirementVo"/>
        where requirement_id = #{requirementId}
    </select>
        
    <insert id="insertSysTechRequirement" parameterType="SysTechRequirement" useGeneratedKeys="true" keyProperty="requirementId">
        insert into sys_tech_requirement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="techRequirement != null and techRequirement != ''">tech_requirement,</if>
            <if test="publisherInvestment != null">publisher_investment,</if>
            <if test="plannedBudget != null">planned_budget,</if>
            <if test="publisherCompany != null and publisherCompany != ''">publisher_company,</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="deadline != null">deadline,</if>
            <if test="requirementTitle != null">requirement_title,</if>
            <if test="requirementCategory != null">requirement_category,</if>
            <if test="priorityLevel != null">priority_level,</if>
            <if test="requirementStatus != null">requirement_status,</if>
            <if test="attachmentUrl != null">attachment_url,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="techRequirement != null and techRequirement != ''">#{techRequirement},</if>
            <if test="publisherInvestment != null">#{publisherInvestment},</if>
            <if test="plannedBudget != null">#{plannedBudget},</if>
            <if test="publisherCompany != null and publisherCompany != ''">#{publisherCompany},</if>
            <if test="contactPerson != null and contactPerson != ''">#{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="deadline != null">#{deadline},</if>
            <if test="requirementTitle != null">#{requirementTitle},</if>
            <if test="requirementCategory != null">#{requirementCategory},</if>
            <if test="priorityLevel != null">#{priorityLevel},</if>
            <if test="requirementStatus != null">#{requirementStatus},</if>
            <if test="attachmentUrl != null">#{attachmentUrl},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysTechRequirement" parameterType="SysTechRequirement">
        update sys_tech_requirement
        <trim prefix="SET" suffixOverrides=",">
            <if test="techRequirement != null and techRequirement != ''">tech_requirement = #{techRequirement},</if>
            <if test="publisherInvestment != null">publisher_investment = #{publisherInvestment},</if>
            <if test="plannedBudget != null">planned_budget = #{plannedBudget},</if>
            <if test="publisherCompany != null and publisherCompany != ''">publisher_company = #{publisherCompany},</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="deadline != null">deadline = #{deadline},</if>
            <if test="requirementTitle != null">requirement_title = #{requirementTitle},</if>
            <if test="requirementCategory != null">requirement_category = #{requirementCategory},</if>
            <if test="priorityLevel != null">priority_level = #{priorityLevel},</if>
            <if test="requirementStatus != null">requirement_status = #{requirementStatus},</if>
            <if test="attachmentUrl != null">attachment_url = #{attachmentUrl},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where requirement_id = #{requirementId}
    </update>

    <delete id="deleteSysTechRequirementByRequirementId" parameterType="Long">
        delete from sys_tech_requirement where requirement_id = #{requirementId}
    </delete>

    <delete id="deleteSysTechRequirementByRequirementIds" parameterType="String">
        delete from sys_tech_requirement where requirement_id in 
        <foreach item="requirementId" collection="array" open="(" separator="," close=")">
            #{requirementId}
        </foreach>
    </delete>
</mapper>