package com.ruoyi.portalconsole.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.IntentionApply;
import com.ruoyi.portalconsole.service.IIntentionApplyService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 意向申请Controller
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
@RestController
@RequestMapping("/IntentionApply")
public class IntentionApplyController extends BaseController
{
    @Autowired
    private IIntentionApplyService intentionApplyService;

    /**
     * 查询意向申请列表
     */
    @RequiresPermissions("portalconsole:IntentionApply:list")
    @GetMapping("/list")
    public TableDataInfo list(IntentionApply intentionApply)
    {
        startPage();
        List<IntentionApply> list = intentionApplyService.selectIntentionApplyList(intentionApply);
        return getDataTable(list);
    }

    /**
     * 导出意向申请列表
     */
    @RequiresPermissions("portalconsole:IntentionApply:export")
    @Log(title = "意向申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IntentionApply intentionApply)
    {
        List<IntentionApply> list = intentionApplyService.selectIntentionApplyList(intentionApply);
        ExcelUtil<IntentionApply> util = new ExcelUtil<IntentionApply>(IntentionApply.class);
        util.exportExcel(response, list, "意向申请数据");
    }

    /**
     * 获取意向申请详细信息
     */
    @RequiresPermissions("portalconsole:IntentionApply:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(intentionApplyService.selectIntentionApplyById(id));
    }

    /**
     * 新增意向申请
     */
    @RequiresPermissions("portalconsole:IntentionApply:add")
    @Log(title = "意向申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IntentionApply intentionApply)
    {
        return toAjax(intentionApplyService.insertIntentionApply(intentionApply));
    }

    /**
     * 修改意向申请
     */
    @RequiresPermissions("portalconsole:IntentionApply:edit")
    @Log(title = "意向申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IntentionApply intentionApply)
    {
        return toAjax(intentionApplyService.updateIntentionApply(intentionApply));
    }

    /**
     * 删除意向申请
     */
    @RequiresPermissions("portalconsole:IntentionApply:remove")
    @Log(title = "意向申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(intentionApplyService.deleteIntentionApplyByIds(ids));
    }
}
