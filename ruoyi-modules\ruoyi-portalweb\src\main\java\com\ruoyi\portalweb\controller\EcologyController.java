package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.Ecology;
import com.ruoyi.portalweb.service.IEcologyService;
import com.ruoyi.portalweb.service.IMemberService;
import com.ruoyi.portalweb.vo.EcologyVO;
import com.ruoyi.portalweb.vo.MemberVO;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 生态协作Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/Ecology")
public class EcologyController extends BaseController
{
    @Autowired
    private IEcologyService ecologyService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private IMemberService memberService;

    /**
     * 查询生态协作列表
     */
    @GetMapping("/list")
    public TableDataInfo list(EcologyVO ecology)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<EcologyVO> list = ecologyService.selectEcologyList(ecology);
        return getDataTable(list);
    }

    /**
     * 导出生态协作列表
     */
    @Log(title = "生态协作", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EcologyVO ecology)
    {
        List<EcologyVO> list = ecologyService.selectEcologyList(ecology);
        ExcelUtil<EcologyVO> util = new ExcelUtil<>(EcologyVO.class);
        util.exportExcel(response, list, "生态协作数据");
    }

    /**
     * 获取生态协作详细信息
     */
    @GetMapping(value = "/{ecologyId}")
    public AjaxResult getInfo(@PathVariable("ecologyId") Long ecologyId)
    {
        return success(ecologyService.selectEcologyByEcologyId(ecologyId));
    }

    /**
     * 新增生态协作
     */
    @Log(title = "生态协作", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Ecology ecology)
    {
        // 获取用户信息存入数据
        MemberVO memberVO = memberService.selectMemberByMemberId(SecurityUtils.getUserId());
        ecology.setCreateBy(memberVO.getMemberRealName());
        ecology.setUpdateBy(memberVO.getMemberRealName());
        ecology.setCompanyId(memberVO.getMemberCompanyId().toString());
        return toAjax(ecologyService.insertEcology(ecology));
    }

    /**
     * 修改生态协作
     */
    @Log(title = "生态协作", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Ecology ecology)
    {

        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        ecology.setUpdateBy(userNickName.getData());
        return toAjax(ecologyService.updateEcology(ecology));
    }

    /**
     * 删除生态协作
     */
    @Log(title = "生态协作", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ecologyIds}")
    public AjaxResult remove(@PathVariable Long[] ecologyIds)
    {
        return toAjax(ecologyService.deleteEcologyByEcologyIds(ecologyIds));
    }
}
