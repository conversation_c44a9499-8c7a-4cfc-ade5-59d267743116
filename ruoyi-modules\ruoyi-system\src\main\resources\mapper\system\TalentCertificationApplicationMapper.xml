<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TalentCertificationApplicationMapper">
    
    <resultMap type="TalentCertificationApplication" id="TalentCertificationApplicationResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="idCard"    column="id_card"    />
        <result property="education"    column="education"    />
        <result property="contact"    column="contact"    />
        <result property="certificationType1"    column="certification_type1"    />
        <result property="skillLevel"    column="skill_level"    />
        <result property="certificationType2"    column="certification_type2"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTalentCertificationApplicationVo">
        select id, name, id_card, education, contact, certification_type1, skill_level, certification_type2, create_time, update_time, status, remark from talent_certification_application
    </sql>

    <select id="selectTalentCertificationApplicationList" parameterType="TalentCertificationApplication" resultMap="TalentCertificationApplicationResult">
        <include refid="selectTalentCertificationApplicationVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="education != null  and education != ''"> and education = #{education}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="certificationType1 != null  and certificationType1 != ''"> and certification_type1 = #{certificationType1}</if>
            <if test="skillLevel != null  and skillLevel != ''"> and skill_level = #{skillLevel}</if>
            <if test="certificationType2 != null  and certificationType2 != ''"> and certification_type2 = #{certificationType2}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectTalentCertificationApplicationById" parameterType="Long" resultMap="TalentCertificationApplicationResult">
        <include refid="selectTalentCertificationApplicationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTalentCertificationApplication" parameterType="TalentCertificationApplication" useGeneratedKeys="true" keyProperty="id">
        insert into talent_certification_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="education != null">education,</if>
            <if test="contact != null and contact != ''">contact,</if>
            <if test="certificationType1 != null">certification_type1,</if>
            <if test="skillLevel != null">skill_level,</if>
            <if test="certificationType2 != null">certification_type2,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="education != null">#{education},</if>
            <if test="contact != null and contact != ''">#{contact},</if>
            <if test="certificationType1 != null">#{certificationType1},</if>
            <if test="skillLevel != null">#{skillLevel},</if>
            <if test="certificationType2 != null">#{certificationType2},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTalentCertificationApplication" parameterType="TalentCertificationApplication">
        update talent_certification_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="education != null">education = #{education},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
            <if test="certificationType1 != null">certification_type1 = #{certificationType1},</if>
            <if test="skillLevel != null">skill_level = #{skillLevel},</if>
            <if test="certificationType2 != null">certification_type2 = #{certificationType2},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTalentCertificationApplicationById" parameterType="Long">
        delete from talent_certification_application where id = #{id}
    </delete>

    <delete id="deleteTalentCertificationApplicationByIds" parameterType="String">
        delete from talent_certification_application where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>