<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="430" height="320" viewBox="0 0 430 320">
  <defs>
    <clipPath id="clip-path">
      <rect id="矩形_21913" data-name="矩形 21913" width="430" height="320" transform="translate(448 1381)" fill="#fff" stroke="#707070" stroke-width="1"/>
    </clipPath>
    <filter id="矩形_21360" x="111.772" y="48.139" width="243" height="172" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="矩形_21361" x="164.772" y="69.139" width="242" height="172" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="矩形_21366" x="74.913" y="92.78" width="159" height="68" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-3"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-3"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="矩形_21371" x="261.772" y="95.139" width="129" height="129" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-4"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-4"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="矩形_21370" x="143.772" y="151.139" width="128" height="47" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-5"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-5"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="矩形_21368" x="81.913" y="105.769" width="81.5" height="26" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-6"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-6"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="矩形_21369" x="81.913" y="118.769" width="39" height="26" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-7"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-7"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient" y1="-0.04" x2="0.919" y2="1.452" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#eaf2fe"/>
      <stop offset="1" stop-color="#428afa" stop-opacity="0"/>
    </linearGradient>
    <filter id="路径_8362" x="311.45" y="145.117" width="31.1" height="27.168" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-8"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-8"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="蒙版组_101" data-name="蒙版组 101" transform="translate(-448 -1381)" clip-path="url(#clip-path)">
    <g id="组_21785" data-name="组 21785">
      <g id="组_21695" data-name="组 21695" transform="translate(-62.228 -27.861)">
        <path id="路径_8363" data-name="路径 8363" d="M.251,127.582C7.019,274.625,224.2,308.463,218.047,251.245s148.553-51.2,166.522-111.716S345.478,19.771,292.491,3.3s-39.51,33.163-103.361,49.22S-6.517-19.461.251,127.582Z" transform="translate(594.988 1377.586) rotate(19)" fill="#eaf2fe"/>
        <g transform="matrix(1, 0, 0, 1, 510.23, 1408.86)" filter="url(#矩形_21360)">
          <rect id="矩形_21360-2" data-name="矩形 21360" width="225" height="154" rx="6" transform="translate(120.77 54.14)" fill="#428afa"/>
        </g>
        <g transform="matrix(1, 0, 0, 1, 510.23, 1408.86)" filter="url(#矩形_21361)">
          <rect id="矩形_21361-2" data-name="矩形 21361" width="224" height="154" rx="6" transform="translate(173.77 75.14)" fill="#f6faff"/>
        </g>
        <g transform="matrix(1, 0, 0, 1, 510.23, 1408.86)" filter="url(#矩形_21366)">
          <rect id="矩形_21366-2" data-name="矩形 21366" width="141" height="50" rx="6" transform="translate(83.91 98.78)" fill="#fff"/>
        </g>
        <g transform="matrix(1, 0, 0, 1, 510.23, 1408.86)" filter="url(#矩形_21371)">
          <rect id="矩形_21371-2" data-name="矩形 21371" width="111" height="111" rx="6" transform="translate(270.77 101.14)" fill="#fff"/>
        </g>
        <g transform="matrix(1, 0, 0, 1, 510.23, 1408.86)" filter="url(#矩形_21370)">
          <g id="矩形_21370-2" data-name="矩形 21370" transform="translate(152.77 157.14)" fill="#f6faff" stroke="#fff" stroke-width="1">
            <rect width="110" height="29" rx="6" stroke="none"/>
            <rect x="0.5" y="0.5" width="109" height="28" rx="5.5" fill="none"/>
          </g>
        </g>
        <rect id="矩形_21362" data-name="矩形 21362" width="59" height="3" rx="1.5" transform="translate(717 1471)" fill="#8ab8ff"/>
        <rect id="矩形_21363" data-name="矩形 21363" width="8" height="7" rx="3.5" transform="translate(640 1471)" fill="#8ab8ff"/>
        <rect id="矩形_21364" data-name="矩形 21364" width="8" height="7" rx="3.5" transform="translate(652 1471)" fill="#ffd800"/>
        <rect id="矩形_21365" data-name="矩形 21365" width="8" height="7" rx="3.5" transform="translate(663 1471)" fill="#7abf65"/>
        <path id="矩形_21367" data-name="矩形 21367" d="M0,0H45a6,6,0,0,1,6,6V44a6,6,0,0,1-6,6H0a0,0,0,0,1,0,0V0A0,0,0,0,1,0,0Z" transform="translate(684.141 1507.642)" fill="#428afa"/>
        <g transform="matrix(1, 0, 0, 1, 510.23, 1408.86)" filter="url(#矩形_21368)">
          <rect id="矩形_21368-2" data-name="矩形 21368" width="63.5" height="8" rx="4" transform="translate(90.91 111.77)" fill="#f6faff"/>
        </g>
        <g transform="matrix(1, 0, 0, 1, 510.23, 1408.86)" filter="url(#矩形_21369)">
          <rect id="矩形_21369-2" data-name="矩形 21369" width="21" height="8" rx="4" transform="translate(90.91 124.77)" fill="#f6faff"/>
        </g>
        <rect id="矩形_21372" data-name="矩形 21372" width="60" height="4" rx="2" transform="translate(766 1495)" fill="#fff"/>
        <circle id="椭圆_1688" data-name="椭圆 1688" cx="22.5" cy="22.5" r="22.5" transform="translate(814 1543)" fill="#eaf2fe"/>
        <path id="盾牌" d="M174.194,96.23l-.344-.133c-11.043-4.268-12.32-9.848-12.32-13.673V67.814l.986.034c7.676.261,10.945-3.2,10.976-3.237l.7-.769.7.769c.026.028,3.124,3.251,10.156,3.251h0c.267,0,.541,0,.818-.014l.986-.034v14.61c0,3.825-1.277,9.4-12.32,13.673Z" transform="translate(662.607 1485.143)" fill="#428afa"/>
        <circle id="椭圆_1689" data-name="椭圆 1689" cx="41" cy="41" r="41" transform="translate(795 1524)" fill="none" stroke="#eaf2fe" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4 4"/>
        <path id="盾牌-2" data-name="盾牌" d="M171.551,89.47l-.272-.105c-8.738-3.377-9.749-7.792-9.749-10.819V66.984l.78.027c6.074.207,8.66-2.535,8.685-2.561l.557-.609.556.609c.02.022,2.472,2.572,8.036,2.572h0c.212,0,.428,0,.647-.011l.78-.027V78.545c0,3.027-1.01,7.442-9.749,10.819Z" transform="translate(665.25 1488.522)" stroke="rgba(255,255,255,0.07)" stroke-width="1" fill="url(#linear-gradient)"/>
        <g transform="matrix(1, 0, 0, 1, 510.23, 1408.86)" filter="url(#路径_8362)">
          <path id="路径_8362-2" data-name="路径 8362" d="M-5755.391,13804.1l4.381,3.072,5.915-6.757" transform="translate(6077.23 -13647.89)" fill="none" stroke="#ffd800" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
        </g>
        <circle id="椭圆_1690" data-name="椭圆 1690" cx="9.5" cy="9.5" r="9.5" transform="translate(826 1515)" fill="#508bfb"/>
        <ellipse id="椭圆_1691" data-name="椭圆 1691" cx="9.5" cy="9" rx="9.5" ry="9" transform="translate(826 1598)" fill="#78aafc"/>
        <ellipse id="椭圆_1692" data-name="椭圆 1692" cx="9.5" cy="9.5" rx="9.5" ry="9.5" transform="translate(861.256 1535.663)" fill="#ff787a"/>
        <ellipse id="椭圆_1693" data-name="椭圆 1693" cx="9.5" cy="9.5" rx="9.5" ry="9.5" transform="translate(808.951 1576.831) rotate(90)" fill="#ffb6b5"/>
        <ellipse id="椭圆_1694" data-name="椭圆 1694" cx="9.5" cy="9.5" rx="9.5" ry="9.5" transform="translate(880.257 1576.831) rotate(90)" fill="#ffb517"/>
        <ellipse id="椭圆_1695" data-name="椭圆 1695" cx="9.5" cy="9.5" rx="9.5" ry="9.5" transform="translate(808.951 1535.663) rotate(90)" fill="#79cb73"/>
        <g id="_040-rocket" data-name="040-rocket" transform="translate(699.229 1499)">
          <path id="路径_8364" data-name="路径 8364" d="M52.109,724.035a2.049,2.049,0,0,1-2.156-2.162c0-2.526,3.684-7.433,4.552-8.294a1.394,1.394,0,0,1,1.968,0l3.932,3.932a1.4,1.4,0,0,1,0,1.968c-.861.867-5.768,4.547-8.294,4.552h-.006Z" transform="translate(-47.874 -683.485)" fill="#ffde33"/>
          <path id="路径_8365" data-name="路径 8365" d="M65.849,778.7h.006c2.526-.005,7.434-3.684,8.294-4.552a1.4,1.4,0,0,0,0-1.968l-1.966-1.966-7.889,7.888a2.126,2.126,0,0,0,1.559.6Z" transform="translate(-61.614 -738.155)" fill="#ffbc33"/>
          <path id="路径_8366" data-name="路径 8366" d="M1.4,302.228a1.394,1.394,0,0,1-1.261-1.989,17.991,17.991,0,0,1,3.525-5.017,17.377,17.377,0,0,1,11.85-5.2,1.344,1.344,0,0,1,1.284.778,1.394,1.394,0,0,1-.167,1.492,30.01,30.01,0,0,0-4.557,7.782,1.394,1.394,0,0,1-1.562.853,13.868,13.868,0,0,0-8.514,1.164,1.386,1.386,0,0,1-.6.135Z" transform="translate(-0.002 -277.948)" fill="#70a8ff"/>
          <path id="路径_8367" data-name="路径 8367" d="M442.152,634.12a1.395,1.395,0,0,1-1.258-2,13.784,13.784,0,0,0,1.163-8.511,1.394,1.394,0,0,1,.853-1.562,30.008,30.008,0,0,0,7.782-4.557,1.394,1.394,0,0,1,2.271,1.117,17.373,17.373,0,0,1-5.194,11.845,18,18,0,0,1-5.021,3.53,1.391,1.391,0,0,1-.595.133Z" transform="translate(-422.408 -591.49)" fill="#196df1"/>
          <path id="路径_8368" data-name="路径 8368" d="M591.473,18.577a1.4,1.4,0,0,1-.986-.409L578.5,6.177a1.394,1.394,0,0,1,.321-2.212A33.1,33.1,0,0,1,592.08.027a4.241,4.241,0,0,1,4.557,4.56,33.088,33.088,0,0,1-3.938,13.26,1.394,1.394,0,0,1-1.226.73Z" transform="translate(-554.021 -0.014)" fill="#70a8ff"/>
          <path id="路径_8369" data-name="路径 8369" d="M737.918,46.775a1.394,1.394,0,0,0,2.212-.321,33.087,33.087,0,0,0,3.938-13.26,4.225,4.225,0,0,0-1.229-3.331L731.922,40.779Z" transform="translate(-701.451 -28.621)" fill="#196df1"/>
          <path id="路径_8370" data-name="路径 8370" d="M114.053,96.764,100.616,83.326A34.086,34.086,0,0,0,89.35,92.451a32.773,32.773,0,0,0-4.978,8.5A32.209,32.209,0,0,0,83.3,104.1l-4.474,2.679a1.394,1.394,0,0,0-.27,2.182l9.863,9.863a1.394,1.394,0,0,0,2.182-.27l2.679-4.474a32.17,32.17,0,0,0,3.147-1.073,32.765,32.765,0,0,0,8.5-4.974A34.1,34.1,0,0,0,114.053,96.764Z" transform="translate(-74.892 -79.858)" fill="#e6eeff"/>
          <path id="路径_8371" data-name="路径 8371" d="M236.982,251.433l-6.719-6.719-23.849,23.849,4.931,4.931a1.394,1.394,0,0,0,2.182-.27l2.679-4.474a32.149,32.149,0,0,0,3.147-1.073,32.765,32.765,0,0,0,8.5-4.974,34.093,34.093,0,0,0,9.132-11.272Z" transform="translate(-197.821 -234.527)" fill="#dae2f2"/>
          <path id="路径_8372" data-name="路径 8372" d="M457.039,315.853a5.539,5.539,0,0,1-3.944-1.631h0a5.582,5.582,0,1,1,3.944,1.632Z" transform="translate(-432.669 -292.019)" fill="#4da6ff"/>
          <path id="路径_8373" data-name="路径 8373" d="M490.66,351.758a5.578,5.578,0,0,0,7.888-7.888l-7.888,7.888Z" transform="translate(-470.233 -329.555)" fill="#4596e6"/>
          <path id="路径_8374" data-name="路径 8374" d="M78.157,586.068a1.4,1.4,0,0,0,.4,1.157l9.863,9.863a1.394,1.394,0,0,0,2.182-.269l2.679-4.474h0L83.3,582.364l-4.474,2.679a1.4,1.4,0,0,0-.668,1.025Z" transform="translate(-74.894 -558.12)" fill="#70a8ff"/>
          <path id="路径_8375" data-name="路径 8375" d="M211.344,711.968a1.394,1.394,0,0,0,2.182-.269l2.679-4.474h0l-4.99-4.99-4.8,4.8,4.931,4.931Z" transform="translate(-197.819 -673)" fill="#196df1"/>
        </g>
        <path id="路径_8376" data-name="路径 8376" d="M-5842.469,13784.635c-3.342,2.076-7.949,2.891-10.6-1.767s-3.373-11.724-3.373-11.724v27.864l30.5.412s-10.753-.893-16.528-5.954C-5844.577,13791.631-5844.535,13788.583-5842.469,13784.635Z" transform="translate(6543.522 -12244.661)" fill="#e6eeff"/>
        <g id="椭圆_1696" data-name="椭圆 1696" transform="translate(833 1427)" fill="#f7fbfe" stroke="#f7fbfe" stroke-width="4">
          <ellipse cx="16" cy="16.5" rx="16" ry="16.5" stroke="none"/>
          <ellipse cx="16" cy="16.5" rx="14" ry="14.5" fill="none"/>
        </g>
        <g id="椭圆_1698" data-name="椭圆 1698" transform="translate(887 1444)" fill="none" stroke="#f7fbfe" stroke-width="4">
          <circle cx="9" cy="9" r="9" stroke="none"/>
          <circle cx="9" cy="9" r="7" fill="none"/>
        </g>
        <g id="椭圆_1699" data-name="椭圆 1699" transform="translate(580 1606)" fill="none" stroke="#f7fbfe" stroke-width="4">
          <circle cx="11" cy="11" r="11" stroke="none"/>
          <circle cx="11" cy="11" r="9" fill="none"/>
        </g>
        <circle id="椭圆_1697" data-name="椭圆 1697" cx="12" cy="12" r="12" transform="translate(589 1566)" fill="#f7fbfe"/>
        <path id="路径_8379" data-name="路径 8379" d="M865.8,1542.275l5.226,9.56" transform="translate(0 -1)" fill="#fff" stroke="#fff" stroke-width="1"/>
        <path id="路径_8380" data-name="路径 8380" d="M873.217,1542.061h-7.555l2.315,4.223h7.5l-3.386-2.185Z" transform="translate(0 -1)" fill="none" stroke="#fff" stroke-width="1"/>
      </g>
      <g id="目标2-L" transform="translate(713.671 1436.972)">
        <path id="路径_8377" data-name="路径 8377" d="M469.12,469.915a.6.6,0,0,1,.6-.6h.005a.6.6,0,0,1,0,1.207h-.005A.6.6,0,0,1,469.12,469.915Z" transform="translate(-409.931 -410.436)" fill="#fff"/>
        <path id="路径_8378" data-name="路径 8378" d="M53.312,59.479a6.483,6.483,0,1,1,12.965-.009v.018A6.483,6.483,0,0,1,59.8,65.962h-.013a6.483,6.483,0,0,1-6.476-6.481m6.03-5.562a5.579,5.579,0,0,0-5.108,5.108h1.942a.452.452,0,1,1,0,.9H54.235a5.579,5.579,0,0,0,5.108,5.108V63.1a.452.452,0,1,1,.9,0v1.942a5.579,5.579,0,0,0,5.108-5.108H63.413a.452.452,0,0,1,0-.9h1.942a5.579,5.579,0,0,0-5.108-5.108v1.942a.452.452,0,0,1-.9,0Z" transform="translate(0 0)" fill="#fff"/>
      </g>
      <path id="路径_8553" data-name="路径 8553" d="M-5867.04,13766.338h8.238" transform="translate(6600 -12252)" fill="none" stroke="#fff" stroke-linecap="round" stroke-width="1"/>
      <path id="路径_8554" data-name="路径 8554" d="M-5867.04,13766.338h8.238" transform="translate(6600 -12249)" fill="none" stroke="#fff" stroke-linecap="round" stroke-width="1"/>
      <path id="路径_8555" data-name="路径 8555" d="M-5867.04,13766.338h5.105" transform="translate(6600 -12246)" fill="none" stroke="#fff" stroke-linecap="round" stroke-width="1"/>
      <path id="减去_627" data-name="减去 627" d="M6605.779-12226.743h0l-3.351-3.48a5.06,5.06,0,0,1,0-6.96,4.621,4.621,0,0,1,3.35-1.442,4.628,4.628,0,0,1,3.352,1.442,5.061,5.061,0,0,1,0,6.96l-3.351,3.48Zm0-10.37a3.09,3.09,0,0,0-3.047,3.124,3.09,3.09,0,0,0,3.047,3.123,3.088,3.088,0,0,0,3.044-3.123A3.088,3.088,0,0,0,6605.779-12237.113Z" transform="translate(-5797.278 13791.137)" fill="#fff"/>
      <path id="路径_8552" data-name="路径 8552" d="M-5832.372,13828.655h12.629" transform="translate(6599.372 -12251.655)" fill="none" stroke="#fff" stroke-width="1"/>
      <g id="多边形_202" data-name="多边形 202" transform="translate(769.372 1577.345)" fill="#fff">
        <path d="M 7.065736293792725 5.5 L 0.9342631101608276 5.5 L 3.999999761581421 0.9013950228691101 L 7.065736293792725 5.5 Z" stroke="none"/>
        <path d="M 3.999999761581421 1.802774906158447 L 1.868516445159912 5 L 6.13148307800293 5 L 3.999999761581421 1.802774906158447 M 3.999999761581421 0 L 8 6 L -4.76837158203125e-07 6 L 3.999999761581421 0 Z" stroke="none" fill="#fff"/>
      </g>
      <g id="矩形_21800" data-name="矩形 21800" transform="translate(730 1558)" fill="none" stroke="#fff" stroke-width="1">
        <rect width="5" height="4" stroke="none"/>
        <rect x="0.5" y="0.5" width="4" height="3" fill="none"/>
      </g>
      <g id="矩形_21801" data-name="矩形 21801" transform="translate(734 1554)" fill="none" stroke="#fff" stroke-width="1">
        <rect width="6" height="8" stroke="none"/>
        <rect x="0.5" y="0.5" width="5" height="7" fill="none"/>
      </g>
      <g id="矩形_21802" data-name="矩形 21802" transform="translate(739 1559)" fill="none" stroke="#fff" stroke-width="1">
        <rect width="5" height="3" stroke="none"/>
        <rect x="0.5" y="0.5" width="4" height="2" fill="none"/>
      </g>
    </g>
  </g>
</svg>
