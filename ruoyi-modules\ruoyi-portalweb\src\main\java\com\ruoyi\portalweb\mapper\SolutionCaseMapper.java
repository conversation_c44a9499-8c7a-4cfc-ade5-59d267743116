package com.ruoyi.portalweb.mapper;

import com.ruoyi.portalweb.api.domain.SolutionCase;
import com.ruoyi.portalweb.api.domain.SolutionPain;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 解决方案实施案例Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface SolutionCaseMapper 
{
    /**
     * 查询解决方案实施案例
     * 
     * @param solutionCaseId 解决方案实施案例主键
     * @return 解决方案实施案例
     */
    public SolutionCase selectSolutionCaseBySolutionCaseId(Long solutionCaseId);

    /**
     * 查询解决方案实施案例列表
     * 
     * @param solutionCase 解决方案实施案例
     * @return 解决方案实施案例集合
     */
    public List<SolutionCase> selectSolutionCaseList(SolutionCase solutionCase);

    /**
	 * 查询列表按解决方案id
	 */
	public List<SolutionCase> selectListBySolutionId(@Param("solutionId") Long solutionId);

    /**
     * 新增解决方案实施案例
     * 
     * @param solutionCase 解决方案实施案例
     * @return 结果
     */
    public int insertSolutionCase(SolutionCase solutionCase);

    /**
     * 新增解决方案实施案例
     *
     * @param solutionCaseList 解决方案实施案例
     * @return 结果
     */
    public int insertSolutionCaseList(List<SolutionCase> solutionCaseList);

    /**
     * 修改解决方案实施案例
     * 
     * @param solutionCase 解决方案实施案例
     * @return 结果
     */
    public int updateSolutionCase(SolutionCase solutionCase);

    /**
     * 删除解决方案实施案例
     * 
     * @param solutionCaseId 解决方案实施案例主键
     * @return 结果
     */
    public int deleteSolutionCaseBySolutionCaseId(Long solutionCaseId);

    /**
     * 批量删除解决方案实施案例
     * 
     * @param solutionCaseIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSolutionCaseBySolutionCaseIds(Long[] solutionCaseIds);

    /**
     * 批量删除解决方案实施案例
     *
     * @param solutionId 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSolutionCaseBySolutionId(Long solutionId);

    /**
     * 批量删除解决方案实施案例
     *
     * @param solutionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSolutionCaseBySolutionIds(Long[] solutionIds);

    public List<SolutionCase> selectSolutionCaseListBySolutionIds(List<Long> solutionIds);
}
