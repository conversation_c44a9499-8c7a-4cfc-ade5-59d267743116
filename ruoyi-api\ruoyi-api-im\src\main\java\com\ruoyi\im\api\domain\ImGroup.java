package com.ruoyi.im.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;

@TableName(value = "im_group")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImGroup extends Model<ImGroup> {

        @TableId(value = "id", type = IdType.AUTO)
        private Long id;//自增

        private String userId;//创建者

        private String groupId;//群组ID

        private String groupName;//群组名称

        private String groupAvatar;//头像

        private String remark;//群组简介

        private Date create_time;//创建时间

        private Date update_time;//更新时间

        @TableField(exist = false)
        private String userIds;
}
