<template>
  <!-- 应用商店 -->
  <el-dialog :title="title" :visible.sync="open" width="70%"  >
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="first">
        <el-form ref="form" :model="form" label-width="120px">
          <el-row>
            <h1>应用信息</h1>
          </el-row>
          <el-row>
            <el-form-item label="应用编码:">
              {{ this.form.appStoreId }}
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="价格:">
             {{ this.form.appStorePrice }}
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="应用名称:" prop="appStoreName">
              {{ this.form.appStoreName }}
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="应用类型:">
              {{ this.form.appStoreTypeName }}
              <!-- <div v-if="this.form.appStoreType===1">研发设计</div>
              <div v-else-if="this.form.appStoreType===2">生产制造</div>
              <div v-else-if="this.form.appStoreType===3">运营管理</div>
              <div v-else-if="this.form.appStoreType===4">质量管控</div>
              <div v-else-if="this.form.appStoreType===5">仓储物流</div>
              <div v-else-if="this.form.appStoreType===6">安全生产</div>
              <div v-else-if="this.form.appStoreType===7">节能减排</div>
              <div v-else-if="this.form.appStoreType===8">运维服务</div> -->
            </el-form-item>
          </el-row>
          
          <el-row>
            <el-form-item label="应用简介:">
              {{ this.form.appStoreIntroduction }}
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="应用详情:">
              {{ this.form.appStoreContent }}
            </el-form-item>
          </el-row>
          <!-- <el-row>
            <el-form-item label="应用封面:">
              <p><img src="" alt=""></p>
            </el-form-item>
          </el-row> -->
          <el-row>
            <el-form-item label="联系人:">
              {{ this.form.appStoreContactsName }}
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="联系方式:">
              {{ this.form.appStoreContactsPhone }}
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="创建时间:">
              {{ this.form.createTime }}
            </el-form-item>
          </el-row>
          <!-- <el-row style="text-align: center;" v-if="form.auditStatus!=='3'">
              <div class="btn">
                  <el-button type="success" plain @click="edit('3')">审批通过</el-button>
                  <el-button type="danger" plain @click="edit('2')">审批不通过</el-button>
              </div>
          </el-row> -->
          <!-- <el-row>
            <h1>商品规格信息</h1>
          </el-row>
          <el-table :data="tableData1" style="width: 100%">
            <el-table-column prop="" label="规格" width="180">
            </el-table-column>
            <el-table-column prop="name" label="使用用户数" width="180">
            </el-table-column>
            <el-table-column prop="address" label="有效时间">
            </el-table-column>
          </el-table>
          <el-row>
            <h1>商品价格信息</h1>
          </el-row>
          <el-table :data="tableData1" style="width: 100%">
            <el-table-column prop="" label="订货编码" width="180">
            </el-table-column>
            <el-table-column prop="" label="商品原价（元）" width="180">
            </el-table-column>
            <el-table-column prop="" label="商品分佣比例（%）">
            </el-table-column>
          </el-table>
          <el-row>
            <h1>商品参数信息</h1>
          </el-row>
          <el-row>
            <el-form-item label="服务咨询电话:">
              <p>898998908</p>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="产品运营联系人手机号:">
              <p>89899890877777</p>
            </el-form-item>
          </el-row> -->
        </el-form>
      </el-tab-pane>
      <!-- <el-tab-pane label="开发管理" name="second">
        <div>
          <el-form ref="form" :model="formData" label-width="200px">
        <el-row>
            <h1>开发管理配置</h1>
          </el-row>
          <el-row>
            <el-form-item label="服务器出口IP:">
              898998908
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="网页端（web）应用地址:">
              898998908
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="网页端（web）体验地址:">
              898998908
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="健康检查服务端地址:">
              898998908
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="开发联系人:">
              898998908
            </el-form-item>
          </el-row>
        </el-form>
        </div>
      </el-tab-pane> -->
    </el-tabs>

  </el-dialog>
</template>
<script>
import {auditAppStore } from "@/api/portalconsole/AppStore";
export default {
  name: "detailDialog",
  props: {
    // form: {
    //   type: Object,
    //   default: null,
    // },
  },
  data() {
    return {
      title:'应用详情',
      activeName: 'first',
      open: false,
      // chargeloading: false,
      id: '',
      tableData1:[],
      form:{},
      formData:{},
    };
  },
  created() {

  },
  methods: {
    /**
      * 显示弹框
      */
    async show(form) {
      this.form = form
      this.form.appStoreContent=this.removePTags(this.form.appStoreContent)
      console.log("form",form)
      this.open = true; // 切换显示
      // this.chargeloading = true;
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    removePTags(str) {
      return str.replace(/<p[^>]*>|<\/p>/gi, '');
    },
    //审批
    edit(status){
      const appStoreIds=[]
      appStoreIds.push(this.form.appStoreId)
      let data={
        appStoreIds:appStoreIds,
        auditStatus:status
      }
      auditAppStore(data).then(response => {
        this.$modal.msgSuccess("操作成功");
        this.open = false;
        this.$emit("submit", 'updata');
      })
    }

  }
};
</script>
<style></style>