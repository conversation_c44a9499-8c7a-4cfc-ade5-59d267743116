package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.TestingConsultMapper;
import com.ruoyi.system.domain.TestingConsult;
import com.ruoyi.system.service.ITestingConsultService;

/**
 * 检测咨询Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class TestingConsultServiceImpl implements ITestingConsultService 
{
    @Autowired
    private TestingConsultMapper testingConsultMapper;

    /**
     * 查询检测咨询
     * 
     * @param id 检测咨询主键
     * @return 检测咨询
     */
    @Override
    public TestingConsult selectTestingConsultById(Long id)
    {
        return testingConsultMapper.selectTestingConsultById(id);
    }

    /**
     * 查询检测咨询列表
     * 
     * @param testingConsult 检测咨询
     * @return 检测咨询
     */
    @Override
    public List<TestingConsult> selectTestingConsultList(TestingConsult testingConsult)
    {
        return testingConsultMapper.selectTestingConsultList(testingConsult);
    }

    /**
     * 新增检测咨询
     * 
     * @param testingConsult 检测咨询
     * @return 结果
     */
    @Override
    public int insertTestingConsult(TestingConsult testingConsult)
    {
        testingConsult.setCreateTime(DateUtils.getNowDate());
        return testingConsultMapper.insertTestingConsult(testingConsult);
    }

    /**
     * 修改检测咨询
     * 
     * @param testingConsult 检测咨询
     * @return 结果
     */
    @Override
    public int updateTestingConsult(TestingConsult testingConsult)
    {
        testingConsult.setUpdateTime(DateUtils.getNowDate());
        return testingConsultMapper.updateTestingConsult(testingConsult);
    }

    /**
     * 批量删除检测咨询
     * 
     * @param ids 需要删除的检测咨询主键
     * @return 结果
     */
    @Override
    public int deleteTestingConsultByIds(Long[] ids)
    {
        return testingConsultMapper.deleteTestingConsultByIds(ids);
    }

    /**
     * 删除检测咨询信息
     * 
     * @param id 检测咨询主键
     * @return 结果
     */
    @Override
    public int deleteTestingConsultById(Long id)
    {
        return testingConsultMapper.deleteTestingConsultById(id);
    }
}
