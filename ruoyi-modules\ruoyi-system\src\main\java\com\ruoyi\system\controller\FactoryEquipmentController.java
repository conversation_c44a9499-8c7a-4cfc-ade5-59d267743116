package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.FactoryEquipment;
import com.ruoyi.system.service.IFactoryEquipmentService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 工厂设备信息Controller
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@RestController
@RequestMapping("/factoryEquipment")
public class FactoryEquipmentController extends BaseController
{
    @Autowired
    private IFactoryEquipmentService factoryEquipmentService;

    /**
     * 查询工厂设备信息列表
     */
    @RequiresPermissions("system:factoryEquipment:list")
    @GetMapping("/list")
    public TableDataInfo list(FactoryEquipment factoryEquipment)
    {
        startPage();
        List<FactoryEquipment> list = factoryEquipmentService.selectFactoryEquipmentList(factoryEquipment);
        return getDataTable(list);
    }

    /**
     * 导出工厂设备信息列表
     */
    @RequiresPermissions("system:factoryEquipment:export")
    @Log(title = "工厂设备信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FactoryEquipment factoryEquipment)
    {
        List<FactoryEquipment> list = factoryEquipmentService.selectFactoryEquipmentList(factoryEquipment);
        ExcelUtil<FactoryEquipment> util = new ExcelUtil<FactoryEquipment>(FactoryEquipment.class);
        util.exportExcel(response, list, "工厂设备信息数据");
    }

    /**
     * 获取工厂设备信息详细信息
     */
    @RequiresPermissions("system:factoryEquipment:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(factoryEquipmentService.selectFactoryEquipmentById(id));
    }

    /**
     * 新增工厂设备信息
     */
    @RequiresPermissions("system:factoryEquipment:add")
    @Log(title = "工厂设备信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FactoryEquipment factoryEquipment)
    {
        return toAjax(factoryEquipmentService.insertFactoryEquipment(factoryEquipment));
    }

    /**
     * 修改工厂设备信息
     */
    @RequiresPermissions("system:factoryEquipment:edit")
    @Log(title = "工厂设备信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FactoryEquipment factoryEquipment)
    {
        return toAjax(factoryEquipmentService.updateFactoryEquipment(factoryEquipment));
    }

    /**
     * 删除工厂设备信息
     */
    @RequiresPermissions("system:factoryEquipment:remove")
    @Log(title = "工厂设备信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(factoryEquipmentService.deleteFactoryEquipmentByIds(ids));
    }
}
