package com.ruoyi.system.domain;

import java.io.Serializable;
import java.util.Date;

/**
    * 好友表
    */
public class SysUserFriend implements Serializable {
    private Long id;

    /**
    * 用户姓名
    */
    private String userName;

    /**
    * 好友姓名
    */
    private String friendUserName;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 0 删除 1未删除
    */
    private Integer deleteFlag;

    /**
    * 修改时间
    */
    private Date updateTime;

    /**
    * 备注
    */
    private String remark;

    /**
    * 好友昵称
    */
    private String friendNickName;

    /**
    * 好友头像
    */
    private String friendAvatar;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFriendUserName() {
        return friendUserName;
    }

    public void setFriendUserName(String friendUserName) {
        this.friendUserName = friendUserName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFriendNickName() {
        return friendNickName;
    }

    public void setFriendNickName(String friendNickName) {
        this.friendNickName = friendNickName;
    }

    public String getFriendAvatar() {
        return friendAvatar;
    }

    public void setFriendAvatar(String friendAvatar) {
        this.friendAvatar = friendAvatar;
    }
}