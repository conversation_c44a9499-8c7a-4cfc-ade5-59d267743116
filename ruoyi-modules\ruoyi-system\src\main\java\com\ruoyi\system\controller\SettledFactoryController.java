package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SettledFactory;
import com.ruoyi.system.domain.dto.SettledFactoryDetailDTO;
import com.ruoyi.system.domain.dto.SettledFactoryAddDTO;
import com.ruoyi.system.service.ISettledFactoryService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 入驻工厂Controller
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@RestController
@RequestMapping("/settledFactory")
public class SettledFactoryController extends BaseController
{
    @Autowired
    private ISettledFactoryService settledFactoryService;

    /**
     * 查询入驻工厂列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SettledFactory settledFactory, @RequestParam(value = "productId", required = false) Long productId)
    {
        startPage();
        List<SettledFactory> list;

        // 如果传入了产品ID，则查询该产品关联的工厂列表
        if (productId != null) {
            list = settledFactoryService.selectSettledFactoryListByProductId(productId);
        } else {
            list = settledFactoryService.selectSettledFactoryList(settledFactory);
        }

        return getDataTable(list);
    }

    /**
     * 导出入驻工厂列表
     */

    @Log(title = "入驻工厂", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SettledFactory settledFactory)
    {
        List<SettledFactory> list = settledFactoryService.selectSettledFactoryList(settledFactory);
        ExcelUtil<SettledFactory> util = new ExcelUtil<SettledFactory>(SettledFactory.class);
        util.exportExcel(response, list, "入驻工厂数据");
    }

    /**
     * 获取入驻工厂详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(settledFactoryService.selectSettledFactoryById(id));
    }

    /**
     * 获取入驻工厂详情（包含关联数据）
     */

    @GetMapping(value = "/detail/{id}")
    public AjaxResult getDetail(@PathVariable("id") Long id)
    {
        return success(settledFactoryService.selectSettledFactoryDetailById(id));
    }

    /**
     * 新增入驻工厂
     */
    @RequiresPermissions("system:settledFactory:add")
    @Log(title = "入驻工厂", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SettledFactory settledFactory)
    {
        return toAjax(settledFactoryService.insertSettledFactory(settledFactory));
    }


    /**
     * 新增入驻工厂（包含关联数据）
     */
    @PostMapping("/add")
    public AjaxResult addWithRelated(@RequestBody SettledFactoryAddDTO factory)
    {
        return toAjax(settledFactoryService.insertSettledFactoryWithRelated(factory));
    }

    /**
     * 修改入驻工厂
     */

    @Log(title = "入驻工厂", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SettledFactory settledFactory)
    {
        return toAjax(settledFactoryService.updateSettledFactory(settledFactory));
    }

    /**
     * 删除入驻工厂
     */

    @Log(title = "入驻工厂", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(settledFactoryService.deleteSettledFactoryByIds(ids));
    }
}
