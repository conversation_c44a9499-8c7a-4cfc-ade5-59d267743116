package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CertificateMapper;
import com.ruoyi.system.domain.Certificate;
import com.ruoyi.system.service.ICertificateService;

/**
 * 证书信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class CertificateServiceImpl implements ICertificateService 
{
    @Autowired
    private CertificateMapper certificateMapper;

    /**
     * 查询证书信息
     * 
     * @param id 证书信息主键
     * @return 证书信息
     */
    @Override
    public Certificate selectCertificateById(Long id)
    {
        return certificateMapper.selectCertificateById(id);
    }

    /**
     * 查询证书信息列表
     * 
     * @param certificate 证书信息
     * @return 证书信息
     */
    @Override
    public List<Certificate> selectCertificateList(Certificate certificate)
    {
        return certificateMapper.selectCertificateList(certificate);
    }

    /**
     * 新增证书信息
     * 
     * @param certificate 证书信息
     * @return 结果
     */
    @Override
    public int insertCertificate(Certificate certificate)
    {
        certificate.setCreateTime(DateUtils.getNowDate());
        return certificateMapper.insertCertificate(certificate);
    }

    /**
     * 修改证书信息
     * 
     * @param certificate 证书信息
     * @return 结果
     */
    @Override
    public int updateCertificate(Certificate certificate)
    {
        certificate.setUpdateTime(DateUtils.getNowDate());
        return certificateMapper.updateCertificate(certificate);
    }

    /**
     * 批量删除证书信息
     * 
     * @param ids 需要删除的证书信息主键
     * @return 结果
     */
    @Override
    public int deleteCertificateByIds(Long[] ids)
    {
        return certificateMapper.deleteCertificateByIds(ids);
    }

    /**
     * 删除证书信息信息
     * 
     * @param id 证书信息主键
     * @return 结果
     */
    @Override
    public int deleteCertificateById(Long id)
    {
        return certificateMapper.deleteCertificateById(id);
    }
}
