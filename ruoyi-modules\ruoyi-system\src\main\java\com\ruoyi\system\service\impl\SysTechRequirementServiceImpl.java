package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysTechRequirementMapper;
import com.ruoyi.system.domain.SysTechRequirement;
import com.ruoyi.system.service.ISysTechRequirementService;

/**
 * 技术需求Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
public class SysTechRequirementServiceImpl implements ISysTechRequirementService 
{
    @Autowired
    private SysTechRequirementMapper sysTechRequirementMapper;

    /**
     * 查询技术需求
     * 
     * @param requirementId 技术需求主键
     * @return 技术需求
     */
    @Override
    public SysTechRequirement selectSysTechRequirementByRequirementId(Long requirementId)
    {
        return sysTechRequirementMapper.selectSysTechRequirementByRequirementId(requirementId);
    }

    /**
     * 查询技术需求列表
     * 
     * @param sysTechRequirement 技术需求
     * @return 技术需求
     */
    @Override
    public List<SysTechRequirement> selectSysTechRequirementList(SysTechRequirement sysTechRequirement)
    {
        return sysTechRequirementMapper.selectSysTechRequirementList(sysTechRequirement);
    }

    /**
     * 新增技术需求
     * 
     * @param sysTechRequirement 技术需求
     * @return 结果
     */
    @Override
    public int insertSysTechRequirement(SysTechRequirement sysTechRequirement)
    {
        sysTechRequirement.setCreateTime(DateUtils.getNowDate());
        return sysTechRequirementMapper.insertSysTechRequirement(sysTechRequirement);
    }

    /**
     * 修改技术需求
     * 
     * @param sysTechRequirement 技术需求
     * @return 结果
     */
    @Override
    public int updateSysTechRequirement(SysTechRequirement sysTechRequirement)
    {
        sysTechRequirement.setUpdateTime(DateUtils.getNowDate());
        return sysTechRequirementMapper.updateSysTechRequirement(sysTechRequirement);
    }

    /**
     * 批量删除技术需求
     * 
     * @param requirementIds 需要删除的技术需求主键
     * @return 结果
     */
    @Override
    public int deleteSysTechRequirementByRequirementIds(Long[] requirementIds)
    {
        return sysTechRequirementMapper.deleteSysTechRequirementByRequirementIds(requirementIds);
    }

    /**
     * 删除技术需求信息
     * 
     * @param requirementId 技术需求主键
     * @return 结果
     */
    @Override
    public int deleteSysTechRequirementByRequirementId(Long requirementId)
    {
        return sysTechRequirementMapper.deleteSysTechRequirementByRequirementId(requirementId);
    }
}
