package com.ruoyi.portalconsole.mapper;

import java.util.List;
import com.ruoyi.portalconsole.domain.Ecology;
import com.ruoyi.portalconsole.domain.vo.EcologyVO;

/**
 * 生态协作Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface EcologyMapper 
{
    /**
     * 查询生态协作
     * 
     * @param ecologyId 生态协作主键
     * @return 生态协作
     */
    public EcologyVO selectEcologyByEcologyId(Long ecologyId);

    /**
     * 查询生态协作列表
     * 
     * @param ecology 生态协作
     * @return 生态协作集合
     */
    public List<EcologyVO> selectEcologyList(Ecology ecology);

    /**
     * 新增生态协作
     * 
     * @param ecology 生态协作
     * @return 结果
     */
    public int insertEcology(Ecology ecology);

    /**
     * 修改生态协作
     * 
     * @param ecology 生态协作
     * @return 结果
     */
    public int updateEcology(Ecology ecology);

    /**
     * 删除生态协作
     * 
     * @param ecologyId 生态协作主键
     * @return 结果
     */
    public int deleteEcologyByEcologyId(Long ecologyId);

    /**
     * 批量删除生态协作
     * 
     * @param ecologyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEcologyByEcologyIds(Long[] ecologyIds);
}
