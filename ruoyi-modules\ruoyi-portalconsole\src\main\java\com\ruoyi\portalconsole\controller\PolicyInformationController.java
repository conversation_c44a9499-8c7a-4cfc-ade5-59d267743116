package com.ruoyi.portalconsole.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.PolicyInformation;
import com.ruoyi.portalconsole.service.IPolicyInformationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 政策资讯Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/PolicyInformation")
public class PolicyInformationController extends BaseController
{
    @Autowired
    private IPolicyInformationService policyInformationService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询政策资讯列表
     */
    @RequiresPermissions("portalconsole:PolicyInformation:list")
    @GetMapping("/list")
    public TableDataInfo list(PolicyInformation policyInformation)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<PolicyInformation> list = policyInformationService.selectPolicyInformationList(policyInformation);
        return getDataTable(list);
    }

    /**
     * 导出政策资讯列表
     */
    @RequiresPermissions("portalconsole:PolicyInformation:export")
    @Log(title = "政策资讯", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PolicyInformation policyInformation)
    {
        List<PolicyInformation> list = policyInformationService.selectPolicyInformationList(policyInformation);
        ExcelUtil<PolicyInformation> util = new ExcelUtil<PolicyInformation>(PolicyInformation.class);
        util.exportExcel(response, list, "政策资讯数据");
    }

    /**
     * 获取政策资讯详细信息
     */
    @RequiresPermissions("portalconsole:PolicyInformation:query")
    @GetMapping(value = "/{policyInformationId}")
    public AjaxResult getInfo(@PathVariable("policyInformationId") Long policyInformationId)
    {
        return success(policyInformationService.selectPolicyInformationByPolicyInformationId(policyInformationId));
    }

    /**
     * 新增政策资讯
     */
    @RequiresPermissions("portalconsole:PolicyInformation:add")
    @Log(title = "政策资讯", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PolicyInformation policyInformation)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        policyInformation.setUpdateBy(userNickName.getData());
        policyInformation.setCreateBy(userNickName.getData());
        return toAjax(policyInformationService.insertPolicyInformation(policyInformation));
    }

    /**
     * 新增政策资讯
     */
    @Log(title = "政策资讯", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    public AjaxResult addByImport(@RequestBody PolicyInformation policyInformation)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        policyInformation.setUpdateBy(userNickName.getData());
        policyInformation.setCreateBy(userNickName.getData());
        return toAjax(policyInformationService.insertPolicyInformation(policyInformation));
    }

    /**
     * 修改政策资讯
     */
    @RequiresPermissions("portalconsole:PolicyInformation:edit")
    @Log(title = "政策资讯", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PolicyInformation policyInformation)
    {
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        policyInformation.setUpdateBy(userNickName.getData());
        return toAjax(policyInformationService.updatePolicyInformation(policyInformation));
    }

    /**
     * 删除政策资讯
     */
    @RequiresPermissions("portalconsole:PolicyInformation:remove")
    @Log(title = "政策资讯", businessType = BusinessType.DELETE)
	@DeleteMapping("/{policyInformationIds}")
    public AjaxResult remove(@PathVariable Long[] policyInformationIds)
    {
        return toAjax(policyInformationService.deletePolicyInformationByPolicyInformationIds(policyInformationIds));
    }
}
