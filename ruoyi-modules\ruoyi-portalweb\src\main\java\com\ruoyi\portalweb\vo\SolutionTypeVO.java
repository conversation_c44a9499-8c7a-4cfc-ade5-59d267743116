package com.ruoyi.portalweb.vo;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.portalweb.api.domain.SolutionType;
import io.swagger.annotations.ApiModelProperty;

/**
 * 解决方案类型对象 solution_type
 */
public class SolutionTypeVO extends SolutionType {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "服务范围名称")
    private String categoryName;

    @ApiModelProperty(value = "数量")
    private String totalCount;

    public String getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(String totalCount) {
        this.totalCount = totalCount;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }
}
