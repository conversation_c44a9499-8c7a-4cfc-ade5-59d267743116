package com.ruoyi.im.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.im.api.domain.ImChatroom;
import com.ruoyi.im.api.domain.ImChatroomMsg;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.im.api.util.RongyunUtils;
import com.ruoyi.im.service.ImChatroomMsgService;
import com.ruoyi.im.service.ImChatroomService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/im/chatroommsg")
public class ImChatroomMsgController {

    @Resource
    private RongyunUtils rongyunUtils;

    @Resource
    private ImChatroomMsgService imChatroomMsgService;

    @Resource
    private ImChatroomService imChatroomService;

    /***
     * ImChatroom分页条件搜索实现
     * @param imChatroomMsg
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/search/{page}/{size}" )
    public TableDataInfo findPage(@RequestBody(required = false)  ImChatroomMsg imChatroomMsg, @PathVariable("page")  int page, @PathVariable("size")  int size, @RequestParam(value = "fields",required = false) String fields){
        Page<ImChatroomMsg> pageSearch = new Page<>(page,size);
        QueryWrapper<ImChatroomMsg> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(imChatroomMsg.getToUserId())){
            wrapper.eq("toUserId",imChatroomMsg.getToUserId());
        }
        if(StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        imChatroomMsgService.page(pageSearch,wrapper);
        TableDataInfo tableDataInfo = new TableDataInfo(pageSearch.getRecords(), (int) pageSearch.getTotal());
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setMsg("success");
        return tableDataInfo;
    }

    /***
     * 多条件搜索数据
     * @param imChatroomMsg
     * @return
     */
    @PostMapping(value = "/search" )
    public R<List<ImChatroomMsg>> findList(@RequestBody(required = false) ImChatroomMsg imChatroomMsg, @RequestParam(value = "fields",required = false) String fields){
        QueryWrapper<ImChatroomMsg> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(imChatroomMsg.getToUserId())){
            wrapper.eq("toUserId",imChatroomMsg.getToUserId());
        }
        if(StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        return R.ok(imChatroomMsgService.list(wrapper)) ;
    }

    /***
     * 多条件搜索数据
     * @param telphone
     * @return
     */
    @GetMapping(value = "/sent" )
    public R<List<String>> findSent(@RequestParam("telphone") String telphone){
        return R.ok(imChatroomMsgService.findSent(telphone)) ;
    }

    /***
     * 修改ImChatroom数据
     * @param imChatroomMsg
     * @return
     */
    @PostMapping(value="/{id}")
    public R<Boolean> update(@RequestBody ImChatroomMsg imChatroomMsg,@PathVariable("id") Long id){
        if(imChatroomMsgService.updateById(imChatroomMsg)){
            return R.ok(true) ;
        }
        return R.ok(false) ;
    }

    /***
     * 发送消息
     * @param imChatroomMsg
     * @return
     */
    @PostMapping(value="/send")
    public R<Long> add(@RequestBody ImChatroomMsg imChatroomMsg){
        if (ObjectUtils.isNotEmpty(imChatroomMsg)) {
            String messageSend = rongyunUtils.messageSend(imChatroomMsg.getFromUserId(),imChatroomMsg.getToUserId(), imChatroomMsg.getMessage());
            JSONObject jsonStr = JSONObject.parseObject(messageSend);

            if ( jsonStr.getString("code").equals(200)) {
                if(imChatroomMsgService.save(imChatroomMsg)){
                    return R.ok(imChatroomMsg.getId()) ;
                }
            }
        }
        return R.fail(400,"发送消息失败") ;
    }

    /***
     * 接受消息
     * @param imChatroomMsg
     * @return
     */
    @PostMapping(value="/receive")
    public R<Boolean> receive(@RequestBody ImChatroomMsg imChatroomMsg){
        if (ObjectUtils.isNotEmpty(imChatroomMsg)) {
            if (imChatroomMsgService.save(imChatroomMsg)) {
                ImChatroom imChatroom = imChatroomService.getOne(new QueryWrapper<ImChatroom>().eq("chatroomId",imChatroomMsg.getToUserId()));
                if(imChatroom!=null){
                    imChatroom.setUpdate_time(new Date());
                    imChatroomService.updateById(imChatroom);
                }
                return R.ok(true) ;
            }
        }
        return R.fail(400,"发送消息失败") ;
    }
}
