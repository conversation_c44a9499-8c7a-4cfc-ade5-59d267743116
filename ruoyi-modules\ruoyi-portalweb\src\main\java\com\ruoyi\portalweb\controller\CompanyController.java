package com.ruoyi.portalweb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.Company;
import com.ruoyi.portalweb.api.domain.Demand;
import com.ruoyi.portalweb.service.ICompanyService;
import com.ruoyi.portalweb.vo.CompanyVO;
import com.ruoyi.portalweb.vo.CustomerAudingVO;
import com.ruoyi.system.api.RemoteUserService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 企业信息Controller
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@RestController
@RequestMapping("/Company")
public class CompanyController extends BaseController {
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询企业信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Company company) {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<Company> list = companyService.selectCompanyList(company);
        return getDataTable(list);
    }
    
    /**
     * 查询企业信息列表
     */
    @GetMapping("/listDesk")
    public TableDataInfo listDesk(Company company) {
        startPage();
        PageUtils.setOrderBy("create_time DESC");
        List<Company> list = companyService.selectCompanyList(company);
        return getDataTable(list);
    }

    /**
     * 导出企业信息列表
     */
    @Log(title = "企业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Company company) {
        List<Company> list = companyService.selectCompanyList(company);
        ExcelUtil<Company> util = new ExcelUtil<Company>(Company.class);
        util.exportExcel(response, list, "企业信息数据");
    }

//    /**
//     * 获取企业信息详细信息
//     */
//    @GetMapping(value = "/{companyId}")
//    public AjaxResult getInfo(@PathVariable("companyId") Long companyId) {
//        return success(companyService.selectCompanyByCompanyId(companyId));
//    }

    /**
     * 获取企业信息详细信息
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取企业信息详细信息", notes = "传入")
    public AjaxResult detail(Company company) {
        return success(companyService.selectCompanyByCompanyId(company.getCompanyId()));
    }

    /**
     * 获取企业信息详细信息
     */
    @GetMapping("/detailDesk")
    @ApiOperation(value = "获取企业信息详细信息", notes = "传入")
    public AjaxResult detailDesk(Company company) {
        return success(companyService.selectCompanyByCompanyId(company.getCompanyId()));
    }

    /**
     * 新增企业信息
     */
    @Log(title = "企业信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CompanyVO company) {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        company.setUpdateBy(userNickName.getData());
        company.setCreateBy(userNickName.getData());
        return toAjax(companyService.insertCompany(company));
    }

    /**
     * 修改企业信息
     */
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CompanyVO company) {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        company.setUpdateBy(userNickName.getData());
        return toAjax(companyService.updateCompany(company));
    }

    /**
     * 删除企业信息
     */
    @Log(title = "企业信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{companyIds}")
    public AjaxResult remove(@PathVariable Long[] companyIds) {
        return toAjax(companyService.deleteCompanyByCompanyIds(companyIds));
    }


    /**
     * 根据web输入的公司关键字，查询所有企业信息
     */
    @GetMapping("/searchByKeywords")
    @ApiOperation(value = "根据输入的公司信息，模糊搜索", notes = "根据输入的公司信息，模糊搜索")
    public TableDataInfo searchByKeywords(@ApiParam(value = "关键字", required = true) @RequestParam String keywords) {
        List<String> strings = companyService.searchByKeywords(keywords);
        return getDataTable(strings);
    }

    /**
     * 根据公司全名，搜索企业信息
     */
    @GetMapping("/searchByCustomerName")
    @ApiOperation(value = "根据公司全称，搜索企业信息", notes = "根据公司全称，搜索企业信息")
    public AjaxResult searchByCustomerName(@ApiParam(value = "公司名称", required = true) @RequestParam String keywords) {
        CustomerAudingVO customerAudingVO = companyService.searchByCustomerName(keywords);
        return success(customerAudingVO);
    }
}
