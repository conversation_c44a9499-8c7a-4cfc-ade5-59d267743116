<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TestingRequirementMapper">
    
    <resultMap type="TestingRequirement" id="TestingRequirementResult">
        <result property="id"    column="id"    />
        <result property="testingContent"    column="testing_content"    />
        <result property="testingRequirements"    column="testing_requirements"    />
        <result property="basicRequirements"    column="basic_requirements"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="companyName"    column="company_name"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTestingRequirementVo">
        select id, testing_content, testing_requirements, basic_requirements, image_url, company_name, contact_person, contact_phone, status, del_flag, create_by, create_time, update_by, update_time, remark from testing_requirement
    </sql>

    <select id="selectTestingRequirementList" parameterType="TestingRequirement" resultMap="TestingRequirementResult">
        <include refid="selectTestingRequirementVo"/>
        <where>  
            <if test="testingContent != null  and testingContent != ''"> and testing_content = #{testingContent}</if>
            <if test="testingRequirements != null  and testingRequirements != ''"> and testing_requirements = #{testingRequirements}</if>
            <if test="basicRequirements != null  and basicRequirements != ''"> and basic_requirements = #{basicRequirements}</if>
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person = #{contactPerson}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectTestingRequirementById" parameterType="Long" resultMap="TestingRequirementResult">
        <include refid="selectTestingRequirementVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTestingRequirement" parameterType="TestingRequirement" useGeneratedKeys="true" keyProperty="id">
        insert into testing_requirement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="testingContent != null">testing_content,</if>
            <if test="testingRequirements != null">testing_requirements,</if>
            <if test="basicRequirements != null">basic_requirements,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="companyName != null">company_name,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="testingContent != null">#{testingContent},</if>
            <if test="testingRequirements != null">#{testingRequirements},</if>
            <if test="basicRequirements != null">#{basicRequirements},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTestingRequirement" parameterType="TestingRequirement">
        update testing_requirement
        <trim prefix="SET" suffixOverrides=",">
            <if test="testingContent != null">testing_content = #{testingContent},</if>
            <if test="testingRequirements != null">testing_requirements = #{testingRequirements},</if>
            <if test="basicRequirements != null">basic_requirements = #{basicRequirements},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTestingRequirementById" parameterType="Long">
        delete from testing_requirement where id = #{id}
    </delete>

    <delete id="deleteTestingRequirementByIds" parameterType="String">
        delete from testing_requirement where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>