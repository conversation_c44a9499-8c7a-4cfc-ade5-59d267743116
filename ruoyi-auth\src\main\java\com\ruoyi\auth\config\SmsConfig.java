package com.ruoyi.auth.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

@Configuration
@RefreshScope
@EnableAsync
public class SmsConfig {
    @Value("${ali.sign}")
    private String sign;

    @Value("${ali.template}")
    private String template;

    @Value("${ali.product}")
    private String product;

    @Value("${ali.domain}")
    private String domain;

    @Value("${ali.ACCESS_KEY_ID}")
    private String accessKeyId;

    @Value("${ali.ACCESS_KEY_SECRET}")
    private String accessKeySecret;

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }
}
