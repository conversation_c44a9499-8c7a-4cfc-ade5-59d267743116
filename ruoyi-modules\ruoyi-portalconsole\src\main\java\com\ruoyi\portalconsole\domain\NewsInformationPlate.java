package com.ruoyi.portalconsole.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 咨询板块对象 news_information_plate
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
public class NewsInformationPlate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 咨询板块ID */
    private Long newsInformationPlateId;

    /** 上级id */
    @Excel(name = "上级id")
    private Long parentId;

    /** 名称 */
    @Excel(name = "名称")
    private String newsInformationPlateName;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setNewsInformationPlateId(Long newsInformationPlateId) 
    {
        this.newsInformationPlateId = newsInformationPlateId;
    }

    public Long getNewsInformationPlateId() 
    {
        return newsInformationPlateId;
    }
    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }
    public void setNewsInformationPlateName(String newsInformationPlateName) 
    {
        this.newsInformationPlateName = newsInformationPlateName;
    }

    public String getNewsInformationPlateName() 
    {
        return newsInformationPlateName;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("newsInformationPlateId", getNewsInformationPlateId())
            .append("parentId", getParentId())
            .append("newsInformationPlateName", getNewsInformationPlateName())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
