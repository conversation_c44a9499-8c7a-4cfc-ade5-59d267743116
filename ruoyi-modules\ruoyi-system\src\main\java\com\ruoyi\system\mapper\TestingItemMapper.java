package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.LaboratoryInfo;
import com.ruoyi.system.domain.LabTestingRelation;
import com.ruoyi.system.domain.TestingItem;
import com.ruoyi.system.domain.dto.TestingItemWithLabsDTO;

/**
 * 检测项目Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface TestingItemMapper 
{
    /**
     * 查询检测项目
     * 
     * @param id 检测项目主键
     * @return 检测项目
     */
    public TestingItem selectTestingItemById(Long id);

    /**
     * 查询检测项目列表
     * 
     * @param testingItem 检测项目
     * @return 检测项目集合
     */
    public List<TestingItem> selectTestingItemList(TestingItem testingItem);

    /**
     * 新增检测项目
     * 
     * @param testingItem 检测项目
     * @return 结果
     */
    public int insertTestingItem(TestingItem testingItem);

    /**
     * 修改检测项目
     * 
     * @param testingItem 检测项目
     * @return 结果
     */
    public int updateTestingItem(TestingItem testingItem);

    /**
     * 删除检测项目
     * 
     * @param id 检测项目主键
     * @return 结果
     */
    public int deleteTestingItemById(Long id);

    /**
     * 批量删除检测项目
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTestingItemByIds(Long[] ids);

    /**
     * 根据检测项目ID查询关联的实验室信息
     * 
     * @param testingId 检测项目ID
     * @return 实验室信息集合
     */
    public List<LaboratoryInfo> selectLabsByTestingId(Long testingId);
    
    /**
     * 根据检测项目ID查询关联关系
     * 
     * @param testingId 检测项目ID
     * @return 关联关系集合
     */
    public List<LabTestingRelation> selectRelationsByTestingId(Long testingId);

    /**
     * 查询检测项目列表并左联查检测实验室
     * 
     * @param labType 实验室类型
     * @return 检测项目及关联实验室信息集合
     */
    public List<TestingItemWithLabsDTO> selectTestingItemLeftJoinLabs(String labType);
}
