package com.ruoyi.portalweb.mapper;


import com.ruoyi.portalweb.api.domain.Supply;
import com.ruoyi.portalweb.vo.SupplyVO;

import java.util.List;

/**
 * 服务供给Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface SupplyMapper 
{
    /**
     * 查询服务供给
     * 
     * @param id 服务供给主键
     * @return 服务供给
     */
    public SupplyVO selectSupplyById(Long id);

    /**
     * 查询服务供给列表
     * 
     * @param supply 服务供给
     * @return 服务供给集合
     */
    public List<SupplyVO> selectSupplyList(SupplyVO supply);

    /**
     * 新增服务供给
     * 
     * @param supply 服务供给
     * @return 结果
     */
    public int insertSupply(Supply supply);

    /**
     * 修改服务供给
     * 
     * @param supply 服务供给
     * @return 结果
     */
    public int updateSupply(Supply supply);

    /**
     * 删除服务供给
     * 
     * @param id 服务供给主键
     * @return 结果
     */
    public int deleteSupplyById(Long id);

    /**
     * 批量删除服务供给
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplyByIds(Long[] ids);

    /**
     * 增加服务供给阅读数量
     *
     * @param id 服务供给主键
     * @return 结果
     */
    public int addSupplyViewCount(Long id);

    public List<SupplyVO> selectDemandListByMemberIds(List<Long> memberIds);

    public List<Supply> selectSupplyListBySupplyIds(List<Long> supplyIds);
}
