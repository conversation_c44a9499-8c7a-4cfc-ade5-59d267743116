package com.ruoyi.auth.controller;

import com.ruoyi.auth.config.SSOClientConfig;
import com.ruoyi.auth.service.SSOClientService;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 主系统SSO客户端控制器
 * 处理SSO相关的客户端操作（不包括回调处理，回调由SSOCallbackController处理）
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sso")
public class SSOClientController {

    private static final Logger log = LoggerFactory.getLogger(SSOClientController.class);

    @Autowired
    private SSOClientService ssoClientService;

    @Autowired
    private SSOClientConfig.SSOClientProperties ssoClientProperties;

    /**
     * 跳转到SSO登录
     *
     * @param redirect 登录成功后的跳转地址
     * @param response HTTP响应
     */
    @GetMapping("/login")
    public void redirectToSSOLogin(@RequestParam(value = "redirect", required = false) String redirect,
                                  HttpServletResponse response) throws IOException {

        log.info("跳转到SSO登录，重定向地址: {}", redirect);

        // 构造SSO登录URL
        StringBuilder loginUrl = new StringBuilder();
        loginUrl.append(ssoClientProperties.getFullLoginUrl());
        loginUrl.append("?client_id=").append(ssoClientProperties.getClientId());
        loginUrl.append("&redirect_uri=").append(java.net.URLEncoder.encode(ssoClientProperties.getCallbackUrl(), "UTF-8"));

        if (StringUtils.isNotEmpty(redirect)) {
            loginUrl.append("&state=").append(java.net.URLEncoder.encode(redirect, "UTF-8"));
        }

        log.info("SSO登录URL: {}", loginUrl.toString());
        response.sendRedirect(loginUrl.toString());
    }



    /**
     * SSO登出接口
     * 
     * @param request HTTP请求
     * @return 登出结果
     */
    @PostMapping("/logout")
    public AjaxResult ssoLogout(HttpServletRequest request) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null) {
                // 清除本地会话
                ssoClientService.clearLocalUserSession(loginUser.getToken());
                
                // 通知SSO服务登出
                ssoClientService.notifySSOLogout(loginUser.getToken());
                
                log.info("用户 {} SSO登出成功", loginUser.getUsername());
            }
            
            return AjaxResult.success("登出成功");
        } catch (Exception e) {
            log.error("SSO登出失败", e);
            return AjaxResult.error("登出失败: " + e.getMessage());
        }
    }

    /**
     * 检查SSO状态
     * 
     * @param request HTTP请求
     * @return SSO状态
     */
    @GetMapping("/status")
    public AjaxResult checkSSOStatus(HttpServletRequest request) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Map<String, Object> status = new HashMap<>();
            
            if (loginUser != null) {
                status.put("isLogin", true);
                status.put("username", loginUser.getUsername());
                status.put("userId", loginUser.getUserid());
                status.put("system", "backend");
                
                // 检查SSO服务状态
                boolean ssoServerStatus = ssoClientService.checkSSOServerStatus();
                status.put("ssoServerStatus", ssoServerStatus);
            } else {
                status.put("isLogin", false);
                status.put("ssoServerStatus", false);
            }
            
            return AjaxResult.success(status);
        } catch (Exception e) {
            log.error("检查SSO状态失败", e);
            return AjaxResult.error("检查SSO状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取SSO登录地址
     * 
     * @param redirect 登录成功后的跳转地址
     * @return SSO登录地址
     */
    @GetMapping("/loginUrl")
    public AjaxResult getSSOLoginUrl(@RequestParam(value = "redirect", required = false) String redirect) {
        try {
            String loginUrl = ssoClientService.getSSOLoginUrl(redirect);
            Map<String, Object> result = new HashMap<>();
            result.put("loginUrl", loginUrl);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取SSO登录地址失败", e);
            return AjaxResult.error("获取登录地址失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     * 
     * @param request HTTP请求
     * @return 用户信息
     */
    @GetMapping("/userinfo")
    public AjaxResult getCurrentUserInfo(HttpServletRequest request) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null) {
                Map<String, Object> result = new HashMap<>();
                result.put("userId", loginUser.getUserid());
                result.put("username", loginUser.getUsername());
                result.put("user", loginUser.getSysUser());
                result.put("permissions", loginUser.getPermissions());
                result.put("roles", loginUser.getRoles());
                
                return AjaxResult.success(result);
            } else {
                return AjaxResult.error("用户未登录");
            }
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return AjaxResult.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 刷新用户权限信息
     * 
     * @param request HTTP请求
     * @return 刷新结果
     */
    @PostMapping("/refresh")
    public AjaxResult refreshUserInfo(HttpServletRequest request) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null) {
                // 重新从SSO获取用户权限信息
                boolean success = ssoClientService.refreshUserPermissions(loginUser.getToken());
                if (success) {
                    return AjaxResult.success("权限信息刷新成功");
                } else {
                    return AjaxResult.error("权限信息刷新失败");
                }
            } else {
                return AjaxResult.error("用户未登录");
            }
        } catch (Exception e) {
            log.error("刷新用户权限信息失败", e);
            return AjaxResult.error("刷新权限信息失败: " + e.getMessage());
        }
    }
}
