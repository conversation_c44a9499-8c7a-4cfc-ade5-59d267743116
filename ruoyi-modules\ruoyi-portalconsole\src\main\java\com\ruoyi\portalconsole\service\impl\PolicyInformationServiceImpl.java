package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.PolicyInformationMapper;
import com.ruoyi.portalconsole.domain.PolicyInformation;
import com.ruoyi.portalconsole.service.IPolicyInformationService;

/**
 * 政策资讯Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class PolicyInformationServiceImpl implements IPolicyInformationService 
{
    @Autowired
    private PolicyInformationMapper policyInformationMapper;

    /**
     * 查询政策资讯
     * 
     * @param policyInformationId 政策资讯主键
     * @return 政策资讯
     */
    @Override
    public PolicyInformation selectPolicyInformationByPolicyInformationId(Long policyInformationId)
    {
        return policyInformationMapper.selectPolicyInformationByPolicyInformationId(policyInformationId);
    }

    /**
     * 查询政策资讯列表
     * 
     * @param policyInformation 政策资讯
     * @return 政策资讯
     */
    @Override
    public List<PolicyInformation> selectPolicyInformationList(PolicyInformation policyInformation)
    {
        return policyInformationMapper.selectPolicyInformationList(policyInformation);
    }

    /**
     * 新增政策资讯
     * 
     * @param policyInformation 政策资讯
     * @return 结果
     */
    @Override
    public int insertPolicyInformation(PolicyInformation policyInformation)
    {
        policyInformation.setCreateTime(DateUtils.getNowDate());
        return policyInformationMapper.insertPolicyInformation(policyInformation);
    }

    /**
     * 修改政策资讯
     * 
     * @param policyInformation 政策资讯
     * @return 结果
     */
    @Override
    public int updatePolicyInformation(PolicyInformation policyInformation)
    {
        policyInformation.setUpdateTime(DateUtils.getNowDate());
        return policyInformationMapper.updatePolicyInformation(policyInformation);
    }

    /**
     * 批量删除政策资讯
     * 
     * @param policyInformationIds 需要删除的政策资讯主键
     * @return 结果
     */
    @Override
    public int deletePolicyInformationByPolicyInformationIds(Long[] policyInformationIds)
    {
        return policyInformationMapper.deletePolicyInformationByPolicyInformationIds(policyInformationIds);
    }

    /**
     * 删除政策资讯信息
     * 
     * @param policyInformationId 政策资讯主键
     * @return 结果
     */
    @Override
    public int deletePolicyInformationByPolicyInformationId(Long policyInformationId)
    {
        return policyInformationMapper.deletePolicyInformationByPolicyInformationId(policyInformationId);
    }
}
