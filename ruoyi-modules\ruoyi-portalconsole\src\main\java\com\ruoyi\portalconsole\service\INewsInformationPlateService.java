package com.ruoyi.portalconsole.service;

import java.util.List;
import com.ruoyi.portalconsole.domain.NewsInformationPlate;

/**
 * 咨询板块Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
public interface INewsInformationPlateService 
{
    /**
     * 查询咨询板块
     * 
     * @param newsInformationPlateId 咨询板块主键
     * @return 咨询板块
     */
    public NewsInformationPlate selectNewsInformationPlateByNewsInformationPlateId(Long newsInformationPlateId);

    /**
     * 查询咨询板块列表
     * 
     * @param newsInformationPlate 咨询板块
     * @return 咨询板块集合
     */
    public List<NewsInformationPlate> selectNewsInformationPlateList(NewsInformationPlate newsInformationPlate);

    /**
     * 新增咨询板块
     * 
     * @param newsInformationPlate 咨询板块
     * @return 结果
     */
    public int insertNewsInformationPlate(NewsInformationPlate newsInformationPlate);

    /**
     * 修改咨询板块
     * 
     * @param newsInformationPlate 咨询板块
     * @return 结果
     */
    public int updateNewsInformationPlate(NewsInformationPlate newsInformationPlate);

    /**
     * 批量删除咨询板块
     * 
     * @param newsInformationPlateIds 需要删除的咨询板块主键集合
     * @return 结果
     */
    public int deleteNewsInformationPlateByNewsInformationPlateIds(Long[] newsInformationPlateIds);

    /**
     * 删除咨询板块信息
     * 
     * @param newsInformationPlateId 咨询板块主键
     * @return 结果
     */
    public int deleteNewsInformationPlateByNewsInformationPlateId(Long newsInformationPlateId);
}
