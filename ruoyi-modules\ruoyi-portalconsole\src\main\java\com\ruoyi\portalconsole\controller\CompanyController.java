package com.ruoyi.portalconsole.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalconsole.domain.vo.CompanyVO;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.Company;
import com.ruoyi.portalconsole.service.ICompanyService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 企业信息Controller
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@RestController
@RequestMapping("/Company")
public class CompanyController extends BaseController
{
    @Autowired
    private ICompanyService companyService;

    @Autowired
    private RemoteUserService remoteUserService;


    /**
     * 查询企业信息列表
     */
    @RequiresPermissions("portalconsole:Company:list")
    @GetMapping("/list")
    public TableDataInfo list(Company company)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<Company> list = companyService.selectCompanyList(company);
        return getDataTable(list);
    }

    /**
     * 导出企业信息列表
     */
    @RequiresPermissions("portalconsole:Company:export")
    @Log(title = "企业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Company company)
    {
        List<Company> list = companyService.selectCompanyList(company);
        ExcelUtil<Company> util = new ExcelUtil<Company>(Company.class);
        util.exportExcel(response, list, "企业信息数据");
    }

    /**
     * 获取企业信息详细信息
     */
    @RequiresPermissions("portalconsole:Company:query")
    @GetMapping(value = "/{companyId}")
    public AjaxResult getInfo(@PathVariable("companyId") Long companyId)
    {
        return success(companyService.selectCompanyByCompanyId(companyId));
    }

    /**
     * 新增企业信息
     */
    @RequiresPermissions("portalconsole:Company:add")
    @Log(title = "企业信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Company company)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        company.setUpdateBy(userNickName.getData());
        company.setCreateBy(userNickName.getData());
        return toAjax(companyService.insertCompany(company));
    }

    /**
     * 修改企业信息
     */
    @RequiresPermissions("portalconsole:Company:edit")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Company company)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        company.setUpdateBy(userNickName.getData());
        return toAjax(companyService.updateCompany(company));
    }

    /**
     * 审核企业信息
     */
    @RequiresPermissions("portalconsole:Company:audit")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody Company company)
    {
        return toAjax(companyService.auditCompany(company));
    }

    /**
     * 批量审核企业信息
     */
    @RequiresPermissions("portalconsole:Company:audit")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PutMapping("/auditBatch")
    public AjaxResult auditBatch(@RequestBody CompanyVO company)
    {
        return toAjax(companyService.auditCompanyBatch(company));
    }

    /**
     * 删除企业信息
     */
    @RequiresPermissions("portalconsole:Company:remove")
    @Log(title = "企业信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{companyIds}")
    public AjaxResult remove(@PathVariable Long[] companyIds)
    {
        return toAjax(companyService.deleteCompanyByCompanyIds(companyIds));
    }
}
