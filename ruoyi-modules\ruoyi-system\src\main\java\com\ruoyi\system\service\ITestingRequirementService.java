package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.TestingRequirement;

/**
 * 检测需求Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-08
 */
public interface ITestingRequirementService 
{
    /**
     * 查询检测需求
     * 
     * @param id 检测需求主键
     * @return 检测需求
     */
    public TestingRequirement selectTestingRequirementById(Long id);

    /**
     * 查询检测需求列表
     * 
     * @param testingRequirement 检测需求
     * @return 检测需求集合
     */
    public List<TestingRequirement> selectTestingRequirementList(TestingRequirement testingRequirement);

    /**
     * 新增检测需求
     * 
     * @param testingRequirement 检测需求
     * @return 结果
     */
    public int insertTestingRequirement(TestingRequirement testingRequirement);

    /**
     * 修改检测需求
     * 
     * @param testingRequirement 检测需求
     * @return 结果
     */
    public int updateTestingRequirement(TestingRequirement testingRequirement);

    /**
     * 批量删除检测需求
     * 
     * @param ids 需要删除的检测需求主键集合
     * @return 结果
     */
    public int deleteTestingRequirementByIds(Long[] ids);

    /**
     * 删除检测需求信息
     * 
     * @param id 检测需求主键
     * @return 结果
     */
    public int deleteTestingRequirementById(Long id);
}
