package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.SolutionType;
import com.ruoyi.portalweb.service.ISolutionTypeService;
import com.ruoyi.portalweb.vo.SolutionTypeVO;
import com.ruoyi.system.api.RemoteUserService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 解决方案类型Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/solutionType")
public class SolutionTypeController extends BaseController
{
    @Autowired
    private ISolutionTypeService solutionTypeService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询解决方案类型列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SolutionType solutionType)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<SolutionTypeVO> list = solutionTypeService.selectSolutionTypeList(solutionType);
        return getDataTable(list);
    }

    /**
     * 查询解决方案类型列表
     */
    @GetMapping("/listDesk")
    public TableDataInfo listDesk(SolutionType solutionType)
    {
//        startPage();
//        PageUtils.setOrderBy("create_time DESC");
        List<SolutionTypeVO> list = solutionTypeService.selectSolutionTypeList(solutionType);
        return getDataTable(list);
    }

    /**
	 * 顶级列表
	 */
	@GetMapping("/parent-list")
    @ApiImplicitParams({
			@ApiImplicitParam(name = "category", value = "服务范围(1服务解决方案;2领域解决方案)", paramType = "query", dataType = "Long"),
	})
	@ApiOperation(value = "顶级列表", notes = "传入")
	public TableDataInfo parentList(SolutionType solutionType) {
		startPage();
        PageUtils.setDefaultOrderBy();
        List<SolutionTypeVO> list = solutionTypeService.parentList(solutionType);
        return getDataTable(list);
	}

    /**
     * 导出解决方案类型列表
     */
    @Log(title = "解决方案类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SolutionType solutionType)
    {
        List<SolutionTypeVO> list = solutionTypeService.selectSolutionTypeList(solutionType);
        ExcelUtil<SolutionTypeVO> util = new ExcelUtil<SolutionTypeVO>(SolutionTypeVO.class);
        util.exportExcel(response, list, "解决方案类型数据");
    }

    /**
     * 获取解决方案类型详细信息
     */
    @GetMapping(value = "/{solutionTypeId}")
    public AjaxResult getInfo(@PathVariable("solutionTypeId") Long solutionTypeId)
    {
        return success(solutionTypeService.selectSolutionTypeBySolutionTypeId(solutionTypeId));
    }

    /**
     * 新增解决方案类型
     */
    @Log(title = "解决方案类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SolutionType solutionType)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        solutionType.setUpdateBy(userNickName.getData());
        solutionType.setCreateBy(userNickName.getData());
        return toAjax(solutionTypeService.insertSolutionType(solutionType));
    }

    /**
     * 修改解决方案类型
     */
    @Log(title = "解决方案类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SolutionType solutionType)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        solutionType.setUpdateBy(userNickName.getData());
        return toAjax(solutionTypeService.updateSolutionType(solutionType));
    }

    /**
     * 删除解决方案类型
     */
    @Log(title = "解决方案类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{solutionTypeIds}")
    public AjaxResult remove(@PathVariable Long[] solutionTypeIds)
    {
        return toAjax(solutionTypeService.deleteSolutionTypeBySolutionTypeIds(solutionTypeIds));
    }

    /**
     * 典型案例分类列表
     */
    @GetMapping("/listClassicCase")
    @ApiOperation(value = "典型案例分类列表", notes = "传入")
    public TableDataInfo listClassicCase(SolutionType solutionType)
    {
        startPage();
//        PageUtils.setDefaultOrderBy();
        List<SolutionTypeVO> list = solutionTypeService.classicCaseList(solutionType);
        return getDataTable(list);
    }

    /**
     * 解决方案分类列表
     */
    @GetMapping("/listSolution")
    @ApiOperation(value = "解决方案分类列表", notes = "传入")
    public TableDataInfo listSolution(SolutionType solutionType)
    {
        startPage();
//        PageUtils.setDefaultOrderBy();
        List<SolutionTypeVO> list = solutionTypeService.solutionList(solutionType);
        return getDataTable(list);
    }
}
