package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.service.ILabTestingRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.LabTestingRelationMapper;
import com.ruoyi.system.domain.LabTestingRelation;

/**
 * 实验室检测项目关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Service
public class LabTestingRelationServiceImpl implements ILabTestingRelationService
{
    @Autowired
    private LabTestingRelationMapper labTestingRelationMapper;

    /**
     * 查询实验室检测项目关联
     *
     * @param id 实验室检测项目关联主键
     * @return 实验室检测项目关联
     */
    @Override
    public LabTestingRelation selectLabTestingRelationById(Long id)
    {
        return labTestingRelationMapper.selectLabTestingRelationById(id);
    }

    /**
     * 查询实验室检测项目关联列表
     *
     * @param labTestingRelation 实验室检测项目关联
     * @return 实验室检测项目关联
     */
    @Override
    public List<LabTestingRelation> selectLabTestingRelationList(LabTestingRelation labTestingRelation)
    {
        return labTestingRelationMapper.selectLabTestingRelationList(labTestingRelation);
    }

    /**
     * 新增实验室检测项目关联
     *
     * @param labTestingRelation 实验室检测项目关联
     * @return 结果
     */
    @Override
    public int insertLabTestingRelation(LabTestingRelation labTestingRelation)
    {
        labTestingRelation.setCreateTime(DateUtils.getNowDate());
        return labTestingRelationMapper.insertLabTestingRelation(labTestingRelation);
    }

    /**
     * 修改实验室检测项目关联
     *
     * @param labTestingRelation 实验室检测项目关联
     * @return 结果
     */
    @Override
    public int updateLabTestingRelation(LabTestingRelation labTestingRelation)
    {
        labTestingRelation.setUpdateTime(DateUtils.getNowDate());
        return labTestingRelationMapper.updateLabTestingRelation(labTestingRelation);
    }

    /**
     * 批量删除实验室检测项目关联
     *
     * @param ids 需要删除的实验室检测项目关联主键
     * @return 结果
     */
    @Override
    public int deleteLabTestingRelationByIds(Long[] ids)
    {
        return labTestingRelationMapper.deleteLabTestingRelationByIds(ids);
    }

    /**
     * 删除实验室检测项目关联信息
     *
     * @param id 实验室检测项目关联主键
     * @return 结果
     */
    @Override
    public int deleteLabTestingRelationById(Long id)
    {
        return labTestingRelationMapper.deleteLabTestingRelationById(id);
    }
}
