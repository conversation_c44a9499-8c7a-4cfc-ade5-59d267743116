<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司邮箱" prop="companyEmail">
        <el-input
          v-model="queryParams.companyEmail"
          placeholder="请输入公司邮箱"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="企业类型" prop="companyOrgType">
        <el-input
          v-model="queryParams.companyOrgType"
          placeholder="请输入企业类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="社会统一信用代码" prop="socialUnityCreditCode">
        <el-input
          v-model="queryParams.socialUnityCreditCode"
          placeholder="请输入社会统一信用代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="服务行业" prop="serviceIndustry">
        <el-input
          v-model="queryParams.serviceIndustry"
          placeholder="请输入服务行业"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="企业规模" prop="companySize">
        <el-input
          v-model="queryParams.companySize"
          placeholder="请输入企业规模"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="联系电话" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="详细地址" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入详细地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="注册资金" prop="registeredCapital">
        <el-input
          v-model="queryParams.registeredCapital"
          placeholder="请输入注册资金"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司法人" prop="companyLegal">
        <el-input
          v-model="queryParams.companyLegal"
          placeholder="请输入公司法人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="成立日期" prop="establishTime">
        <el-input
          v-model="queryParams.establishTime"
          placeholder="请输入成立日期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="注册号" prop="regNumber">
        <el-input
          v-model="queryParams.regNumber"
          placeholder="请输入注册号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="组织机构代码" prop="orgNumber">
        <el-input
          v-model="queryParams.orgNumber"
          placeholder="请输入组织机构代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="实际注册资金" prop="actualCapital">
        <el-input
          v-model="queryParams.actualCapital"
          placeholder="请输入实际注册资金"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:portalconsole:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['portalconsole:portalconsole:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:portalconsole:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:portalconsole:export']"
        >导出</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport1"
          v-hasPermi="['portalconsole:portalconsole:export']"
        >导入模板</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleup"
          v-hasPermi="['portalconsole:portalconsole:export']"
        >批量导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="portalconsoleList" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="关联公司编号" align="center" prop="companyRelatedId" />
      <el-table-column label="公司名称" align="center" prop="companyName" />
      <el-table-column label="公司邮箱" align="center" prop="companyEmail" />
      <el-table-column label="营业执照" align="center" prop="businessLicenseImageUrl" />
      <el-table-column label="社会统一信用代码" align="center" prop="socialUnityCreditCode" />
      <el-table-column label="服务行业" align="center" prop="serviceIndustry" />
      <el-table-column label="企业规模" align="center" prop="companySize" />
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="详细地址" align="center" prop="address" />
      <el-table-column label="注册资金" align="center" prop="registeredCapital" />
      <el-table-column label="企业简介" align="center" prop="intrduction" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="公司法人" align="center" prop="companyLegal" />
      <el-table-column label="成立日期" align="center" prop="establishTime" />
      <el-table-column label="企业状态" align="center" prop="status" />
      <el-table-column label="注册号" align="center" prop="regNumber" />
      <el-table-column label="组织机构代码" align="center" prop="orgNumber" />
      <el-table-column label="企业类型" align="center" prop="companyOrgType" />
      <el-table-column label="曾用名" align="center" prop="historyNameList" />
      <el-table-column label="标签" align="center" prop="label" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_label_campany" :value="scope.row.label" />
        </template>
      </el-table-column>
      <el-table-column label="实际注册资金" align="center" prop="actualCapital" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:portalconsole:edit']"
          >修改</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:portalconsole:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改关联企业信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="form.companyName" placeholder="请输入公司名称" disabled/>
        </el-form-item>
        <el-form-item label="公司邮箱" prop="companyEmail">
          <el-input v-model="form.companyEmail" placeholder="请输入公司邮箱" disabled/>
        </el-form-item>
        <el-form-item label="营业执照" prop="businessLicenseImageUrl">
          <el-input v-model="form.businessLicenseImageUrl" placeholder="请输入营业执照" disabled/>
        </el-form-item>
        <el-form-item label="社会统一信用代码" prop="socialUnityCreditCode">
          <el-input v-model="form.socialUnityCreditCode" placeholder="请输入社会统一信用代码" disabled />
        </el-form-item>
        <el-form-item label="服务行业" prop="serviceIndustry">
          <el-input v-model="form.serviceIndustry" placeholder="请输入服务行业" disabled/>
        </el-form-item>
        <el-form-item label="企业规模" prop="companySize">
          <el-input v-model="form.companySize" placeholder="请输入企业规模" disabled />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话" disabled/>
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入详细地址" disabled/>
        </el-form-item>
        <el-form-item label="注册资金" prop="registeredCapital">
          <el-input v-model="form.registeredCapital" placeholder="请输入注册资金" disabled/>
        </el-form-item>
        <el-form-item label="企业简介" prop="intrduction">
          <el-input v-model="form.intrduction" type="textarea" placeholder="请输入内容" disabled/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" disabled/>
        </el-form-item>
        <el-form-item label="公司法人" prop="companyLegal">
          <el-input v-model="form.companyLegal" placeholder="请输入公司法人" disabled/>
        </el-form-item>
        <el-form-item label="成立日期" prop="establishTime">
          <el-input v-model="form.establishTime" placeholder="请输入成立日期" disabled/>
        </el-form-item>
        <el-form-item label="注册号" prop="regNumber">
          <el-input v-model="form.regNumber" placeholder="请输入注册号" disabled/>
        </el-form-item>
        <el-form-item label="组织机构代码" prop="orgNumber">
          <el-input v-model="form.orgNumber" placeholder="请输入组织机构代码" disabled/>
        </el-form-item>
        <el-form-item label="曾用名" prop="historyNameList">
          <el-input v-model="form.historyNameList" type="textarea" placeholder="请输入内容" disabled/>
        </el-form-item>
        <el-form-item label="实际注册资金" prop="actualCapital">
          <el-input v-model="form.actualCapital" placeholder="请输入实际注册资金" disabled/>
        </el-form-item>
        <el-form-item label="标签" prop="label">
          <el-select v-model="form.label" placeholder="请选择">
                <el-option v-for="dict in dict.type.sys_label_campany" :key="dict.value"
                :label="dict.label"
                :value="dict.value"></el-option>
              </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 批量导入 -->
     <el-dialog title="导入数据" :visible.sync="openDialog" width="70%" append-to-body>
      <el-form ref="form1" :model="form1" :rules="rules1" label-width="90px">
        <el-form-item label="附件" prop="file">
          <el-upload class="upload-demo"
             :file-list='fileAddList' :limit='1'
              action=""  
              v-model="form1.file"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :http-request="uploadFile" >
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">只能上传.xlsx文件</div>
            </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm1('form1')">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
        </el-form-item>
      </el-form>
      
     </el-dialog>
  </div>
</template>

<script>
import { listPortalconsole, getPortalconsole, delPortalconsole, addPortalconsole, updatePortalconsole,importTemplate,importBatch } from "@/api/portalconsole/portalconsole";
import {comUpload} from "@/api/portalconsole/uploadApi";
export default {
  name: "Portalconsole",
  dicts: ['sys_label_campany'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 关联企业信息表格数据
      portalconsoleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null,
        companyEmail: null,
        businessLicenseImageUrl: null,
        socialUnityCreditCode: null,
        serviceIndustry: null,
        companySize: null,
        phone: null,
        address: null,
        registeredCapital: null,
        intrduction: null,
        companyLegal: null,
        establishTime: null,
        status: null,
        regNumber: null,
        orgNumber: null,
        companyOrgType: null,
        historyNameList: null,
        actualCapital: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      openDialog:false,
      fileAddList:[],
      form1:{
        file:''
      },
      rules1:{
        file: [
          { required: true, message: "请上传附件", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询关联企业信息列表 */
    getList() {
      this.loading = true;
      listPortalconsole(this.queryParams).then(response => {
        this.portalconsoleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        companyRelatedId: null,
        companyName: null,
        companyEmail: null,
        businessLicenseImageUrl: null,
        socialUnityCreditCode: null,
        serviceIndustry: null,
        companySize: null,
        phone: null,
        address: null,
        registeredCapital: null,
        intrduction: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        delFlag: null,
        companyLegal: null,
        establishTime: null,
        status: null,
        regNumber: null,
        orgNumber: null,
        companyOrgType: null,
        historyNameList: null,
        actualCapital: null,
        label:null

      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.companyRelatedId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加关联企业信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const companyRelatedId = row.companyRelatedId || this.ids
      getPortalconsole(companyRelatedId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改关联企业信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.companyRelatedId != null) {
            updatePortalconsole(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPortalconsole(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const companyRelatedIds = row.companyRelatedId || this.ids;
      this.$modal.confirm('是否确认删除关联企业信息编号为"' + companyRelatedIds + '"的数据项？').then(function() {
        return delPortalconsole(companyRelatedIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/portalconsole/export', {
        ...this.queryParams
      }, `portalconsole_${new Date().getTime()}.xlsx`)
    },
    //导出模板
    handleExport1(){
      importTemplate().then(res=>{
        console.log("res",res)
        const url = window.URL.createObjectURL(new Blob([res])) // 创建下载链接
        const link = document.createElement('a')
        link.href = url
        link.download = '企业信息模板.xlsx' // 设置文件名
        document.body.appendChild(link)
        link.click() // 触发下载
        document.body.removeChild(link) // 下载后移除元素
                  
      })
    },
    //批量导入
    handleup(){
      this.openDialog=true
    },
     //附件上传
     handleRemove(file, fileList) {
        this.fileAddList.splice(this.fileAddList.findIndex(item => item.id == file.id), 1)
        this.$set(this.form1,'file',this.fileAddList[0].fileUrl)  // 图片全路径
      },
      handlePreview(file) {
        console.log(file);
      },
      beforeRemove(file, fileList) {
        return this.$confirm(`确定移除 ${ file.name }？`);
      },
      uploadFile(params){
        const file = params.file;
        this.form1.file=file
       
        // comUpload(form).then(res => {
        //   let data = res.data;
        //   console.log("data",data)
        //   this.fileAddList.push({
        //     name:data.fileName,
        //     id:data.fileId,
        //     fileUrl:data.fileFullPath
        //   })
          
        //   this.$set(this.form1,'file',this.fileAddList[0].fileUrl)  // 图片全路径
        // })
      },
      submitForm1(formName){
        this.$refs[formName].validate((valid) => {
          if (valid) {  
            let form = new FormData();
            form.append("file", this.form1.file); // 文件对象
            importBatch(form).then(res=>{
                if(res.code==200){
                  this.$message.success('导入成功')
                  this.openDialog=false
                  this.getList()
                }
              })
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      //


  }
};
</script>
