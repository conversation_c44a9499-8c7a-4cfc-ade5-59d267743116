<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.SupplyMapper">

    <resultMap type="com.ruoyi.portalweb.vo.SupplyVO" id="SupplyResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="applicationArea" column="application_area"/>
        <result property="technologyCategory" column="technology_category"/>
        <result property="description" column="description"/>
        <result property="matchDemand" column="match_demand"/>
        <result property="process" column="process"/>
        <result property="productType" column="product_type"/>
        <result property="cooperationType" column="cooperation_type"/>
        <result property="organization" column="organization"/>
        <result property="imageUrl" column="image_url"/>
        <result property="attachment" column="attachment"/>
        <result property="publisher" column="publisher"/>
        <result property="phone" column="phone"/>
        <result property="contact" column="contact"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="recommend" column="recommend"/>
        <result property="onShow" column="on_show"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberId" column="member_id"/>
        <result property="viewCount" column="view_count"/>

        <result property="imageUrlPath" column="image_url_path"/>
        <result property="typeName" column="type_name"/>
        <result property="applicationAreaName" column="application_area_name"/>
        <result property="technologyCategoryName" column="technology_category_name"/>
        <result property="supplyCooperationName" column="supply_cooperation_name"/>
        <result property="supplyProcessName" column="supply_process_name"/>
        <result property="auditStatusName" column="audit_status_name"/>
        <result property="productTypeName" column="product_type_name"/>
    </resultMap>

    <sql id="selectSupply">
        select id, title, type, application_area, technology_category,description,match_demand,process,product_type,cooperation_type, organization,
               image_url, attachment, publisher, phone, contact, audit_status, recommend, on_show,
               create_by, create_time, update_by, update_time, remark,  member_id, view_count from supply
    </sql>

    <sql id="selectSupplyVo">
        select a.*,b.file_full_path as image_url_path
        from supply a
        left join sys_file_info b on a.image_url = b.file_id
    </sql>

    <sql id="Base_Column_List">
		a.*, b.file_full_path AS image_url_path, c.dict_label AS type_name, d.application_area_name AS application_area_name
        , e.dict_label AS technology_category_name, s1.dict_label AS supply_cooperation_name, s2.dict_label AS supply_process_name,
		s3.dict_label as audit_status_name
	</sql>

	<sql id="Base_Table_List">
		FROM supply a
        LEFT JOIN sys_file_info b ON a.image_url = b.file_id
        LEFT JOIN sys_dict_data c ON a.type = c.dict_value AND c.dict_type = 'supply_type'
        LEFT JOIN (
            SELECT de.id, GROUP_CONCAT(af.application_field_name) application_area_name
            FROM supply de
            JOIN application_field af ON FIND_IN_SET(af.application_field_id,de.application_area)
            GROUP BY de.id
        ) d on a.id = d.id
                LEFT JOIN (
            SELECT de.id, GROUP_CONCAT(sy.dict_label) dict_label
            FROM supply de
            JOIN sys_dict_data sy ON FIND_IN_SET(sy.dict_value,de.technology_category) AND sy.dict_type = 'technology_category'
            GROUP BY de.id
        ) e on a.id = e.id
        LEFT JOIN sys_dict_data s1 ON a.cooperation_type = s1.dict_value AND s1.dict_type = 'supply_cooperation'
        LEFT JOIN sys_dict_data s2 ON a.process = s2.dict_value AND s2.dict_type = 'supply_process'
		LEFT JOIN sys_dict_data s3 ON a.audit_status = s3.dict_value AND s3.dict_type = 'demand_status'
	</sql>

    <select id="selectSupplyList" parameterType="SupplyVO" resultMap="SupplyResult">
        SELECT
		<include refid="Base_Column_List" />
		,(select GROUP_CONCAT(sdd.dict_label) from sys_dict_data sdd where FIND_IN_SET(sdd.dict_value,a.product_type) and sdd.dict_type='product_category') product_type_name 
		<include refid="Base_Table_List" />
        <where>
            <if test="title != null  and title != ''">and a.title like concat('%', #{title}, '%')</if>
            <if test="type != null  and type != ''">and a.type = #{type}</if>
            <if test="applicationArea != null  and applicationArea != ''">
                and a.application_area like concat('%', #{applicationArea}, '%')
            </if>
            <if test="technologyCategory != null  and technologyCategory != ''">
                and a.technology_category like concat('%', #{technologyCategory}, '%')
            </if>
            <if test="matchDemand != null  and matchDemand != ''">and a.match_demand = #{matchDemand}</if>
            <if test="process != null  and process != ''">and a.process = #{process}</if>
            <if test="productType != null  and productType != ''">
            	and FIND_IN_SET(#{productType},a.product_type)
            </if>
            
            <if test="cooperationType != null  and cooperationType != ''">and a.cooperation_type = #{cooperationType}</if>
            <if test="organization != null  and organization != ''">and a.organization = #{organization}</if>
            <if test="publisher != null  and publisher != ''">and a.publisher = #{publisher}</if>
            <if test="phone != null  and phone != ''">and a.phone = #{phone}</if>
            <if test="auditStatus != null ">and a.audit_status = #{auditStatus}</if>
            <if test="createTime != null ">and a.crezte_time = #{createTime}</if>
            <if test="onShow != null ">and a.on_show = #{onShow}</if>
            <if test="memberId != null ">and a.member_id = #{memberId}</if>
            <if test="viewCount != null ">and a.view_count = #{viewCount}</if>
            <if test="startTime != null ">and a.create_time &gt;= #{startTime}</if>
            <if test="endTime != null ">and a.create_time &lt;= #{endTime}</if>
            <if test="keyword != null ">and ( a.title like concat('%', #{keyword}, '%') or a.organization like concat('%', #{keyword}, '%') or  a.description like concat('%', #{keyword}, '%') or a.application_area like concat('%', #{keyword}, '%'))</if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <select id="selectSupplyById" parameterType="Long" resultMap="SupplyResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        where a.id = #{id}
    </select>
    <select id="selectDemandListByMemberIds" resultMap="SupplyResult"
            parameterType="List">
    SELECT
        <include refid="Base_Column_List"></include>
        <include refid="Base_Table_List"></include>
        where a.on_show = 0 AND a.audit_status = 2 AND
              a.member_id IN
        <foreach collection="list" open="(" close=")" separator="," item="memberId">
            #{memberId}
        </foreach>
    </select>
    <select id="selectSupplyListBySupplyIds" resultType="com.ruoyi.portalweb.api.domain.Supply"
            parameterType="java.util.List">
        <include refid="selectSupply"/>
        <where>
            id IN
            <foreach collection="list" open="(" separator="," close=")" item="id">#{id}</foreach>
        </where>
    </select>

    <insert id="insertSupply" parameterType="Supply" useGeneratedKeys="true" keyProperty="id">
        insert into supply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="type != null">type,</if>
            <if test="applicationArea != null">application_area,</if>
            <if test="technologyCategory != null">technology_category,</if>
            <if test="description != null">description,</if>
            <if test="matchDemand != null">match_demand,</if>
            <if test="process != null">process,</if>
            <if test="productType != null">product_type,</if>
            <if test="cooperationType != null">cooperation_type,</if>
            <if test="organization != null">organization,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="attachment != null">attachment,</if>
            <if test="publisher != null">publisher,</if>
            <if test="phone != null">phone,</if>
            <if test="contact != null">contact,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="recommend != null">recommend,</if>
            <if test="onShow != null">on_show,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="memberId != null">member_id,</if>
            <if test="viewCount != null">view_count,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="type != null">#{type},</if>
            <if test="applicationArea != null">#{applicationArea},</if>
            <if test="technologyCategory != null">#{technologyCategory},</if>
            <if test="description != null">#{description},</if>
            <if test="matchDemand != null">#{matchDemand},</if>
            <if test="process != null">#{process},</if>
            <if test="productType != null">#{productType},</if>
            <if test="cooperationType != null">#{cooperationType},</if>
            <if test="organization != null">#{organization},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="attachment != null">#{attachment},</if>
            <if test="publisher != null">#{publisher},</if>
            <if test="phone != null">#{phone},</if>
            <if test="contact != null">#{contact},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="recommend != null">#{recommend},</if>
            <if test="onShow != null">#{onShow},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="viewCount != null">#{viewCount},</if>
        </trim>
    </insert>
    <update id="addSupplyViewCount" parameterType="java.lang.Long">
        update supply set view_count = view_count + 1 where id = #{id}
    </update>

    <update id="updateSupply" parameterType="Supply">
        update supply
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="type != null">type = #{type},</if>
            <if test="applicationArea != null">application_area = #{applicationArea},</if>
            <if test="technologyCategory != null">technology_category = #{technologyCategory},</if>
            <if test="description != null">description = #{description},</if>
            <if test="matchDemand != null">match_demand = #{matchDemand},</if>
            <if test="process != null">process = #{process},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="cooperationType != null">cooperation_type = #{cooperationType},</if>
            <if test="organization != null">organization = #{organization},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="attachment != null">attachment = #{attachment},</if>
            <if test="publisher != null">publisher = #{publisher},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="recommend != null">recommend = #{recommend},</if>
            <if test="onShow != null">on_show = #{onShow},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplyById" parameterType="Long">
        delete from supply where id = #{id}
    </delete>

    <delete id="deleteSupplyByIds" parameterType="String">
        delete from supply where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>