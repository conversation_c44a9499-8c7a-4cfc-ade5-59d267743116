package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.OutsourcingRequirementMapper;
import com.ruoyi.system.domain.OutsourcingRequirement;
import com.ruoyi.system.service.IOutsourcingRequirementService;

/**
 * 工序外协需求Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-08
 */
@Service
public class OutsourcingRequirementServiceImpl implements IOutsourcingRequirementService 
{
    @Autowired
    private OutsourcingRequirementMapper outsourcingRequirementMapper;

    /**
     * 查询工序外协需求
     * 
     * @param id 工序外协需求主键
     * @return 工序外协需求
     */
    @Override
    public OutsourcingRequirement selectOutsourcingRequirementById(Long id)
    {
        return outsourcingRequirementMapper.selectOutsourcingRequirementById(id);
    }

    /**
     * 查询工序外协需求列表
     * 
     * @param outsourcingRequirement 工序外协需求
     * @return 工序外协需求
     */
    @Override
    public List<OutsourcingRequirement> selectOutsourcingRequirementList(OutsourcingRequirement outsourcingRequirement)
    {
        return outsourcingRequirementMapper.selectOutsourcingRequirementList(outsourcingRequirement);
    }

    /**
     * 新增工序外协需求
     * 
     * @param outsourcingRequirement 工序外协需求
     * @return 结果
     */
    @Override
    public int insertOutsourcingRequirement(OutsourcingRequirement outsourcingRequirement)
    {
        outsourcingRequirement.setCreateTime(DateUtils.getNowDate());
        return outsourcingRequirementMapper.insertOutsourcingRequirement(outsourcingRequirement);
    }

    /**
     * 修改工序外协需求
     * 
     * @param outsourcingRequirement 工序外协需求
     * @return 结果
     */
    @Override
    public int updateOutsourcingRequirement(OutsourcingRequirement outsourcingRequirement)
    {
        outsourcingRequirement.setUpdateTime(DateUtils.getNowDate());
        return outsourcingRequirementMapper.updateOutsourcingRequirement(outsourcingRequirement);
    }

    /**
     * 批量删除工序外协需求
     * 
     * @param ids 需要删除的工序外协需求主键
     * @return 结果
     */
    @Override
    public int deleteOutsourcingRequirementByIds(Long[] ids)
    {
        return outsourcingRequirementMapper.deleteOutsourcingRequirementByIds(ids);
    }

    /**
     * 删除工序外协需求信息
     * 
     * @param id 工序外协需求主键
     * @return 结果
     */
    @Override
    public int deleteOutsourcingRequirementById(Long id)
    {
        return outsourcingRequirementMapper.deleteOutsourcingRequirementById(id);
    }
}
