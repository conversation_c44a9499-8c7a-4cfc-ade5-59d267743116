package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.FactoryQualification;

/**
 * 工厂资质证件Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface IFactoryQualificationService 
{
    /**
     * 查询工厂资质证件
     * 
     * @param id 工厂资质证件主键
     * @return 工厂资质证件
     */
    public FactoryQualification selectFactoryQualificationById(Long id);

    /**
     * 查询工厂资质证件列表
     * 
     * @param factoryQualification 工厂资质证件
     * @return 工厂资质证件集合
     */
    public List<FactoryQualification> selectFactoryQualificationList(FactoryQualification factoryQualification);

    /**
     * 新增工厂资质证件
     * 
     * @param factoryQualification 工厂资质证件
     * @return 结果
     */
    public int insertFactoryQualification(FactoryQualification factoryQualification);

    /**
     * 修改工厂资质证件
     * 
     * @param factoryQualification 工厂资质证件
     * @return 结果
     */
    public int updateFactoryQualification(FactoryQualification factoryQualification);

    /**
     * 批量删除工厂资质证件
     * 
     * @param ids 需要删除的工厂资质证件主键集合
     * @return 结果
     */
    public int deleteFactoryQualificationByIds(Long[] ids);

    /**
     * 删除工厂资质证件信息
     * 
     * @param id 工厂资质证件主键
     * @return 结果
     */
    public int deleteFactoryQualificationById(Long id);
}
