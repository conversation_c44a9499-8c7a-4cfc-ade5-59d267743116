package com.ruoyi.portalconsole.service;

import java.util.List;
import com.ruoyi.portalconsole.domain.Solution;
import com.ruoyi.portalconsole.domain.vo.SolutionVO;

/**
 * 解决方案Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface ISolutionService 
{
    /**
     * 查询解决方案
     * 
     * @param solutionId 解决方案主键
     * @return 解决方案
     */
    public SolutionVO selectSolutionBySolutionId(Long solutionId);

    /**
     * 查询解决方案
     *
     * @param solutionId 解决方案主键
     * @return 解决方案
     */
    public SolutionVO selectSolutionDetailBySolutionId(Long solutionId);

    /**
     * 查询解决方案列表
     * 
     * @param solution 解决方案
     * @return 解决方案集合
     */
    public List<SolutionVO> selectSolutionList(Solution solution);

    /**
     * 新增解决方案
     * 
     * @param solutionVO 解决方案
     * @return 结果
     */
    public int insertSolution(SolutionVO solutionVO);

    /**
     * 修改解决方案
     * 
     * @param solution 解决方案
     * @return 结果
     */
    public int updateSolution(Solution solution);

    /**
     * 修改解决方案详情
     *
     * @param solutionVO 解决方案
     * @return 结果
     */
    public int updateSolutionDetail(SolutionVO solutionVO);

    /**
     * 批量删除解决方案
     * 
     * @param solutionIds 需要删除的解决方案主键集合
     * @return 结果
     */
    public int deleteSolutionBySolutionIds(Long[] solutionIds);

    /**
     * 删除解决方案信息
     * 
     * @param solutionId 解决方案主键
     * @return 结果
     */
    public int deleteSolutionBySolutionId(Long solutionId);
}
