package com.ruoyi.portalweb.mapper;

import com.ruoyi.portalweb.api.domain.SolutionPain;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 解决方案行业痛点Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface SolutionPainMapper 
{
    /**
     * 查询解决方案行业痛点
     * 
     * @param solutionPainId 解决方案行业痛点主键
     * @return 解决方案行业痛点
     */
    public SolutionPain selectSolutionPainBySolutionPainId(Long solutionPainId);

    /**
     * 查询解决方案行业痛点列表
     * 
     * @param solutionPain 解决方案行业痛点
     * @return 解决方案行业痛点集合
     */
    public List<SolutionPain> selectSolutionPainList(SolutionPain solutionPain);

    /**
	 * 查询列表按解决方案id
	 */
	public List<SolutionPain> selectListBySolutionId(@Param("solutionId") Long solutionId);

    /**
     * 新增解决方案行业痛点
     * 
     * @param solutionPain 解决方案行业痛点
     * @return 结果
     */
    public int insertSolutionPain(SolutionPain solutionPain);

    /**
     * 新增解决方案行业痛点
     *
     * @param solutionPainList 解决方案行业痛点
     * @return 结果
     */
    public int insertSolutionPainList(List<SolutionPain> solutionPainList);

    /**
     * 修改解决方案行业痛点
     * 
     * @param solutionPain 解决方案行业痛点
     * @return 结果
     */
    public int updateSolutionPain(SolutionPain solutionPain);

    /**
     * 删除解决方案行业痛点
     * 
     * @param solutionPainId 解决方案行业痛点主键
     * @return 结果
     */
    public int deleteSolutionPainBySolutionPainId(Long solutionPainId);

    /**
     * 批量删除解决方案行业痛点
     * 
     * @param solutionPainIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSolutionPainBySolutionPainIds(Long[] solutionPainIds);

    /**
     * 批量删除解决方案行业痛点
     *
     * @param solutionId 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSolutionPainBySolutionId(Long solutionId);


    /**
     * 批量删除解决方案行业痛点
     *
     * @param solutionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSolutionPainBySolutionIds(Long[] solutionIds);

    public List<SolutionPain> selectSolutionPainListBySolutionIds(List<Long> solutionIds);
}
