package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.ProcessOutsourcing;
import com.ruoyi.system.service.IProcessOutsourcingService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 工序外协Controller
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@RestController
@RequestMapping("/processOutsourcing")
public class ProcessOutsourcingController extends BaseController
{
    @Autowired
    private IProcessOutsourcingService processOutsourcingService;

    /**
     * 查询工序外协列表
     */

    @GetMapping("/list")
    public TableDataInfo list(ProcessOutsourcing processOutsourcing)
    {
        startPage();
        List<ProcessOutsourcing> list = processOutsourcingService.selectProcessOutsourcingList(processOutsourcing);
        return getDataTable(list);
    }

    /**
     * 导出工序外协列表
     */

    @Log(title = "工序外协", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcessOutsourcing processOutsourcing)
    {
        List<ProcessOutsourcing> list = processOutsourcingService.selectProcessOutsourcingList(processOutsourcing);
        ExcelUtil<ProcessOutsourcing> util = new ExcelUtil<ProcessOutsourcing>(ProcessOutsourcing.class);
        util.exportExcel(response, list, "工序外协数据");
    }

    /**
     * 获取工序外协详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(processOutsourcingService.selectProcessOutsourcingById(id));
    }

    /**
     * 新增工序外协
     */

    @Log(title = "工序外协", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessOutsourcing processOutsourcing)
    {
        return toAjax(processOutsourcingService.insertProcessOutsourcing(processOutsourcing));
    }

    /**
     * 修改工序外协
     */

    @Log(title = "工序外协", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessOutsourcing processOutsourcing)
    {
        return toAjax(processOutsourcingService.updateProcessOutsourcing(processOutsourcing));
    }

    /**
     * 删除工序外协
     */

    @Log(title = "工序外协", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(processOutsourcingService.deleteProcessOutsourcingByIds(ids));
    }
}
