package com.ruoyi.portalconsole.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.SolutionTypeMapper;
import com.ruoyi.portalconsole.domain.SolutionType;
import com.ruoyi.portalconsole.service.ISolutionTypeService;

/**
 * 解决方案类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class SolutionTypeServiceImpl implements ISolutionTypeService 
{
    @Autowired
    private SolutionTypeMapper solutionTypeMapper;

    /**
     * 查询解决方案类型
     * 
     * @param solutionTypeId 解决方案类型主键
     * @return 解决方案类型
     */
    @Override
    public SolutionType selectSolutionTypeBySolutionTypeId(Long solutionTypeId)
    {
        return solutionTypeMapper.selectSolutionTypeBySolutionTypeId(solutionTypeId);
    }

    /**
     * 查询解决方案类型列表
     * 
     * @param solutionType 解决方案类型
     * @return 解决方案类型
     */
    @Override
    public List<SolutionType> selectSolutionTypeList(SolutionType solutionType)
    {
        return solutionTypeMapper.selectSolutionTypeList(solutionType);
    }

    /**
     * 新增解决方案类型
     * 
     * @param solutionType 解决方案类型
     * @return 结果
     */
    @Override
    public int insertSolutionType(SolutionType solutionType)
    {
        solutionType.setCreateTime(DateUtils.getNowDate());
        if(solutionType.getParentId() != null ){
            SolutionType parent = selectSolutionTypeBySolutionTypeId(solutionType.getParentId());
            solutionType.setCategory(parent.getCategory());
        }
        return solutionTypeMapper.insertSolutionType(solutionType);
    }

    /**
     * 修改解决方案类型
     * 
     * @param solutionType 解决方案类型
     * @return 结果
     */
    @Override
    public int updateSolutionType(SolutionType solutionType)
    {
        solutionType.setUpdateTime(DateUtils.getNowDate());
        return solutionTypeMapper.updateSolutionType(solutionType);
    }

    /**
     * 批量删除解决方案类型
     * 
     * @param solutionTypeIds 需要删除的解决方案类型主键
     * @return 结果
     */
    @Override
    public int deleteSolutionTypeBySolutionTypeIds(Long[] solutionTypeIds)
    {
        // 获取所有子级类型
        List<SolutionType> children = selectSolutionTypeAllChildren(solutionTypeIds);
        List<Long> solutionTypeIdsAll = new ArrayList<>();
        for(SolutionType solutionType : children){
            solutionTypeIdsAll.add(solutionType.getSolutionTypeId());
        }

        // 加入本级
        solutionTypeIdsAll.addAll(Arrays.asList(solutionTypeIds));
        // 一次性全部删除
        return solutionTypeMapper.deleteSolutionTypeBySolutionTypeIds(solutionTypeIdsAll.toArray(new Long[0]));
    }

    /**
     * 删除解决方案类型信息
     * 
     * @param solutionTypeId 解决方案类型主键
     * @return 结果
     */
    @Override
    public int deleteSolutionTypeBySolutionTypeId(Long solutionTypeId)
    {
        return solutionTypeMapper.deleteSolutionTypeBySolutionTypeId(solutionTypeId);
    }

    // 查询所有子级解决方案类型
    private List<SolutionType> selectSolutionTypeAllChildren(Long[] parentIds){
        List<SolutionType> solutionTypes = selectSolutionTypeByParentId(parentIds);
        List<Long> solutionTypeIds = new ArrayList<>();
        for(SolutionType solutionType : solutionTypes){
            solutionTypeIds.add(solutionType.getSolutionTypeId());
        }
        if (!solutionTypeIds.isEmpty()){
            List<SolutionType> children = selectSolutionTypeAllChildren(solutionTypeIds.toArray(new Long[0]));
            assert children != null;
            solutionTypes.addAll(children);
        }

        return solutionTypes;
    }

    private List<SolutionType> selectSolutionTypeByParentId(Long[] parentIds){
        return solutionTypeMapper.selectSolutionTypeByParentId(parentIds);
    }
}
