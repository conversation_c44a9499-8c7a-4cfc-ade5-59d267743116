package com.ruoyi.system.domain.dto;

import com.ruoyi.system.domain.ManufactureOrder;
import com.ruoyi.system.domain.MaterialInfo;
import com.ruoyi.system.domain.OrderMaterialRelation;

/**
 * 物料信息与订单关联数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public class MaterialWithOrderDTO extends MaterialInfo {
    
    /** 关联的订单物料关系 */
    private OrderMaterialRelation relation;
    
    /** 关联的制造订单 */
    private ManufactureOrder order;

    public OrderMaterialRelation getRelation() {
        return relation;
    }

    public void setRelation(OrderMaterialRelation relation) {
        this.relation = relation;
    }

    public ManufactureOrder getOrder() {
        return order;
    }

    public void setOrder(ManufactureOrder order) {
        this.order = order;
    }
}
