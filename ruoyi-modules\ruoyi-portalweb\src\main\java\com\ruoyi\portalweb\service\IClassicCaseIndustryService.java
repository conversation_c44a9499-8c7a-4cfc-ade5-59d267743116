package com.ruoyi.portalweb.service;

import java.util.List;
import com.ruoyi.portalweb.api.domain.ClassicCaseIndustry;
import com.ruoyi.portalweb.vo.ClassicCaseIndustryVO;

/**
 * 典型案例行业Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-07
 */
public interface IClassicCaseIndustryService 
{
    /**
     * 查询典型案例行业
     * 
     * @param classicCaseIndustryId 典型案例行业主键
     * @return 典型案例行业
     */
    public ClassicCaseIndustry selectClassicCaseIndustryByClassicCaseIndustryId(Long classicCaseIndustryId);

    /**
     * 查询典型案例行业列表
     * 
     * @param classicCaseIndustry 典型案例行业
     * @return 典型案例行业集合
     */
    public List<ClassicCaseIndustry> selectClassicCaseIndustryList(ClassicCaseIndustry classicCaseIndustry);

    /**
     * 查询典型案例行业列表
     *
     * @param classicCaseIndustry 典型案例行业
     * @return 典型案例行业集合
     */
    public List<ClassicCaseIndustryVO> classicCaseList(ClassicCaseIndustry classicCaseIndustry);

    /**
     * 新增典型案例行业
     * 
     * @param classicCaseIndustry 典型案例行业
     * @return 结果
     */
    public int insertClassicCaseIndustry(ClassicCaseIndustry classicCaseIndustry);

    /**
     * 修改典型案例行业
     * 
     * @param classicCaseIndustry 典型案例行业
     * @return 结果
     */
    public int updateClassicCaseIndustry(ClassicCaseIndustry classicCaseIndustry);

    /**
     * 批量删除典型案例行业
     * 
     * @param classicCaseIndustryIds 需要删除的典型案例行业主键集合
     * @return 结果
     */
    public int deleteClassicCaseIndustryByClassicCaseIndustryIds(Long[] classicCaseIndustryIds);

    /**
     * 删除典型案例行业信息
     * 
     * @param classicCaseIndustryId 典型案例行业主键
     * @return 结果
     */
    public int deleteClassicCaseIndustryByClassicCaseIndustryId(Long classicCaseIndustryId);
}
