package com.ruoyi.system.domain.dto;

import com.ruoyi.system.domain.LabTestingRelation;
import com.ruoyi.system.domain.TestingItem;

import java.util.List;

/**
 * 检测项目数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public class TestingItemDTO {
    
    /** 检测项目信息 */
    private TestingItem testingItem;
    
    /** 关联的实验室ID列表 */
    private List<Long> labIds;
    
    /** 实验室检测项目关联信息列表 */
    private List<LabTestingRelation> labTestingRelations;

    public TestingItem getTestingItem() {
        return testingItem;
    }

    public void setTestingItem(TestingItem testingItem) {
        this.testingItem = testingItem;
    }

    public List<Long> getLabIds() {
        return labIds;
    }

    public void setLabIds(List<Long> labIds) {
        this.labIds = labIds;
    }

    public List<LabTestingRelation> getLabTestingRelations() {
        return labTestingRelations;
    }

    public void setLabTestingRelations(List<LabTestingRelation> labTestingRelations) {
        this.labTestingRelations = labTestingRelations;
    }
}
