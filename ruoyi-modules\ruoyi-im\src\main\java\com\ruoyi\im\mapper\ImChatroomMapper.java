package com.ruoyi.im.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.im.api.domain.ImChatroom;
import com.ruoyi.im.mapper.provider.ImChatroomMapperProvider;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

public interface ImChatroomMapper extends BaseMapper<ImChatroom>
{

    @SelectProvider(type = ImChatroomMapperProvider.class, method = "queryPage")
    List<ImChatroom> queryPage(Page<ImChatroom> pageSearch,@Param("name") String name);
}
