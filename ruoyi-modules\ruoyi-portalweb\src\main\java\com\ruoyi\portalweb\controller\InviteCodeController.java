package com.ruoyi.portalweb.controller;

import java.util.HashMap;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.portalweb.vo.InviteCodeAmount;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalweb.api.domain.InviteCode;
import com.ruoyi.portalweb.service.IInviteCodeService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 企业邀请码Controller
 * 
 * <AUTHOR>
 * @date 2024-08-06
 */
@RestController
@RequestMapping("/invite-code")
@Api(value = "11.企业邀请码", tags = "11.企业邀请码")
public class InviteCodeController extends BaseController
{
    @Autowired
    private IInviteCodeService inviteCodeService;

    /**
     * 查询企业邀请码列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询企业邀请码列表", notes = "传入")
    public TableDataInfo list(InviteCode inviteCode)
    {
        List<InviteCode> list = inviteCodeService.selectInviteCodeList(inviteCode);
        return getDataTable(list);
    }

    /**
     * 导出企业邀请码列表
     */
    @RequiresPermissions("portalweb:invite-code:export")
    @Log(title = "企业邀请码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InviteCode inviteCode)
    {
        List<InviteCode> list = inviteCodeService.selectInviteCodeList(inviteCode);
        ExcelUtil<InviteCode> util = new ExcelUtil<InviteCode>(InviteCode.class);
        util.exportExcel(response, list, "企业邀请码数据");
    }

    /**
     * 获取企业邀请码详细信息
     */
    @GetMapping(value = "/{code}")
    @ApiOperation(value = "查询企业邀请码详情", notes = "传入")
    public AjaxResult getInfo(@PathVariable("code") String code)
    {
        return success(inviteCodeService.selectInviteCodeByCode(code));
    }

    /**
     * 生成企业邀请码
     */
    @Log(title = "企业邀请码", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "生成企业邀请码", notes = "传入")
    public AjaxResult add(@RequestBody InviteCodeAmount vo)
    {
        return AjaxResult.success(inviteCodeService.insertInviteCode(vo));
    }

    /**
     * 使用企业邀请码
     */
    @Log(title = "企业邀请码", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "使用企业邀请码", notes = "传入")
    public AjaxResult use(@RequestBody InviteCode inviteCode)
    {
        try{
            inviteCodeService.updateInviteCode(inviteCode);
        }catch(Exception e){
            if (e instanceof ServiceException){
                HashMap<String, Object> map = new HashMap<String, Object>();
                map.put("msg", e.getMessage());
                map.put("success", false);
                return AjaxResult.success(map);
            }else{
                throw e;
            }
        }
        return AjaxResult.success();
    }

    /**
     * 批量删除企业邀请码
     */
    @Log(title = "企业邀请码", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation(value = "批量删除企业邀请码", notes = "传入")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(inviteCodeService.deleteInviteCodeByIds(ids));
    }

    /**
     * 清除失效的企业邀请码
     */
    @Log(title = "企业邀请码", businessType = BusinessType.DELETE)
    @DeleteMapping
    @ApiOperation(value = "清除失效的企业邀请码")
    public AjaxResult removeInvalid()
    {
        return AjaxResult.success(inviteCodeService.deleteInvalidInviteCodes());
    }
}
