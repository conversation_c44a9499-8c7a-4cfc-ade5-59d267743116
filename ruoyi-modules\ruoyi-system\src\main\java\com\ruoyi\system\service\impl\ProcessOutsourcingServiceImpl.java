package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ProcessOutsourcingMapper;
import com.ruoyi.system.domain.ProcessOutsourcing;
import com.ruoyi.system.service.IProcessOutsourcingService;

/**
 * 工序外协Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class ProcessOutsourcingServiceImpl implements IProcessOutsourcingService 
{
    @Autowired
    private ProcessOutsourcingMapper processOutsourcingMapper;

    /**
     * 查询工序外协
     * 
     * @param id 工序外协主键
     * @return 工序外协
     */
    @Override
    public ProcessOutsourcing selectProcessOutsourcingById(Long id)
    {
        return processOutsourcingMapper.selectProcessOutsourcingById(id);
    }

    /**
     * 查询工序外协列表
     * 
     * @param processOutsourcing 工序外协
     * @return 工序外协
     */
    @Override
    public List<ProcessOutsourcing> selectProcessOutsourcingList(ProcessOutsourcing processOutsourcing)
    {
        return processOutsourcingMapper.selectProcessOutsourcingList(processOutsourcing);
    }

    /**
     * 新增工序外协
     * 
     * @param processOutsourcing 工序外协
     * @return 结果
     */
    @Override
    public int insertProcessOutsourcing(ProcessOutsourcing processOutsourcing)
    {
        processOutsourcing.setCreateTime(DateUtils.getNowDate());
        return processOutsourcingMapper.insertProcessOutsourcing(processOutsourcing);
    }

    /**
     * 修改工序外协
     * 
     * @param processOutsourcing 工序外协
     * @return 结果
     */
    @Override
    public int updateProcessOutsourcing(ProcessOutsourcing processOutsourcing)
    {
        processOutsourcing.setUpdateTime(DateUtils.getNowDate());
        return processOutsourcingMapper.updateProcessOutsourcing(processOutsourcing);
    }

    /**
     * 批量删除工序外协
     * 
     * @param ids 需要删除的工序外协主键
     * @return 结果
     */
    @Override
    public int deleteProcessOutsourcingByIds(Long[] ids)
    {
        return processOutsourcingMapper.deleteProcessOutsourcingByIds(ids);
    }

    /**
     * 删除工序外协信息
     * 
     * @param id 工序外协主键
     * @return 结果
     */
    @Override
    public int deleteProcessOutsourcingById(Long id)
    {
        return processOutsourcingMapper.deleteProcessOutsourcingById(id);
    }
}
