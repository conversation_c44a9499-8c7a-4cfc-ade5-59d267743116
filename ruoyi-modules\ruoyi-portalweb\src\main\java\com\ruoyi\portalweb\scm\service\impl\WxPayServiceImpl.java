package com.ruoyi.portalweb.scm.service.impl;

import cn.hutool.extra.servlet.ServletUtil;
import com.google.gson.Gson;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.portalweb.api.domain.AppStoreOrder;
import com.ruoyi.portalweb.scm.config.HttpUtils;
import com.ruoyi.portalweb.scm.config.WeChatUtils;
import com.ruoyi.portalweb.scm.config.WxPayConfigV3;
import com.ruoyi.portalweb.scm.enums.WxApiType;
import com.ruoyi.portalweb.scm.enums.WxNotifyType;
import com.ruoyi.portalweb.scm.service.IWxPayService;
import com.ruoyi.portalweb.scm.utils.PayUtils;
import com.ruoyi.portalweb.scm.utils.XmlUtils;
import com.ruoyi.portalweb.service.IAppStoreOrderService;
import com.ruoyi.portalweb.vo.AppStoreOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class WxPayServiceImpl implements IWxPayService {

    @Resource
    private WxPayConfigV3 wxPayConfigV3;


    @Resource
    private CloseableHttpClient wxPayClient;

    @Resource
    private IAppStoreOrderService appStoreOrderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> nativePay(AppStoreOrder appStoreOrder) throws Exception {
        AppStoreOrderVO order = appStoreOrderService.selectAppStoreOrderByAppStoreOrderNo(appStoreOrder.getAppStoreOrderNo());
        if (order == null || order.getAppStoreOrderId() == null){
            throw new ServiceException("无效的订单号");
        }


        //调用统一下单API
        HttpPost httpPost = new HttpPost(wxPayConfigV3.getDomain().concat(WxApiType.NATIVE_PAY.getType()));

        // 请求body参数
        Gson gson = new Gson();
        Map paramsMap = new HashMap();
        paramsMap.put("appid", wxPayConfigV3.getAppid());
        paramsMap.put("mchid", wxPayConfigV3.getMchId());
        paramsMap.put("description", order.getRemark());
        paramsMap.put("out_trade_no", order.getAppStoreOrderNo());
        paramsMap.put("notify_url", wxPayConfigV3.getNotifyDomain().concat(WxNotifyType.NATIVE_NOTIFY.getType()));

        Map amountMap = new HashMap();
        // 元转分 appStoreOrder.getTotalAmount()
        BigDecimal totalAmount=order.getAppStorePrice();
        BigDecimal hunded = new BigDecimal(String.valueOf(100));
        int money = totalAmount.multiply(hunded).intValue();
        amountMap.put("total", money);
        amountMap.put("currency", "CNY");
        paramsMap.put("amount", amountMap);

        //将参数转换成json字符串
        String jsonParams = gson.toJson(paramsMap);
        log.info("请求参数 ===> {}" + jsonParams);

        StringEntity entity = new StringEntity(jsonParams, "utf-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);
        httpPost.setHeader("Accept", "application/json");

        //完成签名并执行请求
        CloseableHttpResponse response = wxPayClient.execute(httpPost);

        try {
            String bodyAsString = EntityUtils.toString(response.getEntity());//响应体
            int statusCode = response.getStatusLine().getStatusCode();//响应状态码
            if (statusCode == 200) { //处理成功
                log.info("成功, 返回结果 = " + bodyAsString);
            } else if (statusCode == 204) { //处理成功，无返回Body
                log.info("成功");
            } else {
                log.info("Native下单失败,响应码 = " + statusCode + ",返回结果 = " + bodyAsString);
                throw new IOException("request failed");
            }
            //响应结果
            Map<String, String> resultMap = gson.fromJson(bodyAsString, HashMap.class);
            //二维码
           String codeUrl = resultMap.get("code_url");
            //返回二维码
            Map<String, Object> map = new HashMap<>();
            map.put("codeUrl", codeUrl);
            map.put("orderNo", order.getAppStoreOrderNo());
            return map;
        } finally {
            response.close();
        }
    }

    @Override
    public AjaxResult jsapiPay(AppStoreOrder appStoreOrder, HttpServletRequest request) {
        AppStoreOrder order = appStoreOrderService.selectAppStoreOrderByAppStoreOrderNo(appStoreOrder.getAppStoreOrderNo());
        if (order == null || order.getAppStoreOrderId() == null){
            throw new ServiceException("无效的订单号");
        }
        Map<String, Object> map = new HashMap<>();
        // 获取当前请求的ip地址
        String mchId = wxPayConfigV3.getMchId();
        String secret = "zu1maemo4bs4h3xcbnzgd2v3voubzfrs"; // 这个秘钥需要再商户平台配置
        String orderId = appStoreOrder.getAppStoreOrderNo();
        BigDecimal hunded = new BigDecimal(String.valueOf(100));
        String payMoney = appStoreOrder.getAppStorePrice().multiply(hunded).toString(); // 0.01 元，需要转换成分
        String notifyUrl=wxPayConfigV3.getNotifyDomain().concat(WxNotifyType.NATIVE_NOTIFY.getType());
        String clientIp = ServletUtil.getClientIP(request);
//        String xmlParam = WeChatUtils.genPayParams(orderId, payMoney, appStoreOrder.getOpenId(), mchId, secret,notifyUrl,clientIp);
        String xmlParam = WeChatUtils.genPayParams(orderId, payMoney, appStoreOrder.getBuyMemberId().toString(), mchId, secret,notifyUrl,clientIp);
        String result = HttpUtils.sendPost("https://api.mch.weixin.qq.com/pay/unifiedorder", xmlParam);
        log.info("xmlParam:{}",xmlParam);
        log.info("result:{}",result);
        if(result!=null){
            Map<String, String> weixinResult = XmlUtils.parseXml(result);
            if ("FAIL".equals(weixinResult.get("return_code"))) {
                return AjaxResult.error(weixinResult.get("return_msg").toString());
            } else {
                String appParam = PayUtils.genAppParams(weixinResult.get("prepay_id"),weixinResult.get("nonce_str"),secret,orderId);
                Map<String, String> data = XmlUtils.parseXml(appParam);
                return AjaxResult.success(data);
            }
        }
        return AjaxResult.error("微信支付返回异常，请联系管理员");
    }

}
