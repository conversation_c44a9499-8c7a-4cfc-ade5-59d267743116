package com.ruoyi.system.domain.dto;

import java.util.List;
import com.ruoyi.system.domain.ManufactureOrder;
import com.ruoyi.system.domain.MaterialInfo;
import com.ruoyi.system.domain.OrderMaterialRelation;

/**
 * 订单与物料关联数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public class OrderWithMaterialsDTO extends ManufactureOrder {
    
    /** 订单关联的物料信息列表 */
    private List<MaterialInfo> materials;
    
    /** 订单物料关联关系列表 */
    private List<OrderMaterialRelation> relations;

    public List<MaterialInfo> getMaterials() {
        return materials;
    }

    public void setMaterials(List<MaterialInfo> materials) {
        this.materials = materials;
    }

    public List<OrderMaterialRelation> getRelations() {
        return relations;
    }

    public void setRelations(List<OrderMaterialRelation> relations) {
        this.relations = relations;
    }
}
