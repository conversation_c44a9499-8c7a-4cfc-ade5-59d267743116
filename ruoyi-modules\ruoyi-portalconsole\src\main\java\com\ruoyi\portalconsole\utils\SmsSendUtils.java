package com.ruoyi.portalconsole.utils;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SmsSendUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmsSendUtils.class);

    public static Boolean opAliyunCode(String telephone, SmsConfig smsConfig, String template){
        SendSmsResponse retVal = sendSms(telephone, null, smsConfig, template);
        return "OK".equals(retVal.getCode());
    }


    /**
     *
     * @param telephone  手机号
     * @param templateParam 阿里云模板参数
     * @return
     */
    public static SendSmsResponse sendSms(String telephone,String templateParam, SmsConfig smsConfig, String template) {

        // 可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");

        // 初始化acsClient,暂不支持region化
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", smsConfig.getACCESS_KEY_ID(), smsConfig.getACCESS_KEY_SECRET());
        try {
            DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", smsConfig.getProduct(), smsConfig.getDomain());
        } catch (ClientException e) {
            e.printStackTrace();
        }
        IAcsClient acsClient = new DefaultAcsClient(profile);

        // 组装请求对象-具体描述见控制台-文档部分内容
        SendSmsRequest request = new SendSmsRequest();
        // 必填:待发送手机号
        request.setPhoneNumbers(telephone);
        // 必填:短信签名-可在短信控制台中找到
        request.setSignName(smsConfig.getSign()); // 签名
        // 必填:短信模板-可在短信控制台中找到
        request.setTemplateCode(template);  // 验证码模板
        // 可选:模板中的变量替换JSON串,如模板内容为"亲爱的用户,您的验证码为${code}"时,此处的值为
        request.setTemplateParam(templateParam);
        // 选填-上行短信扩展码(无特殊需求用户请忽略此字段)
        // request.setSmsUpExtendCode("90997");
        // 可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
        request.setOutId("yourOutId");
        // hint 此处可能会抛出异常，注意catch
        SendSmsResponse sendSmsResponse = null;
        try {
            sendSmsResponse = acsClient.getAcsResponse(request);
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                //记录日志
                LOGGER.info("短信发送成功！signName: {}, telephone: {}, templateCode: {}, templateParam: {}", smsConfig.getSign(), telephone, smsConfig.getTemplate(), templateParam);
            } else {
                // 记录日志
                LOGGER.info("短信发送失败！ {}。 signName: {}, telephone: {}, templateCode: {}, templateParam: {}", sendSmsResponse.getMessage(), smsConfig.getSign(), telephone, smsConfig.getTemplate(), templateParam);
            }
        } catch (ClientException e) {
            e.printStackTrace();
            LOGGER.error(e.getMessage(), e);
        }

        return sendSmsResponse;
    }

    public static void sendSms(String sign, String memberPhone, String templateFail, String s) {

    }
}
