package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.system.domain.dto.OrderWithMaterialsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.system.domain.ManufactureOrder;
import com.ruoyi.system.service.IManufactureOrderService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 制造订单Controller
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@RestController
@RequestMapping("/manufactureOrder")
public class ManufactureOrderController extends BaseController
{
    @Autowired
    private IManufactureOrderService manufactureOrderService;

    /**
     * 查询制造订单列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ManufactureOrder manufactureOrder)
    {
        startPage();
        List<ManufactureOrder> list = manufactureOrderService.selectManufactureOrderList(manufactureOrder);
        return getDataTable(list);
    }

    /**
     * 导出制造订单列表
     */
    @Log(title = "制造订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ManufactureOrder manufactureOrder)
    {
        List<ManufactureOrder> list = manufactureOrderService.selectManufactureOrderList(manufactureOrder);
        ExcelUtil<ManufactureOrder> util = new ExcelUtil<ManufactureOrder>(ManufactureOrder.class);
        util.exportExcel(response, list, "制造订单数据");
    }

    /**
     * 获取制造订单详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(manufactureOrderService.selectManufactureOrderById(id));
    }

    /**
     * 获取制造订单详细信息（包含关联物料）
     */
    @GetMapping(value = "/withMaterials/{id}")
    public AjaxResult getInfoWithMaterials(@PathVariable("id") Long id)
    {
        return success(manufactureOrderService.selectOrderWithMaterialsById(id));
    }

    /**
     * 新增制造订单
     */
    @Log(title = "制造订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ManufactureOrder manufactureOrder)
    {
        return toAjax(manufactureOrderService.insertManufactureOrder(manufactureOrder));
    }

    /**
     * 新增制造订单（包含物料信息）
     */
    @Log(title = "制造订单及物料", businessType = BusinessType.INSERT)
    @PostMapping("/withMaterials")
    public AjaxResult addWithMaterials(@RequestBody OrderWithMaterialsDTO orderWithMaterials)
    {
        return toAjax(manufactureOrderService.insertOrderWithMaterials(orderWithMaterials));
    }

    /**
     * 修改制造订单
     */
    @Log(title = "制造订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ManufactureOrder manufactureOrder)
    {
        return toAjax(manufactureOrderService.updateManufactureOrder(manufactureOrder));
    }

    /**
     * 修改制造订单（包含物料信息）
     */
    @Log(title = "制造订单及物料", businessType = BusinessType.UPDATE)
    @PutMapping("/withMaterials")
    public AjaxResult editWithMaterials(@RequestBody OrderWithMaterialsDTO orderWithMaterials)
    {
        return toAjax(manufactureOrderService.updateOrderWithMaterials(orderWithMaterials));
    }

    /**
     * 删除制造订单
     */
    @Log(title = "制造订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(manufactureOrderService.deleteManufactureOrderByIds(ids));
    }
}
