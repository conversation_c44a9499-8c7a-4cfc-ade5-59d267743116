package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.PolicySubmit;
import com.ruoyi.portalweb.service.IPolicySubmitService;
import com.ruoyi.portalweb.vo.PolicySubmitVO;
import com.ruoyi.system.api.RemoteUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 政策申报Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Api(value = "8.政策申报", tags = "8.政策申报")
@RestController
@RequestMapping("/PolicySubmit")
public class PolicySubmitController extends BaseController
{
    @Autowired
    private IPolicySubmitService policySubmitService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询政策申报列表
     */
    @ApiOperation("查询政策申报列表")
    @GetMapping("/list")
    public TableDataInfo list(PolicySubmitVO policySubmit)
    {
        startPage();
        PageUtils.setOrderBy("create_time desc");
        List<PolicySubmitVO> list = policySubmitService.selectPolicySubmitList(policySubmit);
        return getDataTable(list);
    }

    /**
     * 获取政策申报公司名称列表
     */
    @ApiOperation("获取政策申报详细信息")
    @GetMapping(value = "/company")
    public TableDataInfo getCompanyList(PolicySubmitVO policySubmit)
    {
        startPage();
        List<String> data = policySubmitService.selectPolicySubmitByPolicySubmitCompanyList(policySubmit);
        return getDataTable(data);
    }

    /**
     * 查询政策申报列表
     */
    @ApiOperation("查询政策申报列表")
    @GetMapping("/listDesk")
    public TableDataInfo listDesk(PolicySubmitVO policySubmit)
    {
        startPage();
        PageUtils.setOrderBy("create_time desc");
        List<PolicySubmitVO> list = policySubmitService.selectPolicySubmitList(policySubmit);
        return getDataTable(list);
    }

    /**
     * 导出政策申报列表
     */
    @ApiOperation("导出政策申报列表")
    @Log(title = "政策申报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PolicySubmitVO policySubmit)
    {
        List<PolicySubmitVO> list = policySubmitService.selectPolicySubmitList(policySubmit);
        ExcelUtil<PolicySubmitVO> util = new ExcelUtil<PolicySubmitVO>(PolicySubmitVO.class);
        util.exportExcel(response, list, "政策申报数据");
    }

    /**
     * 获取政策申报详细信息
     */
    @ApiOperation("获取政策申报详细信息")
    @GetMapping(value = "/{policySubmitId}")
    public AjaxResult getInfo(@PathVariable("policySubmitId") Long policySubmitId)
    {
        return success(policySubmitService.selectPolicySubmitByPolicySubmitId(policySubmitId));
    }

    /**
     * 获取政策申报详细信息
     */
    @ApiOperation("获取政策申报详细信息")
    @GetMapping(value = "/detailDesk")
    public AjaxResult getInfoDetail(PolicySubmit policySubmit)
    {
        return success(policySubmitService.selectPolicySubmitByPolicySubmitId(policySubmit.getPolicySubmitId()));
    }

    /**
     * 新增政策申报
     */
    @ApiOperation("新增政策申报")
    @Log(title = "政策申报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PolicySubmit policySubmit)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        policySubmit.setUpdateBy(userNickName.getData());
        policySubmit.setCreateBy(userNickName.getData());
        return toAjax(policySubmitService.insertPolicySubmit(policySubmit));
    }

    /**
     * 修改政策申报
     */
    @ApiOperation("修改政策申报")
    @Log(title = "政策申报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PolicySubmit policySubmit)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        policySubmit.setUpdateBy(userNickName.getData());
        return toAjax(policySubmitService.updatePolicySubmit(policySubmit));
    }

    /**
     * 删除政策申报
     */
    @ApiOperation("删除政策申报")
    @Log(title = "政策申报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{policySubmitIds}")
    public AjaxResult remove(@PathVariable Long[] policySubmitIds)
    {
        return toAjax(policySubmitService.deletePolicySubmitByPolicySubmitIds(policySubmitIds));
    }
}
