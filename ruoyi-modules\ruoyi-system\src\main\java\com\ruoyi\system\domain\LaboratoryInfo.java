package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 实验室信息对象 laboratory_info
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public class LaboratoryInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 实验室ID */
    private Long id;

    /** 实验室名称 */
    @Excel(name = "实验室名称")
    private String labName;

    /** 实验室类型 */
    @Excel(name = "实验室类型")
    private String labType;

    /** 实验室地址 */
    @Excel(name = "实验室地址")
    private String labAddress;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactPhone;

    /** 实验室介绍 */
    @Excel(name = "实验室介绍")
    private String labIntroduction;

    /** 实验室图片 */
    @Excel(name = "实验室图片")
    private String labImages;

    /** 检测范围 */
    @Excel(name = "检测范围")
    private String testingScope;

    /** 资质说明 */
    @Excel(name = "资质说明")
    private String qualifications;

    /** CNAS认证 */
    @Excel(name = "CNAS认证")
    private String cnasQualification;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setLabName(String labName) 
    {
        this.labName = labName;
    }

    public String getLabName() 
    {
        return labName;
    }
    public void setLabType(String labType) 
    {
        this.labType = labType;
    }

    public String getLabType() 
    {
        return labType;
    }
    public void setLabAddress(String labAddress) 
    {
        this.labAddress = labAddress;
    }

    public String getLabAddress() 
    {
        return labAddress;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setLabIntroduction(String labIntroduction) 
    {
        this.labIntroduction = labIntroduction;
    }

    public String getLabIntroduction() 
    {
        return labIntroduction;
    }
    public void setLabImages(String labImages) 
    {
        this.labImages = labImages;
    }

    public String getLabImages() 
    {
        return labImages;
    }
    public void setTestingScope(String testingScope) 
    {
        this.testingScope = testingScope;
    }

    public String getTestingScope() 
    {
        return testingScope;
    }
    public void setQualifications(String qualifications) 
    {
        this.qualifications = qualifications;
    }

    public String getQualifications() 
    {
        return qualifications;
    }
    public void setCnasQualification(String cnasQualification) 
    {
        this.cnasQualification = cnasQualification;
    }

    public String getCnasQualification() 
    {
        return cnasQualification;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("labName", getLabName())
            .append("labType", getLabType())
            .append("labAddress", getLabAddress())
            .append("contactPhone", getContactPhone())
            .append("labIntroduction", getLabIntroduction())
            .append("labImages", getLabImages())
            .append("testingScope", getTestingScope())
            .append("qualifications", getQualifications())
            .append("cnasQualification", getCnasQualification())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
