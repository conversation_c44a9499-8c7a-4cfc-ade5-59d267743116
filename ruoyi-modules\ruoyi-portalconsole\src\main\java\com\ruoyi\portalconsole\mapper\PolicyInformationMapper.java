package com.ruoyi.portalconsole.mapper;

import java.util.List;
import com.ruoyi.portalconsole.domain.PolicyInformation;

/**
 * 政策资讯Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface PolicyInformationMapper 
{
    /**
     * 查询政策资讯
     * 
     * @param policyInformationId 政策资讯主键
     * @return 政策资讯
     */
    public PolicyInformation selectPolicyInformationByPolicyInformationId(Long policyInformationId);

    /**
     * 查询政策资讯列表
     * 
     * @param policyInformation 政策资讯
     * @return 政策资讯集合
     */
    public List<PolicyInformation> selectPolicyInformationList(PolicyInformation policyInformation);

    /**
     * 新增政策资讯
     * 
     * @param policyInformation 政策资讯
     * @return 结果
     */
    public int insertPolicyInformation(PolicyInformation policyInformation);

    /**
     * 修改政策资讯
     * 
     * @param policyInformation 政策资讯
     * @return 结果
     */
    public int updatePolicyInformation(PolicyInformation policyInformation);

    /**
     * 删除政策资讯
     * 
     * @param policyInformationId 政策资讯主键
     * @return 结果
     */
    public int deletePolicyInformationByPolicyInformationId(Long policyInformationId);

    /**
     * 批量删除政策资讯
     * 
     * @param policyInformationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePolicyInformationByPolicyInformationIds(Long[] policyInformationIds);
}
