package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.Demand;
import com.ruoyi.portalweb.api.domain.NewsInformation;
import com.ruoyi.portalweb.service.INewsInformationService;
import com.ruoyi.portalweb.vo.NewsInformationVO;
import com.ruoyi.system.api.RemoteUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 动态资讯Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/NewsInformation")
@Api(value = "5.动态资讯", tags = "5.动态资讯")
public class NewsInformationController extends BaseController
{
    @Autowired
    private INewsInformationService newsInformationService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询动态资讯列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询动态资讯列表", notes = "传入")
    public TableDataInfo list(NewsInformationVO newsInformation)
    {
        startPage();
        List<NewsInformationVO> list = newsInformationService.selectNewsInformationList(newsInformation);
        return getDataTable(list);
    }

    /**
     * 查询动态资讯列表
     */
    @GetMapping("/listDesk")
    @ApiOperation(value = "查询动态资讯列表", notes = "传入")
    public TableDataInfo listDesk(NewsInformationVO newsInformation)
    {
        startPage();
        PageUtils.setOrderBy("top desc, create_time desc");
        List<NewsInformationVO> list = newsInformationService.selectNewsInformationList(newsInformation);
        return getDataTable(list);
    }

    /**
     * 导出动态资讯列表
     */
    @Log(title = "动态资讯", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出动态资讯列表", notes = "传入")
    public void export(HttpServletResponse response, NewsInformationVO newsInformation)
    {
        List<NewsInformationVO> list = newsInformationService.selectNewsInformationList(newsInformation);
        ExcelUtil<NewsInformationVO> util = new ExcelUtil<>(NewsInformationVO.class);
        util.exportExcel(response, list, "动态资讯数据");
    }

//    /**
//     * 获取动态资讯详细信息
//     */
//    @GetMapping(value = "/{newsInformationId}")
//    @ApiOperation(value = "获取动态资讯详细信息", notes = "传入")
//    public AjaxResult getInfo(@PathVariable("newsInformationId") Long newsInformationId)
//    {
//        return success(newsInformationService.selectNewsInformationByNewsInformationId(newsInformationId));
//    }

    /**
     * 获取动态资讯详细信息
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取动态资讯详细信息", notes = "传入")
    public AjaxResult detail(NewsInformation newsInformation) {
        return success(newsInformationService.selectNewsInformationByNewsInformationId(newsInformation.getNewsInformationId()));
    }

    /**
     * 获取动态资讯详细信息
     */
    @GetMapping("/detailDesk")
    @ApiOperation(value = "获取动态资讯详细信息", notes = "传入")
    public AjaxResult detailDesk(NewsInformation newsInformation) {
        return success(newsInformationService.selectNewsInformationByNewsInformationId(newsInformation.getNewsInformationId()));
    }

    /**
     * 新增动态资讯
     */
    @Log(title = "动态资讯", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增动态资讯", notes = "传入")
    public AjaxResult add(@RequestBody NewsInformation newsInformation)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        newsInformation.setUpdateBy(userNickName.getData());
        newsInformation.setCreateBy(userNickName.getData());
        return toAjax(newsInformationService.insertNewsInformation(newsInformation));
    }

    /**
     * 修改动态资讯
     */
    @Log(title = "动态资讯", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改动态资讯", notes = "传入")
    public AjaxResult edit(@RequestBody NewsInformation newsInformation)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        newsInformation.setUpdateBy(userNickName.getData());
        return toAjax(newsInformationService.updateNewsInformation(newsInformation));
    }

    /**
     * 删除动态资讯
     */
    @Log(title = "动态资讯", businessType = BusinessType.DELETE)
	@DeleteMapping("/{newsInformationIds}")
    @ApiOperation(value = "删除动态资讯", notes = "传入")
    public AjaxResult remove(@PathVariable Long[] newsInformationIds)
    {
        return toAjax(newsInformationService.deleteNewsInformationByNewsInformationIds(newsInformationIds));
    }
}
