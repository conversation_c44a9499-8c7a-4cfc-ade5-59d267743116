package com.ruoyi.portalweb.service;

import java.util.List;
import com.ruoyi.portalweb.api.domain.BuMemberOnlinePay;

/**
 * 商城线上支付Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface IBuMemberOnlinePayService 
{
    /**
     * 查询商城线上支付
     * 
     * @param id 商城线上支付主键
     * @return 商城线上支付
     */
    public BuMemberOnlinePay selectBuMemberOnlinePayById(Long id);

    /**
     * 查询商城线上支付列表
     * 
     * @param buMemberOnlinePay 商城线上支付
     * @return 商城线上支付集合
     */
    public List<BuMemberOnlinePay> selectBuMemberOnlinePayList(BuMemberOnlinePay buMemberOnlinePay);

    /**
     * 新增商城线上支付
     * 
     * @param buMemberOnlinePay 商城线上支付
     * @return 结果
     */
    public int insertBuMemberOnlinePay(BuMemberOnlinePay buMemberOnlinePay);

    /**
     * 修改商城线上支付
     * 
     * @param buMemberOnlinePay 商城线上支付
     * @return 结果
     */
    public int updateBuMemberOnlinePay(BuMemberOnlinePay buMemberOnlinePay);

    /**
     * 批量删除商城线上支付
     * 
     * @param ids 需要删除的商城线上支付主键集合
     * @return 结果
     */
    public int deleteBuMemberOnlinePayByIds(Long[] ids);

    /**
     * 删除商城线上支付信息
     * 
     * @param id 商城线上支付主键
     * @return 结果
     */
    public int deleteBuMemberOnlinePayById(Long id);

    BuMemberOnlinePay selectBuMemberOnlinePayByAppOrderNo(String appStoreOrderNo);
}
