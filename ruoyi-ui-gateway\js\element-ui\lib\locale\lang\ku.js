'use strict';

exports.__esModule = true;
exports.default = {
  el: {
    colorpicker: {
      confirm: 'Tema<PERSON>',
      clear: 'Paqij bike'
    },
    datepicker: {
      now: '<PERSON>ha',
      today: 'Îro',
      cancel: 'Betal bike',
      clear: 'Paqij bike',
      confirm: 'Temam',
      selectDate: '<PERSON>îrokê bibijêre',
      selectTime: 'Demê bibijêre',
      startDate: 'Dîroka <PERSON>tpêkê',
      startTime: '<PERSON><PERSON>',
      endDate: 'Dîroka Dawî',
      endTime: 'Dema Dawî',
      prevYear: 'Sala <PERSON>ê<PERSON>',
      nextYear: 'Sala <PERSON>',
      prevMonth: '<PERSON>ha <PERSON>',
      nextMonth: '<PERSON>ha <PERSON>',
      year: 'Sal',
      month1: 'Rêben<PERSON>',
      month2: 'Reşemeh',
      month3: 'Adar',
      month4: 'Avrêl',
      month5: 'Gulan',
      month6: 'Pûşber',
      month7: 'Tîrmeh',
      month8: 'Gilavêj',
      month9: 'Re<PERSON><PERSON>',
      month10: 'Kew<PERSON>êr',
      month11: 'Sarmawaz',
      month12: '<PERSON><PERSON><PERSON><PERSON>',
      // week: 'week',
      weeks: {
        sun: 'Yek',
        mon: 'Du<PERSON>',
        tue: 'Sêş',
        wed: 'Çar',
        thu: 'Pên',
        fri: 'În',
        sat: 'Şem'
      },
      months: {
        jan: 'Rêb',
        feb: 'Reş',
        mar: 'Ada',
        apr: 'Avr',
        may: 'Gul',
        jun: 'Pûş',
        jul: 'Tîr',
        aug: 'Gil',
        sep: 'Rez',
        oct: 'Kew',
        nov: 'Sar',
        dec: 'Ber'
      }
    },
    select: {
      loading: 'Bardibe',
      noMatch: 'Li hembere ve agahî tune',
      noData: 'Agahî tune',
      placeholder: 'Bibijêre'
    },
    cascader: {
      noMatch: 'Li hembere ve agahî tune',
      loading: 'Bardibe',
      placeholder: 'Bibijêre',
      noData: 'Agahî tune'
    },
    pagination: {
      goto: 'Biçe',
      pagesize: '/rupel',
      total: 'Tevahî {total}',
      pageClassifier: ''
    },
    messagebox: {
      title: 'Peyam',
      confirm: 'Temam',
      cancel: 'Betal bike',
      error: 'Beyana çewt'
    },
    upload: {
      deleteTip: 'ji bo rake pêl "delete" bike',
      delete: 'Rake',
      preview: 'Pêşdîtin',
      continue: 'Berdewam'
    },
    table: {
      emptyText: 'Agahî tune',
      confirmFilter: 'Piştrast bike',
      resetFilter: 'Jê bibe',
      clearFilter: 'Hemû',
      sumText: 'Kom'
    },
    tree: {
      emptyText: 'Agahî tune'
    },
    transfer: {
      noMatch: 'Li hembere ve agahî tune',
      noData: 'Agahî tune',
      titles: ['Lîste 1', 'Lîste 2'],
      filterPlaceholder: 'Binivîse',
      noCheckedFormat: '{total} lib',
      hasCheckedFormat: '{checked}/{total} bijartin'
    },
    image: {
      error: 'FAILED' // to be translated
    },
    pageHeader: {
      title: 'Back' // to be translated
    },
    popconfirm: {
      confirmButtonText: 'Yes', // to be translated
      cancelButtonText: 'No' // to be translated
    },
    empty: {
      description: 'Agahî tune'
    }
  }
};
