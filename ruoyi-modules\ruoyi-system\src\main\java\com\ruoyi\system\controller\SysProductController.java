package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SysProduct;
import com.ruoyi.system.service.ISysProductService;
import com.ruoyi.system.service.impl.SysProductServiceImpl;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 产品信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/SysProduct")
public class SysProductController extends BaseController
{
    @Autowired
    private ISysProductService sysProductService;

    /**
     * 查询产品信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SysProduct sysProduct)
    {
        startPage();
        List<SysProduct> list = sysProductService.selectSysProductList(sysProduct);
        return getDataTable(list);
    }

    /**
     * 导出产品信息列表
     */
    @RequiresPermissions("system:SysProduct:export")
    @Log(title = "产品信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysProduct sysProduct)
    {
        List<SysProduct> list = sysProductService.selectSysProductList(sysProduct);
        ExcelUtil<SysProduct> util = new ExcelUtil<SysProduct>(SysProduct.class);
        util.exportExcel(response, list, "产品信息数据");
    }

    /**
     * 获取产品信息详细信息
     */
    @RequiresPermissions("system:SysProduct:query")
    @GetMapping(value = "/{productId}")
    public AjaxResult getInfo(@PathVariable("productId") Long productId)
    {
        return success(sysProductService.selectSysProductByProductId(productId));
    }

    /**
     * 新增产品信息
     */
    @RequiresPermissions("system:SysProduct:add")
    @Log(title = "产品信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysProduct sysProduct)
    {
        return toAjax(sysProductService.insertSysProduct(sysProduct));
    }

    /**
     * 新增产品信息（支持工厂ID数组）
     */
    @RequiresPermissions("system:SysProduct:add")
    @Log(title = "产品信息", businessType = BusinessType.INSERT)
    @PostMapping("/addWithFactoryIds")
    public AjaxResult addWithFactoryIds(@RequestBody SysProduct sysProduct, @RequestParam("factoryIds") Long[] factoryIds)
    {
        // 将工厂ID数组转换为逗号分隔的字符串
        if (factoryIds != null && factoryIds.length > 0)
        {
            sysProduct.setFactoryId(SysProductServiceImpl.convertFactoryIdsToString(factoryIds));
        }
        return toAjax(sysProductService.insertSysProduct(sysProduct));
    }

    /**
     * 修改产品信息
     */
    @RequiresPermissions("system:SysProduct:edit")
    @Log(title = "产品信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysProduct sysProduct)
    {
        return toAjax(sysProductService.updateSysProduct(sysProduct));
    }

    /**
     * 修改产品信息（支持工厂ID数组）
     */
    @RequiresPermissions("system:SysProduct:edit")
    @Log(title = "产品信息", businessType = BusinessType.UPDATE)
    @PutMapping("/editWithFactoryIds")
    public AjaxResult editWithFactoryIds(@RequestBody SysProduct sysProduct, @RequestParam("factoryIds") Long[] factoryIds)
    {
        // 将工厂ID数组转换为逗号分隔的字符串
        if (factoryIds != null && factoryIds.length > 0)
        {
            sysProduct.setFactoryId(SysProductServiceImpl.convertFactoryIdsToString(factoryIds));
        }
        return toAjax(sysProductService.updateSysProduct(sysProduct));
    }

    /**
     * 根据工厂ID查询产品列表
     */
    @RequiresPermissions("system:SysProduct:list")
    @GetMapping("/listByFactory/{factoryId}")
    public TableDataInfo listByFactory(@PathVariable("factoryId") Long factoryId)
    {
        startPage();
        SysProduct queryProduct = new SysProduct();
        queryProduct.setFactoryId(factoryId.toString());
        List<SysProduct> list = sysProductService.selectSysProductList(queryProduct);
        return getDataTable(list);
    }

    /**
     * 删除产品信息
     */
    @RequiresPermissions("system:SysProduct:remove")
    @Log(title = "产品信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{productIds}")
    public AjaxResult remove(@PathVariable Long[] productIds)
    {
        return toAjax(sysProductService.deleteSysProductByProductIds(productIds));
    }
}
