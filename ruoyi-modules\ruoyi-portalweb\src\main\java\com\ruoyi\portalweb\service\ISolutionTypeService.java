package com.ruoyi.portalweb.service;


import com.ruoyi.portalweb.api.domain.SolutionType;
import com.ruoyi.portalweb.vo.SolutionTypeVO;

import java.util.List;

/**
 * 解决方案类型Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface ISolutionTypeService 
{
    /**
     * 查询解决方案类型
     * 
     * @param solutionTypeId 解决方案类型主键
     * @return 解决方案类型
     */
    public SolutionTypeVO selectSolutionTypeBySolutionTypeId(Long solutionTypeId);

    /**
     * 查询解决方案类型列表
     * 
     * @param solutionType 解决方案类型
     * @return 解决方案类型集合
     */
    public List<SolutionTypeVO> selectSolutionTypeList(SolutionType solutionType);

    /**
     * 父级列表
     */
    public List<SolutionTypeVO> parentList(SolutionType solutionType);

    /**
     * 新增解决方案类型
     * 
     * @param solutionType 解决方案类型
     * @return 结果
     */
    public int insertSolutionType(SolutionType solutionType);

    /**
     * 修改解决方案类型
     * 
     * @param solutionType 解决方案类型
     * @return 结果
     */
    public int updateSolutionType(SolutionType solutionType);

    /**
     * 批量删除解决方案类型
     * 
     * @param solutionTypeIds 需要删除的解决方案类型主键集合
     * @return 结果
     */
    public int deleteSolutionTypeBySolutionTypeIds(Long[] solutionTypeIds);

    /**
     * 删除解决方案类型信息
     * 
     * @param solutionTypeId 解决方案类型主键
     * @return 结果
     */
    public int deleteSolutionTypeBySolutionTypeId(Long solutionTypeId);

    /**
     * 典型案例分类列表
     */
    public List<SolutionTypeVO> classicCaseList(SolutionType solutionType);

    /**
     * 解决方案分类列表
     */
    public List<SolutionTypeVO> solutionList(SolutionType solutionType);
}
