package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.FactoryEquipment;

/**
 * 工厂设备信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface IFactoryEquipmentService 
{
    /**
     * 查询工厂设备信息
     * 
     * @param id 工厂设备信息主键
     * @return 工厂设备信息
     */
    public FactoryEquipment selectFactoryEquipmentById(Long id);

    /**
     * 查询工厂设备信息列表
     * 
     * @param factoryEquipment 工厂设备信息
     * @return 工厂设备信息集合
     */
    public List<FactoryEquipment> selectFactoryEquipmentList(FactoryEquipment factoryEquipment);

    /**
     * 新增工厂设备信息
     * 
     * @param factoryEquipment 工厂设备信息
     * @return 结果
     */
    public int insertFactoryEquipment(FactoryEquipment factoryEquipment);

    /**
     * 修改工厂设备信息
     * 
     * @param factoryEquipment 工厂设备信息
     * @return 结果
     */
    public int updateFactoryEquipment(FactoryEquipment factoryEquipment);

    /**
     * 批量删除工厂设备信息
     * 
     * @param ids 需要删除的工厂设备信息主键集合
     * @return 结果
     */
    public int deleteFactoryEquipmentByIds(Long[] ids);

    /**
     * 删除工厂设备信息信息
     * 
     * @param id 工厂设备信息主键
     * @return 结果
     */
    public int deleteFactoryEquipmentById(Long id);
}
