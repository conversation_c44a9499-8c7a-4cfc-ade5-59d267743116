package com.ruoyi.portalweb.mapper;

import java.util.List;
import com.ruoyi.portalweb.api.domain.PolicySubmitConsult;

/**
 * 政策辅助申报Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-08
 */
public interface PolicySubmitConsultMapper 
{
    /**
     * 查询政策辅助申报
     * 
     * @param policySubmitConsultId 政策辅助申报主键
     * @return 政策辅助申报
     */
    public PolicySubmitConsult selectPolicySubmitConsultByPolicySubmitConsultId(Long policySubmitConsultId);

    /**
     * 查询政策辅助申报列表
     * 
     * @param policySubmitConsult 政策辅助申报
     * @return 政策辅助申报集合
     */
    public List<PolicySubmitConsult> selectPolicySubmitConsultList(PolicySubmitConsult policySubmitConsult);

    /**
     * 新增政策辅助申报
     * 
     * @param policySubmitConsult 政策辅助申报
     * @return 结果
     */
    public int insertPolicySubmitConsult(PolicySubmitConsult policySubmitConsult);

    /**
     * 修改政策辅助申报
     * 
     * @param policySubmitConsult 政策辅助申报
     * @return 结果
     */
    public int updatePolicySubmitConsult(PolicySubmitConsult policySubmitConsult);

    /**
     * 删除政策辅助申报
     * 
     * @param policySubmitConsultId 政策辅助申报主键
     * @return 结果
     */
    public int deletePolicySubmitConsultByPolicySubmitConsultId(Long policySubmitConsultId);

    /**
     * 批量删除政策辅助申报
     * 
     * @param policySubmitConsultIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePolicySubmitConsultByPolicySubmitConsultIds(Long[] policySubmitConsultIds);
}
