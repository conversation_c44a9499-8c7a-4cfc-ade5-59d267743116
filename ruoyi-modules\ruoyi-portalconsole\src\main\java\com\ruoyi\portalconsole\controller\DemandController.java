package com.ruoyi.portalconsole.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.portalconsole.domain.vo.DemandVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.Demand;
import com.ruoyi.portalconsole.service.IDemandService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 服务需求(NEW)Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/demand")
public class DemandController extends BaseController
{
    @Autowired
    private IDemandService demandService;

    /**
     * 查询服务需求(NEW)列表
     */
    @RequiresPermissions("portalconsole:demand:list")
    @GetMapping("/list")
    public TableDataInfo list(Demand demand)
    {
        startPage();
        PageUtils.setOrderBy("create_time DESC");
        List<DemandVO> list = demandService.selectDemandList(demand);
        return getDataTable(list);
    }

    /**
     * 导出服务需求(NEW)列表
     */
    @RequiresPermissions("portalconsole:demand:export")
    @Log(title = "服务需求(NEW)", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Demand demand)
    {
        List<DemandVO> list = demandService.selectDemandList(demand);
        ExcelUtil<DemandVO> util = new ExcelUtil<DemandVO>(DemandVO.class);
        util.exportExcel(response, list, "服务需求(NEW)数据");
    }

    /**
     * 获取服务需求(NEW)详细信息
     */
    @RequiresPermissions("portalconsole:demand:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(demandService.selectDemandById(id));
    }

    /**
     * 新增服务需求(NEW)
     */
    @RequiresPermissions("portalconsole:demand:add")
    @Log(title = "服务需求(NEW)", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Demand demand) {return toAjax(demandService.insertDemand(demand)); }

    /**
     * 新增服务需求(NEW)
     */
    @Log(title = "服务需求(NEW)", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    public AjaxResult addByImport(@RequestBody Demand demand) {return toAjax(demandService.insertDemand(demand)); }

    /**
     * 修改服务需求(NEW)
     */
    @RequiresPermissions("portalconsole:demand:edit")
    @Log(title = "服务需求(NEW)", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Demand demand)
    {
        return toAjax(demandService.updateDemand(demand));
    }

    /**
     * 审核服务需求(NEW)
     */
    @RequiresPermissions("portalconsole:demand:audit")
    @Log(title = "服务需求(NEW)", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody Demand demand)
    {
        return toAjax(demandService.auditDemand(demand));
    }

    /**
     * 删除服务需求(NEW)
     */
    @RequiresPermissions("portalconsole:demand:remove")
    @Log(title = "服务需求(NEW)", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(demandService.deleteDemandByIds(ids));
    }
}
