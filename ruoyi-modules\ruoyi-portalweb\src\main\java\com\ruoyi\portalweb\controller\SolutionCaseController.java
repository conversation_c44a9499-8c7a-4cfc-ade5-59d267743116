package com.ruoyi.portalweb.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.SolutionCase;
import com.ruoyi.portalweb.service.ISolutionCaseService;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 解决方案实施案例Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/SolutionCase")
public class SolutionCaseController extends BaseController
{
    @Autowired
    private ISolutionCaseService solutionCaseService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询解决方案实施案例列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SolutionCase solutionCase)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<SolutionCase> list = solutionCaseService.selectSolutionCaseList(solutionCase);
        return getDataTable(list);
    }

    /**
     * 导出解决方案实施案例列表
     */
    @Log(title = "解决方案实施案例", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SolutionCase solutionCase)
    {
        List<SolutionCase> list = solutionCaseService.selectSolutionCaseList(solutionCase);
        ExcelUtil<SolutionCase> util = new ExcelUtil<SolutionCase>(SolutionCase.class);
        util.exportExcel(response, list, "解决方案实施案例数据");
    }

    /**
     * 获取解决方案实施案例详细信息
     */
    @GetMapping(value = "/{solutionCaseId}")
    public AjaxResult getInfo(@PathVariable("solutionCaseId") Long solutionCaseId)
    {
        return success(solutionCaseService.selectSolutionCaseBySolutionCaseId(solutionCaseId));
    }

    /**
     * 新增解决方案实施案例
     */
    @Log(title = "解决方案实施案例", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SolutionCase solutionCase)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        solutionCase.setUpdateBy(userNickName.getData());
        solutionCase.setCreateBy(userNickName.getData());
        return toAjax(solutionCaseService.insertSolutionCase(solutionCase));
    }

    /**
     * 修改解决方案实施案例
     */
    @Log(title = "解决方案实施案例", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SolutionCase solutionCase)
    {
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        solutionCase.setUpdateBy(userNickName.getData());
        return toAjax(solutionCaseService.updateSolutionCase(solutionCase));
    }

    /**
     * 删除解决方案实施案例
     */
    @Log(title = "解决方案实施案例", businessType = BusinessType.DELETE)
	@DeleteMapping("/{solutionCaseIds}")
    public AjaxResult remove(@PathVariable Long[] solutionCaseIds)
    {
        return toAjax(solutionCaseService.deleteSolutionCaseBySolutionCaseIds(solutionCaseIds));
    }
}
