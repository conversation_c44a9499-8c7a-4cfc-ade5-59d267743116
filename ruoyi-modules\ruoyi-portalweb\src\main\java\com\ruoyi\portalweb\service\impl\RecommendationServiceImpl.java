package com.ruoyi.portalweb.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.portalweb.api.enums.ReadStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalweb.mapper.RecommendationMapper;
import com.ruoyi.portalweb.api.domain.Recommendation;
import com.ruoyi.portalweb.service.IRecommendationService;

/**
 * 建议及反馈Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-06
 */
@Service
public class RecommendationServiceImpl implements IRecommendationService 
{
    @Autowired
    private RecommendationMapper recommendationMapper;

    /**
     * 查询建议及反馈
     * 
     * @param id 建议及反馈主键
     * @return 建议及反馈
     */
    @Override
    public Recommendation selectRecommendationById(Long id)
    {
        return recommendationMapper.selectRecommendationById(id);
    }

    /**
     * 查询建议及反馈列表
     * 
     * @param recommendation 建议及反馈
     * @return 建议及反馈
     */
    @Override
    public List<Recommendation> selectRecommendationList(Recommendation recommendation)
    {
        return recommendationMapper.selectRecommendationList(recommendation);
    }

    /**
     * 新增建议及反馈
     * 
     * @param recommendation 建议及反馈
     * @return 结果
     */
    @Override
    public int insertRecommendation(Recommendation recommendation)
    {
        recommendation.setCreateTime(DateUtils.getNowDate());
        recommendation.setReadStatus(ReadStatus.UNREAD.getValue());
        return recommendationMapper.insertRecommendation(recommendation);
    }

    /**
     * 修改建议及反馈
     * 
     * @param recommendation 建议及反馈
     * @return 结果
     */
    @Override
    public int updateRecommendation(Recommendation recommendation)
    {
        return recommendationMapper.updateRecommendation(recommendation);
    }

    /**
     * 批量删除建议及反馈
     * 
     * @param ids 需要删除的建议及反馈主键
     * @return 结果
     */
    @Override
    public int deleteRecommendationByIds(Long[] ids)
    {
        return recommendationMapper.deleteRecommendationByIds(ids);
    }

    /**
     * 删除建议及反馈信息
     * 
     * @param id 建议及反馈主键
     * @return 结果
     */
    @Override
    public int deleteRecommendationById(Long id)
    {
        return recommendationMapper.deleteRecommendationById(id);
    }
}
