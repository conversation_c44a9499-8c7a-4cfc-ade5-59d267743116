export default {
  el: {
    colorpicker: {
      confirm: '<PERSON>lih',
      clear: 'Kosongkan'
    },
    datepicker: {
      now: '<PERSON><PERSON><PERSON>',
      today: 'Hari ini',
      cancel: '<PERSON><PERSON>',
      clear: 'Kosongkan',
      confirm: 'Ya',
      selectDate: '<PERSON><PERSON>h tanggal',
      selectTime: '<PERSON>lih waktu',
      startDate: '<PERSON><PERSON>',
      startTime: '<PERSON><PERSON><PERSON>',
      endDate: '<PERSON><PERSON>',
      endTime: '<PERSON><PERSON><PERSON>',
      prevYear: '<PERSON>hun Sebelumnya',
      nextYear: 'Tahun Selanjutnya',
      prevMonth: 'Bulan Sebelumnya',
      nextMonth: 'Bulan Selanjutnya',
      year: '<PERSON>hun',
      month1: 'Januari',
      month2: 'Februari',
      month3: 'Mare<PERSON>',
      month4: 'April',
      month5: 'Mei',
      month6: 'Juni',
      month7: 'Juli',
      month8: 'Agustus',
      month9: 'September',
      month10: 'Oktober',
      month11: 'November',
      month12: 'Desember',
      // week: 'minggu',
      weeks: {
        sun: 'Min',
        mon: 'Sen',
        tue: 'Sel',
        wed: 'Rab',
        thu: 'Kam',
        fri: 'Ju<PERSON>',
        sat: 'Sab'
      },
      months: {
        jan: '<PERSON>',
        feb: 'Feb',
        mar: 'Mar',
        apr: 'Apr',
        may: 'Mei',
        jun: 'Jun',
        jul: 'Jul',
        aug: 'Agu',
        sep: 'Sep',
        oct: 'Okt',
        nov: 'Nov',
        dec: 'Des'
      }
    },
    select: {
      loading: 'Memuat',
      noMatch: 'Tidak ada data yg cocok',
      noData: 'Tidak ada data',
      placeholder: 'Pilih'
    },
    cascader: {
      noMatch: 'Tidak ada data yg cocok',
      loading: 'Memuat',
      placeholder: 'Pilih',
      noData: 'Tidak ada data'
    },
    pagination: {
      goto: 'Pergi ke',
      pagesize: '/laman',
      total: 'Total {total}',
      pageClassifier: ''
    },
    messagebox: {
      title: 'Pesan',
      confirm: 'Ya',
      cancel: 'Batal',
      error: 'Masukan ilegal'
    },
    upload: {
      deleteTip: 'Tekan hapus untuk melanjutkan',
      delete: 'Hapus',
      preview: 'Pratinjau',
      continue: 'Lanjutkan'
    },
    table: {
      emptyText: 'Tidak ada data',
      confirmFilter: 'Konfirmasi',
      resetFilter: 'Atur ulang',
      clearFilter: 'Semua',
      sumText: 'Jml'
    },
    tree: {
      emptyText: 'Tidak ada data'
    },
    transfer: {
      noMatch: 'Tidak ada data yg cocok',
      noData: 'Tidak ada data',
      titles: ['Senarai 1', 'Senarai 2'],
      filterPlaceholder: 'Masukan kata kunci',
      noCheckedFormat: '{total} butir',
      hasCheckedFormat: '{checked}/{total} terpilih'
    },
    image: {
      error: 'GAGAL'
    },
    pageHeader: {
      title: 'Kembali'
    },
    popconfirm: {
      confirmButtonText: 'Ya',
      cancelButtonText: 'Tidak'
    },
    empty: {
      description: 'Tidak ada data'
    }
  }
};
