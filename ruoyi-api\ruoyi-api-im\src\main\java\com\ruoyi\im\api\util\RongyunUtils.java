package com.ruoyi.im.api.util;/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.util
 * @ClassName: RongyunUtils
 * @Author: ${maguojun}
 * @Description: 融云api工具类
 * @Date: 2022/3/10 17:44
 * @Version: 1.0
 */

import com.ruoyi.im.api.dto.SystemNotificationDto;
import io.rong.RongCloud;
import io.rong.messages.*;
import io.rong.methods.group.Group;
import io.rong.methods.message._private.Private;
import io.rong.methods.message.chatroom.Chatroom;
import io.rong.methods.message.system.MsgSystem;
import io.rong.methods.user.User;
import io.rong.models.Result;
import io.rong.models.group.GroupMember;
import io.rong.models.group.GroupModel;
import io.rong.models.message.*;
import io.rong.models.push.*;
import io.rong.models.response.PushResult;
import io.rong.models.response.ResponseResult;
import io.rong.models.response.TokenResult;
import io.rong.models.user.UserModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * @program: ruoyi
 *
 * @description: 融云api工具类
 *
 * @author: MaGuoJun
 *
 * @create: 2022-03-10 17:44
 **/
@Component
@RefreshScope
public class RongyunUtils {

    private  RongCloud rongCloud ;

    public RongyunUtils(@Value("${rongyun.appKey}") String appKey, @Value("${rongyun.appSecret}") String appSecret){
        rongCloud = RongCloud.getInstance(appKey, appSecret);
    }

    /**
    * @Description:
    * @Param:
    * @return:
    * @Author: 融云注册方法
    * @Date:
    */
    public String register(String userId,String name,String portraitUri){
        User user = rongCloud.user;
        /**
         *
         * 注册用户，生成用户在融云的唯一身份标识 Token
         */
        try {
            UserModel userModel = new UserModel()
                    .setId(userId)
                    .setName(name)
                    .setPortrait(portraitUri);
            TokenResult result = user.register(userModel);
            System.out.println("getToken:  " + result.toString());
            return result.toString();
        }catch (Exception e) {
            System.out.println("获取失败");
            return null;
        }
    }


    /**
    * @Description:
    * @Param:
    * @return:
    * @Author: 创建群组
    * @Date:
    */
    public String CreateGroupChat(String groupId,String groupName,String userIds){
            String[] arr = userIds.split(",");
            ArrayList<GroupMember> list = new ArrayList<>();
            for (String userId : arr) {
                GroupMember member = new GroupMember().setId(userId);
                list.add(member);
            }
            GroupMember[] members = list.toArray(new GroupMember[list.size()]);
            Group Group = rongCloud.group;
        try {
            GroupModel group = new GroupModel()
                    .setId(groupId)
                    .setMembers(members)
                    .setName(groupName);
        	Result groupCreateResult = (Result) Group.create(group);
            System.out.println("group create result:  " + groupCreateResult.toString());
            return groupCreateResult.toString();
        }catch(Exception e){
            System.out.println("创建群组失败");
            return null;
        }
    }

    /**
    * @Description:
    * @Param:
    * @return:
    * @Author: 邀请用户加入群组
    * @Date:
    */
    public  String JoinGroup(){
        try {
        GroupMember[] members = {new GroupMember().setId("ghJiu7H1"),new GroupMember().setId("ghJiu7H2")};
        GroupModel  group = new GroupModel()
                .setId("hdiuj87jj")
                .setMembers(members)
                .setName("groupName");
        Result groupInviteResult = (Result)rongCloud.group.invite(group);
        System.out.println("invite:  " + groupInviteResult.toString());
            return groupInviteResult.toString();
        }catch(Exception e){
            System.out.println("邀请用户加入群组失败");
            return null;
        }

    }

    /**
     * API 文档: http://www.rongcloud.cn/docs/server_sdk_api/group/group.html#join
     *
     * 邀请用户加入群组
     *
     */
    public String specifyGroup(String groupId,String userIds,String groupName){
        try {
            String[] arr = userIds.split(",");
            ArrayList<GroupMember> list = new ArrayList<>();
            for (String userId : arr) {
                GroupMember member = new GroupMember().setId(userId);
                list.add(member);
            }
            GroupMember[] members = list.toArray(new GroupMember[list.size()]);
            GroupModel group = new GroupModel()
                    .setId(groupId)
                    .setMembers(members)
                    .setName(groupName);
            Result groupInviteResult = (Result)rongCloud.group.invite(group);
            System.out.println("invite:  " + groupInviteResult.toString());
		System.out.println("join:  " + groupInviteResult.toString());
		return groupInviteResult.toString();
        }catch(Exception e){
            System.out.println("邀请用户加入群组失败");
            return null;
        }
    }

    /**
     * API 文档: http://www.rongcloud.cn/docs/server_sdk_api/group/group.html#quit
     *
     * 退出群组
     *
     */
    public String  signOut(String userId,String groupId,String groupName) {
        Group Group = rongCloud.group;
        try {
            GroupMember[] members = {new GroupMember().setId(userId), new GroupMember().setId(userId)};
            //封装数据
            GroupModel group = new GroupModel()
                    .setId(groupId)
                    .setMembers(members)
                    .setName(groupName);
            Result groupQuitResult = (Result) Group.quit(group);
            System.out.println("quit:  " + groupQuitResult.toString());
            return groupQuitResult.toString();
        } catch (Exception e) {
            System.out.println("退出群组失败");
            return null;
        }
    }

    /**
     * API 文档: http://www.rongcloud.cn/docs/server_sdk_api/message/chatroom.html#send
     *
     * 聊天室消息
     * */
    public String messageSend(String senderId,String chatroomId,String message){
        Chatroom chatroom = rongCloud.message.chatroom;
        try {

            String[] chatroomIds = {chatroomId};
            CustomTxtMessage ctm = new CustomTxtMessage(message);
            ChatroomMessage chatroomMessage = new ChatroomMessage()
                    .setSenderId(senderId)
                    .setTargetId(chatroomIds)
                    .setContent(ctm)
                    .setIsIncludeSender(1)
                    .setObjectName(ctm.getType());
            ResponseResult chatroomResult = chatroom.send(chatroomMessage);
            System.out.println("send chatroom message:  " + chatroomResult.toString());
            return chatroomResult.toString();
        } catch (Exception e) {
            System.out.println("发送消息失败");
            return null;
        }
    }




      /**
     * API 文档: http://www.rongcloud.cn/docs/server_sdk_api/message/system.html#send
     *
     * 发送系统消息
     *
     */
    public  String systemMessage(String senderId,String msgType,String msgContent) {
        register(senderId,"系统通知","");
        MsgSystem system = rongCloud.message.system;
        try {
            BroadcastMessage broadcastMessage = new BroadcastMessage();
            broadcastMessage.setSenderId(senderId);
            broadcastMessage.setObjectName(msgType);
            broadcastMessage.setContent(new TxtMessage(msgContent,null));
            ResponseResult result = system.broadcast(broadcastMessage);
            System.out.println("send system message:  " + result.toString());
            return result.toString();
        } catch (Exception e) {
            System.out.println("发送消息失败");
            return null;
        }
    }

    private BaseMessage buildMsg( SystemNotificationDto dto) {
        switch (dto.getMsgType()){
            case "RC:VcMsg":
                return new VoiceMessage(dto.getMsgContent(),dto.getMsgContentExtra(),dto.getDuration());
                default:
                    return new TxtMessage(dto.getMsgContent(),dto.getMsgContentExtra());
        }
    }


    /**
     * API 文档: http://www.rongcloud.cn/docs/server_sdk_api/message/private.html#send
     *
     * 发送单聊消息<文本, 语音, 文件类型 等消息类型>
     */
    public String singleSystemMessage(String userId, String senderId,String msgType,String msgContent,String msgExtra){
        String[] targetIds = {userId};
        Private Private = rongCloud.message.msgPrivate;
        try {
            SystemNotificationDto dto = new SystemNotificationDto();
            dto.setMsgContent(msgContent);
            dto.setMsgType(msgType);
            dto.setMsgContentExtra(msgExtra);
        PrivateMessage privateMessage = new PrivateMessage()
                .setSenderId(senderId)
                .setTargetId(targetIds)
                .setObjectName(msgType)
                .setContent(buildMsg(dto))
                .setPushContent("")
                .setVerifyBlacklist(0)
                .setIsPersisted(0)
                .setIsCounted(0)
                .setIsIncludeSender(0);
        ResponseResult privateResult = Private.send(privateMessage);
        System.out.println("send private getReqBody:  " + privateResult.getReqBody());
        System.out.println("send private message:  " + privateResult.toString());
        return privateResult.toString();
        } catch (Exception e) {
            System.out.println("发送消息失败");
            return null;
        }
    }

    public String updateGroup(String groupId,String groupName){
        try {

            GroupModel group = new GroupModel()
                    .setId(groupId)
                    .setName(groupName);
            Result result = rongCloud.group.update(group);
            System.out.println("update:  " + result.toString());
            return result.toString();
        }catch(Exception e){
            System.out.println("邀请用户加入群组失败");
            return null;
        }
    }

}
