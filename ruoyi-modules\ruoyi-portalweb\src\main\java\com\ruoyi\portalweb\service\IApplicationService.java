package com.ruoyi.portalweb.service;

import java.util.List;

import com.ruoyi.portalweb.api.domain.Application;


/**
 * 系统对接Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface IApplicationService 
{
    /**
     * 查询系统对接
     * 
     * @param applicationId 系统对接主键
     * @return 系统对接
     */
    public Application selectApplicationByApplicationId(Long applicationId);

    /**
     * 查询系统对接列表
     * 
     * @param application 系统对接
     * @return 系统对接集合
     */
    public List<Application> selectApplicationList(Application application);

    /**
     * 新增系统对接
     * 
     * @param application 系统对接
     * @return 结果
     */
    public int insertApplication(Application application);

    /**
     * 修改系统对接
     * 
     * @param application 系统对接
     * @return 结果
     */
    public int updateApplication(Application application);

    /**
     * 批量删除系统对接
     * 
     * @param applicationIds 需要删除的系统对接主键集合
     * @return 结果
     */
    public int deleteApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 删除系统对接信息
     * 
     * @param applicationId 系统对接主键
     * @return 结果
     */
    public int deleteApplicationByApplicationId(Long applicationId);

    /**
     * 根据appid获取应用详情
     * @param appId
     * @return
     */
	public Application selectApplicationByAppId(String appId);

	/**
	 * 查询所有应用
	 * @return
	 */
	public List<Application> selectAllApplicationList();
}
