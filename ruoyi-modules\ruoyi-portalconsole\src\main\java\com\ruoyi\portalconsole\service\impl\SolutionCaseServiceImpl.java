package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.SolutionCaseMapper;
import com.ruoyi.portalconsole.domain.SolutionCase;
import com.ruoyi.portalconsole.service.ISolutionCaseService;

/**
 * 解决方案实施案例Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class SolutionCaseServiceImpl implements ISolutionCaseService 
{
    @Autowired
    private SolutionCaseMapper solutionCaseMapper;

    /**
     * 查询解决方案实施案例
     * 
     * @param solutionCaseId 解决方案实施案例主键
     * @return 解决方案实施案例
     */
    @Override
    public SolutionCase selectSolutionCaseBySolutionCaseId(Long solutionCaseId)
    {
        return solutionCaseMapper.selectSolutionCaseBySolutionCaseId(solutionCaseId);
    }

    /**
     * 查询解决方案实施案例列表
     * 
     * @param solutionCase 解决方案实施案例
     * @return 解决方案实施案例
     */
    @Override
    public List<SolutionCase> selectSolutionCaseList(SolutionCase solutionCase)
    {
        return solutionCaseMapper.selectSolutionCaseList(solutionCase);
    }

    /**
     * 新增解决方案实施案例
     * 
     * @param solutionCase 解决方案实施案例
     * @return 结果
     */
    @Override
    public int insertSolutionCase(SolutionCase solutionCase)
    {
        solutionCase.setCreateTime(DateUtils.getNowDate());
        return solutionCaseMapper.insertSolutionCase(solutionCase);
    }

    /**
     * 修改解决方案实施案例
     * 
     * @param solutionCase 解决方案实施案例
     * @return 结果
     */
    @Override
    public int updateSolutionCase(SolutionCase solutionCase)
    {
        solutionCase.setUpdateTime(DateUtils.getNowDate());
        return solutionCaseMapper.updateSolutionCase(solutionCase);
    }

    /**
     * 批量删除解决方案实施案例
     * 
     * @param solutionCaseIds 需要删除的解决方案实施案例主键
     * @return 结果
     */
    @Override
    public int deleteSolutionCaseBySolutionCaseIds(Long[] solutionCaseIds)
    {
        return solutionCaseMapper.deleteSolutionCaseBySolutionCaseIds(solutionCaseIds);
    }

    /**
     * 删除解决方案实施案例信息
     * 
     * @param solutionCaseId 解决方案实施案例主键
     * @return 结果
     */
    @Override
    public int deleteSolutionCaseBySolutionCaseId(Long solutionCaseId)
    {
        return solutionCaseMapper.deleteSolutionCaseBySolutionCaseId(solutionCaseId);
    }
}
