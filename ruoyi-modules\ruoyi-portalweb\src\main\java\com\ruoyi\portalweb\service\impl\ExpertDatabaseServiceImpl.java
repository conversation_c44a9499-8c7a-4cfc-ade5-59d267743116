package com.ruoyi.portalweb.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.portalweb.api.domain.ExpertDatabase;
import com.ruoyi.portalweb.mapper.ExpertDatabaseMapper;
import com.ruoyi.portalweb.service.IExpertDatabaseService;
import com.ruoyi.portalweb.vo.ExpertDatabaseVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 专家库Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class ExpertDatabaseServiceImpl implements IExpertDatabaseService
{
    @Autowired
    private ExpertDatabaseMapper expertDatabaseMapper;

    /**
     * 查询专家库
     * 
     * @param expertDatabaseId 专家库主键
     * @return 专家库
     */
    @Override
    public ExpertDatabase selectExpertDatabaseByExpertDatabaseId(Long expertDatabaseId)
    {
        return expertDatabaseMapper.selectExpertDatabaseByExpertDatabaseId(expertDatabaseId);
    }

    /**
     * 查询专家库列表
     * 
     * @param expertDatabase 专家库
     * @return 专家库
     */
    @Override
    public List<ExpertDatabase> selectExpertDatabaseList(ExpertDatabaseVO expertDatabase)
    {
        return expertDatabaseMapper.selectExpertDatabaseList(expertDatabase);
    }

    /**
     * 新增专家库
     * 
     * @param expertDatabase 专家库
     * @return 结果
     */
    @Override
    public int insertExpertDatabase(ExpertDatabase expertDatabase)
    {
        expertDatabase.setCreateTime(DateUtils.getNowDate());
        return expertDatabaseMapper.insertExpertDatabase(expertDatabase);
    }

    /**
     * 修改专家库
     * 
     * @param expertDatabase 专家库
     * @return 结果
     */
    @Override
    public int updateExpertDatabase(ExpertDatabase expertDatabase)
    {
        expertDatabase.setUpdateTime(DateUtils.getNowDate());
        return expertDatabaseMapper.updateExpertDatabase(expertDatabase);
    }

    /**
     * 批量删除专家库
     * 
     * @param expertDatabaseIds 需要删除的专家库主键
     * @return 结果
     */
    @Override
    public int deleteExpertDatabaseByExpertDatabaseIds(Long[] expertDatabaseIds)
    {
        return expertDatabaseMapper.deleteExpertDatabaseByExpertDatabaseIds(expertDatabaseIds);
    }

    /**
     * 删除专家库信息
     * 
     * @param expertDatabaseId 专家库主键
     * @return 结果
     */
    @Override
    public int deleteExpertDatabaseByExpertDatabaseId(Long expertDatabaseId)
    {
        return expertDatabaseMapper.deleteExpertDatabaseByExpertDatabaseId(expertDatabaseId);
    }
}
