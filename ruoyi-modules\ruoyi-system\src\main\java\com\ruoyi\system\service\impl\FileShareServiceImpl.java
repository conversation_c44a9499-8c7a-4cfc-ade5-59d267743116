package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.FileShareMapper;
import com.ruoyi.system.domain.FileShare;
import com.ruoyi.system.service.IFileShareService;

/**
 * 文件共享Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-17
 */
@Service
public class FileShareServiceImpl implements IFileShareService 
{
    @Autowired
    private FileShareMapper fileShareMapper;

    /**
     * 查询文件共享
     * 
     * @param id 文件共享主键
     * @return 文件共享
     */
    @Override
    public FileShare selectFileShareById(Long id)
    {
        return fileShareMapper.selectFileShareById(id);
    }

    /**
     * 查询文件共享列表
     * 
     * @param fileShare 文件共享
     * @return 文件共享
     */
    @Override
    public List<FileShare> selectFileShareList(FileShare fileShare)
    {
        return fileShareMapper.selectFileShareList(fileShare);
    }

    /**
     * 新增文件共享
     * 
     * @param fileShare 文件共享
     * @return 结果
     */
    @Override
    public int insertFileShare(FileShare fileShare)
    {
        fileShare.setCreateTime(DateUtils.getNowDate());
        return fileShareMapper.insertFileShare(fileShare);
    }

    /**
     * 修改文件共享
     * 
     * @param fileShare 文件共享
     * @return 结果
     */
    @Override
    public int updateFileShare(FileShare fileShare)
    {
        fileShare.setUpdateTime(DateUtils.getNowDate());
        return fileShareMapper.updateFileShare(fileShare);
    }

    /**
     * 批量删除文件共享
     * 
     * @param ids 需要删除的文件共享主键
     * @return 结果
     */
    @Override
    public int deleteFileShareByIds(Long[] ids)
    {
        return fileShareMapper.deleteFileShareByIds(ids);
    }

    /**
     * 删除文件共享信息
     * 
     * @param id 文件共享主键
     * @return 结果
     */
    @Override
    public int deleteFileShareById(Long id)
    {
        return fileShareMapper.deleteFileShareById(id);
    }
}
