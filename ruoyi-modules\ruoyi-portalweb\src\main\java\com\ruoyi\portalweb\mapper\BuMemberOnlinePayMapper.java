package com.ruoyi.portalweb.mapper;

import java.util.List;
import com.ruoyi.portalweb.api.domain.BuMemberOnlinePay;

/**
 * 商城线上支付Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface BuMemberOnlinePayMapper 
{
    /**
     * 查询商城线上支付
     * 
     * @param id 商城线上支付主键
     * @return 商城线上支付
     */
    public BuMemberOnlinePay selectBuMemberOnlinePayById(Long id);

    /**
     * 查询商城线上支付列表
     * 
     * @param buMemberOnlinePay 商城线上支付
     * @return 商城线上支付集合
     */
    public List<BuMemberOnlinePay> selectBuMemberOnlinePayList(BuMemberOnlinePay buMemberOnlinePay);

    /**
     * 新增商城线上支付
     * 
     * @param buMemberOnlinePay 商城线上支付
     * @return 结果
     */
    public int insertBuMemberOnlinePay(BuMemberOnlinePay buMemberOnlinePay);

    /**
     * 修改商城线上支付
     * 
     * @param buMemberOnlinePay 商城线上支付
     * @return 结果
     */
    public int updateBuMemberOnlinePay(BuMemberOnlinePay buMemberOnlinePay);

    /**
     * 删除商城线上支付
     * 
     * @param id 商城线上支付主键
     * @return 结果
     */
    public int deleteBuMemberOnlinePayById(Long id);

    /**
     * 批量删除商城线上支付
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuMemberOnlinePayByIds(Long[] ids);

    BuMemberOnlinePay selectBuMemberOnlinePayByAppOrderNo(String appStoreOrderNo);
}
