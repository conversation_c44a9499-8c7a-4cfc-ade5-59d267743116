package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.SysUserFriendHistory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysUserFriendHistoryMapper {

    List<SysUserFriendHistory> getApplyFriendList(@Param("userName") String userName);

    SysUserFriendHistory getDetail(@Param("id") Long id, @Param("userName") String userName);

    int updateState(SysUserFriendHistory sysUserFriendHistory);

    int insertSelective(SysUserFriendHistory sysUserFriendHistory);

    int reloadApply(SysUserFriendHistory sysUserFriendHistory);
}