<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.FactoryPersonnelMapper">
    
    <resultMap type="FactoryPersonnel" id="FactoryPersonnelResult">
        <result property="id"    column="id"    />
        <result property="factoryId"    column="factory_id"    />
        <result property="technicianName"    column="technician_name"    />
        <result property="technicalType"    column="technical_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectFactoryPersonnelVo">
        select id, factory_id, technician_name, technical_type, create_time, update_time from factory_personnel
    </sql>

    <select id="selectFactoryPersonnelList" parameterType="FactoryPersonnel" resultMap="FactoryPersonnelResult">
        <include refid="selectFactoryPersonnelVo"/>
        <where>  
            <if test="factoryId != null "> and factory_id = #{factoryId}</if>
            <if test="technicianName != null  and technicianName != ''"> and technician_name like concat('%', #{technicianName}, '%')</if>
            <if test="technicalType != null  and technicalType != ''"> and technical_type = #{technicalType}</if>
        </where>
    </select>
    
    <select id="selectFactoryPersonnelById" parameterType="Long" resultMap="FactoryPersonnelResult">
        <include refid="selectFactoryPersonnelVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFactoryPersonnel" parameterType="FactoryPersonnel" useGeneratedKeys="true" keyProperty="id">
        insert into factory_personnel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="factoryId != null">factory_id,</if>
            <if test="technicianName != null and technicianName != ''">technician_name,</if>
            <if test="technicalType != null">technical_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="factoryId != null">#{factoryId},</if>
            <if test="technicianName != null and technicianName != ''">#{technicianName},</if>
            <if test="technicalType != null">#{technicalType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateFactoryPersonnel" parameterType="FactoryPersonnel">
        update factory_personnel
        <trim prefix="SET" suffixOverrides=",">
            <if test="factoryId != null">factory_id = #{factoryId},</if>
            <if test="technicianName != null and technicianName != ''">technician_name = #{technicianName},</if>
            <if test="technicalType != null">technical_type = #{technicalType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFactoryPersonnelById" parameterType="Long">
        delete from factory_personnel where id = #{id}
    </delete>

    <delete id="deleteFactoryPersonnelByIds" parameterType="String">
        delete from factory_personnel where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>