package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.FactoryPersonnel;
import com.ruoyi.system.service.IFactoryPersonnelService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 工厂人员能力Controller
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@RestController
@RequestMapping("/personnel")
public class FactoryPersonnelController extends BaseController
{
    @Autowired
    private IFactoryPersonnelService factoryPersonnelService;

    /**
     * 查询工厂人员能力列表
     */
    @RequiresPermissions("system:personnel:list")
    @GetMapping("/list")
    public TableDataInfo list(FactoryPersonnel factoryPersonnel)
    {
        startPage();
        List<FactoryPersonnel> list = factoryPersonnelService.selectFactoryPersonnelList(factoryPersonnel);
        return getDataTable(list);
    }

    /**
     * 导出工厂人员能力列表
     */
    @RequiresPermissions("system:personnel:export")
    @Log(title = "工厂人员能力", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FactoryPersonnel factoryPersonnel)
    {
        List<FactoryPersonnel> list = factoryPersonnelService.selectFactoryPersonnelList(factoryPersonnel);
        ExcelUtil<FactoryPersonnel> util = new ExcelUtil<FactoryPersonnel>(FactoryPersonnel.class);
        util.exportExcel(response, list, "工厂人员能力数据");
    }

    /**
     * 获取工厂人员能力详细信息
     */
    @RequiresPermissions("system:personnel:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(factoryPersonnelService.selectFactoryPersonnelById(id));
    }

    /**
     * 新增工厂人员能力
     */
    @RequiresPermissions("system:personnel:add")
    @Log(title = "工厂人员能力", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FactoryPersonnel factoryPersonnel)
    {
        return toAjax(factoryPersonnelService.insertFactoryPersonnel(factoryPersonnel));
    }

    /**
     * 修改工厂人员能力
     */
    @RequiresPermissions("system:personnel:edit")
    @Log(title = "工厂人员能力", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FactoryPersonnel factoryPersonnel)
    {
        return toAjax(factoryPersonnelService.updateFactoryPersonnel(factoryPersonnel));
    }

    /**
     * 删除工厂人员能力
     */
    @RequiresPermissions("system:personnel:remove")
    @Log(title = "工厂人员能力", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(factoryPersonnelService.deleteFactoryPersonnelByIds(ids));
    }
}
