package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.ClassicCaseIndustryMapper;
import com.ruoyi.portalconsole.domain.ClassicCaseIndustry;
import com.ruoyi.portalconsole.service.IClassicCaseIndustryService;

/**
 * 典型案例行业Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-07
 */
@Service
public class ClassicCaseIndustryServiceImpl implements IClassicCaseIndustryService 
{
    @Autowired
    private ClassicCaseIndustryMapper classicCaseIndustryMapper;

    /**
     * 查询典型案例行业
     * 
     * @param classicCaseIndustryId 典型案例行业主键
     * @return 典型案例行业
     */
    @Override
    public ClassicCaseIndustry selectClassicCaseIndustryByClassicCaseIndustryId(Long classicCaseIndustryId)
    {
        return classicCaseIndustryMapper.selectClassicCaseIndustryByClassicCaseIndustryId(classicCaseIndustryId);
    }

    /**
     * 查询典型案例行业列表
     * 
     * @param classicCaseIndustry 典型案例行业
     * @return 典型案例行业
     */
    @Override
    public List<ClassicCaseIndustry> selectClassicCaseIndustryList(ClassicCaseIndustry classicCaseIndustry)
    {
        return classicCaseIndustryMapper.selectClassicCaseIndustryList(classicCaseIndustry);
    }

    /**
     * 新增典型案例行业
     * 
     * @param classicCaseIndustry 典型案例行业
     * @return 结果
     */
    @Override
    public int insertClassicCaseIndustry(ClassicCaseIndustry classicCaseIndustry)
    {
        classicCaseIndustry.setCreateTime(DateUtils.getNowDate());
        return classicCaseIndustryMapper.insertClassicCaseIndustry(classicCaseIndustry);
    }

    /**
     * 修改典型案例行业
     * 
     * @param classicCaseIndustry 典型案例行业
     * @return 结果
     */
    @Override
    public int updateClassicCaseIndustry(ClassicCaseIndustry classicCaseIndustry)
    {
        classicCaseIndustry.setUpdateTime(DateUtils.getNowDate());
        return classicCaseIndustryMapper.updateClassicCaseIndustry(classicCaseIndustry);
    }

    /**
     * 批量删除典型案例行业
     * 
     * @param classicCaseIndustryIds 需要删除的典型案例行业主键
     * @return 结果
     */
    @Override
    public int deleteClassicCaseIndustryByClassicCaseIndustryIds(Long[] classicCaseIndustryIds)
    {
        return classicCaseIndustryMapper.deleteClassicCaseIndustryByClassicCaseIndustryIds(classicCaseIndustryIds);
    }

    /**
     * 删除典型案例行业信息
     * 
     * @param classicCaseIndustryId 典型案例行业主键
     * @return 结果
     */
    @Override
    public int deleteClassicCaseIndustryByClassicCaseIndustryId(Long classicCaseIndustryId)
    {
        return classicCaseIndustryMapper.deleteClassicCaseIndustryByClassicCaseIndustryId(classicCaseIndustryId);
    }
}
