package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.PolicyInformation;
import com.ruoyi.portalweb.service.IPolicyInformationService;
import com.ruoyi.portalweb.vo.PolicyInformationVO;
import com.ruoyi.system.api.RemoteUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 政策资讯Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Api(value = "7.政策资讯", tags = "7.政策资讯")
@RestController
@RequestMapping("/PolicyInformation")
public class PolicyInformationController extends BaseController
{
    @Autowired
    private IPolicyInformationService policyInformationService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询政策资讯列表
     */
    @ApiOperation(value = "查询政策资讯列表")
    @RequiresPermissions("portalweb:PolicyInformation:list")
    @GetMapping("/list")
    public TableDataInfo list(PolicyInformationVO policyInformation)
    {
        startPage();
        PageUtils.setOrderBy("create_time DESC");
        List<PolicyInformation> list = policyInformationService.selectPolicyInformationList(policyInformation);
        return getDataTable(list);
    }

    /**
     * 查询政策资讯列表
     */
    @ApiOperation(value = "查询政策资讯列表")
    @GetMapping("/recommend")
    public TableDataInfo recommend(PolicyInformationVO policyInformation)
    {
        List<PolicyInformation> list = policyInformationService.selectRecommendPolicyInformationList(policyInformation);
        return getDataTable(list);
    }

    /**
     * 查询政策资讯列表
     */
    @ApiOperation(value = "查询政策资讯列表")
    @GetMapping("/listDesk")
    public TableDataInfo listDesk(PolicyInformationVO policyInformation)
    {
        startPage();
        PageUtils.setOrderBy("top DESC,create_time DESC");
        List<PolicyInformation> list = policyInformationService.selectPolicyInformationList(policyInformation);
        return getDataTable(list);
    }

    /**
     * 导出政策资讯列表
     */
    @ApiOperation(value = "导出政策资讯列表")
    @RequiresPermissions("portalweb:PolicyInformation:export")
    @Log(title = "政策资讯", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PolicyInformationVO policyInformation)
    {
        List<PolicyInformation> list = policyInformationService.selectPolicyInformationList(policyInformation);
        ExcelUtil<PolicyInformation> util = new ExcelUtil<PolicyInformation>(PolicyInformation.class);
        util.exportExcel(response, list, "政策资讯数据");
    }

    /**
     * 获取政策资讯详细信息
     */
    @ApiOperation(value = "获取政策资讯详细信息")
    @RequiresPermissions("portalweb:PolicyInformation:query")
    @GetMapping(value = "/{policyInformationId}")
    public AjaxResult getInfo(@PathVariable("policyInformationId") Long policyInformationId)
    {
        return success(policyInformationService.selectPolicyInformationByPolicyInformationId(policyInformationId));
    }

    /**
     * 获取政策资讯详细信息
     */
    @ApiOperation(value = "获取政策资讯详细信息")
    @GetMapping(value = "/detailDesk")
    public AjaxResult getInfoDetail(PolicyInformation policyInformation)
    {
        return success(policyInformationService.selectPolicyInformationByPolicyInformationId(policyInformation.getPolicyInformationId()));
    }

    /**
     * 新增政策资讯
     */
    @ApiOperation(value = "新增政策资讯")
    @RequiresPermissions("portalweb:PolicyInformation:add")
    @Log(title = "政策资讯", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PolicyInformation policyInformation)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        policyInformation.setUpdateBy(userNickName.getData());
        policyInformation.setCreateBy(userNickName.getData());
        return toAjax(policyInformationService.insertPolicyInformation(policyInformation));
    }

    /**
     * 修改政策资讯
     */
    @ApiOperation(value = "修改政策资讯")
    @RequiresPermissions("portalweb:PolicyInformation:edit")
    @Log(title = "政策资讯", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PolicyInformation policyInformation)
    {
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        policyInformation.setUpdateBy(userNickName.getData());
        return toAjax(policyInformationService.updatePolicyInformation(policyInformation));
    }

    /**
     * 删除政策资讯
     */
    @ApiOperation(value = "删除政策资讯")
    @RequiresPermissions("portalweb:PolicyInformation:remove")
    @Log(title = "政策资讯", businessType = BusinessType.DELETE)
	@DeleteMapping("/{policyInformationIds}")
    public AjaxResult remove(@PathVariable Long[] policyInformationIds)
    {
        return toAjax(policyInformationService.deletePolicyInformationByPolicyInformationIds(policyInformationIds));
    }
}
