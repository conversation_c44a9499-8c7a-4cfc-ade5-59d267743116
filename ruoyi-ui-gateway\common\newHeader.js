
Vue.component('pc-newheader', {
    props: ['token', 'userinfo', 'sel'],
    data: function() {
        return {
            qyList: [],
            hyList: [],
            typeList: [

                {
                    typeName: "领域解决方案",
                    id: 2
                },
                {
                    typeName: "行业解决方案",
                    id: 1
                }
            ],
            allTypeList: [],
            thisTypelist:[],
            yiIndex:0,
            bannerShow: false,
            sonList:[],
            loading:true,
            bannerShow1: false,
        }
    },
    methods: {
        showLogin() {
            // window.location.href = '../zhucedenglu.html'
            // return;
            if (this.token) {
                window.location.href = '/newPages/person/main.html?token=' + this.token
            } else {
                window.location.href = '../zhucedenglu.html'
                // let url;
                // var str = window.location.protocol + '//' + window.location.hostname + window.location.pathname
                // var result = encodeURIComponent(str)
                // if (window.location.host == 'test.ningmengdou.com') {
                //     url = "https://ssotest.ningmengdou.com/single/login?returnUrl=" + result
                // } else if (window.location.host == 'www.ningmengdou.com') {
                //     url = "https://sso.ningmengdou.com/single/newLogin?returnUrl=" + result
                // }
                // window.location.href = url
            }
        },
        goXipin() {
            let url = 'https://xp-tech.ningmengdou.com/kczq/xipin_pc/01index.html'
            if (this.token) {
                url = 'https://xp-tech.ningmengdou.com/kczq/xipin_pc/01index.html?token=' + this.token + '&userName=' + this.userinfo
                    .userName
            }
            window.location.href = url
        },
        goJicaiShop() {
            window.location.href = "https://mdy.ningmengdou.com";
        },
        loginout() {
            let url;
            // var str = window.location.protocol + '//' + window.location.hostname + window.location.pathname + window.location.search
            // var result = encodeURIComponent(str)
            // if (window.location.host == 'test.ningmengdou.com') {
            //     url = "https://ssotest.ningmengdou.com/single/logout?returnUrl=" + result
            // } else if (window.location.host == 'www.ningmengdou.com') {
            //     url = "https://sso.ningmengdou.com/single/logout?returnUrl=" + result
            // }
            YS.deleteJsonFetch('/auth/portallogout').then(res => {
                alert('正在退出，请稍等')
                if(res.code == 200){
                    window.location.href = '/index.html'
                    window.sessionStorage.clear()
                }
                // this.allTypeList = res.rows;
                // this.thisTypelist.push(this.allTypeList[0])
            });
        },
        // getQy() {
        //     let data = {
        //         pageNum: 1,
        //         pageSize: 50,
        //         zoneType: 2,
        //     }
        //     axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
        //     let result = axios({
        //         method: 'get',
        //         url: 'https://www.ningmengdou.com/prod-api/uuc/industryZone/list',
        //         params: data,
        //         transformRequest: [function(data) {
        //             let ret = '';
        //             for (let i in data) {
        //                 ret += encodeURIComponent(i) + '=' + encodeURIComponent(data[i]) + "&";
        //             }
        //             return ret;
        //         }],
        //     }).then(resp => {
        //         if (resp.data.code == 200) {
        //             this.qyList = resp.data.rows;
        //         } else {
        //             //	alert(resp.data.msg)
        //         }
        //
        //     }).catch(error => {
        //         return "exception=" + error;
        //     });
        // },
        // getHy() {
        //     let data = {
        //         pageNum: 1,
        //         pageSize: 50,
        //         zoneType: 1,
        //     }
        //     axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
        //     let result = axios({
        //         method: 'get',
        //         url: 'https://www.ningmengdou.com/prod-api/uuc/industryZone/list',
        //         params: data,
        //         transformRequest: [function(data) {
        //             let ret = '';
        //             for (let i in data) {
        //                 ret += encodeURIComponent(i) + '=' + encodeURIComponent(data[i]) + "&";
        //             }
        //             return ret;
        //         }],
        //     }).then(resp => {
        //         if (resp.data.code == 200) {
        //             this.hyList = resp.data.rows;
        //         } else {
        //             //	alert(resp.data.msg)
        //         }
        //
        //     }).catch(error => {
        //         return "exception=" + error;
        //     });
        // },
        // 解决方案 全部
        getAllList(){
            YS.get('/portalweb/solution/solutionDeskList').then(res => {
                this.loading = false;
                let d = res.data;
                let list = [];
                for(var i in d){
                    let item = {
                        typeName: i,
                        children: []
                    }
                    let sitem = {};
                    for(var j in d[i]){
                        // console.log(d[i][j])
                        sitem = {typeName: j, children:d[i][j] }
                        item.children.push(sitem)
                    }
                    list.push(item)
                }
                this.allTypeList = list;
                this.thisTypelist = [this.allTypeList[0]]
            });
        },
        // 一级 解决方案
        // getType(val) {
        //     YS.get('uuc/newSolution/type/list?parentId='+val, {}).then(res => {
        //         this.typeList = res.rows;
        //     });
        // },
        changeSubMenu(item,index){
            this.loading = true
            this.thisTypelist = []
            this.yiIndex = index
            this.thisTypelist = [this.allTypeList[index]]
            this.loading = false;
            // setTimeout(()=>{
            //     this.loading = false;
            //     this.waterFall()
            // },500)
        },
        jumpDetail(item){
            window.location.href = '/newPages/solutionDetail.html?id=' + item.solutionId
        },
        open(url){
            window.open(url)
        },
        getSonList(){
            YS.getList('uuc/child/website/list', {
                pageNum: 1,
                pageSize: 100,
            }).then(res => {
                console.log(res);
                this.sonList = res.rows
                console.log(this.sonList)
        })},
        sonBanner(){
            this.bannerShow = true
        },
        sonBanner1(){
             this.bannerShow = false
        },
        sonBanner3(){
            this.bannerShow1 = true
        },
        sonBanner4(){
             this.bannerShow1 = false
        },
        hoverMenu(){
            this.loading = true
            setTimeout(()=>{
                this.waterFall()
            },500)
        },
        toEN(){
            window.location.href = 'https://mdgj.ningmengdou.com/website/newPages/index.html'
        },
        waterFall() {
            let item = []
            item=  document.getElementsByClassName("itemnode")
            let width = (1000 -181) / 3
            let columnCount = 3
            let hrr = []
            for(let i= 0 ;i<item.length;i++){
                //定位第一行的图片
                if(i<columnCount){
                    item[i].style.top = "0px"
                    item[i].style.left = i* width+"px"
                    hrr.push(item[i].offsetHeight + 20)
                }else{
                    //第一行之后的
                    //选择总高度最小的列
                    let min = Math.min(...hrr)
                    let index = hrr.indexOf(min)
                    //将每个元素定位到当前总高度最小的列下
                    item[i].style.top = min+"px"
                    item[i].style.left = index * width+"px"
                    //当前定位的元素加入该列
                    hrr[index] += item[i].offsetHeight + 20
                }
            }
            this.loading = false

        },
        toJinRong(){
            // 产业金融 跳转
            // if(this.token){
            //     // 已登录获取tickt后跳转
            //     YS.getUserFetch('/portalweb/Member/getTicket', {}).then(resT => {
            //         window.open('http://116.171.110.240:85/wel/index?ticket=' + resT.data,'blank')
            //         //window.location.href = 'http://116.171.110.240:85/wel/index?ticket=' + resT.data
            //     })
            // } else {
            //     // 未登录先跳转登录后再获取tickt后跳转
            //     window.open('../zhucedenglu.html?to=cyjr','blank')
            //     //window.location.href = '../zhucedenglu.html?to=cyjr'
            // }
            window.open('https://cyjr.qdniiot.com/wel/index','_blank')
        },
        //跳转到供应链协同
        gogyl(){
            // if(this.token){
            //     // 已登录获取tickt后跳转
            //     YS.getUserFetch('/portalweb/Member/getTicket', {}).then(resT => {
            //         window.open('http://116.171.110.240:99/chain/?ticket=' + resT.data,'blank')
            //     })
            // } else {
            //     // 未登录先跳转登录后再获取tickt后跳转
            //     window.open('../zhucedenglu.html?to=gylxt','blank')
            // }
            window.open('https://gyl.qdniiot.com/chain/','_blank')
        },
        //跳转到数字化诊断 单点登录
        goszh(){
            if(this.token){
                // 已登录获取tickt后跳转
                YS.getUserFetch('/portalweb/Member/getTicket', {}).then(resT => {
                    window.open('https://szhzd.qdniiot.com/#/home?ticket=' + resT.data,'_blank')
                })
            } else {
                // 未登录先跳转登录后再获取tickt后跳转
                window.open('../zhucedenglu.html?to=szhzd','_blank')
            }
            //window.open('http://116.171.110.240:96/#/home')
        },
        //跳转到协同办公平台
        goXt(){
            if(this.token){
                // 已登录获取tickt后跳转
                YS.getUserFetch('/portalweb/Member/getTicket', {}).then(resT => {
                    window.open('https://xtbg.qdniiot.com/?ticket=' + resT.data,'_blank')
                })
            } else {
                // 未登录先跳转登录后再获取tickt后跳转
                window.open('../zhucedenglu.html?to=xtbg','_blank')
            }
            // window.open('http://116.171.110.240:88/','blank')
        },
        //跳转到iot
        goIOT(){
            // if(this.token){
            //     // 已登录获取tickt后跳转
            //     YS.getUserFetch('/portalweb/Member/getTicket', {}).then(resT => {
            //         // window.open('http://116.171.110.240:90/?ticket=' + resT.data,'blank')
            //         window.open('http://116.171.110.240:90/transferPage?ticket=' + resT.data,'blank')
            //     })
            // } else {
            //     // 未登录先跳转登录后再获取tickt后跳转
            //     window.open('../zhucedenglu.html?to=iot','blank')
            // }
            window.open('https://iot.qdniiot.com','_blank')
        }
    },
    mounted() {
        // 临时注释 wlj
        // this.getQy()
        // this.getHy()
        // this.getType(0)
        this.getAllList()
        // this.getSonList()
    },
    template: `<header>
        <div class="son" style="display: none">
        <div style="float: right;margin-left: 40px;  line-height: 35px;cursor: pointer;" @mouseover="sonBanner3()">中国站<i class="el-icon-caret-bottom"></i></div> 
      <div style="float: right;margin-left: 40px;  line-height: 35px;cursor: pointer;" @mouseover="sonBanner()">檬豆子站<i class="el-icon-caret-bottom"></i></div> 
      <div style="float: right;line-height: 35px;cursor: pointer;"><i class="el-icon-phone"></i>4008-939-365</div>
      </div>
      <div class="sonBannerall" v-if="bannerShow" @mouseleave="sonBanner1()">
      <div class="sonBanner" >
      <div v-for="(item,index) in sonList" class="sonList" @click="open(item.platUrl)">
          <H5 style="font-size: 16px;width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{item.platName}}</H5>
         <p style="font-size: 12px;width: 100%" class="platDec">{{item.platDec}}</p>
      </div>
      </div> 
      </div>
      <div class="sonBanner1" v-if="bannerShow1"  @mouseleave="sonBanner4()">
        <p style="color: #de791b;">简体中文</p>
        <p @click="toEN">Intl-English</p>
      </div> 
      <div class="black"  v-if="bannerShow"></div>
    <div class="wrap">
    
      <nav id="nav" style="width:1200px">
<!--        <div class="logo"><a href="../newPages/index.html"><img src="../images/logo3.png"></a></div>-->
        <a href="/index.html" class="logo"><img src="../images/new/logo.svg"/><span>黔东南州</span>工业互联网平台</a>
        <ul class="nav" style='padding-left: 96px;width: 1200px;'>
           <li :class="['nav-item',sel==1?'active':'']"><a href="/index.html">首页</a></li>
           <li :class="['nav-item',sel==19?'active':'']"><a href="https://mom.hongzhicn.com">协同办公</a></li>
           <li :class="['nav-item',sel==14?' active':'']" style="position: relative">
            <a  href="javascript:void(0)">服务对接</a>
            <div class="subMenu subMenu1 gxdj" style=" margin-left: 0px; left: 0px; display: none; height: 150px; padding-top: 0px; margin-top: 30px; padding-bottom: 0px; margin-bottom: 0px;">
                <div class="supplySecond">
                    <!--<div class="menuSecond gxdt">
                        <a href="javascript:void(0)">供需大厅</a>
                        <div class="subMenugx" style="width: 100px; margin-left: 0px; left: 92px; display: none; height: 72px; padding-top: 0px; top:0; padding-bottom: 0px; margin-bottom: 0px;">
                            <div class="supplySecond">
                                <div class="menuSecond"><a href="/newPages/demandHall.html">需求大厅</a></div> 
                                <div class="menuSecond"><a href="/newPages/supplyHall.html">供给大厅</a></div>
                            </div>
                        </div>
                    </div> -->
                    <div class="menuSecond"><a href="/newPages/demandHall.html">需求大厅</a></div> 
                    <div class="menuSecond"><a href="/newPages/supplyHall.html">供给大厅</a></div>
                    <div class="menuSecond"><a href="/newPages/enterpriseDirectory.html">企业名录</a></div>
                    <div class="menuSecond"><a href="/newPages/expertThinkTank.html">专家智库</a></div>
                </div>
            </div>
          </li>
          <li :class="['nav-item',sel==13?' active':'']"> <a @click="gogyl" href="javascript:;">供应链协同</a> </li>
           <li :class="['nav-item',sel==12?' active':'']"> <a href="javascript:;" @click="toJinRong">产业金融</a> </li>
          <li :class="['nav-item',sel==15?' active':'']"> <a href="/newPages/scienceTechnologyIndex.html" >产学研</a> </li>
          <li :class="['nav-item',sel==20?' active':'']"> <a href="https://nhgl.qdniiot.com/" >能耗管理</a> </li>
           <li :class="['nav-item',sel==5?' active':'']" style="position: relative;">
            <a href="/newPages/gongyefuwu.html" >数字化服务</a>
            <!-- 二级菜单 S-->
            <div class="subMenu subMenu1 szfw" style="z-index:2; margin-left: 0px; left: 0px; display: none; height: 186px; padding-top: 0px; margin-top: 30px; padding-bottom: 0px; margin-bottom: 0px;">
                <div class="supplySecond">
                    <div class="menuSecond"><a href="javascript:;" @click="goszh">数字化诊断</a></div> 
                    <div class="menuSecond jjfas">
                        <a href="/newPages/solutionIndex.html" >解决方案</a>
                        <!-- 三级菜单 S-->
                        <div class="subMenus" >
                            <div class="subMenubox">
                                <div class="subMenuleft">
                                    <div>
                                        <div v-for="(item,index) in typeList" :key="index" :class="['subMenuleftTitle',yiIndex == index?'subMenuleftTitlehover':'']" @click="changeSubMenu(item,index)">
                                                {{item.typeName}}
                                        </div>
                                    </div>
                                    <div>
                                        <a href="/newPages/dianxinganli.html" >典型案例</a>
                                    </div>
                                    <div><a href="/newPages/solutionIndex.html">查看全部解决方案&nbsp;<i class="el-icon-arrow-right"></i></a></div>
                                </div>
                                <div class="subMenuright" v-loading="loading">
                                    <div v-for="(item,index) in thisTypelist" :key="index">
                                        <div class="yijiTitle">{{item.typeName}}&nbsp;&nbsp;&nbsp;<i class="el-icon-arrow-right"></i></div>
                                        <div class="erjiFlex">
                                            <div v-for="(eitem,eindex) in item.children"  :key="eindex" class="itemnode">
                                                <div class="erjiTitle">{{eitem.typeName}}</div>
                                                <div v-for="(sitem,sindex) in eitem.children" :key="sindex" class="sanjiTitle">
                                                    <div @click="jumpDetail(sitem)">{{sitem.solutionName}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 三级菜单 E-->
                    </div>
                    <div class="menuSecond"><a href="/newPages/yingyongshangdian.html" >应用商店</a></div>
                    <div class="menuSecond"><a href="/newPages/gongyefuwu.html">安全防护</a></div>
                    <div class="menuSecond"><a href="/newPages/creditRepair.html">信用修复</a></div>
                </div>
            </div>
            <!-- 二级菜单 E-->
          </li>
        <!--<li :class="['nav-item',sel==16?' active':'']"><a href="javascript:void(0)"  @click='goIOT'>数字应用平台</a></li>-->
          <li :class="['nav-item',sel==8?' active':'']" style="position: relative;">
            <a href="/newPages/zhengcefuwu.html" >政策服务</a>
            <!-- 二级菜单 S-->
             <div class="subMenu subMenu1 gxdj" style=" margin-left: 0px; left: 0px; display: none; height: 114px; padding-top: 0px; margin-top: 30px; padding-bottom: 0px; margin-bottom: 0px;">
                <div class="supplySecond">
                    <div class="menuSecond"><a href="/newPages/zcServe.html">政策资讯</a></div> 
                    <div class="menuSecond"><a href="/newPages/policyDeclare.html">项目申报</a></div>
                    <div class="menuSecond"><a href="/newPages/auxiliaryDeclare.html">辅助申报</a></div>
                </div>
            </div>
          </li>
          
          <!--<li :class="['nav-item',sel==2?' active':'']" style="position: relative"><a  href="javascript:void(0)">供需大厅</a>-->
              <!-- 二级菜单 S-->
              <!-- <div class="subMenu subMenu1" style="width: 146px; margin-left: 0px; left: 0px; display: none; height: 72px; padding-top: 0px; margin-top: 30px; padding-bottom: 0px; margin-bottom: 0px;">
                   <div class="supplySecond">
                       <div class="menuSecond"><a href="/newPages/demandHall.html">需求大厅</a></div> 
                       <div class="menuSecond"><a href="/newPages/supplyHall.html">供给大厅</a></div>
                   </div>
               </div>
           </li>-->
           <!--<li :class="['nav-item',sel==3?' active':'']"> <a href="javascript:void(0)">解决方案</a> -->
            <!-- 二级菜单 S-->
            <!--<div class="subMenu">
                <div class="subMenubox">
                    <div class="subMenuleft">
                        <div>
                            <div v-for="(item,index) in typeList" :key="index" :class="['subMenuleftTitle',yiIndex == index?'subMenuleftTitlehover':'']" @click="changeSubMenu(item,index)">
                                    {{item.typeName}}
                            </div>
                        </div>
                        <div><a href="/newPages/solutionIndex.html">查看全部解决方案&nbsp;<i class="el-icon-arrow-right"></i></a></div>
                    </div>
                    <div class="subMenuright" v-loading="loading">
                        <div v-for="(item,index) in thisTypelist" :key="index">
                            <div class="yijiTitle">{{item.typeName}}&nbsp;&nbsp;&nbsp;<i class="el-icon-arrow-right"></i></div>
                            <div class="erjiFlex">
                                <div v-for="(eitem,eindex) in item.children"  :key="eindex" class="itemnode">
                                    <div class="erjiTitle">{{eitem.typeName}}</div>
                                    <div v-for="(sitem,sindex) in eitem.children" :key="sindex" class="sanjiTitle">
                                        <div @click="jumpDetail(sitem)">{{sitem.solutionName}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>--> 
            <!-- 二级菜单 E--> 
            <!--</li>--> 
            <!--<li :class="['nav-item',sel==4?' active':'']"><a href="/newPages/dianxinganli.html" >典型案例</a></li>--> 
          
          <!-- <li :class="['nav-item',sel==6?' active':'']"><a href="/newPages/yingyongshangdian.html" >应用商店</a></li>-->
          <!-- <li :class="['nav-item',sel==7?' active':'']"><a href="/newPages/scienceTechnologyIndex.html" >科技创新</a></li>-->
          <li :class="['nav-item',sel==10?' active':'']"><a href="/newPages/newsInformation.html"  >动态资讯</a></li>
            <li :class="['nav-item',sel==21?' active':'']" style="position: relative;">
            <a href="/newPages/introduction.html" >关于我们</a>
            <!-- 二级菜单 S-->
             <div class="subMenu subMenu1 gxdj" style=" margin-left: 0px; left: 0px; display: none; height: 74px; padding-top: 0px; margin-top: 30px; padding-bottom: 0px; margin-bottom: 0px;">
                <div class="supplySecond">
                    <div class="menuSecond"><a href="/newPages/ecologicalCollaboration.html" >生态协作</a></div> 
                    <div class="menuSecond"><a href="/newPages/introduction.html">平台介绍</a></div>
                </div>
            </div>
          </li>



          <!-- <li :class="['nav-item',sel==9?' active':'']"><a href="/newPages/ecologicalCollaboration.html" >生态协作</a></li>
         <li :class="['nav-item',sel==11?' active':'']"> <a href="/newPages/introduction.html">平台介绍</a> </li>-->
         <li :class="['nav-item',sel==18?' active':'']"> <a href="http://www.qdnzgc.com/" target='_blank'>智慧物流</a> </li>
        </ul>
        <div class="person">
		<a href="javascript:void(0)" @click="showLogin" v-if='!token' class="jrgzt" style='margin-right: 20px;'>登录</a>
		<a href="javascript:void(0)" @click="showLogin" v-if="token"  class="jrgzt" >进入工作台</a>
		<a href="javascript:void(0)" @click="loginout" v-if="token" class="loginoutBtn">退出</a></div>
        <div class="clear"></div>
      </nav>
    </div>
  </header>`

});
