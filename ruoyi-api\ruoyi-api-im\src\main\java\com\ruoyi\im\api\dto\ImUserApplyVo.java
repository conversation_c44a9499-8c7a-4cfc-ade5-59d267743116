package com.ruoyi.im.api.dto;/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.api.dto
 * @ClassName: ImUserVo
 * @Author: ${maguojun}
 * @Description: 返回的vo
 * @Date: 2022/3/14 21:52
 * @Version: 1.0
 */

import com.ruoyi.im.api.domain.ImUser;
import com.ruoyi.im.api.domain.ImUserApply;
import lombok.Data;

import java.util.List;

/**
 * @program: ruoyi
 *
 * @description: 返回的vo
 *
 * @author: Ma<PERSON><PERSON><PERSON><PERSON>
 *
 * @create: 2022-03-14 21:52
 **/
@Data
public class ImUserApplyVo extends ImUserApply {

    private ImUser user;
}
