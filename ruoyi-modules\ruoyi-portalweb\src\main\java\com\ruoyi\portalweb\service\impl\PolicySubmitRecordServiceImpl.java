package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.PolicySubmitRecord;
import com.ruoyi.portalweb.mapper.PolicySubmitRecordMapper;
import com.ruoyi.portalweb.service.IPolicySubmitRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 政策申报记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class PolicySubmitRecordServiceImpl implements IPolicySubmitRecordService
{
    @Autowired
    private PolicySubmitRecordMapper policySubmitRecordMapper;

    /**
     * 查询政策申报记录
     * 
     * @param policySubmitRecordId 政策申报记录主键
     * @return 政策申报记录
     */
    @Override
    public PolicySubmitRecord selectPolicySubmitRecordByPolicySubmitRecordId(Long policySubmitRecordId)
    {
        return policySubmitRecordMapper.selectPolicySubmitRecordByPolicySubmitRecordId(policySubmitRecordId);
    }

    /**
     * 查询政策申报记录列表
     * 
     * @param policySubmitRecord 政策申报记录
     * @return 政策申报记录
     */
    @Override
    public List<PolicySubmitRecord> selectPolicySubmitRecordList(PolicySubmitRecord policySubmitRecord)
    {
        return policySubmitRecordMapper.selectPolicySubmitRecordList(policySubmitRecord);
    }

    /**
     * 新增政策申报记录
     * 
     * @param policySubmitRecord 政策申报记录
     * @return 结果
     */
    @Override
    public int insertPolicySubmitRecord(PolicySubmitRecord policySubmitRecord)
    {
        policySubmitRecord.setMemberId(SecurityUtils.getUserId());
        policySubmitRecord.setCreateTime(DateUtils.getNowDate());
        return policySubmitRecordMapper.insertPolicySubmitRecord(policySubmitRecord);
    }

    /**
     * 修改政策申报记录
     * 
     * @param policySubmitRecord 政策申报记录
     * @return 结果
     */
    @Override
    public int updatePolicySubmitRecord(PolicySubmitRecord policySubmitRecord)
    {
        policySubmitRecord.setUpdateTime(DateUtils.getNowDate());
        return policySubmitRecordMapper.updatePolicySubmitRecord(policySubmitRecord);
    }

    /**
     * 批量删除政策申报记录
     * 
     * @param policySubmitRecordIds 需要删除的政策申报记录主键
     * @return 结果
     */
    @Override
    public int deletePolicySubmitRecordByPolicySubmitRecordIds(Long[] policySubmitRecordIds)
    {
        return policySubmitRecordMapper.deletePolicySubmitRecordByPolicySubmitRecordIds(policySubmitRecordIds);
    }

    /**
     * 删除政策申报记录信息
     * 
     * @param policySubmitRecordId 政策申报记录主键
     * @return 结果
     */
    @Override
    public int deletePolicySubmitRecordByPolicySubmitRecordId(Long policySubmitRecordId)
    {
        return policySubmitRecordMapper.deletePolicySubmitRecordByPolicySubmitRecordId(policySubmitRecordId);
    }
}
