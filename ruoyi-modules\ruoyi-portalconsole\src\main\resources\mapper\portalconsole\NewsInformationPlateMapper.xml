<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.NewsInformationPlateMapper">
    
    <resultMap type="NewsInformationPlate" id="NewsInformationPlateResult">
        <result property="newsInformationPlateId"    column="news_information_plate_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="newsInformationPlateName"    column="news_information_plate_name"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectNewsInformationPlateVo">
        select news_information_plate_id, parent_id, news_information_plate_name, del_flag, create_by, create_time, update_by, update_time, remark from news_information_plate
    </sql>

    <select id="selectNewsInformationPlateList" parameterType="NewsInformationPlate" resultMap="NewsInformationPlateResult">
        <include refid="selectNewsInformationPlateVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="newsInformationPlateName != null  and newsInformationPlateName != ''"> and news_information_plate_name like concat('%', #{newsInformationPlateName}, '%')</if>
        </where>
    </select>
    
    <select id="selectNewsInformationPlateByNewsInformationPlateId" parameterType="Long" resultMap="NewsInformationPlateResult">
        <include refid="selectNewsInformationPlateVo"/>
        where news_information_plate_id = #{newsInformationPlateId}
    </select>
        
    <insert id="insertNewsInformationPlate" parameterType="NewsInformationPlate" useGeneratedKeys="true" keyProperty="newsInformationPlateId">
        insert into news_information_plate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="newsInformationPlateName != null">news_information_plate_name,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="newsInformationPlateName != null">#{newsInformationPlateName},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateNewsInformationPlate" parameterType="NewsInformationPlate">
        update news_information_plate
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="newsInformationPlateName != null">news_information_plate_name = #{newsInformationPlateName},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where news_information_plate_id = #{newsInformationPlateId}
    </update>

    <delete id="deleteNewsInformationPlateByNewsInformationPlateId" parameterType="Long">
        delete from news_information_plate where news_information_plate_id = #{newsInformationPlateId}
    </delete>

    <delete id="deleteNewsInformationPlateByNewsInformationPlateIds" parameterType="String">
        delete from news_information_plate where news_information_plate_id in 
        <foreach item="newsInformationPlateId" collection="array" open="(" separator="," close=")">
            #{newsInformationPlateId}
        </foreach>
    </delete>
</mapper>