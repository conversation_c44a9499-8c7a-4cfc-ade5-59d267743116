package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 人才入驻流程对象 talent_settle_process
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public class TalentSettleProcess extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 出生年月 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生年月", width = 30, dateFormat = "yyyy-MM-dd")
    private Date birthDate;

    /** 毕业学校 */
    @Excel(name = "毕业学校")
    private String graduateSchool;

    /** 所在单位 */
    @Excel(name = "所在单位")
    private String currentCompany;

    /** 职务 */
    @Excel(name = "职务")
    private String position;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 所在地 */
    @Excel(name = "所在地")
    private String location;

    /** 最高学历 */
    @Excel(name = "最高学历")
    private String education;

    /** 工作状态 */
    @Excel(name = "工作状态")
    private String workStatus;

    /** 职称 */
    @Excel(name = "职称")
    private String jobTitle;

    /** 岗位分类 */
    @Excel(name = "岗位分类")
    private String positionType;

    /** 技术领域 */
    @Excel(name = "技术领域")
    private String technicalField;

    /** 个人照片 */
    @Excel(name = "个人照片")
    private String photo;

    /** 个人简介 */
    @Excel(name = "个人简介")
    private String introduction;

    /** 附件 */
    @Excel(name = "附件")
    private String attachments;

    /** 当前步骤（1-申请入驻，2-入驻审核，3-开始对接） */
    @Excel(name = "当前步骤", readConverterExp = "1=-申请入驻，2-入驻审核，3-开始对接")
    private Long currentStep;

    /** 审核状态（0-待审核，1-审核通过，2-审核拒绝） */
    @Excel(name = "审核状态", readConverterExp = "0=-待审核，1-审核通过，2-审核拒绝")
    private Long auditStatus;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String auditComment;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date auditTime;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditor;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setBirthDate(Date birthDate) 
    {
        this.birthDate = birthDate;
    }

    public Date getBirthDate() 
    {
        return birthDate;
    }
    public void setGraduateSchool(String graduateSchool) 
    {
        this.graduateSchool = graduateSchool;
    }

    public String getGraduateSchool() 
    {
        return graduateSchool;
    }
    public void setCurrentCompany(String currentCompany) 
    {
        this.currentCompany = currentCompany;
    }

    public String getCurrentCompany() 
    {
        return currentCompany;
    }
    public void setPosition(String position) 
    {
        this.position = position;
    }

    public String getPosition() 
    {
        return position;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setLocation(String location) 
    {
        this.location = location;
    }

    public String getLocation() 
    {
        return location;
    }
    public void setEducation(String education) 
    {
        this.education = education;
    }

    public String getEducation() 
    {
        return education;
    }
    public void setWorkStatus(String workStatus) 
    {
        this.workStatus = workStatus;
    }

    public String getWorkStatus() 
    {
        return workStatus;
    }
    public void setJobTitle(String jobTitle) 
    {
        this.jobTitle = jobTitle;
    }

    public String getJobTitle() 
    {
        return jobTitle;
    }
    public void setPositionType(String positionType) 
    {
        this.positionType = positionType;
    }

    public String getPositionType() 
    {
        return positionType;
    }
    public void setTechnicalField(String technicalField) 
    {
        this.technicalField = technicalField;
    }

    public String getTechnicalField() 
    {
        return technicalField;
    }
    public void setPhoto(String photo) 
    {
        this.photo = photo;
    }

    public String getPhoto() 
    {
        return photo;
    }
    public void setIntroduction(String introduction) 
    {
        this.introduction = introduction;
    }

    public String getIntroduction() 
    {
        return introduction;
    }
    public void setAttachments(String attachments) 
    {
        this.attachments = attachments;
    }

    public String getAttachments() 
    {
        return attachments;
    }
    public void setCurrentStep(Long currentStep) 
    {
        this.currentStep = currentStep;
    }

    public Long getCurrentStep() 
    {
        return currentStep;
    }
    public void setAuditStatus(Long auditStatus) 
    {
        this.auditStatus = auditStatus;
    }

    public Long getAuditStatus() 
    {
        return auditStatus;
    }
    public void setAuditComment(String auditComment) 
    {
        this.auditComment = auditComment;
    }

    public String getAuditComment() 
    {
        return auditComment;
    }
    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }
    public void setAuditor(String auditor) 
    {
        this.auditor = auditor;
    }

    public String getAuditor() 
    {
        return auditor;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("birthDate", getBirthDate())
            .append("graduateSchool", getGraduateSchool())
            .append("currentCompany", getCurrentCompany())
            .append("position", getPosition())
            .append("contactPhone", getContactPhone())
            .append("location", getLocation())
            .append("education", getEducation())
            .append("workStatus", getWorkStatus())
            .append("jobTitle", getJobTitle())
            .append("positionType", getPositionType())
            .append("technicalField", getTechnicalField())
            .append("photo", getPhoto())
            .append("introduction", getIntroduction())
            .append("attachments", getAttachments())
            .append("currentStep", getCurrentStep())
            .append("auditStatus", getAuditStatus())
            .append("auditComment", getAuditComment())
            .append("auditTime", getAuditTime())
            .append("auditor", getAuditor())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
