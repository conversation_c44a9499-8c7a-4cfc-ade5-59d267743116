package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.TalentSettleProcess;
import com.ruoyi.system.service.ITalentSettleProcessService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 人才入驻流程Controller
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@RestController
@RequestMapping("/talentSettleProcess")
public class TalentSettleProcessController extends BaseController
{
    @Autowired
    private ITalentSettleProcessService talentSettleProcessService;

    /**
     * 查询人才入驻流程列表
     */
    @RequiresPermissions("system:talentSettleProcess:list")
    @GetMapping("/list")
    public TableDataInfo list(TalentSettleProcess talentSettleProcess)
    {
        startPage();
        List<TalentSettleProcess> list = talentSettleProcessService.selectTalentSettleProcessList(talentSettleProcess);
        return getDataTable(list);
    }

    /**
     * 导出人才入驻流程列表
     */
    @RequiresPermissions("system:talentSettleProcess:export")
    @Log(title = "人才入驻流程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TalentSettleProcess talentSettleProcess)
    {
        List<TalentSettleProcess> list = talentSettleProcessService.selectTalentSettleProcessList(talentSettleProcess);
        ExcelUtil<TalentSettleProcess> util = new ExcelUtil<TalentSettleProcess>(TalentSettleProcess.class);
        util.exportExcel(response, list, "人才入驻流程数据");
    }

    /**
     * 获取人才入驻流程详细信息
     */
    @RequiresPermissions("system:talentSettleProcess:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(talentSettleProcessService.selectTalentSettleProcessById(id));
    }

    /**
     * 新增人才入驻流程
     */
    @RequiresPermissions("system:talentSettleProcess:add")
    @Log(title = "人才入驻流程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TalentSettleProcess talentSettleProcess)
    {
        return toAjax(talentSettleProcessService.insertTalentSettleProcess(talentSettleProcess));
    }

    /**
     * 修改人才入驻流程
     */
    @RequiresPermissions("system:talentSettleProcess:edit")
    @Log(title = "人才入驻流程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TalentSettleProcess talentSettleProcess)
    {
        return toAjax(talentSettleProcessService.updateTalentSettleProcess(talentSettleProcess));
    }

    /**
     * 删除人才入驻流程
     */
    @RequiresPermissions("system:talentSettleProcess:remove")
    @Log(title = "人才入驻流程", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(talentSettleProcessService.deleteTalentSettleProcessByIds(ids));
    }
}
