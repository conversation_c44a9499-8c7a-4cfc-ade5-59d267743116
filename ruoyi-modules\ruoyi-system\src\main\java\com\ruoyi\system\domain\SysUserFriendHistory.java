package com.ruoyi.system.domain;

import java.io.Serializable;
import java.util.Date;

/**
    * 好友申请表
    */
public class SysUserFriendHistory implements Serializable {
    private Long id;

    /**
    * 用户姓名
    */
    private String userName;

    /**
    * 申请人姓名
    */
    private String appyUserName;

    /**
    * 申请时间
    */
    private Date createTime;

    /**
    * 0 申请中  1 通过 2 拒绝
    */
    private Integer applyState;

    /**
    * 修改时间
    */
    private Date updateTime;

    /**
    * 备注
    */
    private String remark;


    /**
     * 申请人昵称
     */
    private String applyNickName;

    /**
     * 申请人头像
     */
    private String applyAvatar;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAppyUserName() {
        return appyUserName;
    }

    public void setAppyUserName(String appyUserName) {
        this.appyUserName = appyUserName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getApplyState() {
        return applyState;
    }

    public void setApplyState(Integer applyState) {
        this.applyState = applyState;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getApplyNickName() {
        return applyNickName;
    }

    public void setApplyNickName(String applyNickName) {
        this.applyNickName = applyNickName;
    }

    public String getApplyAvatar() {
        return applyAvatar;
    }

    public void setApplyAvatar(String applyAvatar) {
        this.applyAvatar = applyAvatar;
    }
}