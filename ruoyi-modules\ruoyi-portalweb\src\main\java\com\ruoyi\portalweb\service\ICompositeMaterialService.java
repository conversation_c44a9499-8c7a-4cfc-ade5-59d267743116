package com.ruoyi.portalweb.service;

import java.util.List;
import com.ruoyi.portalweb.domain.CompositeMaterial;

/**
 * 复材展厅Service接口
 * 
 * <AUTHOR>
 */
public interface ICompositeMaterialService {
    /**
     * 查询复材展厅列表
     * 
     * @param compositeMaterial 复材展厅信息
     * @return 复材展厅集合
     */
    public List<CompositeMaterial> selectCompositeMaterialList(CompositeMaterial compositeMaterial);

    /**
     * 查询复材展厅详细信息
     * 
     * @param id 复材展厅主键
     * @return 复材展厅
     */
    public CompositeMaterial selectCompositeMaterialById(Long id);

    /**
     * 新增复材展厅
     * 
     * @param compositeMaterial 复材展厅信息
     * @return 结果
     */
    public int insertCompositeMaterial(CompositeMaterial compositeMaterial);

    /**
     * 修改复材展厅
     * 
     * @param compositeMaterial 复材展厅信息
     * @return 结果
     */
    public int updateCompositeMaterial(CompositeMaterial compositeMaterial);

    /**
     * 批量删除复材展厅
     * 
     * @param ids 需要删除的复材展厅主键集合
     * @return 结果
     */
    public int deleteCompositeMaterialByIds(Long[] ids);

    /**
     * 删除复材展厅信息
     * 
     * @param id 复材展厅主键
     * @return 结果
     */
    public int deleteCompositeMaterialById(Long id);
}
