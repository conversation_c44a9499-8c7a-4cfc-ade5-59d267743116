package com.ruoyi.portalconsole.service;

import java.util.List;
import com.ruoyi.portalconsole.domain.PolicyInformationFeedback;
import com.ruoyi.portalconsole.domain.vo.PolicyInformationFeedbackVO;

/**
 * 政策意见反馈Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-26
 */
public interface IPolicyInformationFeedbackService 
{
    /**
     * 查询政策意见反馈
     * 
     * @param policyInformationFeedbackId 政策意见反馈主键
     * @return 政策意见反馈
     */
    public PolicyInformationFeedback selectPolicyInformationFeedbackByPolicyInformationFeedbackId(Long policyInformationFeedbackId);

    /**
     * 查询政策意见反馈列表
     * 
     * @param policyInformationFeedback 政策意见反馈
     * @return 政策意见反馈集合
     */
    public List<PolicyInformationFeedbackVO> selectPolicyInformationFeedbackList(PolicyInformationFeedback policyInformationFeedback);

    /**
     * 新增政策意见反馈
     * 
     * @param policyInformationFeedback 政策意见反馈
     * @return 结果
     */
    public int insertPolicyInformationFeedback(PolicyInformationFeedback policyInformationFeedback);

    /**
     * 修改政策意见反馈
     * 
     * @param policyInformationFeedback 政策意见反馈
     * @return 结果
     */
    public int updatePolicyInformationFeedback(PolicyInformationFeedback policyInformationFeedback);

    /**
     * 批量删除政策意见反馈
     * 
     * @param policyInformationFeedbackIds 需要删除的政策意见反馈主键集合
     * @return 结果
     */
    public int deletePolicyInformationFeedbackByPolicyInformationFeedbackIds(Long[] policyInformationFeedbackIds);

    /**
     * 删除政策意见反馈信息
     * 
     * @param policyInformationFeedbackId 政策意见反馈主键
     * @return 结果
     */
    public int deletePolicyInformationFeedbackByPolicyInformationFeedbackId(Long policyInformationFeedbackId);
}
