package com.ruoyi.portalweb.mapper;


import com.ruoyi.portalweb.api.domain.FileDetail;
import com.ruoyi.portalweb.vo.FileDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务供给Mapper接口
 */
public interface FileDetailMapper
{
    /**
     * 查询服务供给
     */
    public FileDetailVO selectFileDetailById(Long id);

    /**
     * 查询服务供给列表
     * 
     * @param fileDetail 附件子表
     * @return 服务供给集合
     */
    public List<FileDetailVO> selectFileDetailList(FileDetail fileDetail);

    /**
     * 新增服务供给
     * 
     * @param fileDetail 附件子表
     * @return 结果
     */
    public int insertFileDetail(FileDetail fileDetail);

    /**
     * 修改服务供给
     * 
     * @param fileDetail 附件子表
     * @return 结果
     */
    public int updateFileDetail(FileDetail fileDetail);

    /**
     * 删除服务供给
     * 
     * @param id 服务供给主键
     * @return 结果
     */
    public int deleteFileDetailById(Long id);

    /**
     * 批量删除服务供给
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFileDetailByIds(Long[] ids);

    /**
     * 根据billID，物理删除
     */
    int removeBybillId(@Param("parentId") Long parentId, @Param("parentType") String parentType, @Param("type") String type);

    /**
     * 根据采购发票id获取附件列表
     */
    List<FileDetailVO> selectPictureList(@Param("parentId") Long parentId, @Param("parentType") String parentType, @Param("fileUrl") String fileUrl);
}
