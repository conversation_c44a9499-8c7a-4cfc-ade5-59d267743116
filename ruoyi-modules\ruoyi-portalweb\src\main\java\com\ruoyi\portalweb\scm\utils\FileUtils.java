package com.ruoyi.portalweb.scm.utils;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.OSSObject;

import java.io.InputStream;

public class FileUtils {

    /**
     * 这些参数后期可放到配置文件
     */
    public static String ENDPOINT = "https://oss-cn-beijing.aliyuncs.com";
    public static String ACCESS_KEY_ID = "LTAI4G5Udf4KbAUamwr8dKC9";   // 玺品提供的id
    public static String ACCESS_KEY_SECRET = "******************************";// 玺品提供的秘钥
    public static String BUCKET_NAME = "xp-tech";// 玺品提供的 bucket  文件桶


   public static InputStream getOssFile(String key){
       OSSClient ossClient = new OSSClient(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
       OSSObject obj = ossClient.getObject(BUCKET_NAME, key);
       return obj.getObjectContent();
   }


}
