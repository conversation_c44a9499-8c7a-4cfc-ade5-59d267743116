package com.ruoyi.system.api.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 会员对象 member
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public class Member extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 会员ID */
    @ApiModelProperty(value = "会员ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long memberId;

    /** 手机号 */
    @Excel(name = "手机号")
    @ApiModelProperty(value = "手机号")
    private String memberPhone;

    /** 密码 */
    @Excel(name = "密码")
    @ApiModelProperty(value = "密码")
    private String memberPassword;

    /** 真实姓名 */
    @Excel(name = "真实姓名")
    @ApiModelProperty(value = "真实姓名")
    private String memberRealName;



    /** 状态，业务字典 */
    @Excel(name = "状态，业务字典")
    @ApiModelProperty(value = "状态，业务字典")
    private String memberStatus;


    @ApiModelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty(value = "企业规模")
    private String companyScale;

    @ApiModelProperty(value = "所属(关联)公司id")
    private Long companyRelatedId;

    @ApiModelProperty(value = "是否未管理员")
    private String isAdmin;

    @ApiModelProperty(value = "融雲token")
    private String rongYunToken;

    @ApiModelProperty(value = "昵称")
    private String nickname;

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getRongYunToken() {
        return rongYunToken;
    }

    public void setRongYunToken(String rongYunToken) {
        this.rongYunToken = rongYunToken;
    }

    public String getIsAdmin() {return isAdmin;}

    public void setIsAdmin(String isAdmin) {this.isAdmin = isAdmin;}

    public String getCompanyScale() {
        return companyScale;
    }

    public void setCompanyScale(String companyScale) {
        this.companyScale = companyScale;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public void setMemberId(Long memberId)
    {
        this.memberId = memberId;
    }

    public Long getMemberId() 
    {
        return memberId;
    }
    public void setMemberPhone(String memberPhone) 
    {
        this.memberPhone = memberPhone;
    }

    public String getMemberPhone()
    {
        return memberPhone;
    }

    public void setMemberPassword(String memberPassword)
    {
        this.memberPassword = memberPassword;
    }

    public String getMemberPassword()
    {
        return memberPassword;
    }

    public void setMemberRealName(String memberRealName) 
    {
        this.memberRealName = memberRealName;
    }

    public String getMemberRealName() 
    {
        return memberRealName;
    }



    public void setMemberStatus(String memberStatus) 
    {
        this.memberStatus = memberStatus;
    }

    public String getMemberStatus() 
    {
        return memberStatus;
    }

    public Long getCompanyRelatedId() {
        return companyRelatedId;
    }

    public void setCompanyRelatedId(Long companyRelatedId) {
        this.companyRelatedId = companyRelatedId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("memberId", getMemberId())
            .append("memberPhone", getMemberPhone())
            .append("memberRealName", getMemberRealName())
            .append("memberStatus", getMemberStatus())
            .append("companyRelatedId", getCompanyRelatedId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
