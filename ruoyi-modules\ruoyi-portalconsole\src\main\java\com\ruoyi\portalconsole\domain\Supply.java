package com.ruoyi.portalconsole.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 服务供给对象 supply
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public class Supply extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 供给标题 */
    @Excel(name = "供给标题")
    private String title;

    /** 供给类型 */
    @Excel(name = "供给类型")
    private String type;

    /** 应用领域 */
    @Excel(name = "应用领域")
    private String applicationArea;

    /** 技术类别 */
    @Excel(name = "技术类别")
    private String technologyCategory;

    /** 供给描述 */
    @Excel(name = "供给描述")
    private String description;

    /** 匹配需求 */
    @Excel(name = "匹配需求")
    private String matchDemand;

    /** 产品阶段 */
    @Excel(name = "产品阶段")
    private String process;

    /** 产品类型 */
    @Excel(name = "产品类型")
    private String productType;

    /** 合作方式 */
    @Excel(name = "合作方式")
    private String cooperationType;

    /** 发布机构 */
    @Excel(name = "发布机构")
    private String organization;

    /** 产品图片 */
    @Excel(name = "产品图片")
    private String imageUrl;

    /** 附件 */
    @Excel(name = "附件")
    private String attachment;

    /** 发布人 */
    @Excel(name = "发布人")
    private String publisher;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contact;

    /** 审核状态 */
    @Excel(name = "审核状态")
    private Long auditStatus;

    /** 推荐状态 */
    @Excel(name = "推荐状态")
    private Long recommend;

    /** 显示状态 */
    @Excel(name = "显示状态")
    private Long onShow;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateTime;

    /** 会员id */
    @Excel(name = "会员id")
    private Long memberId;

    /** 查看次数 */
    @Excel(name = "查看次数")
    @ApiModelProperty(value = "查看次数")
    private Long viewCount;

    public Long getViewCount() {return viewCount;}

    public void setViewCount(Long viewCount) {this.viewCount = viewCount;}

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setApplicationArea(String applicationArea)
    {
        this.applicationArea = applicationArea;
    }

    public String getApplicationArea()
    {
        return applicationArea;
    }
    public void setTechnologyCategory(String technologyCategory)
    {
        this.technologyCategory = technologyCategory;
    }

    public String getTechnologyCategory()
    {
        return technologyCategory;
    }
    public void setDescription(String description)
    {
        this.description = description;
    }

    public String getDescription()
    {
        return description;
    }
    public void setMatchDemand(String matchDemand)
    {
        this.matchDemand = matchDemand;
    }

    public String getMatchDemand()
    {
        return matchDemand;
    }
    public void setProcess(String process)
    {
        this.process = process;
    }

    public String getProcess()
    {
        return process;
    }
    public void setProductType(String productType)
    {
        this.productType = productType;
    }

    public String getProductType()
    {
        return productType;
    }
    public void setCooperationType(String cooperationType)
    {
        this.cooperationType = cooperationType;
    }

    public String getCooperationType()
    {
        return cooperationType;
    }
    public void setOrganization(String organization)
    {
        this.organization = organization;
    }

    public String getOrganization()
    {
        return organization;
    }
    public void setImageUrl(String imageUrl)
    {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl()
    {
        return imageUrl;
    }
    public void setAttachment(String attachment)
    {
        this.attachment = attachment;
    }

    public String getAttachment()
    {
        return attachment;
    }
    public void setPublisher(String publisher)
    {
        this.publisher = publisher;
    }

    public String getPublisher()
    {
        return publisher;
    }

    public String getPhone() {return phone;}

    public void setPhone(String phone) {this.phone = phone;}

    public String getContact() {return contact;}

    public void setContact(String contact) {this.contact = contact;}

    public void setAuditStatus(Long auditStatus)
    {
        this.auditStatus = auditStatus;
    }

    public Long getAuditStatus()
    {
        return auditStatus;
    }
    public void setRecommend(Long recommend)
    {
        this.recommend = recommend;
    }

    public Long getRecommend()
    {
        return recommend;
    }
    public void setOnShow(Long onShow)
    {
        this.onShow = onShow;
    }

    public Long getOnShow()
    {
        return onShow;
    }
    public void setCreateBy(String createBy)
    {
        this.createBy = createBy;
    }

    public String getCreateBy()
    {
        return createBy;
    }
    public void setCreateTime(Date createTime)
    {
        this.createTime = createTime;
    }

    public Date getCreateTime()
    {
        return createTime;
    }
    public void setUpdateBy(String updateBy)
    {
        this.updateBy = updateBy;
    }

    public String getUpdateBy()
    {
        return updateBy;
    }
    public void setUpdateTime(Date updateTime)
    {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime()
    {
        return updateTime;
    }

    public void setMemberId(Long memberId)
    {
        this.memberId = memberId;
    }

    public Long getMemberId()
    {
        return memberId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("type", getType())
            .append("applicationArea", getApplicationArea())
            .append("technologyCategory", getTechnologyCategory())
            .append("description", getDescription())
            .append("matchDemand", getMatchDemand())
            .append("process", getProcess())
            .append("productType", getProductType())
            .append("cooperationType", getCooperationType())
            .append("organization", getOrganization())
            .append("imageUrl", getImageUrl())
            .append("attachment", getAttachment())
            .append("publisher", getPublisher())
            .append("auditStatus", getAuditStatus())
            .append("recommend", getRecommend())
            .append("onShow", getOnShow())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("memberId", getMemberId())
            .toString();
    }
}
