package com.ruoyi.portalweb.api.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 会员对象 member
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public class Member extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 会员ID */
    @ApiModelProperty(value = "会员ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long memberId;

    /** 手机号 */
    @Excel(name = "手机号")
    @ApiModelProperty(value = "手机号")
    private String memberPhone;

    /** 密码 */
    @Excel(name = "密码")
    @ApiModelProperty(value = "密码")
    private String memberPassword;

    /** 最后登录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "最后登录时间")
    private Date lastLoginTime;

    /** 真实姓名 */
    @Excel(name = "真实姓名")
    @ApiModelProperty(value = "真实姓名")
    private String memberRealName;

    /** 微信号 */
    @Excel(name = "微信号")
    @ApiModelProperty(value = "微信号")
    private String memberWechat;

    /** 方案类型ID（所属行业） */
    @Excel(name = "方案类型ID", readConverterExp = "所=属行业")
    @ApiModelProperty(value = "方案类型ID（所属行业）")
    private Long solutionTypeId;

    /** 职位 */
    @Excel(name = "职位")
    @ApiModelProperty(value = "职位")
    private String memberPost;

    /** 所属公司名称 */
    @Excel(name = "所属公司名称")
    @ApiModelProperty(value = "所属公司名称")
    private String memberCompanyName;

    /** 所属公司省市区编码 */
    @Excel(name = "所属公司省市区编码")
    @ApiModelProperty(value = "所属公司省市区编码")
    private String memberCompanyArea;

    /** 所属公司地址 */
    @Excel(name = "所属公司地址")
    @ApiModelProperty(value = "所属公司地址")
    private String memberCompanyAddr;

    /** 状态，业务字典 */
    @Excel(name = "状态，业务字典")
    @ApiModelProperty(value = "状态，业务字典")
    private String memberStatus;

    /** 认证公司id */
    @Excel(name = "认证公司id")
    @ApiModelProperty(value = "认证公司id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long memberCompanyId;

    /** 删除标志（0代表存在 2代表删除） */
    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    private String delFlag;

    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty(value = "企业规模")
    private String companyScale;

    @ApiModelProperty(value = "所属(关联)公司id")
    private Long companyRelatedId;

    @ApiModelProperty(value = "是否未管理员")
    private String isAdmin;

    @ApiModelProperty(value = "融雲token")
    private String rongYunToken;

    @ApiModelProperty(value = "昵称")
    private String nickname;

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getRongYunToken() {
        return rongYunToken;
    }

    public void setRongYunToken(String rongYunToken) {
        this.rongYunToken = rongYunToken;
    }

    public String getIsAdmin() {return isAdmin;}

    public void setIsAdmin(String isAdmin) {this.isAdmin = isAdmin;}

    public String getCompanyScale() {
        return companyScale;
    }

    public void setCompanyScale(String companyScale) {
        this.companyScale = companyScale;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public void setMemberId(Long memberId)
    {
        this.memberId = memberId;
    }

    public Long getMemberId() 
    {
        return memberId;
    }
    public void setMemberPhone(String memberPhone) 
    {
        this.memberPhone = memberPhone;
    }

    public String getMemberPhone() 
    {
        return memberPhone;
    }
    public void setMemberPassword(String memberPassword) 
    {
        this.memberPassword = memberPassword;
    }

    public String getMemberPassword() 
    {
        return memberPassword;
    }
    public void setLastLoginTime(Date lastLoginTime) 
    {
        this.lastLoginTime = lastLoginTime;
    }

    public Date getLastLoginTime() 
    {
        return lastLoginTime;
    }
    public void setMemberRealName(String memberRealName) 
    {
        this.memberRealName = memberRealName;
    }

    public String getMemberRealName() 
    {
        return memberRealName;
    }
    public void setMemberWechat(String memberWechat) 
    {
        this.memberWechat = memberWechat;
    }

    public String getMemberWechat() 
    {
        return memberWechat;
    }
    public void setSolutionTypeId(Long solutionTypeId) 
    {
        this.solutionTypeId = solutionTypeId;
    }

    public Long getSolutionTypeId() 
    {
        return solutionTypeId;
    }
    public void setMemberPost(String memberPost) 
    {
        this.memberPost = memberPost;
    }

    public String getMemberPost() 
    {
        return memberPost;
    }
    public void setMemberCompanyName(String memberCompanyName) 
    {
        this.memberCompanyName = memberCompanyName;
    }

    public String getMemberCompanyName() 
    {
        return memberCompanyName;
    }
    public void setMemberCompanyArea(String memberCompanyArea) 
    {
        this.memberCompanyArea = memberCompanyArea;
    }

    public String getMemberCompanyArea() 
    {
        return memberCompanyArea;
    }
    public void setMemberCompanyAddr(String memberCompanyAddr) 
    {
        this.memberCompanyAddr = memberCompanyAddr;
    }

    public String getMemberCompanyAddr() 
    {
        return memberCompanyAddr;
    }
    public void setMemberStatus(String memberStatus) 
    {
        this.memberStatus = memberStatus;
    }

    public String getMemberStatus() 
    {
        return memberStatus;
    }
    public void setMemberCompanyId(Long memberCompanyId) 
    {
        this.memberCompanyId = memberCompanyId;
    }

    public Long getMemberCompanyId() 
    {
        return memberCompanyId;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public Long getCompanyRelatedId() {
        return companyRelatedId;
    }

    public void setCompanyRelatedId(Long companyRelatedId) {
        this.companyRelatedId = companyRelatedId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("memberId", getMemberId())
            .append("memberPhone", getMemberPhone())
            .append("memberPassword", getMemberPassword())
            .append("lastLoginTime", getLastLoginTime())
            .append("memberRealName", getMemberRealName())
            .append("memberWechat", getMemberWechat())
            .append("solutionTypeId", getSolutionTypeId())
            .append("memberPost", getMemberPost())
            .append("memberCompanyName", getMemberCompanyName())
            .append("memberCompanyArea", getMemberCompanyArea())
            .append("memberCompanyAddr", getMemberCompanyAddr())
            .append("memberStatus", getMemberStatus())
            .append("memberCompanyId", getMemberCompanyId())
            .append("companyRelatedId", getCompanyRelatedId())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
