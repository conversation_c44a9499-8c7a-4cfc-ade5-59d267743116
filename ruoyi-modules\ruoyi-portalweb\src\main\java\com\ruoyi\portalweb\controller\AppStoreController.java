package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.AppStore;
import com.ruoyi.portalweb.api.enums.AuditStatus;
import com.ruoyi.portalweb.api.enums.OnShow;
import com.ruoyi.portalweb.service.IAppStoreService;
import com.ruoyi.portalweb.vo.AppStoreVO;
import com.ruoyi.system.api.RemoteUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 应用商店Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/AppStore")
@Api(value = "1.应用商店", tags = "1.应用商店")
public class AppStoreController extends BaseController
{
    @Autowired
    private IAppStoreService appStoreService;
    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询应用商店列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询应用商店列表", notes = "传入")
    public TableDataInfo list(AppStoreVO appStore)
    {
        startPage();
        List<AppStoreVO> list = appStoreService.selectAppStoreList(appStore);
        return getDataTable(list);
    }

    /**
     * 查询应用商店推荐列表
     */
    @GetMapping("/recommend")
    @ApiOperation(value = "查询应用商店列表", notes = "传入")
    public TableDataInfo recommend(AppStoreVO appStore)
    {
        List<AppStoreVO> list = appStoreService.selectRecommendAppStoreList(appStore);
        return getDataTable(list);
    }

    /**
     * 查询应用商店列表
     */
    @GetMapping("/listDesk")
    @ApiOperation(value = "查询应用商店列表", notes = "传入")
    public TableDataInfo listDesk(AppStoreVO appStore)
    {
        startPage();
        PageUtils.setOrderBy("create_time DESC");
        appStore.setAuditStatus(AuditStatus.APPROVED.getValue());
        appStore.setOnShow(OnShow._YES.getValue());
        List<AppStoreVO> list = appStoreService.selectAppStoreList(appStore);
        return getDataTable(list);
    }

    /**
     * 导出应用商店列表
     */
    @Log(title = "应用商店", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出应用商店列表", notes = "传入")
    public void export(HttpServletResponse response, AppStoreVO appStore)
    {
        List<AppStoreVO> list = appStoreService.selectAppStoreList(appStore);
        ExcelUtil<AppStoreVO> util = new ExcelUtil<AppStoreVO>(AppStoreVO.class);
        util.exportExcel(response, list, "应用商店数据");
    }

//    /**
//     * 获取应用商店详细信息
//     */
//    @GetMapping(value = "/{appStoreId}")
//    @ApiOperation(value = "获取应用商店详细信息", notes = "传入")
//    public AjaxResult getInfo(@ApiParam(value = "主键", required = true) @PathVariable("appStoreId") Long appStoreId)
//    {
//        return success(appStoreService.selectAppStoreByAppStoreId(appStoreId));
//    }
    
    /**
     * 获取应用商店详细信息
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取服务需求(NEW)详细信息", notes = "传入")
    public AjaxResult detail(AppStore appStore) {
    	return success(appStoreService.selectAppStoreByAppStoreId(appStore.getAppStoreId()));
    }
    
    /**
     * 获取应用商店详细信息
     */
    @GetMapping("/detailDesk")
    @ApiOperation(value = "获取服务需求(NEW)详细信息", notes = "传入")
    public AjaxResult detailDesk(AppStore appStore) {
    	return success(appStoreService.selectAppStoreByAppStoreId(appStore.getAppStoreId()));
    }

    /**
     * 新增应用商店
     */
    @Log(title = "应用商店", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增应用商店", notes = "传入")
    public AjaxResult add(@RequestBody AppStore appStore)
    {
        return AjaxResult.success(appStoreService.insertAppStore(appStore));
    }

    /**
     * 修改应用商店
     */
    @Log(title = "应用商店", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改应用商店", notes = "传入")
    public AjaxResult edit(@RequestBody AppStore appStore)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        appStore.setUpdateBy(userNickName.getData());
        return  AjaxResult.success(appStoreService.updateAppStore(appStore));
    }

    /**
     * 删除应用商店
     */
    @Log(title = "应用商店", businessType = BusinessType.DELETE)
	@DeleteMapping("/{appStoreIds}")
    @ApiOperation(value = "删除应用商店", notes = "传入")
    public AjaxResult remove(@PathVariable Long[] appStoreIds)
    {
        return toAjax(appStoreService.deleteAppStoreByAppStoreIds(appStoreIds));
    }
}
