package com.ruoyi.auth.util;

import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.RandomUtil;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.ruoyi.auth.config.QWTSmsConfig;
import com.ruoyi.auth.config.SmsConfig;
import com.ruoyi.auth.model.QWTSmsResponse;
import com.ruoyi.auth.util.result.CodeMsg;
import com.ruoyi.auth.util.result.Result;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.key.SsoKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * created by micah on 2018-08-03
 * 工具类模块
 */

@RestController
@RequestMapping("/single/util")
@RefreshScope
public class UtilApiController {

    @Autowired
    private SmsConfig smsConfig;

    @Autowired
    private QWTSmsConfig qwtSmsConfig;

    @Resource
    private RedisService redisService;

    /**
     * 发送注册手机验证码（阿里云）
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/get_common_code", method = RequestMethod.GET)
    public Result<Boolean> getCommonCode(@RequestParam("telphone") String telphone) {
        if (!PhoneUtil.isMobile(telphone)) {
            return Result.error(CodeMsg.BIND_ERROR.fillArgs("无效的手机号"));
        }
        String code = RandomUtil.randomNumbers(6);
        Boolean retVal = opAliyunCode(telphone, code);
        if (retVal) {
            redisService.setCacheMapValue(SsoKey.getCode.getPrefix(),telphone,code);
            return Result.success(true);
        } else {
            return Result.error(CodeMsg.BIND_ERROR.fillArgs("短信发送失败"));
        }
    }

    /**
     * 发送注册手机验证码（全网智能通讯平台）
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/get_qwt_code", method = RequestMethod.GET)
    public Result<Boolean> getQWTCode(@RequestParam("telphone") String telphone) {
        if (!PhoneUtil.isMobile(telphone)) {
            return Result.error(CodeMsg.BIND_ERROR.fillArgs("无效的手机号"));
        }
        String code = RandomUtil.randomNumbers(6);
        Boolean retVal = opQWTCode(telphone, code);
        if (retVal) {
            redisService.setCacheMapValue(SsoKey.getCode.getPrefix(),telphone,code);
            return Result.success(true);
        } else {
            return Result.error(CodeMsg.BIND_ERROR.fillArgs("短信发送失败"));
        }
    }

    /**
     * 阿里云密码处理
     *
     * @param telphone
     * @param code
     * @return
     * @throws Exception
     */
    private Boolean opAliyunCode(String telphone, String code){
        SendSmsResponse retVal = SmsSendUtils.sendSms(telphone,  "{ \"code\":\""+code+"\"}", smsConfig);
        return "OK".equals(retVal.getCode());
    }


    /**
     * 全网智能通讯平台
     *
     * @param telphone
     * @param code
     * @return
     * @throws Exception
     */
    private Boolean opQWTCode(String telphone, String code){
        // 构建短信内容，根据全网智能通讯平台的模板格式
        String content = "您的验证码为：" + code + ",如非本人操作，请忽略本短信！";
        QWTSmsResponse retVal = QWTSendUtils.sendQWTSms(telphone, content, qwtSmsConfig);
        return "OK".equals(retVal.getCode());
    }
}
