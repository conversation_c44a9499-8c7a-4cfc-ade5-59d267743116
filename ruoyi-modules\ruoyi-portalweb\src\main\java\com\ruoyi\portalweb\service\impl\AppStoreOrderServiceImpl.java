package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.AppConfig;
import com.ruoyi.portalweb.api.domain.AppStoreOrder;
import com.ruoyi.portalweb.api.enums.AppStoreOrderStatus;
import com.ruoyi.portalweb.api.enums.InvoiceStatus;
import com.ruoyi.portalweb.mapper.AppConfigMapper;
import com.ruoyi.portalweb.mapper.AppStoreMapper;
import com.ruoyi.portalweb.mapper.AppStoreOrderMapper;
import com.ruoyi.portalweb.service.IAppStoreOrderService;
import com.ruoyi.portalweb.utils.NumberGeneraterUtil;
import com.ruoyi.portalweb.vo.AppStoreOrderCountVO;
import com.ruoyi.portalweb.vo.AppStoreOrderVO;
import com.ruoyi.portalweb.vo.AppStoreVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 应用商店订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class AppStoreOrderServiceImpl implements IAppStoreOrderService {
    @Autowired
    private AppStoreOrderMapper appStoreOrderMapper;
    @Autowired
    private AppStoreMapper appStoreMapper;
    @Autowired
    private AppConfigMapper appConfigMapper;

    /**
     * 查询应用商店订单
     *
     * @param appStoreOrderId 应用商店订单主键
     * @return 应用商店订单
     */
    @Override
    public AppStoreOrderVO selectAppStoreOrderByAppStoreOrderId(Long appStoreOrderId) {
        return appStoreOrderMapper.selectAppStoreOrderByAppStoreOrderId(appStoreOrderId);
    }

    /**
     * 查询应用商店订单列表
     *
     * @param appStoreOrder 应用商店订单
     * @return 应用商店订单
     */
    @Override
    public List<AppStoreOrderVO> selectAppStoreOrderList(AppStoreOrder appStoreOrder) {
        return appStoreOrderMapper.selectAppStoreOrderList(appStoreOrder);
    }

    /**
     * 新增应用商店订单
     *
     * @param appStoreOrder 应用商店订单
     * @return 结果
     */
    @Override
    public AppStoreOrder insertAppStoreOrder(AppStoreOrder appStoreOrder) {
        if (Objects.isNull(appStoreOrder.getAppStoreId())) {
            throw new ServiceException("应用id不能为空");
        }
        //校验应用
        AppStoreVO appStoreVO = appStoreMapper.selectAppStoreByAppStoreId(appStoreOrder.getAppStoreId());
        if (appStoreVO == null) {
            throw new ServiceException("未找到应用");
        }

        appStoreOrder.setSaleMemberId(appStoreVO.getCompanyId());
        appStoreOrder.setBuyMemberId(SecurityUtils.getUserId());
        appStoreOrder.setAppStorePrice(appStoreVO.getAppStorePrice());
        appStoreOrder.setAppStoreOrderNo(NumberGeneraterUtil.getInstance().generateTimeStampCode("AS"));
        appStoreOrder.setCreateTime(DateUtils.getNowDate());
        appStoreOrder.setAppStoreName(appStoreVO.getAppStoreName());
        appStoreOrder.setOrderStatus(AppStoreOrderStatus.UNPAID.getCode());
        appStoreOrder.setInvoiceStatus(InvoiceStatus.PENDING.getValue());
        appStoreOrder.setDeliveryMethod(appStoreVO.getDeliveryMethod());
        appStoreOrder.setSupply(appStoreVO.getSupply());
        appStoreOrder.setAppServer(appStoreVO.getAppServer());
        appStoreOrder.setPhone(appStoreVO.getAppStoreContactsPhone());

        appStoreOrderMapper.insertAppStoreOrder(appStoreOrder);
        return appStoreOrder;
    }

    /**
     * 修改应用商店订单
     *
     * @param appStoreOrder 应用商店订单
     * @return 结果
     */
    @Override
    public int updateAppStoreOrder(AppStoreOrder appStoreOrder) {
        appStoreOrder.setUpdateTime(DateUtils.getNowDate());
        if (Objects.equals(appStoreOrder.getOrderStatus(), AppStoreOrderStatus.DELIVERED.getCode())) {
            appStoreOrder.setDeliveryTime(DateUtils.getNowDate());
        }else if (Objects.equals(appStoreOrder.getOrderStatus(), AppStoreOrderStatus.CONFIRMED.getCode())) {
            appStoreOrder.setConfirmTime(DateUtils.getNowDate());
        }
        return appStoreOrderMapper.updateAppStoreOrder(appStoreOrder);
    }
    /**
     * 修改应用商店订单
     *
     * @param appStoreOrder 应用商店订单
     * @return 结果
     */
    @Override
    public int updateAppStoreOrderByOrderNo(AppStoreOrder appStoreOrder) {
        appStoreOrder.setUpdateTime(DateUtils.getNowDate());
        return appStoreOrderMapper.updateAppStoreOrderByOrderNo(appStoreOrder);
    }


    /**
     * 批量删除应用商店订单
     *
     * @param appStoreOrderIds 需要删除的应用商店订单主键
     * @return 结果
     */
    @Override
    public int deleteAppStoreOrderByAppStoreOrderIds(Long[] appStoreOrderIds) {
        return appStoreOrderMapper.deleteAppStoreOrderByAppStoreOrderIds(appStoreOrderIds);
    }

    /**
     * 删除应用商店订单信息
     *
     * @param appStoreOrderId 应用商店订单主键
     * @return 结果
     */
    @Override
    public int deleteAppStoreOrderByAppStoreOrderId(Long appStoreOrderId) {
        return appStoreOrderMapper.deleteAppStoreOrderByAppStoreOrderId(appStoreOrderId);
    }

    @Override
    public int updateAppStoreOrderInvoice(AppStoreOrder appStoreOrder) {
        if(!Objects.equals(appStoreOrder.getInvoiceStatus(), InvoiceStatus.APPLIED.getValue())
                && !Objects.equals(appStoreOrder.getInvoiceStatus(), InvoiceStatus.FINISHED.getValue())){
            throw new ServiceException("unexpected invoice status value" );
        }
        AppStoreOrder order = new AppStoreOrder();
        order.setAppStoreOrderId(appStoreOrder.getAppStoreOrderId());
        order.setInvoiceStatus(appStoreOrder.getInvoiceStatus());
        if (Objects.equals(appStoreOrder.getInvoiceStatus(), InvoiceStatus.FINISHED.getValue())){
            order.setInvoiceImageUrl(appStoreOrder.getInvoiceImageUrl());
        }

        if (Objects.equals(appStoreOrder.getOrderStatus(), AppStoreOrderStatus.DELIVERED.getCode())){
            AppConfig appConfig = appConfigMapper.selectAppConfigByAppStoreId(appStoreOrder.getAppStoreId());
            if (appConfig == null) {
                throw new ServiceException("未找到应用配置信息");
            }
            order.setDownloadUrl(appConfig.getDownloadUrl());
            order.setErweima(appConfig.getErweima());
            order.setAppWebTrialUrl(appConfig.getAppWebTrialUrl());
            order.setAppWebUrl(appConfig.getAppWebUrl());
            order.setIp(appConfig.getIp());
            order.setTestToken(appConfig.getTestToken());
            order.setContact(appConfig.getContact());
            order.setHealthInspectionUrl(appConfig.getHealthInspectionUrl());
        }

        return appStoreOrderMapper.updateAppStoreOrder(order);
    }

    @Override
    public AppStoreOrderCountVO selectAppStoreOrderCount(AppStoreOrderVO appStoreOrder) {
        AppStoreOrderVO wrapper = new AppStoreOrderVO();
        if (Objects.equals(appStoreOrder.getType(), "sale")){
            wrapper.setSaleMemberId(SecurityUtils.getUserId());
        }else {
            wrapper.setBuyMemberId(SecurityUtils.getUserId());
        }
        appStoreOrder.setOrderStatus(AppStoreOrderStatus.PAID.getCode());
        return appStoreOrderMapper.selectAppStoreOrderCount(wrapper);
    }

    @Override
    public AppStoreOrderVO selectAppStoreOrderByAppStoreOrderNo(String appStoreOrderNo) {
        return appStoreOrderMapper.selectAppStoreOrderByAppStoreOrderNo(appStoreOrderNo);
    }
}
