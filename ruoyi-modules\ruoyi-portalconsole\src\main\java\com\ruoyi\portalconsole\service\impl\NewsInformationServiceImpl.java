package com.ruoyi.portalconsole.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.NewsInformationMapper;
import com.ruoyi.portalconsole.domain.NewsInformation;
import com.ruoyi.portalconsole.service.INewsInformationService;

/**
 * 动态资讯Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class NewsInformationServiceImpl implements INewsInformationService 
{
    @Autowired
    private NewsInformationMapper newsInformationMapper;

    /**
     * 查询动态资讯
     * 
     * @param newsInformationId 动态资讯主键
     * @return 动态资讯
     */
    @Override
    public NewsInformation selectNewsInformationByNewsInformationId(Long newsInformationId)
    {
        return newsInformationMapper.selectNewsInformationByNewsInformationId(newsInformationId);
    }

    /**
     * 查询动态资讯列表
     * 
     * @param newsInformation 动态资讯
     * @return 动态资讯
     */
    @Override
    public List<NewsInformation> selectNewsInformationList(NewsInformation newsInformation)
    {
        return newsInformationMapper.selectNewsInformationList(newsInformation);
    }

    /**
     * 新增动态资讯
     * 
     * @param newsInformation 动态资讯
     * @return 结果
     */
    @Override
    public int insertNewsInformation(NewsInformation newsInformation)
    {
        newsInformation.setCreateTime(DateUtils.getNowDate());
        return newsInformationMapper.insertNewsInformation(newsInformation);
    }

    /**
     * 修改动态资讯
     * 
     * @param newsInformation 动态资讯
     * @return 结果
     */
    @Override
    public int updateNewsInformation(NewsInformation newsInformation)
    {
        newsInformation.setUpdateTime(DateUtils.getNowDate());
        return newsInformationMapper.updateNewsInformation(newsInformation);
    }

    /**
     * 批量删除动态资讯
     * 
     * @param newsInformationIds 需要删除的动态资讯主键
     * @return 结果
     */
    @Override
    public int deleteNewsInformationByNewsInformationIds(Long[] newsInformationIds)
    {
        return newsInformationMapper.deleteNewsInformationByNewsInformationIds(newsInformationIds);
    }

    /**
     * 删除动态资讯信息
     * 
     * @param newsInformationId 动态资讯主键
     * @return 结果
     */
    @Override
    public int deleteNewsInformationByNewsInformationId(Long newsInformationId)
    {
        return newsInformationMapper.deleteNewsInformationByNewsInformationId(newsInformationId);
    }
}
