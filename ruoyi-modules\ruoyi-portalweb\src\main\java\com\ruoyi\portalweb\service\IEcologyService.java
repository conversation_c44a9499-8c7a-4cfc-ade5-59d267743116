package com.ruoyi.portalweb.service;


import com.ruoyi.portalweb.api.domain.Ecology;
import com.ruoyi.portalweb.vo.EcologyVO;

import java.util.List;

/**
 * 生态协作Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface IEcologyService 
{
    /**
     * 查询生态协作
     * 
     * @param ecologyId 生态协作主键
     * @return 生态协作
     */
    public EcologyVO selectEcologyByEcologyId(Long ecologyId);

    /**
     * 查询生态协作列表
     * 
     * @param ecology 生态协作
     * @return 生态协作集合
     */
    public List<EcologyVO> selectEcologyList(EcologyVO ecology);

    /**
     * 新增生态协作
     * 
     * @param ecology 生态协作
     * @return 结果
     */
    public int insertEcology(Ecology ecology);

    /**
     * 修改生态协作
     * 
     * @param ecology 生态协作
     * @return 结果
     */
    public int updateEcology(Ecology ecology);

    /**
     * 批量删除生态协作
     * 
     * @param ecologyIds 需要删除的生态协作主键集合
     * @return 结果
     */
    public int deleteEcologyByEcologyIds(Long[] ecologyIds);

    /**
     * 删除生态协作信息
     * 
     * @param ecologyId 生态协作主键
     * @return 结果
     */
    public int deleteEcologyByEcologyId(Long ecologyId);
}
