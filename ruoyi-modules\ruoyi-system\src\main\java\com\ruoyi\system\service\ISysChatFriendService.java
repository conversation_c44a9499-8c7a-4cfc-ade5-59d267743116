package com.ruoyi.system.service;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.system.domain.SysUserFriend;
import com.ruoyi.system.domain.SysUserFriendHistory;

/**
 * 用户交互
 * 好友系统
 */
public interface ISysChatFriendService
{


    /**
     * 获取好友列表
     * @param userName
     * @return
     */
    AjaxResult getFriendList(String userName);

    /**
     * 删除好友
     * @param friendUseName  好友表friendUseName
     * @param userName 当前登录用户的手机号
     */
    AjaxResult deleteFriend(String friendUseName,String userName);

    /**
     * 修改好友信息
     * @param sysUserFriend
     *        friendUserName 好友表userName
     *        userName 当前登录用户手机号
     *        remark 备注
     */
    AjaxResult updateFriend(SysUserFriend sysUserFriend);

    /**
     * 好友申请历史
     * @param userName
     * @param pageNum
     * @param pageSize
     */
    AjaxResult getApplyFriendList(String userName,int pageNum,int pageSize);

    /**
     * 更改申请状态
     * @param sysUserFriendHistory
     *         id 表主键id
     *         userName 登录人的电话号码
     *         applyState 1 通过 2拒绝
     *
     */
    AjaxResult changeApplyState(SysUserFriendHistory sysUserFriendHistory);

    /**
     * 申请好友
     * @param sysUserFriendHistory
     *        userName 申请人手机号
     *        appyUserName 被申请人手机号
     *        remark 申请备注
     * @return
     */
    AjaxResult applyFriend(SysUserFriendHistory sysUserFriendHistory);

    /**
     * 查询用户列表
     * @param searchKey  查询内容（手机号 或者昵称）
     * @param pageNum
     * @param pageSize
     * @param userName
     * @return
     */
    AjaxResult searchUser(String searchKey,int pageNum,int pageSize,String userName);

}
