package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.UserChatMessage;

public interface UserChatMessageMapper {

    int insertSelective(UserChatMessage record);

    int deleteByPrimaryKey(Long id);

    int insert(UserChatMessage record);

    int insertOrUpdate(UserChatMessage record);

    int insertOrUpdateSelective(UserChatMessage record);



    UserChatMessage selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserChatMessage record);

    int updateByPrimaryKey(UserChatMessage record);
}