package com.ruoyi.portalconsole.service;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

import com.ruoyi.portalconsole.domain.CompanyRelated;

/**
 * 关联企业信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
public interface ICompanyRelatedService 
{
    /**
     * 查询关联企业信息
     * 
     * @param companyRelatedId 关联企业信息主键
     * @return 关联企业信息
     */
    public CompanyRelated selectCompanyRelatedByCompanyRelatedId(Long companyRelatedId);

    /**
     * 查询关联企业信息列表
     * 
     * @param companyRelated 关联企业信息
     * @return 关联企业信息集合
     */
    public List<CompanyRelated> selectCompanyRelatedList(CompanyRelated companyRelated);

    /**
     * 新增关联企业信息
     * 
     * @param companyRelated 关联企业信息
     * @return 结果
     */
    public int insertCompanyRelated(CompanyRelated companyRelated);

    /**
     * 新增关联企业信息
     *
     * @param companyRelated 关联企业信息
     * @return 结果
     */
    public int importCompanyRelated(CompanyRelated companyRelated);

    /**
     * 修改关联企业信息
     * 
     * @param companyRelated 关联企业信息
     * @return 结果
     */
    public int updateCompanyRelated(CompanyRelated companyRelated);

    /**
     * 批量删除关联企业信息
     * 
     * @param companyRelatedIds 需要删除的关联企业信息主键集合
     * @return 结果
     */
    public int deleteCompanyRelatedByCompanyRelatedIds(Long[] companyRelatedIds);

    /**
     * 删除关联企业信息信息
     * 
     * @param companyRelatedId 关联企业信息主键
     * @return 结果
     */
    public int deleteCompanyRelatedByCompanyRelatedId(Long companyRelatedId);

    public Boolean batchImportSlFormulasInfo(List<CompanyRelated> list);

    public List<CompanyRelated> getListByExcel(InputStream is, String fileName);
}
