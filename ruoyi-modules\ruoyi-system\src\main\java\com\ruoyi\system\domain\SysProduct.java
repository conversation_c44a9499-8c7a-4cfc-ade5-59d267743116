package com.ruoyi.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 产品信息对象 sys_product
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public class SysProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 产品ID */
    private Long productId;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String productName;

    /** 产品图片地址 */
    @Excel(name = "产品图片地址")
    private String productImageUrl;

    /** 关联的复材展厅ID */
    @Excel(name = "关联的复材展厅ID")
    private Long exhibitionHallId;

    /** 展厅名称（关联查询字段） */
    @Excel(name = "展厅名称")
    private String exhibitionHallName;

    /** 关联的入驻工厂ID（多个用逗号分隔） */
    @Excel(name = "关联的入驻工厂ID")
    private String factoryId;

    /** 产品描述 */
    @Excel(name = "产品描述")
    private String productDesc;

    /** 产品价格 */
    @Excel(name = "产品价格")
    private BigDecimal productPrice;

    /** 产品分类 */
    @Excel(name = "产品分类")
    private String productCategory;

    /** 产品状态（0正常 1停用） */
    @Excel(name = "产品状态", readConverterExp = "0=正常,1=停用")
    private String productStatus;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Long sortOrder;

    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId() 
    {
        return productId;
    }
    public void setProductName(String productName) 
    {
        this.productName = productName;
    }

    public String getProductName() 
    {
        return productName;
    }
    public void setProductImageUrl(String productImageUrl) 
    {
        this.productImageUrl = productImageUrl;
    }

    public String getProductImageUrl() 
    {
        return productImageUrl;
    }
    public void setExhibitionHallId(Long exhibitionHallId) 
    {
        this.exhibitionHallId = exhibitionHallId;
    }

    public Long getExhibitionHallId()
    {
        return exhibitionHallId;
    }

    public void setExhibitionHallName(String exhibitionHallName)
    {
        this.exhibitionHallName = exhibitionHallName;
    }

    public String getExhibitionHallName()
    {
        return exhibitionHallName;
    }

    public void setFactoryId(String factoryId)
    {
        this.factoryId = factoryId;
    }

    public String getFactoryId()
    {
        return factoryId;
    }
    public void setProductDesc(String productDesc) 
    {
        this.productDesc = productDesc;
    }

    public String getProductDesc() 
    {
        return productDesc;
    }
    public void setProductPrice(BigDecimal productPrice) 
    {
        this.productPrice = productPrice;
    }

    public BigDecimal getProductPrice() 
    {
        return productPrice;
    }
    public void setProductCategory(String productCategory) 
    {
        this.productCategory = productCategory;
    }

    public String getProductCategory() 
    {
        return productCategory;
    }
    public void setProductStatus(String productStatus) 
    {
        this.productStatus = productStatus;
    }

    public String getProductStatus() 
    {
        return productStatus;
    }
    public void setSortOrder(Long sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder() 
    {
        return sortOrder;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("productId", getProductId())
            .append("productName", getProductName())
            .append("productImageUrl", getProductImageUrl())
            .append("exhibitionHallId", getExhibitionHallId())
            .append("exhibitionHallName", getExhibitionHallName())
            .append("factoryId", getFactoryId())
            .append("productDesc", getProductDesc())
            .append("productPrice", getProductPrice())
            .append("productCategory", getProductCategory())
            .append("productStatus", getProductStatus())
            .append("sortOrder", getSortOrder())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
