package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.system.service.ILabTestingRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.LabTestingRelation;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 实验室检测项目关联Controller
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@RestController
@RequestMapping("/labTestingRelation")
public class LabTestingRelationController extends BaseController
{
    @Autowired
    private ILabTestingRelationService labTestingRelationService;

    /**
     * 查询实验室检测项目关联列表
     */
    @RequiresPermissions("system:labTestingRelation:list")
    @GetMapping("/list")
    public TableDataInfo list(LabTestingRelation labTestingRelation)
    {
        startPage();
        List<LabTestingRelation> list = labTestingRelationService.selectLabTestingRelationList(labTestingRelation);
        return getDataTable(list);
    }

    /**
     * 导出实验室检测项目关联列表
     */
    @RequiresPermissions("system:labTestingRelation:export")
    @Log(title = "实验室检测项目关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LabTestingRelation labTestingRelation)
    {
        List<LabTestingRelation> list = labTestingRelationService.selectLabTestingRelationList(labTestingRelation);
        ExcelUtil<LabTestingRelation> util = new ExcelUtil<LabTestingRelation>(LabTestingRelation.class);
        util.exportExcel(response, list, "实验室检测项目关联数据");
    }

    /**
     * 获取实验室检测项目关联详细信息
     */
    @RequiresPermissions("system:labTestingRelation:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(labTestingRelationService.selectLabTestingRelationById(id));
    }

    /**
     * 新增实验室检测项目关联
     */
    @RequiresPermissions("system:labTestingRelation:add")
    @Log(title = "实验室检测项目关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LabTestingRelation labTestingRelation)
    {
        return toAjax(labTestingRelationService.insertLabTestingRelation(labTestingRelation));
    }

    /**
     * 修改实验室检测项目关联
     */
    @RequiresPermissions("system:labTestingRelation:edit")
    @Log(title = "实验室检测项目关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LabTestingRelation labTestingRelation)
    {
        return toAjax(labTestingRelationService.updateLabTestingRelation(labTestingRelation));
    }

    /**
     * 删除实验室检测项目关联
     */
    @RequiresPermissions("system:labTestingRelation:remove")
    @Log(title = "实验室检测项目关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(labTestingRelationService.deleteLabTestingRelationByIds(ids));
    }
}
