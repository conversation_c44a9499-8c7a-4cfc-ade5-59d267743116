package com.ruoyi.portalconsole.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.util.RandomUtil;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalconsole.domain.Message;
import com.ruoyi.portalconsole.domain.vo.CompanyVO;
import com.ruoyi.portalconsole.domain.vo.MemberTelephoneVO;
import com.ruoyi.portalconsole.enums.CompanyStatus;
import com.ruoyi.portalconsole.enums.MessageStatus;
import com.ruoyi.portalconsole.enums.Title;
import com.ruoyi.portalconsole.service.IMemberService;
import com.ruoyi.portalconsole.service.IMessageService;
import com.ruoyi.portalconsole.utils.SmsSendUtils;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.CompanyMapper;
import com.ruoyi.portalconsole.domain.Company;
import com.ruoyi.portalconsole.service.ICompanyService;

/**
 * 企业信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Service
public class CompanyServiceImpl implements ICompanyService 
{
    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private IMemberService memberService;

    @Autowired
    private IMessageService messageService;

    @Value("${ali.sign}")
    private String sign;

    @Value("${ali.templatePass}")
    private String templatePass;

    @Value("${ali.templateFail}")
    private String templateFail;

    /**
     * 查询企业信息
     * 
     * @param companyId 企业信息主键
     * @return 企业信息
     */
    @Override
    public Company selectCompanyByCompanyId(Long companyId)
    {
        return companyMapper.selectCompanyByCompanyId(companyId);
    }


    /**
     * 查询企业信息列表
     * 
     * @param company 企业信息
     * @return 企业信息
     */
    @Override
    public List<Company> selectCompanyList(Company company)
    {
        return companyMapper.selectCompanyList(company);
    }

    /**
     * 新增企业信息
     * 
     * @param company 企业信息
     * @return 结果
     */
    @Override
    public int insertCompany(Company company)
    {
        company.setCreateTime(DateUtils.getNowDate());
        return companyMapper.insertCompany(company);
    }

    /**
     * 修改企业信息
     *
     * @param company 企业信息
     * @return 结果
     */
    @Override
    public int updateCompany(Company company)
    {
        company.setUpdateTime(DateUtils.getNowDate());
        return companyMapper.updateCompany(company);
    }

    /**
     * 审核企业信息
     *
     * @param company 企业信息
     * @return 结果
     */
    @Override
    public int auditCompany(Company company)
    {
        // 查询会员信息
        List<Long> companyIds = new ArrayList<>();
        companyIds.add(company.getCompanyId());
        List<MemberTelephoneVO> memberTelephoneVOS = memberService.selectMembersByCompanyIds(companyIds);

        // 修改审核状态
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        Company audit = new Company();
        audit.setUpdateBy(userNickName.getData());
        audit.setCompanyStatus(company.getCompanyStatus());
        audit.setCompanyId(company.getCompanyId());
        int i = updateCompany(audit);

        // 通知用户消息
        if (!memberTelephoneVOS.isEmpty()){
            for (MemberTelephoneVO memberInfo : memberTelephoneVOS){
                System.out.println("memberId:"+memberInfo.getMemberId()  + "##################################");
                noticeMsg(company, memberInfo);
            }
        }

        return i;
    }

    private void noticeMsg(Company company, MemberTelephoneVO memberInfo) {
        Message message = new Message().initData(Title.COMPANY.getName(),"您提交的"+ Title.COMPANY.getName() +"已审核", MessageStatus.UNREAD.getCode(),memberInfo.getMemberId());
        messageService.insertMessageByAsync(message);
        String code = RandomUtil.randomNumbers(6);
        if (Objects.equals(company.getCompanyStatus(), CompanyStatus.REJECTED.getCode())){
            // 发送短信
            SmsSendUtils.sendSms(sign,memberInfo.getMemberPhone(), templateFail, "{ \"code\":\""+ code +"\"}");
        }else if (Objects.equals(company.getCompanyStatus(), CompanyStatus.ACCEPTED.getCode())){
            // 发送短信
            SmsSendUtils.sendSms(sign,memberInfo.getMemberPhone(), templatePass, "{ \"code\":\""+ code+"\"}");
        }
    }

    /**
     * 审核企业信息
     *
     * @param company 企业信息
     * @return 结果
     */
    @Override
    public int auditCompanyBatch(CompanyVO company)
    {
        // 查询会员信息
        List<MemberTelephoneVO> memberTelephoneVOS = memberService.selectMembersByCompanyIds(company.getCompanyIds());
        // 修改审核状态
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        CompanyVO audit = new CompanyVO();
        audit.setUpdateBy(userNickName.getData());
        audit.setCompanyStatus(company.getCompanyStatus());
        audit.setCompanyIds(company.getCompanyIds());
        int i = companyMapper.auditCompanyBatch(audit);

        // 通知用户消息
        if (!memberTelephoneVOS.isEmpty()){
            for (MemberTelephoneVO memberInfo : memberTelephoneVOS){
                noticeMsg(company, memberInfo);
            }
        }

        return i;
    }

    /**
     * 批量删除企业信息
     * 
     * @param companyIds 需要删除的企业信息主键
     * @return 结果
     */
    @Override
    public int deleteCompanyByCompanyIds(Long[] companyIds)
    {
        return companyMapper.deleteCompanyByCompanyIds(companyIds);
    }

    /**
     * 删除企业信息信息
     * 
     * @param companyId 企业信息主键
     * @return 结果
     */
    @Override
    public int deleteCompanyByCompanyId(Long companyId)
    {
        return companyMapper.deleteCompanyByCompanyId(companyId);
    }
}
