package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.Certificate;
import com.ruoyi.system.service.ICertificateService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 证书信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@RestController
@RequestMapping("/certificate")
public class CertificateController extends BaseController
{
    @Autowired
    private ICertificateService certificateService;

    /**
     * 查询证书信息列表
     */

    @GetMapping("/list")
    public TableDataInfo list(Certificate certificate)
    {
        startPage();
        List<Certificate> list = certificateService.selectCertificateList(certificate);
        return getDataTable(list);
    }

    /**
     * 导出证书信息列表
     */

    @Log(title = "证书信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Certificate certificate)
    {
        List<Certificate> list = certificateService.selectCertificateList(certificate);
        ExcelUtil<Certificate> util = new ExcelUtil<Certificate>(Certificate.class);
        util.exportExcel(response, list, "证书信息数据");
    }

    /**
     * 获取证书信息详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(certificateService.selectCertificateById(id));
    }

    /**
     * 新增证书信息
     */

    @Log(title = "证书信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Certificate certificate)
    {
        return toAjax(certificateService.insertCertificate(certificate));
    }

    /**
     * 修改证书信息
     */

    @Log(title = "证书信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Certificate certificate)
    {
        return toAjax(certificateService.updateCertificate(certificate));
    }

    /**
     * 删除证书信息
     */

    @Log(title = "证书信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(certificateService.deleteCertificateByIds(ids));
    }
}
