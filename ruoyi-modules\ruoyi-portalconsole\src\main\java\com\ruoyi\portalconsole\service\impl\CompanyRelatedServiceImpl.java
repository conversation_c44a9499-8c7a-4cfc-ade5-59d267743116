package com.ruoyi.portalconsole.service.impl;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalconsole.mapper.CompanyRelatedMapper;
import com.ruoyi.portalconsole.domain.CompanyRelated;
import com.ruoyi.portalconsole.service.ICompanyRelatedService;

/**
 * 关联企业信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
@Service
public class CompanyRelatedServiceImpl implements ICompanyRelatedService 
{
    @Autowired
    private CompanyRelatedMapper companyRelatedMapper;

    /**
     * 查询关联企业信息
     * 
     * @param companyRelatedId 关联企业信息主键
     * @return 关联企业信息
     */
    @Override
    public CompanyRelated selectCompanyRelatedByCompanyRelatedId(Long companyRelatedId)
    {
        return companyRelatedMapper.selectCompanyRelatedByCompanyRelatedId(companyRelatedId);
    }

    /**
     * 查询关联企业信息列表
     * 
     * @param companyRelated 关联企业信息
     * @return 关联企业信息
     */
    @Override
    public List<CompanyRelated> selectCompanyRelatedList(CompanyRelated companyRelated)
    {

        return companyRelatedMapper.selectCompanyRelatedList(companyRelated);
    }

    /**
     * 新增关联企业信息
     * 
     * @param companyRelated 关联企业信息
     * @return 结果
     */
    @Override
    public int insertCompanyRelated(CompanyRelated companyRelated)
    {
        companyRelated.setCreateTime(DateUtils.getNowDate());
        return companyRelatedMapper.insertCompanyRelated(companyRelated);
    }

    /**
     * 导入关联企业信息
     *
     * @param companyRelated 关联企业信息
     * @return 结果
     */
    @Override
    public int importCompanyRelated(CompanyRelated companyRelated)
    {
        CompanyRelated wapper = new CompanyRelated();
        wapper.setCompanyName(companyRelated.getCompanyName());
        List<CompanyRelated> companyRelateds = selectCompanyRelatedList(wapper);
        if (!companyRelateds.isEmpty()){
            return 0;
        }
        companyRelated.setCreateTime(DateUtils.getNowDate());
        return companyRelatedMapper.insertCompanyRelated(companyRelated);
    }

    /**
     * 修改关联企业信息
     * 
     * @param companyRelated 关联企业信息
     * @return 结果
     */
    @Override
    public int updateCompanyRelated(CompanyRelated companyRelated)
    {
        companyRelated.setUpdateTime(DateUtils.getNowDate());
        return companyRelatedMapper.updateCompanyRelated(companyRelated);
    }

    /**
     * 批量删除关联企业信息
     * 
     * @param companyRelatedIds 需要删除的关联企业信息主键
     * @return 结果
     */
    @Override
    public int deleteCompanyRelatedByCompanyRelatedIds(Long[] companyRelatedIds)
    {
        return companyRelatedMapper.deleteCompanyRelatedByCompanyRelatedIds(companyRelatedIds);
    }

    /**
     * 删除关联企业信息信息
     * 
     * @param companyRelatedId 关联企业信息主键
     * @return 结果
     */
    @Override
    public int deleteCompanyRelatedByCompanyRelatedId(Long companyRelatedId)
    {
        return companyRelatedMapper.deleteCompanyRelatedByCompanyRelatedId(companyRelatedId);
    }

    @Override
    public List<CompanyRelated> getListByExcel(InputStream is, String fileName) {
        //获取解析excel后的list集合
        List<CompanyRelated> companyRelateds = new ExcelUtil<>(CompanyRelated.class).importExcel(is);
        return companyRelateds;
    }

    @Override
    public Boolean batchImportSlFormulasInfo(List<CompanyRelated> list) {
        //判断list是否重复
        //判断list与数据库中的是否存在
        int index = 0;
        List<Integer> existed = new ArrayList<>();
        for (CompanyRelated one : list) {
            index++;
            //判断数据库中是否存在该数据
            CompanyRelated companyRelated = new CompanyRelated();
            companyRelated.setCompanyName(one.getCompanyName());
            List<CompanyRelated> companyRelatedList = companyRelatedMapper.selectCompanyRelatedList(companyRelated);
            //判断数据库中是否存在
            if (!companyRelatedList.isEmpty()) {
                existed.add(index);
            }
        }
        if (!existed.isEmpty()){
            throw new ServiceException("excel中第"+existed+"行在数据库中存在");
        }
        //如果不存在，则添加数据
        int count = 0;
        List<CompanyRelated> insertList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            count++;
            list.get(i).setCreateTime(DateUtils.getNowDate());
            list.get(i).setUpdateTime(DateUtils.getNowDate());
            insertList.add(list.get(i));
            if (count >=500 || i ==list.size()-1) {
              companyRelatedMapper.batchImportSlFormulasInfo(insertList);
              count = 0;
              insertList = new ArrayList<>();
            }
        }
        return true;
    }
}
