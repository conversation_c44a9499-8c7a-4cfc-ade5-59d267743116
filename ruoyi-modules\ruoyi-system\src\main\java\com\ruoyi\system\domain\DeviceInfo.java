package com.ruoyi.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 设备信息对象 device_info
 *
 * <AUTHOR>
 * @date 2025-03-06
 */
public class DeviceInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 设备ID */
    private Long id;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String name;

    /** 设备分类 */
    @Excel(name = "设备分类")
    private String category;

    /** 设备规格 */
    @Excel(name = "设备规格")
    private String specifications;

    /** 所属单位/位置 */
    @Excel(name = "所属单位/位置")
    private String location;

    /** 设备用途描述 */
    @Excel(name = "设备用途描述")
    private String description;

    /** 设备图片 */
    @Excel(name = "设备图片")
    private String images;

    /** 租用模式 */
    @Excel(name = "租用模式")
    private String rentMode;

    /** 租用价格 */
    @Excel(name = "租用价格")
    private BigDecimal rentPrice;

    /** 压力(MPa) */
    @Excel(name = "压力(MPa)")
    private String pressure;

    /** 温度 */
    @Excel(name = "温度")
    private String temperature;

    /** 尺寸 */
    @Excel(name = "尺寸")
    private String dimension;

        //所属企业
    private String belongingCompany;

    //审核状态 0.未审核 1审核通过，2，审核未通过
    private Integer checkStatus;


    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getBelongingCompany() {
        return belongingCompany;
    }

    public void setBelongingCompany(String belongingCompany) {
        this.belongingCompany = belongingCompany;
    }

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String modelNumber;


    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setCategory(String category)
    {
        this.category = category;
    }

    public String getCategory()
    {
        return category;
    }
    public void setSpecifications(String specifications)
    {
        this.specifications = specifications;
    }

    public String getSpecifications()
    {
        return specifications;
    }
    public void setLocation(String location)
    {
        this.location = location;
    }

    public String getLocation()
    {
        return location;
    }
    public void setDescription(String description)
    {
        this.description = description;
    }

    public String getDescription()
    {
        return description;
    }
    public void setImages(String images)
    {
        this.images = images;
    }

    public String getImages()
    {
        return images;
    }
    public void setRentMode(String rentMode)
    {
        this.rentMode = rentMode;
    }

    public String getRentMode()
    {
        return rentMode;
    }
    public void setRentPrice(BigDecimal rentPrice)
    {
        this.rentPrice = rentPrice;
    }

    public BigDecimal getRentPrice()
    {
        return rentPrice;
    }
    public void setPressure(String pressure)
    {
        this.pressure = pressure;
    }

    public String getPressure()
    {
        return pressure;
    }
    public void setTemperature(String temperature)
    {
        this.temperature = temperature;
    }

    public String getTemperature()
    {
        return temperature;
    }
    public void setDimension(String dimension)
    {
        this.dimension = dimension;
    }

    public String getDimension()
    {
        return dimension;
    }
    public void setModelNumber(String modelNumber)
    {
        this.modelNumber = modelNumber;
    }

    public String getModelNumber()
    {
        return modelNumber;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("category", getCategory())
                .append("specifications", getSpecifications())
                .append("location", getLocation())
                .append("description", getDescription())
                .append("images", getImages())
                .append("rentMode", getRentMode())
                .append("rentPrice", getRentPrice())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("pressure", getPressure())
                .append("temperature", getTemperature())
                .append("dimension", getDimension())
                .append("modelNumber", getModelNumber())
                .toString();
    }
}
