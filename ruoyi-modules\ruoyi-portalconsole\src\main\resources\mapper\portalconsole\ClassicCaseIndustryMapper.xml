<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.ClassicCaseIndustryMapper">
    
    <resultMap type="ClassicCaseIndustry" id="ClassicCaseIndustryResult">
        <result property="classicCaseIndustryId"    column="classic_case_industry_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="classicCaseIndustryName"    column="classic_case_industry_name"    />
        <result property="category"    column="category"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectClassicCaseIndustryVo">
        select classic_case_industry_id, parent_id, classic_case_industry_name, category, del_flag, create_by, create_time, update_by, update_time, remark from classic_case_industry
    </sql>

    <select id="selectClassicCaseIndustryList" parameterType="ClassicCaseIndustry" resultMap="ClassicCaseIndustryResult">
        <include refid="selectClassicCaseIndustryVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="classicCaseIndustryName != null  and classicCaseIndustryName != ''"> and classic_case_industry_name like concat('%', #{classicCaseIndustryName}, '%')</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
        </where>
    </select>
    
    <select id="selectClassicCaseIndustryByClassicCaseIndustryId" parameterType="Long" resultMap="ClassicCaseIndustryResult">
        <include refid="selectClassicCaseIndustryVo"/>
        where classic_case_industry_id = #{classicCaseIndustryId}
    </select>
        
    <insert id="insertClassicCaseIndustry" parameterType="ClassicCaseIndustry" useGeneratedKeys="true" keyProperty="classicCaseIndustryId">
        insert into classic_case_industry
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="classicCaseIndustryName != null">classic_case_industry_name,</if>
            <if test="category != null">category,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="classicCaseIndustryName != null">#{classicCaseIndustryName},</if>
            <if test="category != null">#{category},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateClassicCaseIndustry" parameterType="ClassicCaseIndustry">
        update classic_case_industry
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="classicCaseIndustryName != null">classic_case_industry_name = #{classicCaseIndustryName},</if>
            <if test="category != null">category = #{category},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where classic_case_industry_id = #{classicCaseIndustryId}
    </update>

    <delete id="deleteClassicCaseIndustryByClassicCaseIndustryId" parameterType="Long">
        delete from classic_case_industry where classic_case_industry_id = #{classicCaseIndustryId}
    </delete>

    <delete id="deleteClassicCaseIndustryByClassicCaseIndustryIds" parameterType="String">
        delete from classic_case_industry where classic_case_industry_id in 
        <foreach item="classicCaseIndustryId" collection="array" open="(" separator="," close=")">
            #{classicCaseIndustryId}
        </foreach>
    </delete>
</mapper>