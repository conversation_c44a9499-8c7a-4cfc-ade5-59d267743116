package com.ruoyi.portalweb.mapper;


import com.ruoyi.portalweb.api.domain.EcologyCategory;

import java.util.List;

/**
 * 生态类别Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface EcologyCategoryMapper 
{
    /**
     * 查询生态类别
     * 
     * @param ecologyCategoryId 生态类别主键
     * @return 生态类别
     */
    public EcologyCategory selectEcologyCategoryByEcologyCategoryId(Long ecologyCategoryId);

    /**
     * 查询生态类别列表
     * 
     * @param ecologyCategory 生态类别
     * @return 生态类别集合
     */
    public List<EcologyCategory> selectEcologyCategoryList(EcologyCategory ecologyCategory);

    /**
     * 新增生态类别
     * 
     * @param ecologyCategory 生态类别
     * @return 结果
     */
    public int insertEcologyCategory(EcologyCategory ecologyCategory);

    /**
     * 修改生态类别
     * 
     * @param ecologyCategory 生态类别
     * @return 结果
     */
    public int updateEcologyCategory(EcologyCategory ecologyCategory);

    /**
     * 删除生态类别
     * 
     * @param ecologyCategoryId 生态类别主键
     * @return 结果
     */
    public int deleteEcologyCategoryByEcologyCategoryId(Long ecologyCategoryId);

    /**
     * 批量删除生态类别
     * 
     * @param ecologyCategoryIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEcologyCategoryByEcologyCategoryIds(Long[] ecologyCategoryIds);
}
