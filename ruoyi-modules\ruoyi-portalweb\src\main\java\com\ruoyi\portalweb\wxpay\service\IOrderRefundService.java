package com.ruoyi.portalweb.wxpay.service;

import com.ruoyi.portalweb.api.domain.AppStoreOrder;
import com.ruoyi.portalweb.vo.MemberVO;

public interface IOrderRefundService {
     boolean applyRefund(AppStoreOrder appStoreOrder, String remoteAddr);

     void applyRefundCallBack(String outReFundNo, String successTime, MemberVO member,AppStoreOrder appStoreOrder);

      void applyRefundCallBackError(String outReFundNo, String errorMsg);
}
