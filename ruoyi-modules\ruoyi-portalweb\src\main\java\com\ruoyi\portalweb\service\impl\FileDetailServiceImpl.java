package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.FileDetail;
import com.ruoyi.portalweb.mapper.FileDetailMapper;
import com.ruoyi.portalweb.service.IFileDetailService;
import com.ruoyi.portalweb.vo.FileDetailVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 附件子表Service业务层处理
 */
@Service
public class FileDetailServiceImpl implements IFileDetailService
{
    @Autowired
    private FileDetailMapper fileDetailMapper;

    /**
     * 查询附件子表
     * 
     * @param id 附件子表主键
     * @return 附件子表
     */
    @Override
    public FileDetailVO selectFileDetailById(Long id)
    {
        return fileDetailMapper.selectFileDetailById(id);
    }

    /**
     * 查询附件子表列表
     * 
     * @param fileDetail 附件子表
     * @return 附件子表
     */
    @Override
    public List<FileDetailVO> selectFileDetailList(FileDetailVO fileDetail)
    {
        return fileDetailMapper.selectFileDetailList(fileDetail);
    }

    /**
     * 新增附件子表
     * 
     * @param fileDetail 附件子表
     * @return 结果
     */
    @Override
    public int insertFileDetail(FileDetail fileDetail)
    {
        return fileDetailMapper.insertFileDetail(fileDetail);
    }

    /**
     * 修改附件子表
     * 
     * @param fileDetail 附件子表
     * @return 结果
     */
    @Override
    public int updateFileDetail(FileDetail fileDetail)
    {
        return fileDetailMapper.updateFileDetail(fileDetail);
    }

    /**
     * 批量删除附件子表
     * 
     * @param ids 需要删除的附件子表主键
     * @return 结果
     */
    @Override
    public int deleteFileDetailByIds(Long[] ids)
    {
        return fileDetailMapper.deleteFileDetailByIds(ids);
    }

    /**
     * 删除附件子表信息
     * 
     * @param id 附件子表主键
     * @return 结果
     */
    @Override
    public int deleteFileDetailById(Long id)
    {
        return fileDetailMapper.deleteFileDetailById(id);
    }

	/**
     * 根据billID，物理删除
     */
    @Override
    public int removeBybillId(Long parentId, String parentType ,String type) {
        return fileDetailMapper.removeBybillId(parentId,parentType , type);
    }

    /**
     * 根据billid获取附件列表
     */
    public List<FileDetailVO> selectPictureList(Long parentId, String parentType ,String fileUrl) {
        List<FileDetailVO> picVO = fileDetailMapper.selectPictureList(parentId,parentType, fileUrl);
        return picVO;
    }
}
