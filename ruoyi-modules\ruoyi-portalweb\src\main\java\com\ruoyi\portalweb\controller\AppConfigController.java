package com.ruoyi.portalweb.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.portalweb.api.domain.AppConfig;
import com.ruoyi.portalweb.service.IAppConfigService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 应用配置Controller
 * 
 * <AUTHOR>
 * @date 2024-07-22
 */
@RestController
@RequestMapping("/AppConfig")
@Api(value = "10.应用商店配置", tags = "10.应用商店配置")
public class AppConfigController extends BaseController
{
    @Autowired
    private IAppConfigService appConfigService;

    /**
     * 查询应用配置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(AppConfig appConfig)
    {
        startPage();
        List<AppConfig> list = appConfigService.selectAppConfigList(appConfig);
        return getDataTable(list);
    }

    /**
     * 导出应用配置列表
     */
    @Log(title = "应用配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppConfig appConfig)
    {
        List<AppConfig> list = appConfigService.selectAppConfigList(appConfig);
        ExcelUtil<AppConfig> util = new ExcelUtil<AppConfig>(AppConfig.class);
        util.exportExcel(response, list, "应用配置数据");
    }

    /**
     * 获取应用配置详细信息
     */
    @GetMapping(value = "/{appConfigId}")
    public AjaxResult getInfo(@PathVariable("appConfigId") Long appConfigId)
    {
        return success(appConfigService.selectAppConfigByAppConfigId(appConfigId));
    }

    /**
     * 获取应用配置详细信息
     */
    @GetMapping
    public AjaxResult getInfoByAppStoreId(@RequestParam("appStoreId") Long appStoreId)
    {
        return success(appConfigService.selectAppConfigByAppStoreId(appStoreId));
    }

    /**
     * 新增应用配置
     */
    @Log(title = "应用配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppConfig appConfig)
    {
        return  AjaxResult.success(appConfigService.insertAppConfig(appConfig));
    }

//    /**
//     * 修改应用配置
//     */
//    @RequiresPermissions("portalweb:AppConfig:edit")
//    @Log(title = "应用配置", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody AppConfig appConfig)
//    {
//        return toAjax(appConfigService.updateAppConfig(appConfig));
//    }

    /**
     * 修改应用配置
     */
    @Log(title = "应用配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult editByAppStoreId(@RequestBody AppConfig appConfig)
    {
        return toAjax(appConfigService.updateAppConfigByAppStoreId(appConfig));
    }

    /**
     * 删除应用配置
     */
    @Log(title = "应用配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{appConfigIds}")
    public AjaxResult remove(@PathVariable Long[] appConfigIds)
    {
        return toAjax(appConfigService.deleteAppConfigByAppConfigIds(appConfigIds));
    }
}
