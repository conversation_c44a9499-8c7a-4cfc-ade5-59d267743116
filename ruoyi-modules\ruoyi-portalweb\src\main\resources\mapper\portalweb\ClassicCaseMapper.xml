<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.ClassicCaseMapper">

    <resultMap id="ClassicCaseResult" type="com.ruoyi.portalweb.vo.ClassicCaseVO">
        <result property="classicCaseId" column="classic_case_id"/>
        <result property="solutionTypeId" column="solution_type_id"/>
        <result property="classicCaseName" column="classic_case_name"/>
        <result property="classicCaseCompany" column="classic_case_company"/>
        <result property="classicCaseIntroduction" column="classic_case_introduction"/>
        <result property="classicCaseBackground" column="classic_case_background"/>
        <result property="classicCaseContent" column="classic_case_content"/>
        <result property="classicCaseEffects" column="classic_case_effects"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>

        <result property="solutionTypeName" column="solution_type_name"/>
    </resultMap>

    <sql id="selectClassicCaseVo">
        select classic_case_id, solution_type_id, classic_case_name, classic_case_company, classic_case_introduction,
        classic_case_background, classic_case_content, classic_case_effects, del_flag, create_by, create_time,
        update_by, update_time, remark
        from classic_case
    </sql>

    <sql id="Base_Column_List">
        a.*, b.solution_type_name
    </sql>

    <sql id="Base_Table_List">
        FROM classic_case a
        LEFT JOIN solution_type b ON a.solution_type_id = b.solution_type_id
    </sql>

    <select id="selectClassicCaseList" parameterType="ClassicCaseVO" resultMap="ClassicCaseResult">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Table_List"/>
        <where>
            <if test="keywords != null  and keywords != ''">
                AND (
                    a.classic_case_name like concat(concat('%',#{keywords}),'%')
                    OR a.classic_case_company like concat(concat('%',#{keywords}),'%')
                )
            </if>
            <if test="solutionTypeId != null ">
                and a.solution_type_id in (
                    WITH recursive tmp_type AS (
                        SELECT * FROM solution_type WHERE solution_type_id = #{solutionTypeId}
                        UNION ALL
                        SELECT a.*
                        FROM solution_type a
                        INNER JOIN tmp_type b ON a.parent_id = b.solution_type_id
                    )
                    SELECT solution_type_id FROM tmp_type
                )
            </if>
            <if test="category != null and category != ''">
                and a.solution_type_id in (
                    WITH recursive tmp_type AS (
                        SELECT * FROM solution_type WHERE category = #{category}
						UNION ALL
						SELECT a.*
						FROM solution_type a
						INNER JOIN tmp_type b ON a.parent_id = b.solution_type_id
                    )
                    SELECT solution_type_id FROM tmp_type
                )
            </if>
            <if test="classicCaseName != null  and classicCaseName != ''">
                and a.classic_case_name like concat('%', #{classicCaseName}, '%')
            </if>
            <if test="classicCaseCompany != null  and classicCaseCompany != ''">
                and a.classic_case_company = #{classicCaseCompany}
            </if>
            <if test="classicCaseIntroduction != null  and classicCaseIntroduction != ''">
                and a.classic_case_introduction = #{classicCaseIntroduction}
            </if>
            <if test="classicCaseBackground != null  and classicCaseBackground != ''">
                and a.classic_case_background = #{classicCaseBackground}
            </if>
            <if test="classicCaseContent != null  and classicCaseContent != ''">
                and a.classic_case_content = #{classicCaseContent}
            </if>
            <if test="classicCaseEffects != null  and classicCaseEffects != ''">
                and a.classic_case_effects = #{classicCaseEffects}
            </if>
        </where>
    </select>

    <select id="selectClassicCaseByClassicCaseId" parameterType="Long" resultMap="ClassicCaseResult">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Table_List"/>
        where a.classic_case_id = #{classicCaseId}
    </select>

    <insert id="insertClassicCase" parameterType="ClassicCase" useGeneratedKeys="true" keyProperty="classicCaseId">
        insert into classic_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="solutionTypeId != null">solution_type_id,</if>
            <if test="classicCaseName != null">classic_case_name,</if>
            <if test="classicCaseCompany != null">classic_case_company,</if>
            <if test="classicCaseIntroduction != null">classic_case_introduction,</if>
            <if test="classicCaseBackground != null">classic_case_background,</if>
            <if test="classicCaseContent != null">classic_case_content,</if>
            <if test="classicCaseEffects != null">classic_case_effects,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="solutionTypeId != null">#{solutionTypeId},</if>
            <if test="classicCaseName != null">#{classicCaseName},</if>
            <if test="classicCaseCompany != null">#{classicCaseCompany},</if>
            <if test="classicCaseIntroduction != null">#{classicCaseIntroduction},</if>
            <if test="classicCaseBackground != null">#{classicCaseBackground},</if>
            <if test="classicCaseContent != null">#{classicCaseContent},</if>
            <if test="classicCaseEffects != null">#{classicCaseEffects},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateClassicCase" parameterType="ClassicCase">
        update classic_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="solutionTypeId != null">solution_type_id = #{solutionTypeId},</if>
            <if test="classicCaseName != null">classic_case_name = #{classicCaseName},</if>
            <if test="classicCaseCompany != null">classic_case_company = #{classicCaseCompany},</if>
            <if test="classicCaseIntroduction != null">classic_case_introduction = #{classicCaseIntroduction},</if>
            <if test="classicCaseBackground != null">classic_case_background = #{classicCaseBackground},</if>
            <if test="classicCaseContent != null">classic_case_content = #{classicCaseContent},</if>
            <if test="classicCaseEffects != null">classic_case_effects = #{classicCaseEffects},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where classic_case_id = #{classicCaseId}
    </update>

    <delete id="deleteClassicCaseByClassicCaseId" parameterType="Long">
        delete from classic_case where classic_case_id = #{classicCaseId}
    </delete>

    <delete id="deleteClassicCaseByClassicCaseIds" parameterType="String">
        delete from classic_case where classic_case_id in
        <foreach item="classicCaseId" collection="array" open="(" separator="," close=")">
            #{classicCaseId}
        </foreach>
    </delete>
</mapper>