package com.ruoyi.portalweb.controller;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.Supply;
import com.ruoyi.portalweb.service.ISupplyService;
import com.ruoyi.portalweb.vo.SupplyVO;
import com.ruoyi.system.api.RemoteUserService;

import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 服务供给Controller
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/supply")
public class SupplyController extends BaseController
{
    @Autowired
    private ISupplyService supplyService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询服务供给列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SupplyVO supply)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<SupplyVO> list = supplyService.selectSupplyList(supply);
        return getDataTable(list);
    }

    /**
     * 查询服务供给列表
     */
    @GetMapping("/listDesk")
    public TableDataInfo listDesk(SupplyVO supply)
    {
        startPage();
        PageUtils.setOrderBy("create_time DESC");
        List<SupplyVO> list = supplyService.selectSupplyList(supply);
        return getDataTable(list);
    }

    /**
     * 查询服务供给列表
     */
    @GetMapping("/listDesk/CompanyRelated")
    public TableDataInfo listDeskCompanyRelated(@RequestParam(value = "companyRelatedId") Long companyRelatedId)
    {
        List<SupplyVO> list = supplyService.listDeskCompanyRelated(companyRelatedId);
        return getDataTable(list);
    }

    /**
     * 导出服务供给列表
     */
    @Log(title = "服务供给", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SupplyVO supply)
    {
        List<SupplyVO> list = supplyService.selectSupplyList(supply);
        ExcelUtil<SupplyVO> util = new ExcelUtil<SupplyVO>(SupplyVO.class);
        util.exportExcel(response, list, "服务供给数据");
    }

//    /**
//     * 获取服务供给详细信息
//     */
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return success(supplyService.selectSupplyById(id));
//    }
    
    /**
     * 获取服务供给详细信息
     */
    @GetMapping("/detail")
    @ApiOperation(value = "获取服务供给详细信息", notes = "传入")
    public AjaxResult detail(Supply supply) {
        return success(supplyService.selectSupplyById(supply.getId()));
    }
    
    /**
     * 获取服务供给详细信息
     */
    @GetMapping("/detailDesk")
    @ApiOperation(value = "获取服务供给详细信息", notes = "传入")
    public AjaxResult detailDesk(Supply supply) {
        return success(supplyService.detailDesk(supply.getId()));
    }

    /**
     * 新增服务供给
     */
    @Log(title = "服务供给", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SupplyVO supply)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        supply.setCreateBy(userNickName.getData());
        supply.setUpdateBy(userNickName.getData());
        supply.setPublisher(userNickName.getData());
        return toAjax(supplyService.insertSupply(supply));
    }

    /**
     * 修改服务供给
     */
    @Log(title = "服务供给", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Supply supply)
    {
        return toAjax(supplyService.updateSupply(supply));
    }

    /**
     * 删除服务供给
     */
    @Log(title = "服务供给", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(supplyService.deleteSupplyByIds(ids));
    }
}
