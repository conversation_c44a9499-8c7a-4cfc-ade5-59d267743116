package com.ruoyi.portalconsole.mapper;

import java.util.List;
import com.ruoyi.portalconsole.domain.NewsInformationPlate;

/**
 * 咨询板块Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
public interface NewsInformationPlateMapper 
{
    /**
     * 查询咨询板块
     * 
     * @param newsInformationPlateId 咨询板块主键
     * @return 咨询板块
     */
    public NewsInformationPlate selectNewsInformationPlateByNewsInformationPlateId(Long newsInformationPlateId);

    /**
     * 查询咨询板块列表
     * 
     * @param newsInformationPlate 咨询板块
     * @return 咨询板块集合
     */
    public List<NewsInformationPlate> selectNewsInformationPlateList(NewsInformationPlate newsInformationPlate);

    /**
     * 新增咨询板块
     * 
     * @param newsInformationPlate 咨询板块
     * @return 结果
     */
    public int insertNewsInformationPlate(NewsInformationPlate newsInformationPlate);

    /**
     * 修改咨询板块
     * 
     * @param newsInformationPlate 咨询板块
     * @return 结果
     */
    public int updateNewsInformationPlate(NewsInformationPlate newsInformationPlate);

    /**
     * 删除咨询板块
     * 
     * @param newsInformationPlateId 咨询板块主键
     * @return 结果
     */
    public int deleteNewsInformationPlateByNewsInformationPlateId(Long newsInformationPlateId);

    /**
     * 批量删除咨询板块
     * 
     * @param newsInformationPlateIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewsInformationPlateByNewsInformationPlateIds(Long[] newsInformationPlateIds);
}
