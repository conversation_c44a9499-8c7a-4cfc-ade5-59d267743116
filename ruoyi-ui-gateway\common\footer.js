Vue.component('pc-foot', {
    mounted() {
		var qrcode = new QRCode("app-qrcode", {
			text: "https://www.pgyer.com/nmdou",	//要生成二维码的链接
			width: 85,	//二维码的宽度
			height: 85,	//二维码的高度
			colorDark : "#000000",	//前景色
			colorLight : "#ffffff",	//背景色
			correctLevel : QRCode.CorrectLevel.H	//纠错等级
		});
        //document.write(unescape("%3Cspan id='cnzz_stat_icon_1278845058'%3E%3C/span%3E%3Cscript src='https://s4.cnzz.com/z_stat.php%3Fid%3D1278845058%26online%3D1%26show%3Dline' type='text/javascript'%3E%3C/script%3E"));	
    },
    template: `
				<div class="bottom_bg">
				  <div class="bottom">
				    <div class="bottom_top">
				      <div class="bottom1"><img src="person/images/qiyelogo2.png"></div>
				      <div class="bottom2">
				        <p><span class="iconfont icon-youxiang"></span>&nbsp;:&nbsp;<EMAIL></p>
				        <p><span class="iconfont icon-dianhua" style="font-size:22px"></span>&nbsp;:&nbsp;4008-939-365</p>
				        <p><span class="iconfont icon-youxiang"><a href="000falvyinsi.html">《法律声明及隐私保护》</a> </span></p>
				      </div>
				      <div class="bottom3"><a href="081gongsijieshao.html">关于我们</a> | <a href="084lianxiwomen.html">联系我们</a> | <a href="#">帮助中心</a></div>
				      <div class="bottom4"><img src="images/erweima.png">
					  	<div class="bottom5" id="app-qrcode" style="width: 85px; height: 118px; position: absolute; left: 32px; top: 100px;text-align: center;border: 2px solid;padding: 1px;">
							皖豆云APP  
						</div> 
					  </div>
				    </div>
				    <div class="youlian"><a href="http://gxt.shandong.gov.cn/"  target="_blank">山东省工业和信息化厅</a> | <a href="http://kjt.shandong.gov.cn/" target="_blank">山东省科学技术厅</a> | <a href="http://qdstc.qingdao.gov.cn/"  target="_blank">青岛市科学技术局</a> | <a href="http://gxj.qingdao.gov.cn/n28356049/index.html" target="_blank">青岛市工业和信息化局</a> | <a href="http://jx.ah.gov.cn/" target="_blank">安徽省经济和信息化厅</a></div>
					
					<div class="youlian"><a href="https://www.wandouclouds.com/" target="_blank">皖豆云工业互联网平台</a> | <a href="http://www.fxmuyeplat.com/" target="_blank">木业工业互联网平台</a></div>
					

				    <div class="banquan">Copyright©上海檬豆网络科技有限公司版权所有 913101093507202199 <a href="https://beian.miit.gov.cn" target="_blank">沪ICP备15042831号-1</a></div>
				</div>`
});
