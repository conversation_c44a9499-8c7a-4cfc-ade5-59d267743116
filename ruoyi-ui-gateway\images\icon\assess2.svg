<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="392.772" height="247.52" viewBox="0 0 392.772 247.52">
  <defs>
    <filter id="矩形_21379" x="165" y="131.52" width="198" height="116" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="椭圆_1705" x="247.145" y="150.099" width="86.837" height="86.836" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <clipPath id="clip-path">
      <ellipse id="椭圆_1704" data-name="椭圆 1704" cx="34.418" cy="34.418" rx="34.418" ry="34.418" transform="translate(0 0)" fill="#83abf4"/>
    </clipPath>
    <filter id="矩形_21380" x="178.461" y="197.519" width="81" height="30" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-3"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-3"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="矩形_21381" x="178.461" y="184.519" width="49" height="26" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-4"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-4"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="矩形_21382" x="0" y="15.52" width="203" height="120" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-5"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-5"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="矩形_21408" x="8.679" y="95.52" width="81" height="30" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-6"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-6"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="椭圆_1706" x="93.567" y="30.52" width="158" height="158" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-7"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-7"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="矩形_21410" x="205" y="30.52" width="112" height="67" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-8"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-8"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="组_21693" data-name="组 21693" transform="translate(-986.114 -1713.24)">
    <g id="组_17927" data-name="组 17927" transform="translate(335.886 266.129)">
      <path id="路径_8363" data-name="路径 8363" d="M-17.52,35.615C4.616,4.293,138.934-51.627,220.755-25.084S334.335,53.845,288.9,87.078,89.664,203.81,32.873,188.44,20.557,154.129-17.52,116.8-39.655,66.936-17.52,35.615Z" transform="translate(969.784 1640.517) rotate(180)" fill="#eaf2fe"/>
      <g id="椭圆_1696" data-name="椭圆 1696" transform="translate(680 1623)" fill="#f7fbfe" stroke="#f7fbfe" stroke-width="4">
        <ellipse cx="16" cy="16.5" rx="16" ry="16.5" stroke="none"/>
        <ellipse cx="16" cy="16.5" rx="14" ry="14.5" fill="none"/>
      </g>
      <g id="椭圆_1698" data-name="椭圆 1698" transform="translate(722 1605)" fill="none" stroke="#f7fbfe" stroke-width="4">
        <circle cx="9" cy="9" r="9" stroke="none"/>
        <circle cx="9" cy="9" r="7" fill="none"/>
      </g>
      <g id="椭圆_1699" data-name="椭圆 1699" transform="translate(983 1447.111)" fill="none" stroke="#f7fbfe" stroke-width="4">
        <circle cx="11" cy="11" r="11" stroke="none"/>
        <circle cx="11" cy="11" r="9" fill="none"/>
      </g>
      <circle id="椭圆_1697" data-name="椭圆 1697" cx="14.888" cy="14.888" r="14.888" transform="translate(1013.225 1463.336)" fill="#f7fbfe"/>
    </g>
    <g id="组_17930" data-name="组 17930" transform="translate(-27.886 8.76)">
      <g transform="matrix(1, 0, 0, 1, 1014, 1704.48)" filter="url(#矩形_21379)">
        <rect id="矩形_21379-2" data-name="矩形 21379" width="180" height="98" rx="6" transform="translate(174 137.52)" fill="#f6faff"/>
      </g>
      <g id="组_17929" data-name="组 17929" transform="translate(1270.145 1855.042)">
        <g transform="matrix(1, 0, 0, 1, -256.15, -150.56)" filter="url(#椭圆_1705)">
          <ellipse id="椭圆_1705-2" data-name="椭圆 1705" cx="34.418" cy="34.418" rx="34.418" ry="34.418" transform="translate(256.15 156.1)" fill="#5685db"/>
        </g>
        <ellipse id="椭圆_1702" data-name="椭圆 1702" cx="34.418" cy="34.418" rx="34.418" ry="34.418" transform="translate(0 0)" fill="#83abf4"/>
        <g id="蒙版组_71" data-name="蒙版组 71" transform="translate(0 0)" clip-path="url(#clip-path)">
          <path id="路径_8397" data-name="路径 8397" d="M-5291.5,14000.627v37.521l59.924,29.808-27.7-69.034Z" transform="translate(5325.918 -14003.731)" fill="#f8cf00"/>
        </g>
        <g id="蒙版组_70" data-name="蒙版组 70" transform="translate(0 0)" clip-path="url(#clip-path)">
          <path id="路径_8396" data-name="路径 8396" d="M-5291.5,14000.627v37.521l35.521-27.733-3.3-11.493Z" transform="translate(5325.918 -14003.731)" fill="#f37907"/>
        </g>
      </g>
      <path id="路径_8398" data-name="路径 8398" d="M-5339.339,13971.171l13.278-13.277h27.91" transform="translate(6656.689 -12105.309)" fill="none" stroke="#f37907" stroke-width="1"/>
      <path id="路径_8399" data-name="路径 8399" d="M-5339.339,13971.171l13.278-13.277h27.91" transform="translate(6656.689 -12078.943)" fill="none" stroke="#f8cf00" stroke-width="1"/>
      <path id="路径_8400" data-name="路径 8400" d="M-5398,14027.9l-7.315-10.131h-21.645" transform="translate(6677.424 -12152.623)" fill="none" stroke="#83abf4" stroke-width="1"/>
      <g transform="matrix(1, 0, 0, 1, 1014, 1704.48)" filter="url(#矩形_21380)">
        <rect id="矩形_21380-2" data-name="矩形 21380" width="63" height="12" rx="6" transform="translate(187.46 203.52)" fill="#fff"/>
      </g>
      <g transform="matrix(1, 0, 0, 1, 1014, 1704.48)" filter="url(#矩形_21381)">
        <rect id="矩形_21381-2" data-name="矩形 21381" width="31" height="8" rx="4" transform="translate(187.46 190.52)" fill="#fff"/>
      </g>
    </g>
    <g id="组_17932" data-name="组 17932" transform="translate(-33.886 -39.24)">
      <g transform="matrix(1, 0, 0, 1, 1020, 1752.48)" filter="url(#矩形_21382)">
        <rect id="矩形_21382-2" data-name="矩形 21382" width="185" height="102" rx="6" transform="translate(9 21.52)" fill="#f6faff"/>
      </g>
      <g id="组_17931" data-name="组 17931" transform="translate(10.499 -1.499)">
        <path id="联合_337" data-name="联合 337" d="M6599-12225v-35h18v35l-9,6Z" transform="translate(-5570 14052.5)" fill="#cbd3dc"/>
        <path id="联合_342" data-name="联合 342" d="M6599-12225v-35h18v35l-9,6Z" transform="translate(-5542 14052.5)" fill="#cbd3dc"/>
        <path id="联合_348" data-name="联合 348" d="M6599-12225v-35h18v35l-9,6Z" transform="translate(-5514 14052.5)" fill="#cbd3dc"/>
        <path id="多边形_125" data-name="多边形 125" d="M9,0l9,6L9,12,0,6Z" transform="translate(1028.998 1786.499)" fill="#eef1f0"/>
        <path id="多边形_132" data-name="多边形 132" d="M9,0l9,6L9,12,0,6Z" transform="translate(1056.998 1786.5)" fill="#eef1f0"/>
        <path id="多边形_141" data-name="多边形 141" d="M9,0l9,6L9,12,0,6Z" transform="translate(1084.998 1786.5)" fill="#eef1f0"/>
        <path id="交叉_21" data-name="交叉 21" d="M6608-12238h9v15l-9,6Z" transform="translate(-5570.001 14065.502)" fill="#5e8eed"/>
        <path id="联合_343" data-name="联合 343" d="M6599-12226v-15h18v15l-9,6Z" transform="translate(-5542.002 14068.502)" fill="#5e8eed"/>
        <path id="交叉_23" data-name="交叉 23" d="M6608-12205.5v-35.4h9v29.406l-9,6Z" transform="translate(-5542 14054)" fill="#ff7b00"/>
        <path id="交叉_25" data-name="交叉 25" d="M6608-12205.5v-42.726h9v36.728l-9,6Z" transform="translate(-5514 14054)" fill="#fdae03"/>
        <path id="交叉_22" data-name="交叉 22" d="M0,21H9V6L0,0Z" transform="translate(1038 1848.5) rotate(180)" fill="#89bff8"/>
        <path id="交叉_24" data-name="交叉 24" d="M6600-12211.5v-29.406h9v35.406Z" transform="translate(-5543 14054)" fill="#fd932b"/>
        <path id="交叉_26" data-name="交叉 26" d="M6600-12212.772V-12248h9v42.5Z" transform="translate(-5515 14054)" fill="#fbca00"/>
        <path id="多边形_123" data-name="多边形 123" d="M9,0l9,6L9,12,0,6Z" transform="translate(1028.998 1821.502)" fill="#d4e4f3"/>
        <path id="多边形_130" data-name="多边形 130" d="M9,0l9,6L9,12,0,6Z" transform="translate(1056.998 1807)" fill="#ffa353"/>
        <path id="多边形_139" data-name="多边形 139" d="M9,0l9,6L9,12,0,6Z" transform="translate(1084.998 1799.497)" fill="#f9e177"/>
      </g>
      <g transform="matrix(1, 0, 0, 1, 1020, 1752.48)" filter="url(#矩形_21408)">
        <rect id="矩形_21408-2" data-name="矩形 21408" width="63" height="12" rx="6" transform="translate(17.68 101.52)" fill="#fff"/>
      </g>
    </g>
    <g transform="matrix(1, 0, 0, 1, 986.11, 1713.24)" filter="url(#椭圆_1706)">
      <circle id="椭圆_1706-2" data-name="椭圆 1706" cx="70" cy="70" r="70" transform="translate(102.57 36.52)" fill="#428afa"/>
    </g>
    <g id="组_17928" data-name="组 17928" transform="translate(-12.798 -6.018)">
      <path id="路径_8388" data-name="路径 8388" d="M-5629.4,14228.076s13.5,6.75,26.779,4.648,11.218-6.447,0-14.5S-5629.4,14228.076-5629.4,14228.076Z" transform="translate(6809.184 -12438.519)" fill="#c8e0ff"/>
      <path id="路径_8389" data-name="路径 8389" d="M-5647.672,14211.73s-18.369-4.094-29.434,15.05-11.4,21.467-22.02,25.893-32.312-6.529-27.443-27.11,30.208-28.66,48.578-26.226A64.546,64.546,0,0,1-5647.672,14211.73Z" transform="translate(6856.776 -12430.14)" fill="#f6faff"/>
      <path id="路径_8390" data-name="路径 8390" d="M0,11.93s13.5,6.75,26.779,4.647,11.218-6.446,0-14.5S0,11.93,0,11.93Z" transform="translate(1126.94 1844.813) rotate(-120)" fill="#c8e0ff"/>
      <path id="路径_8391" data-name="路径 8391" d="M79.6,12.813S61.23,8.72,50.165,27.863s-11.4,21.467-22.02,25.893S-4.167,47.227.7,26.646,30.91-2.015,49.279.42A64.546,64.546,0,0,1,79.6,12.813Z" transform="translate(1144.412 1892.778) rotate(-120)" fill="#f6faff"/>
      <path id="路径_8392" data-name="路径 8392" d="M0,11.93s13.5,6.75,26.779,4.647,11.218-6.446,0-14.5S0,11.93,0,11.93Z" transform="translate(1211.542 1856.983) rotate(120)" fill="#c8e0ff"/>
      <path id="路径_8393" data-name="路径 8393" d="M79.6,12.813S61.23,8.72,50.165,27.863s-11.4,21.467-22.02,25.893S-4.167,47.227.7,26.646,30.91-2.015,49.279.42A64.546,64.546,0,0,1,79.6,12.813Z" transform="translate(1244.346 1817.868) rotate(120)" fill="#f6faff"/>
      <text id="_" data-name="？" transform="translate(1145.646 1807.711)" fill="#428afa" font-size="17" font-family="'\?\?\?\?\?\?-W3', 字体圈伟君黑"><tspan x="0" y="0">？</tspan></text>
      <g id="椭圆_1701" data-name="椭圆 1701" transform="translate(1198.224 1807.975)" fill="none" stroke="#428afa" stroke-width="2">
        <circle cx="5.936" cy="5.936" r="5.936" stroke="none"/>
        <circle cx="5.936" cy="5.936" r="4.936" fill="none"/>
      </g>
      <path id="路径_8394" data-name="路径 8394" d="M-5371.169,14068.05l2.632,2.417" transform="translate(6580.622 -12249.509)" fill="none" stroke="#428afa" stroke-width="2"/>
      <path id="路径_8395" data-name="路径 8395" d="M-5435.5,14116.853h16" transform="translate(6590.05 -12252.185)" fill="none" stroke="#428afa" stroke-width="2"/>
      <g id="矩形_21376" data-name="矩形 21376" transform="translate(1155.345 1854.616)" fill="none" stroke="#428afa" stroke-width="2">
        <rect width="5.936" height="8.48" stroke="none"/>
        <rect x="1" y="1" width="3.936" height="6.48" fill="none"/>
      </g>
      <g id="矩形_21378" data-name="矩形 21378" transform="translate(1163.825 1856.312)" fill="none" stroke="#428afa" stroke-width="2">
        <rect width="5.936" height="6.784" stroke="none"/>
        <rect x="1" y="1" width="3.936" height="4.784" fill="none"/>
      </g>
      <g id="矩形_21377" data-name="矩形 21377" transform="translate(1159.585 1852.072)" fill="none" stroke="#428afa" stroke-width="2">
        <rect width="5.936" height="11.024" stroke="none"/>
        <rect x="1" y="1" width="3.936" height="9.024" fill="none"/>
      </g>
    </g>
    <g id="组_17933" data-name="组 17933" transform="translate(-48.886 -30.24)">
      <g transform="matrix(1, 0, 0, 1, 1035, 1743.48)" filter="url(#矩形_21410)">
        <g id="矩形_21410-2" data-name="矩形 21410" transform="translate(214 36.52)" fill="#f6faff" stroke="#fff" stroke-width="1">
          <rect width="94" height="49" rx="6" stroke="none"/>
          <rect x="0.5" y="0.5" width="93" height="48" rx="5.5" fill="none"/>
        </g>
      </g>
      <circle id="椭圆_1707" data-name="椭圆 1707" cx="7.5" cy="7.5" r="7.5" transform="translate(1264 1790)" fill="#fff"/>
      <path id="减去_578" data-name="减去 578" d="M26.993,14H.007C0,13.833,0,13.689,0,13.562a13.5,13.5,0,0,1,3.955-9.59,13.456,13.456,0,0,1,19.091,0A13.5,13.5,0,0,1,27,13.562c0,.138,0,.285-.007.438Z" transform="translate(1258 1806)" fill="#fff"/>
      <rect id="矩形_21412" data-name="矩形 21412" width="28" height="8" rx="4" transform="translate(1292 1795)" fill="#fff"/>
      <rect id="矩形_21413" data-name="矩形 21413" width="44" height="8" rx="4" transform="translate(1292 1808)" fill="#fff"/>
    </g>
  </g>
</svg>
