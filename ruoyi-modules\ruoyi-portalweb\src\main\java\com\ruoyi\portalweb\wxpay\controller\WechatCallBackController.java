package com.ruoyi.portalweb.wxpay.controller;

import java.io.*;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.Security;
import java.util.Map;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import cn.hutool.core.util.XmlUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.portalweb.service.IBuMemberOnlineRefundService;
import com.ruoyi.portalweb.wxpay.service.IOrderRefundService;
import com.ruoyi.portalweb.wxpay.service.IOrderService;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 控制器：微信支付回调
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wechat/callback")
@AllArgsConstructor
@Slf4j
public class WechatCallBackController {


	@Autowired
	private IOrderService orderService;
	@Autowired
	private IOrderRefundService orderRefundService;

	@Autowired
	private IBuMemberOnlineRefundService buMemberOnlineRefundService;


	/**
	 * 微信支付异步通知
	 *
	 * @throws InterruptedException
	 */
	@RequestMapping(value = "/recharge")
	public String recharge(HttpServletRequest request) {
		InputStream is = null;
		String xmlBack = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml> ";
		String xmlSuccessBack = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg>"
				+ "</xml> ";
		try {
			is = request.getInputStream();
			// 将InputStream转换成String
			BufferedReader reader = new BufferedReader(new InputStreamReader(is));
			StringBuilder sb = new StringBuilder();
			String line = null;
			while ((line = reader.readLine()) != null) {
				sb.append(line + "\n");
			}
			Map<String, Object> map = XmlUtil.xmlToMap(sb.toString());
			if ("SUCCESS".equals(map.get("return_code"))) {
				String outTradeNo = map.get("out_trade_no").toString();
				String bankType = map.get("bank_type").toString();
				R<Object> r = orderService.rechargeCallBack(outTradeNo, true,bankType);
				if (200 == r.getCode()) {
					xmlBack = xmlSuccessBack;
				}
			}
		} catch (Exception e) {
			e.printStackTrace(new PrintWriter(System.out.printf(this.getClass()+":recharge","回调错误"+e.getMessage())));
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return xmlBack;
	}

	/**
	 * 微信退款异步通知：未出库退款
	 *
	 * @throws IOException
	 */
	@RequestMapping(value = "/refund")
	public String refund(HttpServletRequest request) throws IOException {
		InputStream is = null;
		String xmlBack = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[报文为空]]></return_msg></xml> ";
		String xmlSuccessBack = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg>" + "</xml> ";
		try {
			is = request.getInputStream();
			// 将InputStream转换成String
			BufferedReader reader = new BufferedReader(new InputStreamReader(is));
			StringBuilder sb = new StringBuilder();
			String line = null;
			while ((line = reader.readLine()) != null) {
				sb.append(line + "\n");
			}
			Map<String, Object> map = XmlUtil.xmlToMap(sb.toString());
			if ("SUCCESS".equals(map.get("return_code"))) {
				Map<String,Object> dataMap = this.getWechatRefundCallBack(map);
				String outRefundNo = (String)dataMap.get("out_refund_no");// 退款订单号
				String refundStatus = (String)dataMap.get("refund_status");// 退款状态
				String successTime = (String)dataMap.get("success_time");// 退款状态
				if ("SUCCESS".equals(refundStatus)) {
					orderRefundService.applyRefundCallBack(outRefundNo, successTime,null, null);
					xmlBack = xmlSuccessBack;
				}else{
					String errorMsg = "退款异常";
					if("CHANGE".equals(refundStatus)){
						errorMsg = "退款异常";
					}else if("REFUNDCLOSE".equals(refundStatus)){
						errorMsg = "退款关闭";
					}
					orderRefundService.applyRefundCallBackError(outRefundNo,errorMsg);
				}
			}else{
				String errorMsg = (String) map.get("return_msg");
				System.out.println(this.getClass()+":refund "+" 回调错误"+errorMsg);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return xmlBack;
	}

	/**
	 * 微信退款回调
	 *
	 * @param map
	 * @return
	 */
	protected Map<String, Object> getWechatRefundCallBack(Map<String, Object> map) {
		String mchId = map.get("mch_id").toString();
		// 查询商户的MCH_ID
//		SysConfig sysConfig = sysConfigService.selectByKV(SysConfigConstant.WX_MCH_ID, mchId);
		// 查询商户的MCH_KEY
//		if (sysConfig == null) {
//			throw new ServiceException("未获取到微信商户ID");
//		}
		String wxMchKey = "111";
		String reqInfo = null;
		try {
			reqInfo = decrypt((String)map.get("req_info"), wxMchKey);
		} catch (Exception e) {
			throw new ServiceException("解密微信退款信息出现错误:" + e.getMessage());
		}
		Map<String, Object> reqMap = XmlUtil.xmlToMap(reqInfo);
//				String outRefundNo = reqMap.containsKey("out_refund_no") ? (String)reqMap.get("out_refund_no") : "";// 退款订单号
//		String refundStatus = reqMap.containsKey("refund_status") ? (String)reqMap.get("refund_status") : "";// 退款状态
//		String successTime = reqMap.containsKey("success_time") ? (String)reqMap.get("success_time") : "";// 退款成功时间
//		String refundRecvAccout = reqMap.containsKey("refund_recv_accout") ? (String)reqMap.get("refund_recv_accout") : "";// 退款入账账户

		return reqMap;
	}

	private String decrypt(String reqInfo, String key) throws NoSuchPaddingException, NoSuchAlgorithmException, NoSuchProviderException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException, IllegalBlockSizeException, BadPaddingException {
		byte[] reqInfoB = Base64.decodeBase64(reqInfo);
		String key_ = DigestUtils.md5Hex(key);

		if (Security.getProvider("BC") == null){
			Security.addProvider(new BouncyCastleProvider());
		}
		Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding", "BC");
		SecretKeySpec secretKeySpec = new SecretKeySpec(key_.getBytes(), "AES");
		cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
		return new String(cipher.doFinal(reqInfoB));
	}


}
