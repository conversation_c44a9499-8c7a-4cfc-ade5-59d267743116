package com.ruoyi.portalconsole.service;

import java.util.List;
import com.ruoyi.portalconsole.domain.SolutionType;

/**
 * 解决方案类型Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface ISolutionTypeService 
{
    /**
     * 查询解决方案类型
     * 
     * @param solutionTypeId 解决方案类型主键
     * @return 解决方案类型
     */
    public SolutionType selectSolutionTypeBySolutionTypeId(Long solutionTypeId);

    /**
     * 查询解决方案类型列表
     * 
     * @param solutionType 解决方案类型
     * @return 解决方案类型集合
     */
    public List<SolutionType> selectSolutionTypeList(SolutionType solutionType);

    /**
     * 新增解决方案类型
     * 
     * @param solutionType 解决方案类型
     * @return 结果
     */
    public int insertSolutionType(SolutionType solutionType);

    /**
     * 修改解决方案类型
     * 
     * @param solutionType 解决方案类型
     * @return 结果
     */
    public int updateSolutionType(SolutionType solutionType);

    /**
     * 批量删除解决方案类型
     * 
     * @param solutionTypeIds 需要删除的解决方案类型主键集合
     * @return 结果
     */
    public int deleteSolutionTypeBySolutionTypeIds(Long[] solutionTypeIds);

    /**
     * 删除解决方案类型信息
     * 
     * @param solutionTypeId 解决方案类型主键
     * @return 结果
     */
    public int deleteSolutionTypeBySolutionTypeId(Long solutionTypeId);
}
