package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.TalentInfoApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.TalentInfo;
import com.ruoyi.system.service.ITalentInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 人才信息Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@RestController
@RequestMapping("/info")
public class TalentInfoController extends BaseController
{
    @Autowired
    private ITalentInfoService talentInfoService;

    /**
     * 查询人才信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TalentInfo talentInfo)
    {
        Long userId = SecurityUtils.getUserId();
        startPage();
        List<TalentInfo> list = talentInfoService.selectTalentInfoList(talentInfo);
        return getDataTable(list);
    }

    /**
     * 导出人才信息列表
     */
    @Log(title = "人才信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TalentInfo talentInfo)
    {
        List<TalentInfo> list = talentInfoService.selectTalentInfoList(talentInfo);
        ExcelUtil<TalentInfo> util = new ExcelUtil<TalentInfo>(TalentInfo.class);
        util.exportExcel(response, list, "人才信息数据");
    }

    /**
     * 获取人才信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(talentInfoService.selectTalentInfoById(id));
    }

    /**
     * 新增人才信息
     */
    @Log(title = "人才信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TalentInfo talentInfo)
    {
        return toAjax(talentInfoService.insertTalentInfo(talentInfo));
    }

    /**
     * 修改人才信息
     */
    @Log(title = "人才信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TalentInfo talentInfo)
    {
        return toAjax(talentInfoService.updateTalentInfo(talentInfo));
    }

    /**
     * 删除人才信息
     */
    @Log(title = "人才信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(talentInfoService.deleteTalentInfoByIds(ids));
    }

    /**
     * 根据用户ID获取人才信息
     */
    @GetMapping("/getByUserId/{userId}")
    public R<TalentInfoApi> getByUserId(@PathVariable("userId") Long userId)
    {
        return R.ok(talentInfoService.selectTalentInfoByUserId(userId));
    }
}
