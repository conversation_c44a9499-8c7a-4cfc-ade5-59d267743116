package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 工序外协对象 process_outsourcing
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public class ProcessOutsourcing extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 外协工序名称 */
    @Excel(name = "外协工序名称")
    private String processName;

    /** 外协单位 */
    @Excel(name = "外协单位")
    private String outsourcingUnit;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactPhone;

    /** 状态（进行中/已完成） */
    @Excel(name = "状态", readConverterExp = "进=行中/已完成")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setProcessName(String processName) 
    {
        this.processName = processName;
    }

    public String getProcessName() 
    {
        return processName;
    }
    public void setOutsourcingUnit(String outsourcingUnit) 
    {
        this.outsourcingUnit = outsourcingUnit;
    }

    public String getOutsourcingUnit() 
    {
        return outsourcingUnit;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("processName", getProcessName())
            .append("outsourcingUnit", getOutsourcingUnit())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
