package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ManufactureOrder;

/**
 * 制造订单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface ManufactureOrderMapper
{
    /**
     * 查询制造订单
     *
     * @param id 制造订单主键
     * @return 制造订单
     */
    public ManufactureOrder selectManufactureOrderById(Long id);

    /**
     * 查询制造订单列表
     *
     * @param manufactureOrder 制造订单
     * @return 制造订单集合
     */
    public List<ManufactureOrder> selectManufactureOrderList(ManufactureOrder manufactureOrder);

    /**
     * 新增制造订单
     *
     * @param manufactureOrder 制造订单
     * @return 结果
     */
    public int insertManufactureOrder(ManufactureOrder manufactureOrder);

    /**
     * 修改制造订单
     *
     * @param manufactureOrder 制造订单
     * @return 结果
     */
    public int updateManufactureOrder(ManufactureOrder manufactureOrder);

    /**
     * 删除制造订单
     *
     * @param id 制造订单主键
     * @return 结果
     */
    public int deleteManufactureOrderById(Long id);

    /**
     * 批量删除制造订单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteManufactureOrderByIds(Long[] ids);
}
