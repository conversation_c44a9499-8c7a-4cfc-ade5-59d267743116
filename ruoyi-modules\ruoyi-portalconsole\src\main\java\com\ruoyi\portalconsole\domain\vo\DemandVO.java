package com.ruoyi.portalconsole.domain.vo;



import com.ruoyi.portalweb.api.domain.Demand;
import io.swagger.annotations.ApiModelProperty;

/**
 * 服务需求对象
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public class DemandVO extends Demand {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "需求类型")
    private String typeName;

    @ApiModelProperty(value = "链接")
    private String imageUrlPath;

    @ApiModelProperty(value = "应用领域名称")
    private String applicationAreaName;

    @ApiModelProperty(value = "审核状态名称")
    private String auditStatusName;

    public String getAuditStatusName() {
        return auditStatusName;
    }

    public void setAuditStatusName(String auditStatusName) {
        this.auditStatusName = auditStatusName;
    }

    public String getApplicationAreaName() {
        return applicationAreaName;
    }

    public void setApplicationAreaName(String applicationAreaName) {
        this.applicationAreaName = applicationAreaName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getImageUrlPath() {
        return imageUrlPath;
    }

    public void setImageUrlPath(String imageUrlPath) {
        this.imageUrlPath = imageUrlPath;
    }

}
