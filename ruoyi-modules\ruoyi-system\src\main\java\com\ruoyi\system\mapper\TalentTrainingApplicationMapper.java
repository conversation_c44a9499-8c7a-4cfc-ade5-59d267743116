package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.TalentTrainingApplication;

/**
 * 人才培训基地申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
public interface TalentTrainingApplicationMapper 
{
    /**
     * 查询人才培训基地申请
     * 
     * @param id 人才培训基地申请主键
     * @return 人才培训基地申请
     */
    public TalentTrainingApplication selectTalentTrainingApplicationById(Long id);

    /**
     * 查询人才培训基地申请列表
     * 
     * @param talentTrainingApplication 人才培训基地申请
     * @return 人才培训基地申请集合
     */
    public List<TalentTrainingApplication> selectTalentTrainingApplicationList(TalentTrainingApplication talentTrainingApplication);

    /**
     * 新增人才培训基地申请
     * 
     * @param talentTrainingApplication 人才培训基地申请
     * @return 结果
     */
    public int insertTalentTrainingApplication(TalentTrainingApplication talentTrainingApplication);

    /**
     * 修改人才培训基地申请
     * 
     * @param talentTrainingApplication 人才培训基地申请
     * @return 结果
     */
    public int updateTalentTrainingApplication(TalentTrainingApplication talentTrainingApplication);

    /**
     * 删除人才培训基地申请
     * 
     * @param id 人才培训基地申请主键
     * @return 结果
     */
    public int deleteTalentTrainingApplicationById(Long id);

    /**
     * 批量删除人才培训基地申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTalentTrainingApplicationByIds(Long[] ids);
}
