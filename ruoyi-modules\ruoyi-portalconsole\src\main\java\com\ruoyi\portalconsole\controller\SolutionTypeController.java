package com.ruoyi.portalconsole.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.SolutionType;
import com.ruoyi.portalconsole.service.ISolutionTypeService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 解决方案类型Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/solutionType")
public class SolutionTypeController extends BaseController
{
    @Autowired
    private ISolutionTypeService solutionTypeService;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询解决方案类型列表
     */
    @RequiresPermissions("portalconsole:solutionType:list")
    @GetMapping("/list")
    public TableDataInfo list(SolutionType solutionType)
    {
        startPage();
        PageUtils.setDefaultOrderBy();
        List<SolutionType> list = solutionTypeService.selectSolutionTypeList(solutionType);
        return getDataTable(list);
    }

    /**
     * 导出解决方案类型列表
     */
    @RequiresPermissions("portalconsole:solutionType:export")
    @Log(title = "解决方案类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SolutionType solutionType)
    {
        List<SolutionType> list = solutionTypeService.selectSolutionTypeList(solutionType);
        ExcelUtil<SolutionType> util = new ExcelUtil<SolutionType>(SolutionType.class);
        util.exportExcel(response, list, "解决方案类型数据");
    }

    /**
     * 获取解决方案类型详细信息
     */
    @RequiresPermissions("portalconsole:solutionType:query")
    @GetMapping(value = "/{solutionTypeId}")
    public AjaxResult getInfo(@PathVariable("solutionTypeId") Long solutionTypeId)
    {
        return success(solutionTypeService.selectSolutionTypeBySolutionTypeId(solutionTypeId));
    }

    /**
     * 新增解决方案类型
     */
    @RequiresPermissions("portalconsole:solutionType:add")
    @Log(title = "解决方案类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SolutionType solutionType)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        solutionType.setUpdateBy(userNickName.getData());
        solutionType.setCreateBy(userNickName.getData());
        return toAjax(solutionTypeService.insertSolutionType(solutionType));
    }

    /**
     * 修改解决方案类型
     */
    @RequiresPermissions("portalconsole:solutionType:edit")
    @Log(title = "解决方案类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SolutionType solutionType)
    {
        // 获取用户信息存入数据
        R<String> userNickName = remoteUserService.getUserNickName(SecurityUtils.getUsername(), SecurityConstants.INNER);
        solutionType.setUpdateBy(userNickName.getData());
        return toAjax(solutionTypeService.updateSolutionType(solutionType));
    }

    /**
     * 删除解决方案类型
     */
    @RequiresPermissions("portalconsole:solutionType:remove")
    @Log(title = "解决方案类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{solutionTypeIds}")
    public AjaxResult remove(@PathVariable Long[] solutionTypeIds)
    {
        return toAjax(solutionTypeService.deleteSolutionTypeBySolutionTypeIds(solutionTypeIds));
    }
}
