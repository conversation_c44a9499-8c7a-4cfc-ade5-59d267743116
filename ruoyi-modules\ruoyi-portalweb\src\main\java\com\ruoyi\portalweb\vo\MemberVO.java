package com.ruoyi.portalweb.vo;

import com.ruoyi.portalweb.api.domain.Member;
import io.swagger.annotations.ApiModelProperty;

/**
 * 会员对象 member
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public class MemberVO extends Member {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司名称")
    private String companyName;
    @ApiModelProperty(value = "认证状态")
    private String companyStatusName;
    @ApiModelProperty(value = "认证状态")
    private String companyStatus;

    @ApiModelProperty(value = "所属行业")
    private String solutionTypeName;
    @ApiModelProperty(value = "职位")
    private String memberPostName;
    @ApiModelProperty(value = "企业规模")
    private String companyScaleName;
    @ApiModelProperty(value = "真实姓名(认证)")
    private String companyRealName;

    public String getCompanyRealName() {
        return companyRealName;
    }

    public void setCompanyRealName(String companyRealName) {
        this.companyRealName = companyRealName;
    }

    public String getSolutionTypeName() {
        return solutionTypeName;
    }

    public void setSolutionTypeName(String solutionTypeName) {
        this.solutionTypeName = solutionTypeName;
    }

    public String getMemberPostName() {
        return memberPostName;
    }

    public void setMemberPostName(String memberPostName) {
        this.memberPostName = memberPostName;
    }

    public String getCompanyScaleName() {
        return companyScaleName;
    }

    public void setCompanyScaleName(String companyScaleName) {
        this.companyScaleName = companyScaleName;
    }

    public String getCompanyStatus() {
        return companyStatus;
    }

    public void setCompanyStatus(String companyStatus) {
        this.companyStatus = companyStatus;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyStatusName() {
        return companyStatusName;
    }

    public void setCompanyStatusName(String companyStatusName) {
        this.companyStatusName = companyStatusName;
    }
}
