Vue.component('pc-header', {
    props: ['token', 'userinfo', 'sel'],
    data: function() {
        return { qyList: [], hyList: [] }
    },
    methods: {
        showLogin() {
            if (this.token) {
                //window.open('person/main.html?token='+this.token)
                window.location.href = 'person/main.html?token=' + this.token
            } else {
                // window.location.href = '00zhucedenglu.html'
               // let url;
               // var str = window.location.protocol + '//' + window.location.hostname + window.location.pathname
               // var result = encodeURIComponent(str)
               //  if (window.location.host == 'test.ningmengdou.com') {
               //      url = "https://ssotest.ningmengdou.com/single/login?returnUrl=" + result
               //  } else if (window.location.host == 'www.ningmengdou.com') {
               //      url = "https://sso.ningmengdou.com/single/login?returnUrl=" + result
               //  }
               //  window.location.href = url
            }
        },
        goXipin() {

            let url = 'https://xp-tech.ningmengdou.com/kczq/xipin_pc/01index.html'
            if (this.token) {
                url = 'https://xp-tech.ningmengdou.com/kczq/xipin_pc/01index.html?token=' + this.token + '&userName=' + this.userinfo.userName
            }
            window.location.href = url
                // var newWin = window.open('','_blank');
                // setTimeout(()=>{
                //     //这里使用setTimeout非常重要，没有将无法实现
                //     	//原因是window.open会中断正在执行的进程，这样能保证其它代码执行完成再执行这个。
                //     	newWin.location = url; //改变页面的location
                // }, 500)
        },
        loginout() {
            let url;
            // var str = window.location.href;
            var str = window.location.protocol + '//' + window.location.hostname + window.location.pathname
            var result = encodeURIComponent(str)
            if (window.location.host == 'test.ningmengdou.com') {
                url = "https://ssotest.ningmengdou.com/single/logout?returnUrl=" + result
            } else if (window.location.host == 'www.ningmengdou.com') {
                url = "https://sso.ningmengdou.com/single/logout?returnUrl=" + result
            }
            window.location.href = url
            window.sessionStorage.clear()
        },


    },
    mounted() {


    },
    template: `<header>
    <div class="wrap">
      <nav id="nav">
        <div class="logo"><a href="01index.html"><img src="images/logo4.png"></a></div>
        <ul class="nav">
           <li :class="['nav-item',sel==1?' active':'']"><a href="101factory_index.html">云豆工场首页</a></li>
      
          <li :class="['nav-item',sel==2?'active':'']"><a href="103factory_fuwushang_list.html">服务商</a> </li>

<li :class="['nav-item',sel==3?'active':'']"><a href="104factory_fuwu_list.html">设计服务</a> </li>
<li :class="['nav-item',sel==4?'active':'']"><a href="102factory_xuqiu_list.html">项目需求</a> </li>
<li :class="['nav-item',sel==5?'active':'']"><a href="105shejishi_list.html">设计师资源</a> </li>
<li :class="['nav-item',sel==6?'active':'']"><a href="106yundouzixun.html?levelCode=18">云豆资讯</a> </li>
        </ul>
        <div class="person">
		
		<a href="javascript:void(0)" @click="showLogin" v-if='!token' class="jrgzt">登录</a>
		<a href="javascript:void(0)" @click="showLogin" v-if='token' class="jrgzt">进入工作台</a>
		<a href="javascript:void(0)" @click="loginout" v-if='token'>退出</a></div>
        <div class="clear"></div>
      </nav>
    </div>
  </header>`

});
