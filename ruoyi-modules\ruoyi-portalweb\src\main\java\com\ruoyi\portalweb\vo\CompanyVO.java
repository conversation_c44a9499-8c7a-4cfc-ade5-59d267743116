package com.ruoyi.portalweb.vo;


import com.ruoyi.portalweb.api.domain.Company;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * 企业信息对象 company
 */
public class CompanyVO extends Company {
    private static final long serialVersionUID = 1L;

//    @ApiModelProperty(value = "真实姓名")
//    private String companyRealName;

    @ApiModelProperty(value = "会员真实姓名")
    private String memberRealName;
    /**
     * 传入附件
     */
    @ApiModelProperty(value = "传入附件")
    private List<FileDetailVO> alFileDetailVOs = new ArrayList<FileDetailVO>();

    @ApiModelProperty(value = "传入附件")
    private List<FileDetailVO> alFile0601 = new ArrayList<FileDetailVO>();

    @ApiModelProperty(value = "传入附件")
    private List<FileDetailVO> alFile0602 = new ArrayList<FileDetailVO>();


    public String getMemberRealName() {
        return memberRealName;
    }

    public void setMemberRealName(String memberRealName) {
        this.memberRealName = memberRealName;
    }

    public List<FileDetailVO> getAlFile0601() {
        return alFile0601;
    }

    public void setAlFile0601(List<FileDetailVO> alFile0601) {
        this.alFile0601 = alFile0601;
    }

    public List<FileDetailVO> getAlFile0602() {
        return alFile0602;
    }

    public void setAlFile0602(List<FileDetailVO> alFile0602) {
        this.alFile0602 = alFile0602;
    }

    public List<FileDetailVO> getAlFileDetailVOs() {
        return alFileDetailVOs;
    }

    public void setAlFileDetailVOs(List<FileDetailVO> alFileDetailVOs) {
        this.alFileDetailVOs = alFileDetailVOs;
    }

//    public String getMemberRealName() {
//        return memberRealName;
//    }
//
//    public void setMemberRealName(String memberRealName) {
//        this.memberRealName = memberRealName;
//    }
}
