package com.ruoyi.portalweb.wxpay.service.impl;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.AppStoreOrder;
import com.ruoyi.portalweb.api.enums.AppStoreOrderStatus;
import com.ruoyi.portalweb.api.domain.BuMemberOnlinePay;
import com.ruoyi.portalweb.service.IAppStoreOrderService;
import com.ruoyi.portalweb.service.IBuMemberOnlinePayService;
import com.ruoyi.portalweb.service.IMemberService;
import com.ruoyi.portalweb.vo.MemberVO;
import com.ruoyi.portalweb.wxpay.pay.WxPayUtil;
import com.ruoyi.portalweb.wxpay.service.IOrderRefundService;
import com.ruoyi.portalweb.wxpay.service.IWechatService;
import com.ruoyi.portalweb.api.domain.BuMemberOnlineRefund;
import com.ruoyi.portalweb.service.IBuMemberOnlineRefundService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

import static com.ruoyi.portalweb.wxpay.service.impl.OrderServiceImpl.YES;
import static com.ruoyi.portalweb.wxpay.service.impl.OrderServiceImpl.NO;

/**
 * 售后订单接口实现类
 *
 * <AUTHOR>
 *
 */
@Service
@AllArgsConstructor
@Slf4j
public class OrderReturnServiceImpl implements IOrderRefundService {

	@Autowired
	private IWechatService wechatService;
	@Autowired
	private IMemberService memberService;
	@Autowired
	private IAppStoreOrderService appStoreOrderService;
	@Autowired
	private IBuMemberOnlinePayService buMemberOnlinePayService;
	@Autowired
	private IBuMemberOnlineRefundService buMemberOnlineRefundService;

	/**
	 * 退款
	 *
	 * @param appStoreOrder
	 * @param remoteAddr
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean applyRefund(AppStoreOrder appStoreOrder, String remoteAddr) {
		// 获取会员信息
		MemberVO member = memberService.selectMemberByMemberId(SecurityUtils.getUserId());
		if (appStoreOrder == null || appStoreOrder.getAppStoreOrderId() == null || appStoreOrder.getAppStoreOrderId() == 0) {
			throw new ServiceException("传入的订单ID为空");
		}

		AppStoreOrder appOrder = appStoreOrderService.selectAppStoreOrderByAppStoreOrderId(appStoreOrder.getAppStoreOrderId());
		if (appOrder == null) {
			throw new ServiceException("无效的订单ID");
		}

		BuMemberOnlinePay buMemberOnlinePay = buMemberOnlinePayService.selectBuMemberOnlinePayByAppOrderNo(appOrder.getAppStoreOrderNo());
		if (buMemberOnlinePay == null) {
			throw new ServiceException("未找到订单相关的支付信息");
		}

		BuMemberOnlineRefund buMemberOnlineRefund = getBuMemberOnlineRefund(member, appOrder, buMemberOnlinePay);
		buMemberOnlineRefundService.insertBuMemberOnlineRefund(buMemberOnlineRefund);
		// TODO GENERATE
		String wxCallBackUrl = "?????";
		wechatService.wxReturn(buMemberOnlinePay.getPayOrderNo(), buMemberOnlineRefund.getRefundOrderNo(), buMemberOnlinePay.getMoney(), buMemberOnlinePay.getMoney(), remoteAddr, null, wxCallBackUrl, member.getMemberCompanyId().toString());
		// 审核退款：退款回调
		AppStoreOrder updateOrder = new AppStoreOrder();
		updateOrder.setAppStoreOrderId(appStoreOrder.getAppStoreOrderId());
		updateOrder.setAppStoreOrderNo(appStoreOrder.getAppStoreOrderNo());
		updateOrder.setOrderStatus(AppStoreOrderStatus.REFUNDING.getCode());
		appStoreOrderService.updateAppStoreOrderByOrderNo(updateOrder);
		return true;
	}

	/**
	 * 申请退款:回调
	 *
	 * @param outReFundNo
	 * @param member
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public void applyRefundCallBack(String outReFundNo, String successTime,MemberVO member, AppStoreOrder appStoreOrder){
		// 获取退款信息
		String appStoreOrderNo = null;
		if (Objects.equals(appStoreOrder, null) ){
			BuMemberOnlineRefund buMemberOnlineRefund = buMemberOnlineRefundService.selectBuMemberOnlineRefundByRefundOrderNo(outReFundNo);
			appStoreOrderNo = buMemberOnlineRefund.getAppStoreOrderNo();
		}else{
			appStoreOrderNo = appStoreOrder.getAppStoreOrderNo();
		}
		AppStoreOrder updateOrder = new AppStoreOrder();
		updateOrder.setAppStoreOrderNo(appStoreOrderNo);
		updateOrder.setOrderStatus(AppStoreOrderStatus.REFUNDING.getCode());
		appStoreOrderService.updateAppStoreOrderByOrderNo(updateOrder);

		BuMemberOnlineRefund buMemberOnlineRefund = new BuMemberOnlineRefund();
		buMemberOnlineRefund.setRefundOrderNo(outReFundNo);
		buMemberOnlineRefund.setStatus(YES);
		buMemberOnlineRefund.setRefundStatus("SUCCESS");
		buMemberOnlineRefund.setSuccessTime(successTime);
		buMemberOnlineRefundService.updateBuMemberOnlineRefundByRefundOrderNo(buMemberOnlineRefund);
	}

	/**
	 * 申请退款:回调错误
	 *
	 * @param outReFundNo
	 * @param errorMsg
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public void applyRefundCallBackError(String outReFundNo, String errorMsg){
		// 获取退款信息
		BuMemberOnlineRefund buMemberOnlineRefund = buMemberOnlineRefundService.selectBuMemberOnlineRefundByRefundOrderNo(outReFundNo);
		if(buMemberOnlineRefund == null){
			return;
		}
		if(Objects.equals(buMemberOnlineRefund.getStatus(), YES)){
			return;
		}
		BuMemberOnlineRefund buMemberOnlineRefund1 = new BuMemberOnlineRefund();
		buMemberOnlineRefund1.setRefundOrderNo(outReFundNo);
		buMemberOnlineRefund1.setRefundStatus("FAIL");
		buMemberOnlineRefund1.setRemark(errorMsg);
		buMemberOnlineRefundService.updateBuMemberOnlineRefundByRefundOrderNo(buMemberOnlineRefund1);
	}


	private static BuMemberOnlineRefund getBuMemberOnlineRefund(MemberVO member, AppStoreOrder appOrder, BuMemberOnlinePay buMemberOnlinePay) {
		BuMemberOnlineRefund buMemberOnlineRefund = new BuMemberOnlineRefund();
		buMemberOnlineRefund.setMemberId(member.getMemberId());
		buMemberOnlineRefund.setRefundOrderNo(WxPayUtil.getOutReFundNo());
		buMemberOnlineRefund.setAppStoreOrderNo(appOrder.getAppStoreOrderNo());
		buMemberOnlineRefund.setMoney(buMemberOnlinePay.getMoney());
		buMemberOnlineRefund.setCustomerId(member.getMemberCompanyId()); //TODO
		buMemberOnlineRefund.setTenantId(member.getMemberCompanyId().toString()); //TODO
		buMemberOnlineRefund.setPayOrderNo(buMemberOnlinePay.getPayOrderNo());
		buMemberOnlineRefund.setStatus(NO);
		buMemberOnlineRefund.setOnlinePayStyle("WX"); // todo
		return buMemberOnlineRefund;
	}

}
