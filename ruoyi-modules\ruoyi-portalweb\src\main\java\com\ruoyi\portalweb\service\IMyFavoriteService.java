package com.ruoyi.portalweb.service;

import java.util.List;
import com.ruoyi.portalweb.api.domain.MyFavorite;
import com.ruoyi.portalweb.vo.MyFavoriteVO;

/**
 * 我的收藏Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-25
 */
public interface IMyFavoriteService 
{
    /**
     * 查询我的收藏
     * 
     * @param myFavoriteId 我的收藏主键
     * @return 我的收藏
     */
    public MyFavorite selectMyFavoriteByMyFavoriteId(Long myFavoriteId);

    /**
     * 查询我的收藏
     *
     * @param type 我的收藏类型
     * @param issueId 我的收藏数据的主键
     * @return 我的收藏
     */
    public MyFavorite selectMyFavoriteByIssueId(String type, Long issueId);

    /**
     * 查询我的收藏列表
     * 
     * @param myFavorite 我的收藏
     * @return 我的收藏集合
     */
    public List<MyFavoriteVO> selectMyFavoriteList(MyFavorite myFavorite);

    /**
     * 新增我的收藏
     * 
     * @param myFavorite 我的收藏
     * @return 结果
     */
    public int insertMyFavorite(MyFavorite myFavorite);

    /**
     * 修改我的收藏
     * 
     * @param myFavorite 我的收藏
     * @return 结果
     */
    public int updateMyFavorite(MyFavorite myFavorite);

    /**
     * 批量删除我的收藏
     * 
     * @param myFavoriteIds 需要删除的我的收藏主键集合
     * @return 结果
     */
    public int deleteMyFavoriteByMyFavoriteIds(Long[] myFavoriteIds);

    /**
     * 删除我的收藏信息
     * 
     * @param myFavoriteId 我的收藏主键
     * @return 结果
     */
    public int deleteMyFavoriteByMyFavoriteId(Long myFavoriteId);
}
