package com.ruoyi.im.api.enums;

/**
 * 客服类型
 */
public enum ImModule {

    FRUIT("fruit", "成果大厅"),
    REQUIRE("require", "需求大厅"),
    DEMAND("demand", "产业需求"),
    EXPERT("expert", "专家中心"),
    EQUIP("equip", "仪器设备"),
    INFOR("infor", "科创资讯"),
    PLATFORM("platform", "研发平台"),
    SELECT("select", "新品优推"),
    ACTIVITY("activity", "活动"),
    SERVICE("service", "服务市场"),
    MATERIAL("material", "物料需求"),
    AGENCY("agency", "服务机构");

    private final String key;
    private final String value;

    ImModule(String key, String value) {
        this.key = key;
        this.value = value;
    }

    //根据key获取枚举
    public static ImModule getImModule(String key) {
        if (null == key) {
            return null;
        }
        for (ImModule temp : ImModule.values()) {
            if (temp.getKey().equals(key)) {
                return temp;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
