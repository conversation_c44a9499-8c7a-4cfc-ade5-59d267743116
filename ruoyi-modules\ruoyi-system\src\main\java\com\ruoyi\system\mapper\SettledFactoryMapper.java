package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SettledFactory;

/**
 * 入驻工厂Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
public interface SettledFactoryMapper
{
    /**
     * 查询入驻工厂
     *
     * @param id 入驻工厂主键
     * @return 入驻工厂
     */
    public SettledFactory selectSettledFactoryById(Long id);

    /**
     * 查询入驻工厂列表
     *
     * @param settledFactory 入驻工厂
     * @return 入驻工厂集合
     */
    public List<SettledFactory> selectSettledFactoryList(SettledFactory settledFactory);

    /**
     * 根据产品ID查询关联的工厂列表
     *
     * @param productId 产品ID
     * @return 入驻工厂集合
     */
    public List<SettledFactory> selectSettledFactoryListByProductId(Long productId);

    /**
     * 新增入驻工厂
     *
     * @param settledFactory 入驻工厂
     * @return 结果
     */
    public int insertSettledFactory(SettledFactory settledFactory);

    /**
     * 修改入驻工厂
     *
     * @param settledFactory 入驻工厂
     * @return 结果
     */
    public int updateSettledFactory(SettledFactory settledFactory);

    /**
     * 删除入驻工厂
     *
     * @param id 入驻工厂主键
     * @return 结果
     */
    public int deleteSettledFactoryById(Long id);

    /**
     * 批量删除入驻工厂
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSettledFactoryByIds(Long[] ids);
}
