<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.MemberMapper">

    <resultMap type="com.ruoyi.portalweb.vo.MemberVO" id="MemberResult">
        <result property="memberId" column="member_id"/>
        <result property="memberPhone" column="member_phone"/>
        <result property="memberPassword" column="member_password"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="memberRealName" column="member_real_name"/>
        <result property="memberWechat" column="member_wechat"/>
        <result property="solutionTypeId" column="solution_type_id"/>
        <result property="memberPost" column="member_post"/>
        <result property="memberCompanyName" column="member_company_name"/>
        <result property="memberCompanyArea" column="member_company_area"/>
        <result property="memberCompanyAddr" column="member_company_addr"/>
        <result property="memberStatus" column="member_status"/>
        <result property="memberCompanyId" column="member_company_id"/>
        <result property="companyRelatedId" column="company_related_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="email" column="email"/>
        <result property="avatar" column="avatar"/>
        <result property="companyScale" column="company_scale"/>
        <result property="isAdmin" column="is_admin"/>

        <result property="companyName" column="company_name"/>
        <result property="companyStatusName" column="company_status_name"/>
        <result property="companyStatus" column="company_status"/>
        <result property="solutionTypeName" column="solution_type_name"/>
        <result property="memberPostName" column="member_post_name"/>
        <result property="companyScaleName" column="company_scale_name"/>
        <result property="companyRealName" column="company_real_name"/>
    </resultMap>

    <sql id="selectMemberVo">
        select member_id, member_phone, member_password, last_login_time, member_real_name, member_wechat, is_admin,
        solution_type_id, member_post, member_company_name, member_company_area, member_company_addr, member_status,
        member_company_id, company_related_id, del_flag, create_by, create_time, update_by, update_time, remark ,email,avatar,company_scale
        from member
    </sql>

    <sql id="Base_Column_List">
		a.*,b.company_name,c.dict_label as company_status_name,b.company_status, b.company_real_name as company_real_name
        ,d.solution_type_name,s1.dict_label as member_post_name,s2.dict_label as company_scale_name
	</sql>

	<sql id="Base_Table_List">
		FROM member a
        LEFT JOIN company b ON a.member_company_id = b.company_id
        LEFT JOIN sys_dict_data c ON b.company_status = c.dict_value AND c.dict_type = 'company_status'
        LEFT JOIN solution_type d on d.solution_type_id = a.solution_type_id
        LEFT JOIN sys_dict_data s1 ON a.member_post = s1.dict_value AND s1.dict_type = 'member_post'
        LEFT JOIN sys_dict_data s2 ON a.company_scale = s2.dict_value AND s2.dict_type = 'company_scale'
	</sql>

    <select id="selectMemberByPhone" parameterType="String" resultMap="MemberResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        where a.member_phone = #{memberPhone} and a.del_flag = '0'
    </select>

    <select id="selectMemberList" parameterType="Member" resultMap="MemberResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        <where>
            a.del_flag = 0
            <if test="memberPhone != null  and memberPhone != ''">and a.member_phone = #{memberPhone}</if>
            <if test="memberPassword != null  and memberPassword != ''">and a.member_password = #{memberPassword}</if>
            <if test="lastLoginTime != null ">and a.last_login_time = #{lastLoginTime}</if>
            <if test="memberRealName != null  and memberRealName != ''">
                and a.member_real_name like concat('%', #{memberRealName}, '%')
            </if>
            <if test="memberWechat != null  and memberWechat != ''">and a.member_wechat = #{memberWechat}</if>
            <if test="solutionTypeId != null ">and a.solution_type_id = #{solutionTypeId}</if>
            <if test="memberPost != null  and memberPost != ''">and a.member_post = #{memberPost}</if>
            <if test="memberCompanyName != null  and memberCompanyName != ''">
                and a.member_company_name like concat('%', #{memberCompanyName}, '%')
            </if>
            <if test="memberCompanyArea != null  and memberCompanyArea != ''">
                and a.member_company_area = #{memberCompanyArea}
            </if>
            <if test="memberCompanyAddr != null  and memberCompanyAddr != ''">
                and a.member_company_addr = #{memberCompanyAddr}
            </if>
            <if test="memberStatus != null  and memberStatus != ''">and a.member_status = #{memberStatus}</if>
            <if test="memberCompanyId != null ">and a.member_company_id = #{memberCompanyId}</if>
            <if test="companyRelatedId != null ">and a.company_related_id = #{companyRelatedId}</if>
            <if test="companyScale != null ">and a.company_scale = #{companyScale}</if>
            <if test="isAdmin != null and isAdmin != ''">and a.is_admin = #{isAdmin}</if>
        </where>
    </select>

    <select id="selectMemberByMemberId" parameterType="Long" resultMap="MemberResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        where a.member_id = #{memberId}
    </select>

    <insert id="insertMember" parameterType="Member" useGeneratedKeys="true" keyProperty="memberId">
        insert into member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="memberPhone != null">member_phone,</if>
            <if test="memberPassword != null">member_password,</if>
            <if test="lastLoginTime != null">last_login_time,</if>
            <if test="memberRealName != null">member_real_name,</if>
            <if test="memberWechat != null">member_wechat,</if>
            <if test="solutionTypeId != null">solution_type_id,</if>
            <if test="memberPost != null">member_post,</if>
            <if test="memberCompanyName != null">member_company_name,</if>
            <if test="memberCompanyArea != null">member_company_area,</if>
            <if test="memberCompanyAddr != null">member_company_addr,</if>
            <if test="memberStatus != null">member_status,</if>
            <if test="memberCompanyId != null">member_company_id,</if>
            <if test="companyRelatedId != null">company_related_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="email != null">email,</if>
            <if test="avatar != null">avatar,</if>
            <if test="companyScale != null">company_scale,</if>
            <if test="isAdmin != null">is_admin,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="memberPhone != null">#{memberPhone},</if>
            <if test="memberPassword != null">#{memberPassword},</if>
            <if test="lastLoginTime != null">#{lastLoginTime},</if>
            <if test="memberRealName != null">#{memberRealName},</if>
            <if test="memberWechat != null">#{memberWechat},</if>
            <if test="solutionTypeId != null">#{solutionTypeId},</if>
            <if test="memberPost != null">#{memberPost},</if>
            <if test="memberCompanyName != null">#{memberCompanyName},</if>
            <if test="memberCompanyArea != null">#{memberCompanyArea},</if>
            <if test="memberCompanyAddr != null">#{memberCompanyAddr},</if>
            <if test="memberStatus != null">#{memberStatus},</if>
            <if test="memberCompanyId != null">#{memberCompanyId},</if>
            <if test="companyRelatedId != null">#{companyRelatedId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="email != null">#{email},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="companyScale != null">#{companyScale},</if>
            <if test="isAdmin != null">#{isAdmin},</if>
        </trim>
    </insert>

    <update id="updateMember" parameterType="Member">
        update member
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberPhone != null">member_phone = #{memberPhone},</if>
            <if test="memberPassword != null">member_password = #{memberPassword},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
            <if test="memberRealName != null">member_real_name = #{memberRealName},</if>
            <if test="memberWechat != null">member_wechat = #{memberWechat},</if>
            <if test="solutionTypeId != null">solution_type_id = #{solutionTypeId},</if>
            <if test="memberPost != null">member_post = #{memberPost},</if>
            <if test="memberCompanyName != null">member_company_name = #{memberCompanyName},</if>
            <if test="memberCompanyArea != null">member_company_area = #{memberCompanyArea},</if>
            <if test="memberCompanyAddr != null">member_company_addr = #{memberCompanyAddr},</if>
            <if test="memberStatus != null">member_status = #{memberStatus},</if>
            <if test="memberCompanyId != null">member_company_id = #{memberCompanyId},</if>
            <if test="companyRelatedId != null">company_related_id = #{companyRelatedId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="email != null">email = #{email},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="companyScale != null">company_scale = #{companyScale},</if>
            <if test="isAdmin != null">is_admin = #{isAdmin},</if>
        </trim>
        where member_id = #{memberId}
    </update>
    <update id="updateMemberPassword" parameterType="Member">
        update member set member_password = #{memberPassword} where member_phone = #{memberPhone}
    </update>
    <update id="quitCompanyRelated">
        update member set company_related_id = null, member_company_id = null, member_company_name = null, member_status = '3', is_admin = 'N'
        <where>
            member_id = #{memberId}
            <if test="companyRelatedId != null and companyRelatedId != ''">and company_related_id = #{companyRelatedId}</if>
        </where>

    </update>

    <delete id="deleteMemberByMemberId" parameterType="Long">
        delete from member where member_id = #{memberId}
    </delete>

    <delete id="deleteMemberByMemberIds" parameterType="String">
        delete from member where member_id in
        <foreach item="memberId" collection="array" open="(" separator="," close=")">
            #{memberId}
        </foreach>
    </delete>
</mapper>