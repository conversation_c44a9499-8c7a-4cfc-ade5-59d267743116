package com.ruoyi.portalweb.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.portalweb.mapper.PolicyLabelMapper;
import com.ruoyi.portalweb.api.domain.PolicyLabel;
import com.ruoyi.portalweb.service.IPolicyLabelService;

/**
 * 政策标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
public class PolicyLabelServiceImpl implements IPolicyLabelService 
{
    @Autowired
    private PolicyLabelMapper policyLabelMapper;

    /**
     * 查询政策标签
     * 
     * @param policyLabelId 政策标签主键
     * @return 政策标签
     */
    @Override
    public PolicyLabel selectPolicyLabelByPolicyLabelId(Long policyLabelId)
    {
        return policyLabelMapper.selectPolicyLabelByPolicyLabelId(policyLabelId);
    }

    /**
     * 查询政策标签列表
     * 
     * @param policyLabel 政策标签
     * @return 政策标签
     */
    @Override
    public List<PolicyLabel> selectPolicyLabelList(PolicyLabel policyLabel)
    {
        return policyLabelMapper.selectPolicyLabelList(policyLabel);
    }

    /**
     * 查询全部政策标签列表
     *
     * @param policyLabel 政策标签
     * @return 政策标签
     */
    @Override
    public  List<List<PolicyLabel>> selectPolicyLabelAll(PolicyLabel policyLabel)
    {
        List<PolicyLabel> policyLabels = policyLabelMapper.selectPolicyLabelList(policyLabel);
        List<List<PolicyLabel>>  resultLabels = new ArrayList<>();
        HashMap<String, Boolean> groupMap = new HashMap<>(); // 标记已添加的政策标签分组
        for(PolicyLabel p : policyLabels){

            // 当前组已获取则跳过
            if (groupMap.get(p.getPolicyLabelGroup()) != null){
                continue;
            }

            // 获取当前组的所有标签数据
            groupMap.put(p.getPolicyLabelGroup(), true);
            List<PolicyLabel> labels = new ArrayList<>();
            for(PolicyLabel label : policyLabels){
                if (label.getPolicyLabelGroup().equals(p.getPolicyLabelGroup())){
                    labels.add(label);
                }
            }
            resultLabels.add(labels);
        }

        return resultLabels;
    }

    /**
     * 新增政策标签
     * 
     * @param policyLabel 政策标签
     * @return 结果
     */
    @Override
    public int insertPolicyLabel(PolicyLabel policyLabel)
    {
        policyLabel.setCreateTime(DateUtils.getNowDate());
        return policyLabelMapper.insertPolicyLabel(policyLabel);
    }

    /**
     * 修改政策标签
     * 
     * @param policyLabel 政策标签
     * @return 结果
     */
    @Override
    public int updatePolicyLabel(PolicyLabel policyLabel)
    {
        policyLabel.setUpdateTime(DateUtils.getNowDate());
        return policyLabelMapper.updatePolicyLabel(policyLabel);
    }

    /**
     * 批量删除政策标签
     * 
     * @param policyLabelIds 需要删除的政策标签主键
     * @return 结果
     */
    @Override
    public int deletePolicyLabelByPolicyLabelIds(Long[] policyLabelIds)
    {
        return policyLabelMapper.deletePolicyLabelByPolicyLabelIds(policyLabelIds);
    }

    /**
     * 删除政策标签信息
     * 
     * @param policyLabelId 政策标签主键
     * @return 结果
     */
    @Override
    public int deletePolicyLabelByPolicyLabelId(Long policyLabelId)
    {
        return policyLabelMapper.deletePolicyLabelByPolicyLabelId(policyLabelId);
    }
}
