package com.ruoyi.system.utils;

import com.ruoyi.common.core.utils.DateUtils;

public class RandomUtil {

    /**
     * 年月日时分秒+2位数的随机id 共16位
     * @return
     */
    public static long randomLong16(){
        String currentDate= DateUtils.dateTime();
        String randomNum=getRandomNum(8);
        return Long.valueOf(currentDate+randomNum);
    }

    public static String getRandomNum(int length) {
        char[] codeSeq = {'0','1','2', '3', '4', '5', '6', '7', '8', '9'};
        java.util.Random random = new java.util.Random();
        StringBuilder s = new StringBuilder();
        for (int i = 0; i < length; i++) {
            String r = String.valueOf(codeSeq[random.nextInt(codeSeq.length)]);
            s.append(r);
        }
        return s.toString();
    }
}
