package com.ruoyi.portalweb.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.portalweb.api.domain.PolicyInformation;
import com.ruoyi.portalweb.mapper.PolicyInformationMapper;
import com.ruoyi.portalweb.service.IMemberService;
import com.ruoyi.portalweb.service.IPolicyInformationService;
import com.ruoyi.portalweb.vo.MemberVO;
import com.ruoyi.portalweb.vo.PolicyInformationVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.ruoyi.common.core.utils.PageUtils.startPage;

/**
 * 政策资讯Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class PolicyInformationServiceImpl implements IPolicyInformationService
{
    @Autowired
    private PolicyInformationMapper policyInformationMapper;

    @Autowired
    private IMemberService memberService;

    /**
     * 查询政策资讯
     * 
     * @param policyInformationId 政策资讯主键
     * @return 政策资讯
     */
    @Override
    public PolicyInformation selectPolicyInformationByPolicyInformationId(Long policyInformationId)
    {
        return policyInformationMapper.selectPolicyInformationByPolicyInformationId(policyInformationId);
    }

    /**
     * 查询政策资讯列表
     * 
     * @param policyInformation 政策资讯
     * @return 政策资讯
     */
    @Override
    public List<PolicyInformation> selectPolicyInformationList(PolicyInformationVO policyInformation)
    {
        return policyInformationMapper.selectPolicyInformationList(policyInformation);
    }

    /**
     * 新增政策资讯
     * 
     * @param policyInformation 政策资讯
     * @return 结果
     */
    @Override
    public int insertPolicyInformation(PolicyInformation policyInformation)
    {
        policyInformation.setCreateTime(DateUtils.getNowDate());
        return policyInformationMapper.insertPolicyInformation(policyInformation);
    }

    /**
     * 修改政策资讯
     * 
     * @param policyInformation 政策资讯
     * @return 结果
     */
    @Override
    public int updatePolicyInformation(PolicyInformation policyInformation)
    {
        policyInformation.setUpdateTime(DateUtils.getNowDate());
        return policyInformationMapper.updatePolicyInformation(policyInformation);
    }

    /**
     * 批量删除政策资讯
     * 
     * @param policyInformationIds 需要删除的政策资讯主键
     * @return 结果
     */
    @Override
    public int deletePolicyInformationByPolicyInformationIds(Long[] policyInformationIds)
    {
        return policyInformationMapper.deletePolicyInformationByPolicyInformationIds(policyInformationIds);
    }

    /**
     * 删除政策资讯信息
     * 
     * @param policyInformationId 政策资讯主键
     * @return 结果
     */
    @Override
    public int deletePolicyInformationByPolicyInformationId(Long policyInformationId)
    {
        return policyInformationMapper.deletePolicyInformationByPolicyInformationId(policyInformationId);
    }

    @Override
    public List<PolicyInformation> selectRecommendPolicyInformationList(PolicyInformationVO policyInformation) {
        // 获取用户行业信息
        MemberVO memberVO = memberService.selectMemberByMemberId(SecurityUtils.getUserId());
        if (memberVO.getSolutionTypeName() == null || Objects.equals(memberVO.getSolutionTypeName(), "")) {
            return null;
        }
        startPage();
        PageUtils.setOrderBy("top DESC, update_time DESC");
        PolicyInformationVO wrapper = new PolicyInformationVO();
        wrapper.setKeyword(memberVO.getSolutionTypeName());
        return policyInformationMapper.selectPolicyInformationList(wrapper);
    }
}
