<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="356.886" height="266" viewBox="0 0 356.886 266">
  <defs>
    <filter id="减去_628" x="68" y="54" width="161.999" height="147.001" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="矩形_21811" x="92" y="104" width="110" height="29" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="矩形_21812" x="92" y="137" width="83" height="29" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-3"/>
      <feFlood flood-color="#669bcc" flood-opacity="0.2"/>
      <feComposite operator="in" in2="blur-3"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="组_21784" data-name="组 21784" transform="translate(-488 -1975)">
    <g id="组_21696" data-name="组 21696" transform="translate(-62.228 579.757)">
      <path id="路径_8363" data-name="路径 8363" d="M46.6,63.379C82.646,40.079,233.02-5.271,253.749,32.8S348.27,8.687,366.24,69.2s-80.09,203.867-130.693,155.449-68.441-88.464-128.509-75.358S10.564,86.679,46.6,63.379Z" transform="translate(923 1661.361) rotate(180)" fill="#eaf2fe"/>
    </g>
    <path id="路径_8556" data-name="路径 8556" d="M-6096,14344l275.818-89.335" transform="translate(6600 -12267)" fill="none" stroke="#cbdfff" stroke-width="5"/>
    <rect id="矩形_21810" data-name="矩形 21810" width="144" height="129" rx="6" transform="translate(616 2049)" fill="#428afa"/>
    <path id="路径_8557" data-name="路径 8557" d="M-5936.907,14486.592-5822,14262.338" transform="translate(6600 -12271.732)" fill="none" stroke="#cbdfff" stroke-width="5"/>
    <g transform="matrix(1, 0, 0, 1, 488, 1975)" filter="url(#减去_628)">
      <path id="减去_628-2" data-name="减去 628" d="M6749-12005H6617a6.006,6.006,0,0,1-6-6v-117a6.006,6.006,0,0,1,6-6h7v12a6.007,6.007,0,0,0,6,6h14a6.007,6.007,0,0,0,6-6v-12h20v12a6.006,6.006,0,0,0,6,6h14a6.007,6.007,0,0,0,6-6v-12h20v12a6.007,6.007,0,0,0,6,6h14a6.007,6.007,0,0,0,6-6v-12h7a6.007,6.007,0,0,1,6,6v117A6.007,6.007,0,0,1,6749-12005Z" transform="translate(-6534 12194)" fill="#fff"/>
    </g>
    <rect id="矩形_21803" data-name="矩形 21803" width="14" height="29" rx="6" transform="translate(584 2020)" fill="#428afa"/>
    <rect id="矩形_21804" data-name="矩形 21804" width="14" height="29" rx="6" transform="translate(630 2020)" fill="#428afa"/>
    <rect id="矩形_21805" data-name="矩形 21805" width="14" height="29" rx="6" transform="translate(676 2020)" fill="#428afa"/>
    <g transform="matrix(1, 0, 0, 1, 488, 1975)" filter="url(#矩形_21811)">
      <rect id="矩形_21811-2" data-name="矩形 21811" width="92" height="11" rx="5.5" transform="translate(101 110)" fill="#428afa"/>
    </g>
    <g transform="matrix(1, 0, 0, 1, 488, 1975)" filter="url(#矩形_21812)">
      <rect id="矩形_21812-2" data-name="矩形 21812" width="65" height="11" rx="5.5" transform="translate(101 143)" fill="#428afa"/>
    </g>
    <path id="路径_8558" data-name="路径 8558" d="M-5941,14486c2.03.563-155.567-143.309-155.567-143.309" transform="translate(6600 -12267)" fill="none" stroke="#cbdfff" stroke-width="7"/>
    <g id="铅笔" transform="translate(614.258 1640.414) rotate(45)">
      <path id="路径_8404" data-name="路径 8404" d="M375.422,279.5,357.6,338.47l6.443,21.691,17.338-14.508,17.808-58.97Z" transform="translate(0 -87.561)" fill="#ffdcb3"/>
      <path id="路径_8405" data-name="路径 8405" d="M383.034,254.311l23.774,7.18L389,320.462l-23.774-7.18Z" transform="translate(-6.54 -65.954)" fill="#fecd44"/>
      <path id="路径_8406" data-name="路径 8406" d="M388.3,797.1l2.077,7.041,5.633-4.708Z" transform="translate(-26.333 -531.54)" fill="#aeaba8"/>
      <path id="路径_8407" data-name="路径 8407" d="M402.9,712.617l9.416-16.9,3.4-5.32,4.523,7.709Z" transform="translate(-38.857 -440.016)" fill="#cc9d71"/>
      <path id="路径_8408" data-name="路径 8408" d="M405.958,808.1l-3.058,5.476,5.632-4.708Z" transform="translate(-38.857 -540.976)" fill="#231f20"/>
      <path id="路径_8409" data-name="路径 8409" d="M404.393,679.677l-1.493,19.3,9.416-16.9.085-7.581-5.49.74Z" transform="translate(-38.857 -426.378)" fill="#f0bf92"/>
      <path id="路径_8410" data-name="路径 8410" d="M365.622,340.86a6.952,6.952,0,0,0-3.471-2.83,6.832,6.832,0,0,0-4.452.441l17.808-58.97,7.922,2.39-17.808,58.97Z" transform="translate(-0.086 -87.561)" fill="#87abff"/>
      <path id="路径_8411" data-name="路径 8411" d="M421.322,357.76a5.866,5.866,0,0,0-7.922-2.39l17.808-58.97,7.922,2.39Z" transform="translate(-47.863 -102.057)" fill="#3d74ff"/>
      <path id="路径_8412" data-name="路径 8412" d="M477.022,374.56a7.448,7.448,0,0,0-3.471-2.83,7.37,7.37,0,0,0-4.452.441l17.808-58.97,7.922,2.39Z" transform="translate(-95.641 -116.467)" fill="#1f53cc"/>
      <path id="路径_8413" data-name="路径 8413" d="M403.384,802.6l-.484,6.258,3.058-5.476Z" transform="translate(-38.857 -536.258)" fill="#63585b"/>
      <path id="路径_8414" data-name="路径 8414" d="M506.667,199.122,482.9,191.939l1.721-5.689a12.414,12.414,0,1,1,23.767,7.183Z" transform="translate(-107.478)" fill="#fc355d"/>
      <path id="路径_8415" data-name="路径 8415" d="M475.191,251.694l26.265,7.932-1.081,3.581-26.265-7.932Z" transform="translate(-99.938 -63.71)" fill="#fecd44"/>
      <path id="路径_8416" data-name="路径 8416" d="M474.075,255.28l1.082-3.581,8.755,2.644-1.081,3.581Z" transform="translate(-99.908 -63.714)" fill="#ffce87"/>
      <path id="路径_8417" data-name="路径 8417" d="M535.656,273.829l1.081-3.581,8.755,2.644-1.081,3.581Z" transform="translate(-152.73 -79.625)" fill="#ffb53d"/>
      <path id="路径_8418" data-name="路径 8418" d="M623.292,209.513c1.109,1.693,1.451,3.428,1.735,3.243s.412-2.233-.711-3.926-3.03-2.39-3.314-2.2S622.182,207.82,623.292,209.513Z" transform="translate(-225.909 -25.026)" fill="#fff"/>
      <path id="路径_8419" data-name="路径 8419" d="M483.778,284.194l.7-2.3-7.922-2.39-1.053,3.442Z" transform="translate(-101.131 -87.561)" fill="#7898e3"/>
      <path id="路径_8420" data-name="路径 8420" d="M541.964,299.842l.356-1.152L534.4,296.3l-.7,2.3Z" transform="translate(-151.052 -101.971)" fill="#3463d9"/>
      <path id="路径_8421" data-name="路径 8421" d="M600.078,315.5l-7.922-2.4-.356,1.152Z" transform="translate(-200.889 -116.382)" fill="#1a46ab"/>
    </g>
    <g id="椭圆_1792" data-name="椭圆 1792" transform="translate(786.886 2028.24)" fill="none" stroke="#f7fbfe" stroke-width="4">
      <circle cx="11" cy="11" r="11" stroke="none"/>
      <circle cx="11" cy="11" r="9" fill="none"/>
    </g>
    <circle id="椭圆_1791" data-name="椭圆 1791" cx="14.888" cy="14.888" r="14.888" transform="translate(815.111 2015.225)" fill="#f7fbfe"/>
    <circle id="椭圆_1790" data-name="椭圆 1790" cx="14" cy="14" r="14" transform="translate(764 1975)" fill="#ff787a"/>
    <circle id="椭圆_1793" data-name="椭圆 1793" cx="16" cy="16" r="16" transform="translate(488 2061)" fill="#79cb73"/>
    <circle id="椭圆_1797" data-name="椭圆 1797" cx="22" cy="22" r="22" transform="translate(637 2197)" fill="#ffb517"/>
  </g>
</svg>
