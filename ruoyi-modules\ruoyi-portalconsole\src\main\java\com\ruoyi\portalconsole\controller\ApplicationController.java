package com.ruoyi.portalconsole.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalconsole.domain.Application;
import com.ruoyi.portalconsole.service.IApplicationService;

/**
 * 系统对接Controller
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
@RestController
@RequestMapping("/Application")
public class ApplicationController extends BaseController
{
    @Autowired
    private IApplicationService applicationService;

    /**
     * 查询系统对接列表
     */
    @RequiresPermissions("portalconsole:Application:list")
    @GetMapping("/list")
    public TableDataInfo list(Application application)
    {
        startPage();
        List<Application> list = applicationService.selectApplicationList(application);
        return getDataTable(list);
    }

    /**
     * 导出系统对接列表
     */
    @RequiresPermissions("portalconsole:Application:export")
    @Log(title = "系统对接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Application application)
    {
        List<Application> list = applicationService.selectApplicationList(application);
        ExcelUtil<Application> util = new ExcelUtil<Application>(Application.class);
        util.exportExcel(response, list, "系统对接数据");
    }

    /**
     * 获取系统对接详细信息
     */
    @RequiresPermissions("portalconsole:Application:query")
    @GetMapping(value = "/{applicationId}")
    public AjaxResult getInfo(@PathVariable("applicationId") Long applicationId)
    {
        return success(applicationService.selectApplicationByApplicationId(applicationId));
    }

    /**
     * 新增系统对接
     */
    @RequiresPermissions("portalconsole:Application:add")
    @Log(title = "系统对接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Application application)
    {
        return toAjax(applicationService.insertApplication(application));
    }

    /**
     * 修改系统对接
     */
    @RequiresPermissions("portalconsole:Application:edit")
    @Log(title = "系统对接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Application application)
    {
        return toAjax(applicationService.updateApplication(application));
    }

    /**
     * 删除系统对接
     */
    @RequiresPermissions("portalconsole:Application:remove")
    @Log(title = "系统对接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{applicationIds}")
    public AjaxResult remove(@PathVariable Long[] applicationIds)
    {
        return toAjax(applicationService.deleteApplicationByApplicationIds(applicationIds));
    }
    
    
    /**
     * 查询系统对接列表
     */
    @GetMapping("/applicationbyappid/{appid}")
    public R<Application> selectApplicationByAppId(String appId)
    {
    	Application application = applicationService.selectApplicationByAppId(appId);
        return R.ok(application);
    }

    
}
