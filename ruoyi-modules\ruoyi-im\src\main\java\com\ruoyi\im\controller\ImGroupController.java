package com.ruoyi.im.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.im.api.domain.ImGroup;
import com.ruoyi.im.api.domain.ImGroupUser;
import com.ruoyi.im.api.util.RongyunUtils;
import com.ruoyi.im.service.ImGroupService;
import com.ruoyi.im.service.ImGroupUserService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/im/group")
public class ImGroupController {

    @Resource
    private RongyunUtils rongyunUtils;

    @Resource
    private ImGroupService imGroupService;

    @Resource
    private ImGroupUserService imGroupUserService;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddhhmmss");
    /***
     * ImGroup分页条件搜索实现
     * @param imGroup
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/search/{page}/{size}" )
    public TableDataInfo findPage(@RequestBody(required = false)  ImGroup imGroup, @PathVariable("page")  int page, @PathVariable("size")  int size, @RequestParam(value = "fields",required = false) String fields){
        Page<ImGroup> pageSearch = new Page<>(page,size);
        QueryWrapper<ImGroup> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        imGroupService.page(pageSearch,wrapper);
        TableDataInfo tableDataInfo = new TableDataInfo(pageSearch.getRecords(), (int) pageSearch.getTotal());
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setMsg("success");
        return tableDataInfo;
    }

    /***
     * 多条件搜索数据
     * @param imGroup
     * @return
     */
    @PostMapping(value = "/search" )
    public R<List<ImGroup>> findList(@RequestBody(required = false)  ImGroup imGroup, @RequestParam(value = "fields",required = false) String fields){
        QueryWrapper<ImGroup> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(fields))
            wrapper.select(fields);
        wrapper.orderByDesc("id");
        return R.ok(imGroupService.list(wrapper)) ;
    }

    /***
     * 新增ImGroup数据 创建群组
     * @param imGroup
     * @return
     */
    @PostMapping(value="/add")
    public R<String> add(@RequestBody ImGroup imGroup){
        //创建群组id
        imGroup.setGroupId( "G_"+imGroup.getUserId()+"_"+dateFormat.format(new Date()));
        imGroup.setGroupAvatar("https://xp-tech.oss-cn-beijing.aliyuncs.com/20220325/1648178392997773.png?Expires=4801778393&OSSAccessKeyId=LTAI4G5Udf4KbAUamwr8dKC9&Signature=3CMJJn4VhwKJ9v5wDmk1T6iO9W0%3D");
        //融云创建
        String createGroupChat = rongyunUtils.CreateGroupChat(imGroup.getGroupId(), imGroup.getGroupName(),imGroup.getUserIds());
        //校验是否创建成功
        if (StringUtils.isNotBlank(createGroupChat)) {
            JSONObject jsonStr = JSONObject.parseObject(createGroupChat);
            if (jsonStr.getString("code").equals("200")) {
                if (imGroupService.save(imGroup)) {
                    String[] split = imGroup.getUserIds().split(",");
                    ArrayList<ImGroupUser> list = new ArrayList<>();
                    for (String str : split) {
                        ImGroupUser imGroupUser = new ImGroupUser();
                        imGroupUser.setGroupId(imGroup.getGroupId());
                        imGroupUser.setUserId(str);
                        imGroupUser.setCreate_time(new Date());
                        list.add(imGroupUser);
                    }
                    //创建群聊人员
                    if (imGroupUserService.saveBatch(list)) {
                        return R.ok(imGroup.getGroupId());
                    }
                }
            }
        }
        return R.fail(400, "缺少必要的请求参数");
    }



    /***
     * 解散群组
     * @param imGroup
     * @return
     */
    @PostMapping(value="/deleteGroup")
    public R<Boolean> deleteGroup(@RequestBody ImGroup imGroup){
        //校验是否有内容
        if (ObjectUtils.isNotEmpty(imGroup)) {
                if (imGroupService.remove(new QueryWrapper<ImGroup>().eq("groupId",imGroup.getGroupId()))) {
                    if (imGroupUserService.remove(new QueryWrapper<ImGroupUser>().eq("groupId",imGroup.getGroupId()))) {
                        return R.ok(true);
                    }
                }
            }

        return R.ok(false);
    }


    /***
     * 修改ImGroup数据
     * @param imGroup
     * @return
     */
    @PostMapping(value="/update")
    public R<Boolean> update(@RequestBody ImGroup imGroup){
        String resul = rongyunUtils.updateGroup(imGroup.getGroupId(), imGroup.getGroupName());
        if (StringUtils.isNotBlank(resul)) {
            JSONObject jsonStr = JSONObject.parseObject(resul);
            //校验在融云是否成功
            if (jsonStr.getString("code").equals("200")) {
                if (imGroupService.update(imGroup,new QueryWrapper<ImGroup>().eq("groupId",imGroup.getGroupId()))) {
                    return R.ok(true) ;
                }
            }
        }
        return R.ok(false) ;
    }

}
