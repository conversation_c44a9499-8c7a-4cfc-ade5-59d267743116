package com.ruoyi.im.api;

import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.im.api.domain.ImChatroomMsg;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.api
 * @ClassName: RemoteImChatroomService
 * @Author: ${maguojun}
 * @Description: ${description}
 * @Date: 2022/3/16 13:51
 * @Version: 1.0
 */
@FeignClient(contextId = "remoteImChatroomMsgService", value = ServiceNameConstants.IM_SERVICE)
public interface RemoteImChatroomMsgService {

    /***
     * 接受消息
     * @param imChatroomMsg
     * @return
     */
    @PostMapping(value="/im/chatroommsg/receive")
    AjaxResult receive(@RequestBody ImChatroomMsg imChatroomMsg);


    @GetMapping(value = "/im/chatroommsg/sent" )
    R<List<String>> findSent(@RequestParam("telphone") String telphone);

    /***
     * 多条件搜索数据
     * @param imChatroomMsg
     * @return
     */
    @PostMapping(value = "/im/chatroommsg/search" )
    R<List<ImChatroomMsg>> findList(@RequestBody(required = false) ImChatroomMsg imChatroomMsg, @RequestParam(value = "fields",required = false) String fields);


    @PostMapping(value = "/im/chatroommsg/search/{page}/{size}" )
    R<TableDataInfo> findPage(@RequestBody(required = false)  ImChatroomMsg imChatroomMsg, @PathVariable("page")  int page, @PathVariable("size")  int size, @RequestParam(value = "fields",required = false) String fields);

}
