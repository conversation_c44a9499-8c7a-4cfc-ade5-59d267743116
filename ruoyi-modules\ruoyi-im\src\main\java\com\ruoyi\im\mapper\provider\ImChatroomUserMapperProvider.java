package com.ruoyi.im.mapper.provider;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class ImChatroomUserMapperProvider {

    public String findUser(Map map) {
        String ids = (String)map.get("ids");
        List<String> list = Arrays.asList(ids.split(","));
        StringBuilder sb = new StringBuilder("select chatroomId,count(id) number from im_chatroom_user where chatroomId in (");
        for (int i = 0; i < list.size(); i++) {
            sb.append("'").append(list.get(i)).append("'");
            if (i < list.size() - 1) {
                sb.append(",");
            }
        }
        sb.append(") ");
        sb.append("group by chatroomId");
        return sb.toString();
    }
}
