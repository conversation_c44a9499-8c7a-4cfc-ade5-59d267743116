package com.ruoyi.system.domain.dto;

import com.ruoyi.system.domain.LaboratoryInfo;
import com.ruoyi.system.domain.LabTestingRelation;
import com.ruoyi.system.domain.TestingItem;

import java.util.List;

/**
 * 检测项目与实验室关联数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-03-08
 */
public class TestingItemWithLabsDTO {
    
    /** 检测项目信息 */
    private TestingItem testingItem;
    
    /** 关联的实验室信息列表 */
    private List<LaboratoryInfo> labs;
    
    /** 实验室检测项目关联信息列表 */
    private List<LabTestingRelation> labTestingRelations;

    public TestingItem getTestingItem() {
        return testingItem;
    }

    public void setTestingItem(TestingItem testingItem) {
        this.testingItem = testingItem;
    }

    public List<LaboratoryInfo> getLabs() {
        return labs;
    }

    public void setLabs(List<LaboratoryInfo> labs) {
        this.labs = labs;
    }

    public List<LabTestingRelation> getLabTestingRelations() {
        return labTestingRelations;
    }

    public void setLabTestingRelations(List<LabTestingRelation> labTestingRelations) {
        this.labTestingRelations = labTestingRelations;
    }
}
