package com.ruoyi.portalweb.wxpay.service;

import com.ruoyi.common.core.domain.R;

import java.math.BigDecimal;

public interface IWechatService {
     String wxNativePay(String outTradeNo, BigDecimal payMoney, String spbillCreateIp, Long productId, String body, String tenantId);

     R<Object> wxReturn(String outTradeNo, String outRefundNo, BigDecimal totalMoney, BigDecimal refundMoney,
                              String spbillCreateIp, String refundDesc, String wxCallBackUrl, String tenantId);
}
