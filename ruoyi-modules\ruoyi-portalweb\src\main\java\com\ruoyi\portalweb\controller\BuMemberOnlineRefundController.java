package com.ruoyi.portalweb.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.portalweb.api.domain.BuMemberOnlineRefund;
import com.ruoyi.portalweb.service.IBuMemberOnlineRefundService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 商城用户线上退款Controller
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@RestController
@RequestMapping("/BuMemberOnlineRefund")
public class BuMemberOnlineRefundController extends BaseController
{
    @Autowired
    private IBuMemberOnlineRefundService buMemberOnlineRefundService;

    /**
     * 查询商城用户线上退款列表
     */
    @RequiresPermissions("portalweb:BuMemberOnlineRefund:list")
    @GetMapping("/list")
    public TableDataInfo list(BuMemberOnlineRefund buMemberOnlineRefund)
    {
        startPage();
        List<BuMemberOnlineRefund> list = buMemberOnlineRefundService.selectBuMemberOnlineRefundList(buMemberOnlineRefund);
        return getDataTable(list);
    }

    /**
     * 导出商城用户线上退款列表
     */
    @RequiresPermissions("portalweb:BuMemberOnlineRefund:export")
    @Log(title = "商城用户线上退款", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BuMemberOnlineRefund buMemberOnlineRefund)
    {
        List<BuMemberOnlineRefund> list = buMemberOnlineRefundService.selectBuMemberOnlineRefundList(buMemberOnlineRefund);
        ExcelUtil<BuMemberOnlineRefund> util = new ExcelUtil<BuMemberOnlineRefund>(BuMemberOnlineRefund.class);
        util.exportExcel(response, list, "商城用户线上退款数据");
    }

    /**
     * 获取商城用户线上退款详细信息
     */
    @RequiresPermissions("portalweb:BuMemberOnlineRefund:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(buMemberOnlineRefundService.selectBuMemberOnlineRefundById(id));
    }

    /**
     * 新增商城用户线上退款
     */
    @RequiresPermissions("portalweb:BuMemberOnlineRefund:add")
    @Log(title = "商城用户线上退款", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuMemberOnlineRefund buMemberOnlineRefund)
    {
        return toAjax(buMemberOnlineRefundService.insertBuMemberOnlineRefund(buMemberOnlineRefund));
    }

    /**
     * 修改商城用户线上退款
     */
    @RequiresPermissions("portalweb:BuMemberOnlineRefund:edit")
    @Log(title = "商城用户线上退款", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuMemberOnlineRefund buMemberOnlineRefund)
    {
        return toAjax(buMemberOnlineRefundService.updateBuMemberOnlineRefund(buMemberOnlineRefund));
    }

    /**
     * 删除商城用户线上退款
     */
    @RequiresPermissions("portalweb:BuMemberOnlineRefund:remove")
    @Log(title = "商城用户线上退款", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(buMemberOnlineRefundService.deleteBuMemberOnlineRefundByIds(ids));
    }
}
