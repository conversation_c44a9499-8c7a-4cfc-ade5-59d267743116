package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.OutsourcingRequirement;
import com.ruoyi.system.service.IOutsourcingRequirementService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 工序外协需求Controller
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@RestController
@RequestMapping("/outsourcingRequirement")
public class OutsourcingRequirementController extends BaseController
{
    @Autowired
    private IOutsourcingRequirementService outsourcingRequirementService;

    /**
     * 查询工序外协需求列表
     */

    @GetMapping("/list")
    public TableDataInfo list(OutsourcingRequirement outsourcingRequirement)
    {
        startPage();
        List<OutsourcingRequirement> list = outsourcingRequirementService.selectOutsourcingRequirementList(outsourcingRequirement);
        return getDataTable(list);
    }

    /**
     * 导出工序外协需求列表
     */

    @Log(title = "工序外协需求", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OutsourcingRequirement outsourcingRequirement)
    {
        List<OutsourcingRequirement> list = outsourcingRequirementService.selectOutsourcingRequirementList(outsourcingRequirement);
        ExcelUtil<OutsourcingRequirement> util = new ExcelUtil<OutsourcingRequirement>(OutsourcingRequirement.class);
        util.exportExcel(response, list, "工序外协需求数据");
    }

    /**
     * 获取工序外协需求详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(outsourcingRequirementService.selectOutsourcingRequirementById(id));
    }

    /**
     * 新增工序外协需求
     */

    @Log(title = "工序外协需求", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OutsourcingRequirement outsourcingRequirement)
    {
        return toAjax(outsourcingRequirementService.insertOutsourcingRequirement(outsourcingRequirement));
    }

    /**
     * 修改工序外协需求
     */

    @Log(title = "工序外协需求", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OutsourcingRequirement outsourcingRequirement)
    {
        return toAjax(outsourcingRequirementService.updateOutsourcingRequirement(outsourcingRequirement));
    }

    /**
     * 删除工序外协需求
     */

    @Log(title = "工序外协需求", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(outsourcingRequirementService.deleteOutsourcingRequirementByIds(ids));
    }
}
