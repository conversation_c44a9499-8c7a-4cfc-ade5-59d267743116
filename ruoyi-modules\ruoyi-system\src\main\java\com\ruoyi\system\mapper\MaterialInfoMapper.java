package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.MaterialInfo;
import com.ruoyi.system.domain.dto.MaterialWithOrderDTO;

/**
 * 物料信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface MaterialInfoMapper 
{
    /**
     * 查询物料信息
     * 
     * @param id 物料信息主键
     * @return 物料信息
     */
    public MaterialInfo selectMaterialInfoById(Long id);

    /**
     * 查询物料信息列表
     * 
     * @param materialInfo 物料信息
     * @return 物料信息集合
     */
    public List<MaterialInfo> selectMaterialInfoList(MaterialInfo materialInfo);
    
    /**
     * 查询物料信息列表（包含订单关联信息）
     * 
     * @param materialInfo 物料信息
     * @return 物料信息与订单关联数据集合
     */
    public List<MaterialWithOrderDTO> selectMaterialInfoWithOrderList(MaterialInfo materialInfo);

    /**
     * 新增物料信息
     * 
     * @param materialInfo 物料信息
     * @return 结果
     */
    public int insertMaterialInfo(MaterialInfo materialInfo);

    /**
     * 修改物料信息
     * 
     * @param materialInfo 物料信息
     * @return 结果
     */
    public int updateMaterialInfo(MaterialInfo materialInfo);

    /**
     * 删除物料信息
     * 
     * @param id 物料信息主键
     * @return 结果
     */
    public int deleteMaterialInfoById(Long id);

    /**
     * 批量删除物料信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMaterialInfoByIds(Long[] ids);
}
