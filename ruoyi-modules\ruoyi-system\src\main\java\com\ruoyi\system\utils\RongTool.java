package com.ruoyi.system.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.domain.UserChatMessage;
import io.rong.RongCloud;
import io.rong.messages.TxtMessage;
import io.rong.methods.group.Group;
import io.rong.methods.message._private.Private;
import io.rong.methods.message.system.MsgSystem;
import io.rong.methods.user.User;
import io.rong.models.Result;
import io.rong.models.group.GroupMember;
import io.rong.models.group.GroupModel;
import io.rong.models.message.GroupMessage;
import io.rong.models.message.PrivateMessage;
import io.rong.models.message.SystemMessage;
import io.rong.models.response.ResponseResult;
import io.rong.models.response.TokenResult;
import io.rong.models.response.UserGroupQueryResult;
import io.rong.models.user.UserModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;


/**
 * 融云工具类
 */
@Component
@RefreshScope
public class RongTool {
    private static final Logger LOGGER = LoggerFactory.getLogger(RongTool.class);

    private  RongCloud rongCloud ;

    public RongTool(@Value("${rongyun.appKey}") String appKey, @Value("${rongyun.appSecret}") String appSecret){
       rongCloud = RongCloud.getInstance(appKey, appSecret);
    }

    /**
     * 获取用户token,永久有效
     *
     * @param userId   用户id
     * @param userName 用户名称
     * @param picUrl   用户头像
     * @return 返回用户token（String）
     */
    public String getToken(String userId, String userName, String picUrl) {
        try {
            User user = rongCloud.user;
            /**
             * API 文档: http://www.rongcloud.cn/docs/server_sdk_api/user/user.html#register
             *
             * 注册用户，生成用户在融云的唯一身份标识 Token
             */
            UserModel userModel = new UserModel()
                    .setId(userId)
                    .setName(userName)
                    .setPortrait(picUrl);
            TokenResult result = user.register(userModel);
            System.out.println(result.toString());
            return  result.getToken();
        }catch (Exception e){
            LOGGER.error("获取用户融云token失败{}",e);
            return "";
        }
    }

    /**
     * 创建群
     * @param groupId
     * @param groupName
     * @param groupUserNames
     * @return
     */
    public Integer createGroup(String groupId,String groupName,String... groupUserNames) {
        try {
            Group group = rongCloud.group;
            GroupMember[] members = new GroupMember[groupUserNames.length];

            for(int i = 0;i<groupUserNames.length;i++){
                String groupUserName = groupUserNames[i];
                GroupMember groupMember = new GroupMember().setId(groupUserName);
                members[i]=groupMember;
            }

            GroupModel groupModel = new GroupModel()
                    .setId(groupId)
                    .setMembers(members)
                    .setName(groupName);
            Result groupCreateResult = (Result)group.create(groupModel);
            return  groupCreateResult.getCode();
        }catch (Exception e){
            LOGGER.error("获取用户融云token失败{}",e);
            return 500;
        }
    }

    /**
     * 修改群名称
     * @param groupId
     * @param groupName
     * @return
     */
    public Integer updateGroup(String groupId,String groupName) {
        try {
            Group group = rongCloud.group;


            GroupModel groupModel = new GroupModel()
                    .setId(groupId)
                    .setName(groupName);
            Result groupCreateResult = (Result)group.update(groupModel);
            return  groupCreateResult.getCode();
        }catch (Exception e){
            LOGGER.error("修改融云群组名失败{}",e);
            return 500;
        }
    }

    /**
     * 解散群
     * @param groupId
     * @param userName
     * @return
     */
    public Integer dismissGroup(String groupId,String userName) {
        try {
            Group group = rongCloud.group;

            GroupMember[] members = new GroupMember[]{new GroupMember().setId(userName)};

            GroupModel groupModel = new GroupModel()
                    .setId(groupId)
                    .setMembers(members);
            Result groupCreateResult = (Result)group.dismiss(groupModel);
            return  groupCreateResult.getCode();
        }catch (Exception e){
            LOGGER.error("融云解散群组失败{}",e);
            return 500;
        }
    }

    /**
     * 用户加入群组
     * @param groupId
     * @param groupName
     * @param groupUserNames
     * @return
     */
    public Integer joinGroup(String groupId,String groupName,String... groupUserNames) {
        try {
            Group group = rongCloud.group;
            GroupMember[] members = new GroupMember[groupUserNames.length];

            for(int i = 0;i<groupUserNames.length;i++){
                String groupUserName = groupUserNames[i];
                GroupMember groupMember = new GroupMember().setId(groupUserName);
                members[i]=groupMember;
            }

            GroupModel groupModel = new GroupModel()
                    .setId(groupId)
                    .setMembers(members)
                    .setName(groupName);
            Result groupCreateResult = (Result)group.join(groupModel);
            return  groupCreateResult.getCode();
        }catch (Exception e){
            LOGGER.error("融云用户加入群组失败{}",e);
            return 500;
        }
    }

    /**
     * 用户推出群组
     * @param groupId
     * @param groupUserNames
     * @return
     */
    public Integer quitGroup(String groupId,String... groupUserNames) {
        try {
            Group group = rongCloud.group;
            GroupMember[] members = new GroupMember[groupUserNames.length];

            for(int i = 0;i<groupUserNames.length;i++){
                String groupUserName = groupUserNames[i];
                GroupMember groupMember = new GroupMember().setId(groupUserName);
                members[i]=groupMember;
            }

            GroupModel groupModel = new GroupModel()
                    .setId(groupId)
                    .setMembers(members);
            Result groupCreateResult = (Result)group.quit(groupModel);
            return  groupCreateResult.getCode();
        }catch (Exception e){
            LOGGER.error("融云用户退出群组失败{}",e);
            return 500;
        }
    }

    /**
     * 查询用户加入的群组列表
     * @param userName
     * @return
     */
    public UserGroupQueryResult queryUserGroup(String userName) {
        try {
            User user = rongCloud.user;

            UserModel userModel = new UserModel()
                    .setId(userName);

            UserGroupQueryResult userGroupQueryResult = (UserGroupQueryResult)user.getGroups(userModel);
            return  userGroupQueryResult;
        }catch (Exception e){
            LOGGER.error("查询用户加入的群组列表失败{}",e);
            return null;
        }
    }
    //TODO 发送单聊信息
    public int sendPrivateMessage(UserChatMessage userChatMessage) {
        try {
            Private msgPrivate = rongCloud.message.msgPrivate;
            //TODO 将内容转化为json字符串
            String json= JSON.toJSONString(userChatMessage);

            TxtMessage txtMessage = new TxtMessage(json, "");
            PrivateMessage privateMessage = new PrivateMessage()
            .setSenderId(userChatMessage.getSendUserName())
            .setTargetId(userChatMessage.getTargetId().split(","))
            .setObjectName(txtMessage.getType())
            .setContent(txtMessage)
            //TODO 本人发送的消息在会话中是从给自己发送一份
            .setIsIncludeSender(1);
            ResponseResult privateResult = msgPrivate.send(privateMessage);
            return  privateResult.getCode();
        }catch (Exception e){
            LOGGER.error("用户发送信息失败{}",e);
            return 500;
        }
    }
    //TODO 发送单聊信息
    public int sendGroupMessage(UserChatMessage userChatMessage) {
        try {
            io.rong.methods.message.group.Group group = rongCloud.message.group;
            //TODO 将内容转化为json字符串
            String json= JSON.toJSONString(userChatMessage);

            TxtMessage txtMessage = new TxtMessage(json, "");
            GroupMessage groupMessage = new GroupMessage()
                    .setSenderId(userChatMessage.getSendUserName())
                    .setTargetId(userChatMessage.getTargetId().split(","))
                    .setObjectName(txtMessage.getType())
                    .setContent(txtMessage)
                    .setIsIncludeSender(1);
            ResponseResult groupResult = group.send(groupMessage);
            return  groupResult.getCode();
        }catch (Exception e){
            LOGGER.error("用户发送群信息失败{}",e);
            return 500;
        }
    }

    /**
     * 发送系统消息
     * @param jsonObject : type 消息类型  1 好友申请通知
     *                     targetId 消息接收者
     *                     senderUserId 实际消息发送者
     *                     systemSender 系统消息的发送者 在融云后台创建系统用户作为系统消息发送者 systemFriendApply(好友申请系统用户)
     *                     remark 备注
     *
     * @return
     *
     */
    public int sendSystemMessage(JSONObject jsonObject) {
        try {
            MsgSystem system =  rongCloud.message.system;
            TxtMessage txtMessage = new TxtMessage(JSON.toJSONString(jsonObject), "");
            SystemMessage systemMessage = new SystemMessage()
                    .setSenderId(jsonObject.getString("systemSender"))
                    .setTargetId(jsonObject.getString("targetId").split(","))
                    .setObjectName(txtMessage.getType())
                    .setContent(txtMessage)
                    .setIsPersisted(1)
                    .setIsCounted(1);

            ResponseResult result = system.send(systemMessage);
            return  result.getCode();
        }catch (Exception e){
            LOGGER.error("用户发送群信息失败{}",e);
            return 500;
        }
    }

//

//    ResponseResult privateResult = Private.send(privateMessage);




}
